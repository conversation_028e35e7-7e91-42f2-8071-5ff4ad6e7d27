//@version=5
indicator("ICT Multi-Timeframe AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500)

// === INPUTS ===
// General Settings
show_only_intraday = input.bool(false, "Show Only on Intraday Timeframes")
max_periods = input.int(3, "Months to Display", minval=1, maxval=12)

// AMR Settings
amr_months = input.int(6, "Months to Average", minval=1, maxval=24)
show_full_amr = input.bool(true, "Show Full AMR")
show_one_third_amr = input.bool(true, "Show 1/3 AMR")
show_two_third_amr = input.bool(true, "Show 2/3 AMR")
show_half_amr = input.bool(true, "Show 1/2 AMR")

// Style Settings
line_width = input.int(1, "Line Width", minval=1, maxval=5)
show_labels = input.bool(true, "Show Labels")
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"])

// === CORE LOGIC ===
// Get monthly data using security() with lookahead_off to prevent repainting
[monthlyOpen, monthlyHigh, monthlyLow] = request.security(syminfo.tickerid, "M", [open, high, low], lookahead=barmerge.lookahead_off)

// Calculate monthly range and AMR
monthlyRange = monthlyHigh - monthlyLow
amrValue = ta.sma(monthlyRange, amr_months)

// Detect new month (works on all timeframes including 1min)
isNewMonth = ta.change(time("M")) or barstate.isfirst

// Store monthly values when new month begins
var float[] monthOpens = array.new_float()
var float[] fullUpLevels = array.new_float()
var float[] fullDownLevels = array.new_float()
var float[] oneThirdUpLevels = array.new_float()
var float[] oneThirdDownLevels = array.new_float()
var float[] twoThirdUpLevels = array.new_float()
var float[] twoThirdDownLevels = array.new_float()
var float[] halfUpLevels = array.new_float()
var float[] halfDownLevels = array.new_float()

if isNewMonth
    // Calculate all levels for the new month
    currentOpen = monthlyOpen
    fullUp = currentOpen + amrValue
    fullDown = currentOpen - amrValue
    oneThirdUp = currentOpen + amrValue/3
    oneThirdDown = currentOpen - amrValue/3
    twoThirdUp = currentOpen + amrValue*2/3
    twoThirdDown = currentOpen - amrValue*2/3
    halfUp = currentOpen + amrValue/2
    halfDown = currentOpen - amrValue/2
    
    // Store values in arrays
    array.unshift(monthOpens, currentOpen)
    array.unshift(fullUpLevels, fullUp)
    array.unshift(fullDownLevels, fullDown)
    array.unshift(oneThirdUpLevels, oneThirdUp)
    array.unshift(oneThirdDownLevels, oneThirdDown)
    array.unshift(twoThirdUpLevels, twoThirdUp)
    array.unshift(twoThirdDownLevels, twoThirdDown)
    array.unshift(halfUpLevels, halfUp)
    array.unshift(halfDownLevels, halfDown)
    
    // Trim arrays to max_periods
    if array.size(monthOpens) > max_periods
        array.pop(monthOpens)
        array.pop(fullUpLevels)
        array.pop(fullDownLevels)
        array.pop(oneThirdUpLevels)
        array.pop(oneThirdDownLevels)
        array.pop(twoThirdUpLevels)
        array.pop(twoThirdDownLevels)
        array.pop(halfUpLevels)
        array.pop(halfDownLevels)

// === DRAWING LOGIC ===
// Function to draw levels efficiently
drawLevels(float[] levels, color lineColor, string labelText) =>
    for i = 0 to math.min(array.size(levels) - 1, max_periods - 1)
        level = array.get(levels, i)
        line.new(bar_index[1], level, bar_index, level, color=lineColor, width=line_width, extend=extend.right)
        if show_labels
            label.new(bar_index, level, text=labelText + (i == 0 ? "" : str.format(" ({0})", i+1)), 
                      color=color.new(lineColor, 70), textcolor=color.white, 
                      style=label.style_label_left, size=label_size)

// Draw all levels
if show_full_amr
    drawLevels(fullUpLevels, color.red, "AMR+")
    drawLevels(fullDownLevels, color.red, "AMR-")

if show_one_third_amr
    drawLevels(oneThirdUpLevels, color.blue, "1/3+")
    drawLevels(oneThirdDownLevels, color.blue, "1/3-")

if show_two_third_amr
    drawLevels(twoThirdUpLevels, color.orange, "2/3+")
    drawLevels(twoThirdDownLevels, color.orange, "2/3-")

if show_half_amr
    drawLevels(halfUpLevels, color.purple, "1/2+")
    drawLevels(halfDownLevels, color.purple, "1/2-")

// Draw monthly opens
drawLevels(monthOpens, color.green, "MO")