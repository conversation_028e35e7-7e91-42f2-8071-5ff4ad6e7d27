//@version=5
// indicator("No Wick Candle & Volume Imbalance"
//   , overlay=true
//   , max_boxes_count = 500
//   , max_labels_count = 500
//   , max_lines_count = 500)

//------------------------------------------------------------------------------
// Settings
//------------------------------------------------------------------------------

// Volume Imbalance
show_vi = input(true, 'Show Volume Imbalances (VI)', inline='vi_css', group='Volume Imbalance')
bull_vi_css = input.color(#2157f3, '', inline='vi_css', group='Volume Imbalance')
bear_vi_css = input.color(#ff1100, '', inline='vi_css', group='Volume Imbalance')

vi_usewidth  = input(false, 'Min Width',  inline='vi_width', group='Volume Imbalance')
vi_gapwidth  = input.float(0, '',         inline='vi_width', group='Volume Imbalance')
vi_method    = input.string('Points', '', options=['Points', '%', 'ATR'], inline='vi_width', group='Volume Imbalance')
vi_extend    = input.int(50, 'Extend VI', minval=0, group='Volume Imbalance')

//------------------------------------------------------------------------------
// New: Wick Tolerance
//------------------------------------------------------------------------------
// You can interpret this threshold in various ways:
// 1) An absolute price level (e.g., 0.1 => 0.1 points).
// 2) A fraction of the total candle size. If using this approach, you can
//    compute the candle range and multiply to get an absolute threshold.
// 3) A fraction of an ATR-based range.
// For simplicity, let's do a straightforward absolute threshold (in price points).
wickTolerance = input.float(0.05, "Wick Tolerance (absolute)", minval=0.0)

// ATR for optional usage in the volume imbalance logic
atrLen = 200
atrVal = ta.atr(atrLen)

//------------------------------------------------------------------------------
// Functions
//------------------------------------------------------------------------------
imbalance_detection(show, usewidth, method, width, top, btm, condition) =>
    var is_width = true
    var count = 0.0

    if usewidth
        dist = top - btm
        is_width := switch method
            'Points' => dist > width
            '%'      => dist / btm * 100 > width
            'ATR'    => dist > atrVal * width

    is_true = show and condition and is_width
    count += is_true ? 1 : 0
    [is_true, count]


bull_filled(condition, btm) =>
    var btms = array.new_float(0)
    var count = 0
    if condition
        array.unshift(btms, btm)
    size = array.size(btms)
    for i = (size > 0 ? size - 1 : na) to 0
        value = array.get(btms, i)
        if low < value
            array.remove(btms, i)
            count += 1
    count


bear_filled(condition, top) =>
    var tops = array.new_float(0)
    var count = 0
    if condition
        array.unshift(tops, top)
    size = array.size(tops)
    for i = (size > 0 ? size - 1 : na) to 0
        value = array.get(tops, i)
        if high > value
            array.remove(tops, i)
            count += 1
    count

//------------------------------------------------------------------------------
// Volume Imbalances
//------------------------------------------------------------------------------
// Bullish
bull_gap_top = math.min(close, open)
bull_gap_btm = math.max(close[1], open[1])

[bull_vi, bull_vi_count] = imbalance_detection(show_vi,vi_usewidth,vi_method,vi_gapwidth,bull_gap_top,bull_gap_btm,open > close[1] and high[1] > low and close > close[1] and open > open[1] and high[1] < bull_gap_top)

bull_vi_filled = bull_filled(bull_vi[1], bull_gap_btm[1])

// Bearish
bear_gap_top = math.min(close[1], open[1])
bear_gap_btm = math.max(close, open)

[bear_vi, bear_vi_count] = imbalance_detection(show_vi,vi_usewidth,vi_method,vi_gapwidth,bear_gap_top,bear_gap_btm,open < close[1] and low[1] < high and close < close[1] and open < open[1] and low[1] > bear_gap_btm)

bear_vi_filled = bear_filled(bear_vi[1], bear_gap_top[1])

// Draw boxes for new VIs
n = bar_index
if bull_vi
    box.new(n-1, bull_gap_top, n + vi_extend, bull_gap_btm, border_color=bull_vi_css, bgcolor=na, border_style=line.style_dotted)

if bear_vi
    box.new(n-1, bear_gap_top, n + vi_extend, bear_gap_btm, border_color=bear_vi_css, bgcolor=na, border_style=line.style_dotted)

//------------------------------------------------------------------------------
// No Wick Candle Conditions (With Tolerance)
//------------------------------------------------------------------------------
// If you’d like to consider a candle as “no wick” when the wick is smaller 
// than `wickTolerance`, we can check the difference between open and low 
// (for bullish) or high and open (for bearish).

// — Option A (Absolute Tolerance in Price Points) —
bullishCondition = close > open and (open - low) <= wickTolerance
bearishCondition = close < open and (high - open) <= wickTolerance

// — Option B (Ratio to Candle Range) —
// If you prefer a ratio approach, for example a ratio of total candle size, 
// you could do something like this:
// candleRange = high - low
// smallWick   = candleRange * wickTolerance
// bullishCondition = close > open and (open - low) <= smallWick
// bearishCondition = close < open and (high - open) <= smallWick

plotshape(bullishCondition, title="No Wick (Tolerance) Green Candle", color=color.green, style=shape.arrowup, size=size.normal, location=location.belowbar)
plotshape(bearishCondition, title="No Wick (Tolerance) Red Candle", color=color.red, style=shape.arrowdown, size=size.normal, location=location.abovebar)

//------------------------------------------------------------------------------
// Alerts
//------------------------------------------------------------------------------
alertcondition(bull_vi, 'Bullish VI', 'Bullish VI detected')
alertcondition(bear_vi, 'Bearish VI', 'Bearish VI detected')

// OPTIONAL: If you want an alert the moment a bullish or bearish VI is filled,
// you could do something like:
alertcondition(bull_vi_filled > bull_vi_filled[1], "Bullish VI Filled", "A bullish volume imbalance was just filled.")
alertcondition(bear_vi_filled > bear_vi_filled[1], "Bearish VI Filled", "A bearish volume imbalance was just filled.")



