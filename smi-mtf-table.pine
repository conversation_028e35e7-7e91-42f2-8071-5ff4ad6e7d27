// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © Ravi

//@version=5
indicator("SMI Multi-Timeframe Table [Ravi]", "SMI MTF Table", overlay = true, max_labels_count = 500)

//---------------------------------------------------------------------------------------------------------------------}
//User Inputs
//---------------------------------------------------------------------------------------------------------------------{
group1 = "Toggle       Timeframe                    Weight"

tf1Tog = input.bool(true, title = "", group = group1, inline = "1")
tf1 = input.timeframe("5", title = "", group = group1, inline = "1")
tf1Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "1")

tf2Tog = input.bool(true, title = "", group = group1, inline = "2")
tf2 = input.timeframe("10", title = "", group = group1, inline = "2")
tf2Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "2")

tf3Tog = input.bool(true, title = "", group = group1, inline = "3")
tf3 = input.timeframe("15", title = "", group = group1, inline = "3")
tf3Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "3")

tf4Tog = input.bool(true, title = "", group = group1, inline = "4")
tf4 = input.timeframe("30", title = "", group = group1, inline = "4")
tf4Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "4")

tf5Tog = input.bool(true, title = "", group = group1, inline = "5")
tf5 = input.timeframe("60", title = "", group = group1, inline = "5")
tf5Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "5")

tf6Tog = input.bool(true, title = "", group = group1, inline = "6")
tf6 = input.timeframe("120", title = "", group = group1, inline = "6")
tf6Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "6")

tf7Tog = input.bool(true, title = "", group = group1, inline = "7")
tf7 = input.timeframe("240", title = "", group = group1, inline = "7")
tf7Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "7")

tf8Tog = input.bool(true, title = "", group = group1, inline = "8")
tf8 = input.timeframe("D", title = "", group = group1, inline = "8")
tf8Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "8")

tf9Tog = input.bool(true, title = "", group = group1, inline = "9")
tf9 = input.timeframe("W", title = "", group = group1, inline = "9")
tf9Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "9")

tf10Tog = input.bool(true, title = "", group = group1, inline = "10")
tf10 = input.timeframe("M", title = "", group = group1, inline = "10")
tf10Multi = input.float(1, title = "", step = 0.1, group = group1, inline = "10")

// SMI Settings
group2 = "SMI Settings"
x = input.int(25, "SMI Index Period", minval = 1, group = group2)
rr = input.int(14, "SMI Volume Flow Period", minval = 1, group = group2)
peakslen = input.int(500, "SMI Normalization Period", minval = 1, group = group2)
thr = input.float(0.9, "SMI High Interest Threshold", minval = 0.01, maxval = 0.99, group = group2)

// Style Settings
green = input.color(#089981, title = "SMI Active Color", group = "Style")
red = input.color(#f23645, title = "No SMI Color", group = "Style")
silver = input.color(color.silver, title = "Neutral Color", group = "Style")

showDash = input.bool(true,title = "Show Dashboard", group = "Dashboard")
dashLoc  = str.lower(str.replace_all(input.string("Top Right",title =  "Location", options = ["Top Right", "Bottom Right", "Bottom Left"] , group = "Dashboard")," ","_"))
textSize = str.lower(input.string("Small", title = "Size", options = ["Tiny", "Small", "Normal"], group = "Dashboard"))
vert = input.string("Vertical", title = "Orientation", group = "Dashboard", options = ["Vertical", "Horizontal"]) == "Vertical"

//---------------------------------------------------------------------------------------------------------------------}
//Functions
//---------------------------------------------------------------------------------------------------------------------{

// SMI Calculation Function - moved request.security outside conditional
get_htf_smi_data(_tf) =>
    request.security("", _tf, [ta.pvi, ta.nvi], lookahead = barmerge.lookahead_off)

// Calculate SMI condition from HTF data
calc_smi_condition(htf_pvi, htf_nvi) =>
    htf_dumb = htf_pvi - ta.ema(htf_pvi, 255)
    htf_smart = htf_nvi - ta.ema(htf_nvi, 255)

    htf_drsi = ta.rsi(htf_dumb, rr)
    htf_srsi = ta.rsi(htf_smart, rr)

    htf_r = htf_srsi / htf_drsi
    htf_sums = math.sum(htf_r, x)
    htf_peak = ta.highest(htf_sums, peakslen)
    htf_index = htf_sums / htf_peak

    htf_index > thr

// Check for SMI signal (arrow) - shows arrow as long as SMI condition is met
calc_smi_arrow(htf_pvi, htf_nvi) =>
    htf_dumb = htf_pvi - ta.ema(htf_pvi, 255)
    htf_smart = htf_nvi - ta.ema(htf_nvi, 255)

    htf_drsi = ta.rsi(htf_dumb, rr)
    htf_srsi = ta.rsi(htf_smart, rr)

    htf_r = htf_srsi / htf_drsi
    htf_sums = math.sum(htf_r, x)
    htf_peak = ta.highest(htf_sums, peakslen)
    htf_index = htf_sums / htf_peak

    // Return true as long as SMI condition is met (original behavior)
    htf_index > thr

//For table colors - Green if SMI arrow appearing, Red if not
get_col(_val) => _val ? green : red

//For table text - show "SMI" when arrow is appearing
get_text(_val) => _val ? "SMI" : ""

//For text color, if the requested timeframe is below the current timeframe we want to let the user know about it.
//This function changes the text color to red
check_tf(_tf) => timeframe.in_seconds(_tf) < timeframe.in_seconds(timeframe.period) ? color.red : color.white
//This function changes the tooltip
check_tf_tt(_tf) => timeframe.in_seconds(_tf) < timeframe.in_seconds(timeframe.period) ?
  "The Requested Timeframe should be Equal-to or Higher than the Current Chart Timeframe.\nThis information should be considered inaccurate for analysis.\nIt is recommended that you change or disable this timeframe.": 
  na

//---------------------------------------------------------------------------------------------------------------------}
//Calculations
//---------------------------------------------------------------------------------------------------------------------{

// Get HTF data for all timeframes (outside conditionals)
[htf1_pvi, htf1_nvi] = get_htf_smi_data(tf1)
[htf2_pvi, htf2_nvi] = get_htf_smi_data(tf2)
[htf3_pvi, htf3_nvi] = get_htf_smi_data(tf3)
[htf4_pvi, htf4_nvi] = get_htf_smi_data(tf4)
[htf5_pvi, htf5_nvi] = get_htf_smi_data(tf5)
[htf6_pvi, htf6_nvi] = get_htf_smi_data(tf6)
[htf7_pvi, htf7_nvi] = get_htf_smi_data(tf7)
[htf8_pvi, htf8_nvi] = get_htf_smi_data(tf8)
[htf9_pvi, htf9_nvi] = get_htf_smi_data(tf9)
[htf10_pvi, htf10_nvi] = get_htf_smi_data(tf10)

// Calculate current timeframe SMI for arrows
current_dumb = ta.pvi - ta.ema(ta.pvi, 255)
current_smart = ta.nvi - ta.ema(ta.nvi, 255)
current_drsi = ta.rsi(current_dumb, rr)
current_srsi = ta.rsi(current_smart, rr)
current_r = current_srsi / current_drsi
current_sums = math.sum(current_r, x)
current_peak = ta.highest(current_sums, peakslen)
current_index = current_sums / current_peak
current_smi_condition = current_index > thr

data = array.new_bool(na)

//Getting SMI arrow signals for each timeframe (only when new signal occurs)
val1 = tf1Tog ? calc_smi_arrow(htf1_pvi, htf1_nvi) : false
val2 = tf2Tog ? calc_smi_arrow(htf2_pvi, htf2_nvi) : false
val3 = tf3Tog ? calc_smi_arrow(htf3_pvi, htf3_nvi) : false
val4 = tf4Tog ? calc_smi_arrow(htf4_pvi, htf4_nvi) : false
val5 = tf5Tog ? calc_smi_arrow(htf5_pvi, htf5_nvi) : false
val6 = tf6Tog ? calc_smi_arrow(htf6_pvi, htf6_nvi) : false
val7 = tf7Tog ? calc_smi_arrow(htf7_pvi, htf7_nvi) : false
val8 = tf8Tog ? calc_smi_arrow(htf8_pvi, htf8_nvi) : false
val9 = tf9Tog ? calc_smi_arrow(htf9_pvi, htf9_nvi) : false
val10 = tf10Tog ? calc_smi_arrow(htf10_pvi, htf10_nvi) : false

//Sending data to array
data.push(val1)
data.push(val2)
data.push(val3)
data.push(val4)
data.push(val5)
data.push(val6)
data.push(val7)
data.push(val8)
data.push(val9)
data.push(val10)

//---------------------------------------------------------------------------------------------------------------------}
//SMI Arrows on Current Timeframe
//---------------------------------------------------------------------------------------------------------------------{

// Display SMI arrows on current timeframe (as long as SMI condition is met)
plotshape(series= current_smi_condition ? 1 : na, title="High Smart Money Interest", color=color.rgb(233, 239, 233), style=shape.arrowup, size=size.normal, location=location.belowbar, force_overlay=true)

//---------------------------------------------------------------------------------------------------------------------}
//Table
//---------------------------------------------------------------------------------------------------------------------{

//Table setup
var tb = table.new(dashLoc, 10, 10
  , bgcolor      = #1e222d
  , border_color = #373a46
  , border_width = 1
  , frame_color  = #373a46
  , frame_width  = 1) 

//Getting the widths for each display style
vert_width = textSize == "normal" ? 0.5 : 0.25
flat_width = textSize == "normal" ? 2 : 1

//Sending everything to the table
if showDash
    if tf1Tog
        tb.cell((vert?0:0),(vert?0:1), tf1,text_color = check_tf(tf1), text_size = textSize, tooltip = check_tf_tt(tf1))
        tb.cell((vert?1:0),(vert?0:0), get_text(val1), bgcolor = get_col(val1), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf2Tog
        tb.cell((vert?0:1),(vert?1:1), tf2,text_color = check_tf(tf2), text_size = textSize, tooltip = check_tf_tt(tf2))
        tb.cell((vert?1:1),(vert?1:0), get_text(val2), bgcolor = get_col(val2), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf3Tog
        tb.cell((vert?0:2),(vert?2:1), tf3,text_color = check_tf(tf3), text_size = textSize, tooltip = check_tf_tt(tf3))
        tb.cell((vert?1:2),(vert?2:0), get_text(val3), bgcolor = get_col(val3), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf4Tog
        tb.cell((vert?0:3),(vert?3:1), tf4,text_color = check_tf(tf4), text_size = textSize, tooltip = check_tf_tt(tf4))
        tb.cell((vert?1:3),(vert?3:0), get_text(val4), bgcolor = get_col(val4), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf5Tog
        tb.cell((vert?0:4),(vert?4:1), tf5,text_color = check_tf(tf5), text_size = textSize, tooltip = check_tf_tt(tf5))
        tb.cell((vert?1:4),(vert?4:0), get_text(val5), bgcolor = get_col(val5), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf6Tog
        tb.cell((vert?0:5),(vert?5:1), tf6,text_color = check_tf(tf6), text_size = textSize, tooltip = check_tf_tt(tf6))
        tb.cell((vert?1:5),(vert?5:0), get_text(val6), bgcolor = get_col(val6), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf7Tog
        tb.cell((vert?0:6),(vert?6:1), tf7,text_color = check_tf(tf7), text_size = textSize, tooltip = check_tf_tt(tf7))
        tb.cell((vert?1:6),(vert?6:0), get_text(val7), bgcolor = get_col(val7), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf8Tog
        tb.cell((vert?0:7),(vert?7:1), tf8,text_color = check_tf(tf8), text_size = textSize, tooltip = check_tf_tt(tf8))
        tb.cell((vert?1:7),(vert?7:0), get_text(val8), bgcolor = get_col(val8), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf9Tog
        tb.cell((vert?0:8),(vert?8:1), tf9,text_color = check_tf(tf9), text_size = textSize, tooltip = check_tf_tt(tf9))
        tb.cell((vert?1:8),(vert?8:0), get_text(val9), bgcolor = get_col(val9), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

    if tf10Tog
        tb.cell((vert?0:9),(vert?9:1), tf10,text_color = check_tf(tf10), text_size = textSize, tooltip = check_tf_tt(tf10))
        tb.cell((vert?1:9),(vert?9:0), get_text(val10), bgcolor = get_col(val10), text_color = color.white, text_size = textSize, height = 1, width = (vert?vert_width:flat_width))

//---------------------------------------------------------------------------------------------------------------------}
