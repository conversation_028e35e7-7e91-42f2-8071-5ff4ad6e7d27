//@version=5
indicator("Multi Deviation Scaled Moving Average [Ravi] - Extended", "Multi DSMA - [Ravi] - Ext", overlay=true)

// ---------------------------------------------------------------------------------------------------------------------
// USER INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

int period = input.int(30, title="Period")
int step = 100 - input.int(60, "Sensitivity", minval = 0, maxval = 100, tooltip = "The Lower input Lower sensitivity")
series float src = hlc3

// Timeframe Inputs for DSMA
bool show_tf1 = input.bool(true, title="Show 3-Minute DSMA")
tf1 = input.timeframe('3', "3-Minute Timeframe")

bool show_tf2 = input.bool(false, title="Show 5-Minute DSMA")
tf2 = input.timeframe('5', "5-Minute Timeframe")

bool show_tf3 = input.bool(true, title="Show 15-Minute DSMA")
tf3 = input.timeframe('15', "15-Minute Timeframe")

bool show_tf4 = input.bool(true, title="Show 1-Hour DSMA")
tf4 = input.timeframe('60', "1-Hour Timeframe")

bool show_tf5 = input.bool(true, title="Show 4-Hour DSMA")
tf5 = input.timeframe('240', "4-Hour Timeframe")

bool show_tf6 = input.bool(true, title="Show 1-Day DSMA")
tf6 = input.timeframe('D', "1-Day Timeframe")

// Configurable Colors for upper and lower for each timeframe
color upper_color1 = input.color(#41a1ce, "3-Minute Upper Color")
color down_color1 = input.color(#ce8541, "3-Minute Down Color")
color upper_color2 = input.color(#41a1ce, "5-Minute Upper Color")
color down_color2 = input.color(#ce8541, "5-Minute Down Color")
color upper_color3 = input.color(#41a1ce, "15-Minute Upper Color")
color down_color3 = input.color(#ce8541, "15-Minute Down Color")
color upper_color4 = input.color(#41a1ce, "1-Hour Upper Color")
color down_color4 = input.color(#ce8541, "1-Hour Down Color")
color upper_color5 = input.color(#41a1ce, "4-Hour Upper Color")
color down_color5 = input.color(#ce8541, "4-Hour Down Color")
color upper_color6 = input.color(#41a1ce, "1-Day Upper Color")
color down_color6 = input.color(#ce8541, "1-Day Down Color")

// ---------------------------------------------------------------------------------------------------------------------
// INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

// Function to calculate the Deviation Scaled Moving Average (DSMA)
dsma(src, int period)=>
    var float a1 = 0.0
    var float b1 = 0.0
    var float c1 = 0.0
    var float c2 = 0.0
    var float c3 = 0.0
    var float filt = 0.0
    var float dsma = 0.0
    var float s = 0.0

    if barstate.isfirst
        pi = 3.1415926535897932
        g = math.sqrt(2)
        s := 2 * pi / period
        a1 := math.exp(-g * pi / (0.5 * period))
        b1 := 2 * a1 * math.cos(g * s / (0.5 * period))
        c2 := b1
        c3 := -a1 * a1
        c1 := 1 - c2 - c3

    zeros = (close - close[2])
    filt := c1 * (zeros + zeros[1]) / 2 + c2 * nz(filt[1]) + c3 * nz(filt[2])

    rms = math.sqrt(ta.ema(math.pow(filt, 2), period))
    scaled_filt = rms != 0 ? filt / rms : 0
    alpha1 = math.abs(scaled_filt) * 5 / period
    dsma := alpha1 * close + (1 - alpha1) * nz(dsma[1])

    dsma

// Function to calculate trend percentage, color, and average DSMA
percent_trend(src, period, step, upper_color, down_color)=>
    dsma_arr = array.new<float>()

    length = period
    dsma1 = dsma(src, length)
    length += step 
    dsma2 = dsma(src, length)
    length += step 
    dsma3 = dsma(src, length)
    length += step 
    dsma4 = dsma(src, length)
    length += step 
    dsma5 = dsma(src, length)
    length += step 
    dsma6 = dsma(src, length)
    length += step 
    dsma7 = dsma(src, length)
    length += step 
    dsma8 = dsma(src, length)

    array.push(dsma_arr, dsma1)
    array.push(dsma_arr, dsma2)
    array.push(dsma_arr, dsma3)
    array.push(dsma_arr, dsma4)
    array.push(dsma_arr, dsma5)
    array.push(dsma_arr, dsma6)
    array.push(dsma_arr, dsma7)
    array.push(dsma_arr, dsma8)

    val = 0.14285714285714285714285714285714

    score = 0.
    for i = 0 to array.size(dsma_arr) - 1
        dsma = array.get(dsma_arr, i)
        if dsma > array.get(dsma_arr, 7)
            score += val

    color =  score > 0.5 
             ? color.from_gradient(score, 0.5, 1, na, upper_color) 
             : color.from_gradient(score, 0, 0.5, down_color, na)

    [score, array.avg(dsma_arr), color]

// ---------------------------------------------------------------------------------------------------------------------
// VISUALIZATION
// ---------------------------------------------------------------------------------------------------------------------

// Function to check if price retraces back to DSMA after moving above
// Function to check if price retraces back to DSMA after moving above for "BUY" signals
is_price_retrace_for_buy(src, dsma) =>
    price_was_above = ta.valuewhen(src > dsma, 1, 1)
    price_touches_dsma = ta.crossunder(src, dsma)
    price_was_above and price_touches_dsma

// Function to check if price retraces back to DSMA after moving below for "SELL" signals
is_price_retrace_for_sell(src, dsma) =>
    price_was_below = ta.valuewhen(src < dsma, 1, 1)
    price_touches_dsma = ta.crossover(src, dsma)
    price_was_below and price_touches_dsma

// 3-Minute DSMA
[score1, ma1, color1] = request.security(syminfo.tickerid, tf1, percent_trend(src, period, step, upper_color1, down_color1))
plot(show_tf1 ? ma1 : na, color = color1, linewidth = 2, transp = 80)
plotshape(show_tf1 and is_price_retrace_for_buy(close, ma1) ? ma1 : na, title="Buy 3-Min", location=location.belowbar, color=color.new(upper_color1, 0), size=size.small, style=shape.labelup, text="3-↑")
plotshape(show_tf1 and is_price_retrace_for_sell(close, ma1) ? ma1 : na, title="Sell 3-Min", location=location.abovebar, color=color.new(down_color1, 0), size=size.small, style=shape.labeldown, text="3-↓")


// 5-Minute DSMA
[score2, ma2, color2] = request.security(syminfo.tickerid, tf2, percent_trend(src, period, step, upper_color2, down_color2))
plot(show_tf2 ? ma2 : na, color = color2, linewidth = 2, transp = 80)
plotshape(show_tf2 and is_price_retrace_for_buy(close, ma2) ? ma2 : na, title="Buy 5-Min", location=location.belowbar, color=color.new(upper_color2, 0), size=size.small, style=shape.labelup, text="5-↑")
plotshape(show_tf2 and is_price_retrace_for_sell(close, ma2) ? ma2 : na, title="Sell 5-Min", location=location.abovebar, color=color.new(down_color2, 0), size=size.small, style=shape.labeldown, text="5-↓")

// 15-Minute DSMA
[score3, ma3, color3] = request.security(syminfo.tickerid, tf3, percent_trend(src, period, step, upper_color3, down_color3))
plot(show_tf3 ? ma3 : na, color = color3, linewidth = 2, transp = 80)
plotshape(show_tf3 and is_price_retrace_for_buy(close, ma3) ? ma3 : na, title="Buy 15-Min", location=location.belowbar, color=color.new(upper_color3, 0), size=size.small, style=shape.labelup, text="15-↑")
plotshape(show_tf3 and is_price_retrace_for_sell(close, ma3) ? ma3 : na, title="Sell 15-Min", location=location.abovebar, color=color.new(down_color3, 0), size=size.small, style=shape.labeldown, text="15-↓")

// 1-Hour DSMA
[score4, ma4, color4] = request.security(syminfo.tickerid, tf4, percent_trend(src, period, step, upper_color4, down_color4))
plot(show_tf4 ? ma4 : na, color = color4, linewidth = 2, transp = 80)
plotshape(show_tf4 and is_price_retrace_for_buy(close, ma4) ? ma4 : na, title="Buy 1-Hour", location=location.belowbar, color=color.new(upper_color4, 0), size=size.small, style=shape.labelup, text="1hr-↑")
plotshape(show_tf4 and is_price_retrace_for_sell(close, ma4) ? ma4 : na, title="Sell 1-Hour", location=location.abovebar, color=color.new(down_color4, 0), size=size.small, style=shape.labeldown, text="1hr-↓")

// 4-Hour DSMA
[score5, ma5, color5] = request.security(syminfo.tickerid, tf5, percent_trend(src, period, step, upper_color5, down_color5))
plot(show_tf5 ? ma5 : na, color = color5, linewidth = 2, transp = 80)
plotshape(show_tf5 and is_price_retrace_for_buy(close, ma5) ? ma5 : na, title="Buy 4-Hour", location=location.belowbar, color=color.new(upper_color5, 0), size=size.small, style=shape.labelup, text="4hr-↑")
plotshape(show_tf5 and is_price_retrace_for_sell(close, ma5) ? ma5 : na, title="Sell 4-Hour", location=location.abovebar, color=color.new(down_color5, 0), size=size.small, style=shape.labeldown, text="4hr-↓")

// 1-Day DSMA
[score6, ma6, color6] = request.security(syminfo.tickerid, tf6, percent_trend(src, period, step, upper_color6, down_color6))
plot(show_tf6 ? ma6 : na, color = color6, linewidth = 2, transp = 80)
plotshape(show_tf6 and is_price_retrace_for_buy(close, ma6) ? ma6 : na, title="Buy 1-Day", location=location.belowbar, color=color.new(upper_color6, 0), size=size.small, style=shape.labelup, text="1d-↑")
