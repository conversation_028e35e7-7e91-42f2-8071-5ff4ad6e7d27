//@version=5
indicator("Advanced Time Technique (ATT)", overlay=true, max_boxes_count=500, max_labels_count=500)

// Calculates time-based ATT numbers (3, 11, 17, 29, 41, 47, 53, 59)

// Input for container timeframe multiplier
string group_att = "ATT Settings"
int containerMultiplier = input.int(60, "Container Multiplier", tooltip="Number of bars in one container cycle", group=group_att)
bool useCustomSession = input.bool(false, "Use Custom Session", tooltip="Enable to set custom market open time", group=group_att)
string sessionStart = input.session("0000-2359", "Trading Session", tooltip="Format: 0000-2359", group=group_att)
float labelOffset = input.float(0.0003, "Label Offset", minval=0.0001, maxval=0.01, step=0.001, group=group_att)
bool showNumbers = input.bool(true, "Show ATT Numbers", tooltip="Display ATT numbers on chart", group=group_att)
bool showSwingsOnly = input.bool(false, "Show Only at Swings", tooltip="Only show ATT numbers at swing points", group=group_att)

// Higher Timeframe Settings
string group_htf = "Higher Timeframe Settings"
bool showHTF = input.bool(true, "Show Higher Timeframe", tooltip="Display higher timeframe reference", group=group_htf)
string htfTimeframe = input.string("D", "Timeframe", options=["15", "30", "60", "240", "D", "W", "M"], tooltip="Higher timeframe to reference", group=group_htf)
color htfColor = input.color(color.new(#2196f3, 70), "HTF Box Color", group=group_htf)
int htfWidth = input.int(1, "HTF Line Width", minval=1, maxval=4, group=group_htf)
string htfStyle = input.string("Dotted", "HTF Line Style", options=["Solid", "Dotted", "Dashed"], group=group_htf)
bool htfExtend = input.bool(true, "Extend HTF Box", tooltip="Extend HTF box to the right", group=group_htf)
bool showHTFLabels = input.bool(true, "Show HTF Labels", tooltip="Display HTF OHLC values", group=group_htf)

// ATT Number Appearance
string group_appearance = "ATT Number Appearance"
color attColor = input.color(color.yellow, "ATT Number Color", group=group_appearance)
color attHighColor = input.color(color.lime, "ATT High Color", group=group_appearance)
color attLowColor = input.color(color.red, "ATT Low Color", group=group_appearance)
string attSize = input.string("Small", "ATT Number Size", options=["Tiny", "Small", "Normal", "Large"], group=group_appearance)
// We'll use the attSize string directly in conditional statements instead of trying to compute attTextSize here

// ATT Numbers
var att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

// Function to check if current bar is ATT number
isAttNumber(int pos) =>
    bool result = false
    for num in att_numbers
        if pos == num
            result := true
    result

// Get session info
var float sessionStartHour = useCustomSession ? str.tonumber(str.substring(sessionStart, 0, 2)) : 0
var float sessionStartMinute = useCustomSession ? str.tonumber(str.substring(sessionStart, 2, 4)) : 0

// Calculate bars since session start using more reliable method
bool isNewSession = na(time[1]) or (dayofweek != dayofweek[1]) or (hour == sessionStartHour and minute == sessionStartMinute and second == 0)
var int barsSinceSession = 0
barsSinceSession := isNewSession ? 0 : barsSinceSession + 1

// Adjust for session start with better time handling
var int sessionOffset = 0
if useCustomSession
    float currentMinutes = (hour * 60 + minute)
    float sessionStartMinutes = (sessionStartHour * 60 + sessionStartMinute)

    // Handle session crossing midnight
    if currentMinutes < sessionStartMinutes
        currentMinutes := currentMinutes + 1440  // Add 24 hours in minutes

    // Convert to int after calculation to avoid precision issues
    sessionOffset := int(math.round(currentMinutes - sessionStartMinutes))

// Calculate adjusted position with better precision
int adjustedPosition = (barsSinceSession + sessionOffset) % containerMultiplier

// Store ATT positions for debugging
var attPositions = array.new_int()
if isAttNumber(adjustedPosition) and array.size(attPositions) < 20
    array.push(attPositions, adjustedPosition)

// Improved Swing Detection with configurable lookback
int swingLength = input.int(3, "Swing Detection Length", minval=2, maxval=10, group=group_att)
int swingStrength = input.int(2, "Swing Strength", minval=1, maxval=5, tooltip="Higher values require stronger swings", group=group_att)

// More robust swing detection
bool swingHighCondition = false
bool swingLowCondition = false

// Check if current bar is a swing high
if high == ta.highest(high, swingLength)
    int highCount = 0
    for i = 1 to swingLength
        if high > high[i]
            highCount += 1
    swingHighCondition := highCount >= swingStrength

// Check if current bar is a swing low
if low == ta.lowest(low, swingLength)
    int lowCount = 0
    for i = 1 to swingLength
        if low < low[i]
            lowCount += 1
    swingLowCondition := lowCount >= swingStrength

// Function already defined above

// Higher Timeframe Data
f_htf_ohlc(htf) =>
    var htf_o = 0., var htf_h = 0., var htf_l = 0., htf_c = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.

    if ta.change(time(htf))
        htf_ox := htf_o
        htf_o := open
        htf_hx := htf_h
        htf_h := high
        htf_lx := htf_l
        htf_l := low
        htf_cx := htf_c[1]
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low, htf_l)
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c]

// Get HTF data
[htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c] = f_htf_ohlc(htfTimeframe)

// Draw HTF box with improved styling
var box htfBox = na
var label htfLabel = na

// Get line style based on user selection
var lineStyle = htfStyle == "Solid" ? line.style_solid : htfStyle == "Dotted" ? line.style_dotted : line.style_dashed

// Extension type based on user preference
var extendType = htfExtend ? extend.right : extend.none

if showHTF and ta.change(time(htfTimeframe))
    // Delete previous box and label
    box.delete(htfBox)
    label.delete(htfLabel)

    // Create new box with user-defined styling
    htfBox := box.new(bar_index[1],htf_hx,bar_index,htf_lx,htfColor,htfWidth,lineStyle,extendType,xloc.bar_index,bgcolor=color.new(htfColor, 90))

    // Add HTF OHLC label if enabled
    if showHTFLabels
        string labelText = htfTimeframe + "\nO: " + str.tostring(htf_ox, "#.####") +
                   "\nH: " + str.tostring(htf_hx, "#.####") +
                   "\nL: " + str.tostring(htf_lx, "#.####") +
                   "\nC: " + str.tostring(htf_cx, "#.####")

        htfLabel := label.new(bar_index[1],htf_hx,labelText,color=color.new(htfColor, 20),style=label.style_label_down,textcolor=color.white,size=size.small)

// Mark ATT points with improved visibility
// We need to use plotshape in global scope, not inside conditional blocks

// ATT High points - Tiny size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingHighCondition)) and
    attSize == "Tiny",
    title="ATT High (Tiny)",
    style=shape.circle,
    location=location.abovebar,
    color=attHighColor,
    size=size.tiny
)

// ATT Low points - Tiny size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingLowCondition)) and
    attSize == "Tiny",
    title="ATT Low (Tiny)",
    style=shape.circle,
    location=location.belowbar,
    color=attLowColor,
    size=size.tiny
)

// ATT High points - Small size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingHighCondition)) and
    attSize == "Small",
    title="ATT High (Small)",
    style=shape.circle,
    location=location.abovebar,
    color=attHighColor,
    size=size.small
)

// ATT Low points - Small size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingLowCondition)) and
    attSize == "Small",
    title="ATT Low (Small)",
    style=shape.circle,
    location=location.belowbar,
    color=attLowColor,
    size=size.small
)

// ATT High points - Normal size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingHighCondition)) and
    attSize == "Normal",
    title="ATT High (Normal)",
    style=shape.circle,
    location=location.abovebar,
    color=attHighColor,
    size=size.normal
)

// ATT Low points - Normal size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingLowCondition)) and
    attSize == "Normal",
    title="ATT Low (Normal)",
    style=shape.circle,
    location=location.belowbar,
    color=attLowColor,
    size=size.normal
)

// ATT High points - Large size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingHighCondition)) and
    attSize == "Large",
    title="ATT High (Large)",
    style=shape.circle,
    location=location.abovebar,
    color=attHighColor,
    size=size.large
)

// ATT Low points - Large size
plotshape(
    isAttNumber(adjustedPosition) and
    (not showSwingsOnly or (showSwingsOnly and swingLowCondition)) and
    attSize == "Large",
    title="ATT Low (Large)",
    style=shape.circle,
    location=location.belowbar,
    color=attLowColor,
    size=size.large
)

// Add labels for ATT numbers with improved styling
// Unlike plotshape, label.new can be used in local scope, but we'll use a more consistent approach

// Create tooltip text
string tooltipText = "ATT Number: " + str.tostring(adjustedPosition) + "\nPosition in cycle: " + str.tostring(adjustedPosition) + "/" + str.tostring(containerMultiplier)

// Function to create labels - this is allowed since we're not using plotting functions inside
createLabelIfNeeded(int pos, float price, bool isHigh) =>
    if showNumbers and isAttNumber(adjustedPosition)
        bool showLabel = not showSwingsOnly or (showSwingsOnly and (isHigh ? swingHighCondition : swingLowCondition))
        if showLabel
            color labelColor = isHigh ? attHighColor : attLowColor
            label.style labelStyle = isHigh ? label.style_label_down : label.style_label_up
            label.new(
                pos,
                price,
                str.tostring(adjustedPosition),
                color=color.new(labelColor, 80),
                style=labelStyle,
                textcolor=color.white,
                size=attSize == "Tiny" ? size.tiny :
                     attSize == "Small" ? size.small :
                     attSize == "Normal" ? size.normal :
                     size.large,
                tooltip=tooltipText
            )

// Create high label
createLabelIfNeeded(bar_index, high + (high * labelOffset), true)

// Create low label
createLabelIfNeeded(bar_index, low - (low * labelOffset), false)

// Debug info with more details
if barstate.islast
    // Create a more informative debug label
    string debugText = "ATT Info:\n" +
               "Current Position: " + str.tostring(adjustedPosition) + "/" + str.tostring(containerMultiplier) + "\n" +
               "Bars Since Session: " + str.tostring(barsSinceSession) + "\n" +
               "Session Offset: " + str.tostring(sessionOffset) + "\n" +
               "Is ATT Number: " + (isAttNumber(adjustedPosition) ? "Yes" : "No") + "\n" +
               "Recent ATT Numbers: "

    // Add recent ATT positions to debug text
    for i = 0 to math.min(array.size(attPositions) - 1, 5)
        debugText := debugText + str.tostring(array.get(attPositions, i)) + ", "

    // Create the debug label
    label.new(bar_index,high,debugText,yloc=yloc.price,style=label.style_label_right,color=color.new(color.gray, 50),textcolor=color.white,size=size.small)