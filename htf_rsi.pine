//@version=5
indicator("Multi-Timeframe RSI [Ravi]", "MTF RSI", overlay=false)

// -----------------------------------------------------------------------------
// Input Parameters
// -----------------------------------------------------------------------------
group_rsi = "RSI Settings"
rsiLength = input.int(14, "RSI Length", minval=1, group=group_rsi)
rsiSource = input.source(close, "RSI Source", group=group_rsi)
rsiObThresh = input.int(70, "Overbought Level", minval=50, maxval=100, group=group_rsi)
rsiOsThresh = input.int(30, "Oversold Level", minval=0, maxval=50, group=group_rsi)

group_tf = "Timeframe Settings"
show_1h = input.bool(true, "Show 1H", inline="1h", group=group_tf)
show_4h = input.bool(true, "Show 4H", inline="4h", group=group_tf)
show_daily = input.bool(true, "Show Daily", inline="d", group=group_tf)
show_weekly = input.bool(true, "Show Weekly", inline="w", group=group_tf)

// -----------------------------------------------------------------------------
// RSI Calculations
// -----------------------------------------------------------------------------
rsi_current = ta.rsi(rsiSource, rsiLength)
rsi_1h = request.security(syminfo.tickerid, "60", ta.rsi(rsiSource, rsiLength))
rsi_4h = request.security(syminfo.tickerid, "240", ta.rsi(rsiSource, rsiLength))
rsi_daily = request.security(syminfo.tickerid, "D", ta.rsi(rsiSource, rsiLength))
rsi_weekly = request.security(syminfo.tickerid, "W", ta.rsi(rsiSource, rsiLength))

// -----------------------------------------------------------------------------
// Plotting
// -----------------------------------------------------------------------------
// Current Timeframe
plot(rsi_current, "Current TF RSI", color=#2962FF)

// Higher Timeframes
plot(show_1h ? rsi_1h : na, "1H RSI", color=#00BCD4, linewidth=2)
plot(show_4h ? rsi_4h : na, "4H RSI", color=#4CAF50, linewidth=2)
plot(show_daily ? rsi_daily : na, "Daily RSI", color=#FFC107, linewidth=2)
plot(show_weekly ? rsi_weekly : na, "Weekly RSI", color=#FF5252, linewidth=2)

// Overbought/Oversold Levels
h1 = hline(rsiObThresh, "Overbought", color=#FF5252, linestyle=hline.style_dashed)
h2 = hline(rsiOsThresh, "Oversold", color=#4CAF50, linestyle=hline.style_dashed)
fill(h1, h2, color=color.rgb(126, 87, 194, 90), title="Middle Zone")

// -----------------------------------------------------------------------------
// Alerts
// -----------------------------------------------------------------------------
// Current Timeframe Alerts
alertcondition(ta.crossover(rsi_current, rsiOsThresh), title="Current TF RSI Crosses Above Oversold", message="Current TF RSI crossed above oversold level")
alertcondition(ta.crossunder(rsi_current, rsiObThresh), title="Current TF RSI Crosses Below Overbought", message="Current TF RSI crossed below overbought level")

// Higher Timeframe Alerts
alertcondition(ta.crossover(rsi_daily, rsiOsThresh), title="Daily RSI Crosses Above Oversold", message="Daily RSI crossed above oversold level")
alertcondition(ta.crossunder(rsi_daily, rsiObThresh), title="Daily RSI Crosses Below Overbought", message="Daily RSI crossed below overbought level")