// Released under the Mozilla Public License 2.0 — see https://mozilla.org/MPL/2.0/
// © 2025 Drysdale Trading Group – refactored from an open-source template script by @noop-noop

//@version=5
indicator("Initial Balance Wave Map", shorttitle="IB", overlay=true, max_bars_back=5000)

//----IB indicator -----
// Options
// @version=5

// === INPUTS ===
ib_session               = input.session("0820-0920", title="Calculation period for the initial balance", group="Calculation period")
show_extra_levels        = input.bool(true,  "Show extra levels (IBH x2 & IBL x2)",                 group="Information")
show_intermediate_levels = input.bool(true,  "Show intermediate levels (50%)",                      group="Information")
show_ib_calculation_area = input.bool(true,  "Initial balance calculation period coloration",      group="Information")
fill_ib_areas            = input.bool(true,  "Colour IB areas",                                    group="Information")
only_current_levels      = input.bool(false, "Only display the current IB Levels",                 group="Information")
only_current_zone        = input.bool(false, "Only display the current IB calculation area",      group="Information")

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === DRAWING PARAMETERS ===
lvl_width         = input.int(1,           "Daily price level width",                               group="Drawings")
high_col          = input.color(color.green,   "Initial balance high levels color",                group="Drawings")
low_col           = input.color(color.red,     "Initial balance low levels color",                 group="Drawings")
middle_col        = input.color(#ffa726,       "50% initial balance color",                        group="Drawings")
main_levels_style = input.string("Solid",  "Main levels line style",     options=["Solid","Dashed","Dotted"], group="Drawings")
ext_levels_style  = input.string("Dashed", "Extended levels line style", options=["Solid","Dashed","Dotted"], group="Drawings")
int_levels_style  = input.string("Dotted", "Intermediate levels line style", options=["Solid","Dashed","Dotted"], group="Drawings")
fill_ib_color     = input.color(#b8851faa, "IB area background color",                         group="Drawings")
ext = extend.none  // manage extension manually

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === UTILITIES ===
inSession(sess) => na(time(timeframe.period, sess)) == false
get_levels(n) =>
    float h = high[1]
    float l = low[1]
    for i = 1 to n
        if low[i]  < l
            l := low[i]
        if high[i] > h
            h := high[i]
    [h, l, (h + l) / 2]

// Function to manage line arrays based on max_periods
max_periods = 20
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// === STATE VARIABLES ===
var line_style_value = get_line_style(main_levels_style)
var label_size_value = get_label_size(label_size)

// === SESSION STATE ===
var float ib_delta       = na
var int   offset         = 0
var float[] delta_history = array.new_float(20)
ins = inSession(ib_session)
bgcolor(show_ib_calculation_area and ins ? #673ab730 : na, title="IB calculation zone")

// === LINE AND LABEL ARRAYS ===
var array<line> ibh_lines = array.new<line>()
var array<line> ibl_lines = array.new<line>()
var array<line> ibm_lines = array.new<line>()
var array<line> ib_plus_lines = array.new<line>()
var array<line> ib_minus_lines = array.new<line>()
var array<line> ib_plus2_lines = array.new<line>()
var array<line> ib_minus2_lines = array.new<line>()
var array<line> ibm_plus_lines = array.new<line>()
var array<line> ibm_minus_lines = array.new<line>()

var array<label> ibh_labels = array.new<label>()
var array<label> ibl_labels = array.new<label>()
var array<label> ibm_labels = array.new<label>()
var array<label> ib_plus_labels = array.new<label>()
var array<label> ib_minus_labels = array.new<label>()
var array<label> ib_plus2_labels = array.new<label>()
var array<label> ib_minus2_labels = array.new<label>()
var array<label> ibm_plus_labels = array.new<label>()
var array<label> ibm_minus_labels = array.new<label>()

// === CURRENT LINES AND LABELS ===
var line  ibh_line        = na
var line  ibl_line        = na
var line  ibm_line        = na
var line  ib_plus_line    = na
var line  ib_minus_line   = na
var line  ib_plus2_line   = na
var line  ib_minus2_line  = na
var line  ibm_plus_line   = na
var line  ibm_minus_line  = na
var box   ib_area         = na

var label ibh_label       = na
var label ibl_label       = na
var label ibm_label       = na
var label ib_plus_label   = na
var label ib_minus_label  = na
var label ib_plus2_label  = na
var label ib_minus2_label = na
var label ibm_plus_label  = na
var label ibm_minus_label = na

// === BUILD SESSION ===
if ins
    offset += 1

if ins[1] and not ins
    // calculate levels
    [h, l, m] = get_levels(offset)
    ib_delta := h - l

    // update history
    if array.size(delta_history) >= 20
        array.shift(delta_history)
    array.push(delta_history, ib_delta)

    // Delete old labels when a new session starts
    if show_labels
        delete_all_labels(ibh_labels)
        delete_all_labels(ibl_labels)
        delete_all_labels(ibm_labels)
        delete_all_labels(ib_plus_labels)
        delete_all_labels(ib_minus_labels)
        delete_all_labels(ib_plus2_labels)
        delete_all_labels(ib_minus2_labels)
        delete_all_labels(ibm_plus_labels)
        delete_all_labels(ibm_minus_labels)

    // Create main level lines
    ibh_line := line.new(bar_index, h, bar_index, h, color=high_col, width=lvl_width, style=line_style_value)
    array.unshift(ibh_lines, ibh_line)
    manage_line_history(ibh_lines)

    ibl_line := line.new(bar_index, l, bar_index, l, color=low_col, width=lvl_width, style=line_style_value)
    array.unshift(ibl_lines, ibl_line)
    manage_line_history(ibl_lines)

    // Main level labels
    if show_labels
        ibh_label := label.new(bar_index + label_x_offset_bars, h + label_y_offset, "IBH" + (show_price_in_label ? str.format(" ({0})", h) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
        array.push(ibh_labels, ibh_label)

        ibl_label := label.new(bar_index + label_x_offset_bars, l + label_y_offset, "IBL" + (show_price_in_label ? str.format(" ({0})", l) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
        array.push(ibl_labels, ibl_label)

    // intermediate 50% line
    if show_intermediate_levels
        ibm_line := line.new(bar_index, m, bar_index, m, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
        array.unshift(ibm_lines, ibm_line)
        manage_line_history(ibm_lines)

        if show_labels
            ibm_label := label.new(bar_index + label_x_offset_bars, m + label_y_offset, "IBM" + (show_price_in_label ? str.format(" ({0})", m) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
            array.push(ibm_labels, ibm_label)

    // extra levels
    if show_extra_levels
        ib_plus_line := line.new(bar_index, h + ib_delta, bar_index, h + ib_delta, color=high_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_plus_lines, ib_plus_line)
        manage_line_history(ib_plus_lines)

        ib_minus_line := line.new(bar_index, l - ib_delta, bar_index, l - ib_delta, color=low_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_minus_lines, ib_minus_line)
        manage_line_history(ib_minus_lines)

        ib_plus2_line := line.new(bar_index, h + ib_delta * 2, bar_index, h + ib_delta * 2, color=high_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_plus2_lines, ib_plus2_line)
        manage_line_history(ib_plus2_lines)

        ib_minus2_line := line.new(bar_index, l - ib_delta * 2, bar_index, l - ib_delta * 2, color=low_col, width=lvl_width, style=get_line_style(ext_levels_style))
        array.unshift(ib_minus2_lines, ib_minus2_line)
        manage_line_history(ib_minus2_lines)

        if show_labels
            ib_plus_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta) + label_y_offset, "IBH+Δ" + (show_price_in_label ? str.format(" ({0})", h + ib_delta) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
            array.push(ib_plus_labels, ib_plus_label)

            ib_minus_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta) + label_y_offset, "IBL-Δ" + (show_price_in_label ? str.format(" ({0})", l - ib_delta) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
            array.push(ib_minus_labels, ib_minus_label)

            ib_plus2_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta * 2) + label_y_offset, "IBH+2Δ" + (show_price_in_label ? str.format(" ({0})", h + ib_delta * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=high_col, color=color.new(color.black, 100))
            array.push(ib_plus2_labels, ib_plus2_label)

            ib_minus2_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta * 2) + label_y_offset, "IBL-2Δ" + (show_price_in_label ? str.format(" ({0})", l - ib_delta * 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=low_col, color=color.new(color.black, 100))
            array.push(ib_minus2_labels, ib_minus2_label)

        if show_intermediate_levels
            ibm_plus_line := line.new(bar_index, h + ib_delta / 2, bar_index, h + ib_delta / 2, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
            array.unshift(ibm_plus_lines, ibm_plus_line)
            manage_line_history(ibm_plus_lines)

            ibm_minus_line := line.new(bar_index, l - ib_delta / 2, bar_index, l - ib_delta / 2, color=middle_col, width=lvl_width, style=get_line_style(int_levels_style))
            array.unshift(ibm_minus_lines, ibm_minus_line)
            manage_line_history(ibm_minus_lines)

            if show_labels
                ibm_plus_label := label.new(bar_index + label_x_offset_bars, (h + ib_delta / 2) + label_y_offset, "IBM+Δ/2" + (show_price_in_label ? str.format(" ({0})", h + ib_delta / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
                array.push(ibm_plus_labels, ibm_plus_label)

                ibm_minus_label := label.new(bar_index + label_x_offset_bars, (l - ib_delta / 2) + label_y_offset, "IBM-Δ/2" + (show_price_in_label ? str.format(" ({0})", l - ib_delta / 2) : ""), style=label.style_label_left, size=label_size_value, textcolor=middle_col, color=color.new(color.black, 100))
                array.push(ibm_minus_labels, ibm_minus_label)

    // fill the IB area
    if fill_ib_areas
        ib_area := box.new(bar_index[offset], h, bar_index, l, bgcolor=fill_ib_color, border_color=color.new(color.black, 100))

    offset := 0

// === UPDATE LINES DURING THE CURRENT DAY ===
if not ins and not na(ibh_line)
    // Update main level lines
    line.set_x2(ibh_line, bar_index)
    line.set_x2(ibl_line, bar_index)

    if show_labels
        label.set_x(ibh_label, bar_index + label_x_offset_bars)
        label.set_x(ibl_label, bar_index + label_x_offset_bars)

    if show_intermediate_levels and not na(ibm_line)
        line.set_x2(ibm_line, bar_index)
        if show_labels
            label.set_x(ibm_label, bar_index + label_x_offset_bars)

    if show_extra_levels
        if not na(ib_plus_line)
            line.set_x2(ib_plus_line, bar_index)
        if not na(ib_minus_line)
            line.set_x2(ib_minus_line, bar_index)
        if not na(ib_plus2_line)
            line.set_x2(ib_plus2_line, bar_index)
        if not na(ib_minus2_line)
            line.set_x2(ib_minus2_line, bar_index)

        if show_labels
            if not na(ib_plus_label)
                label.set_x(ib_plus_label, bar_index + label_x_offset_bars)
            if not na(ib_minus_label)
                label.set_x(ib_minus_label, bar_index + label_x_offset_bars)
            if not na(ib_plus2_label)
                label.set_x(ib_plus2_label, bar_index + label_x_offset_bars)
            if not na(ib_minus2_label)
                label.set_x(ib_minus2_label, bar_index + label_x_offset_bars)

        if show_intermediate_levels
            if not na(ibm_plus_line)
                line.set_x2(ibm_plus_line, bar_index)
            if not na(ibm_minus_line)
                line.set_x2(ibm_minus_line, bar_index)

            if show_labels
                if not na(ibm_plus_label)
                    label.set_x(ibm_plus_label, bar_index + label_x_offset_bars)
                if not na(ibm_minus_label)
                    label.set_x(ibm_minus_label, bar_index + label_x_offset_bars)