//@version=5
indicator('Smaller Time Frame Box Drawer', '', true, max_boxes_count = 500, max_bars_back = 5000, max_lines_count = 500, max_labels_count = 500)

// Inputs for the new indicator
group_box = 'Box Drawing Settings'
box_tf = input.timeframe('1 Hour', 'Timeframe', group=group_box)
box_color = input.color(color.new(#26a69a, 85), 'Box Color', group=group_box)
box_width = input.int(1, 'Box Width', minval=1, maxval=5, group=group_box)

// Function to draw boxes around smaller time frame candles
f_drawBox(_htf, _color, _width) =>
    [O1, H1, L1, C1, O0, H0, L0, C0] = request.security(syminfo.tickerid, _htf, [open, high, low, close])
    var box hl = na
    var int x11 = na
    var int x1 = na

    if ta.change(time(_htf))
        x11 := x1
        x1 := bar_index

        if L1 != 0
            box.delete(hl)
            hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, _color, _width, line.style_dotted, extend.none, xloc.bar_index, _color)

// Call the function to draw boxes
f_drawBox(box_tf, box_color, box_width)