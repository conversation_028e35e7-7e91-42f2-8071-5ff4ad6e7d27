//@version=5
// Single Period AMR Levels Indicator
// This indicator draws Average Monthly Range (AMR) levels for a single configurable period
// Features:
// 1. Draws AMR levels of a single period
// 2. Draws true month opening horizontal and vertical lines
// 3. When new true month opening is detected, old lines stay but end at the new true month open
//    and new lines start with labels

indicator("Single Period AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500, max_bars_back=5000)

// === INPUTS ===
// General settings
amr_period = input.int(6, title="AMR Period (Months)", minval=1, maxval=12, group="AMR Settings")
show_full_amr = input.bool(true, title="Show Full AMR", group="AMR Levels")
show_one_third_amr = input.bool(true, title="Show 1/3 AMR", group="AMR Levels")
show_two_third_amr = input.bool(true, title="Show 2/3 AMR", group="AMR Levels")
show_half_amr = input.bool(true, title="Show 1/2 AMR", group="AMR Levels")

// True month open settings
true_month_open_hour = input.int(4, title="True Month Open Hour (UTC)", minval=0, maxval=23, group="True Month Open")
use_exchange_time = input.bool(false, title="Use Exchange Time Instead of UTC", group="True Month Open")
show_vertical_month = input.bool(true, title="Show Vertical Line at Month Open", group="True Month Open")

// Visual settings
amr_color = input.color(color.yellow, title="AMR Level Color", group="Visual Settings")
true_month_open_color = input.color(color.red, title="True Month Open Color", group="Visual Settings")
line_style_value = input.string("Dotted", title="Line Style", options=["Solid", "Dotted", "Dashed"], group="Visual Settings")
line_width = input.int(1, title="Line Width", minval=1, maxval=4, group="Visual Settings")
show_labels = input.bool(true, title="Show Labels", group="Visual Settings")
show_price_in_label = input.bool(false, title="Show Price in Label", group="Visual Settings")
label_size_value = input.string("Small", title="Label Size", options=["Tiny", "Small", "Normal", "Large"], group="Visual Settings")
label_y_offset = input.float(0.0, title="Label Y Offset", step=0.1, group="Visual Settings")
show_historical = input.bool(true, title="Show Historical Levels", group="Visual Settings")
max_periods = input.int(12, title="Maximum Periods to Display", minval=1, maxval=24, group="Visual Settings")

// === VARIABLES ===
// Line style mapping
var line_style = line.style_solid
if line_style_value == "Dotted"
    line_style := line.style_dotted
else if line_style_value == "Dashed"
    line_style := line.style_dashed

// Label size mapping
var label_size = size.small
if label_size_value == "Tiny"
    label_size := size.tiny
else if label_size_value == "Normal"
    label_size := size.normal
else if label_size_value == "Large"
    label_size := size.large

// Structure to store month data
type MonthData
    int timestamp
    float open_price
    float amr_value
    line vertical_line
    line tmo_line
    line full_up_line
    line full_down_line
    line one_third_up_line
    line one_third_down_line
    line two_third_up_line
    line two_third_down_line
    line half_up_line
    line half_down_line
    int start_bar
    int end_bar

// Array to store month data
var month_data_array = array.new<MonthData>()

// Labels for current month
var tmo_label = label.new(na, na, "", xloc=xloc.bar_index)
var full_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var full_down_label = label.new(na, na, "", xloc=xloc.bar_index)
var one_third_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var one_third_down_label = label.new(na, na, "", xloc=xloc.bar_index)
var two_third_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var two_third_down_label = label.new(na, na, "", xloc=xloc.bar_index)
var half_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var half_down_label = label.new(na, na, "", xloc=xloc.bar_index)

// Store the last AMR period to detect changes
var int last_amr_period = amr_period

// === FUNCTIONS ===
// Calculate AMR (Average Monthly Range)
calculate_amr(lookback_period) =>
    // Request monthly high, low values
    [mh, ml, mo] = request.security(syminfo.tickerid, "M", [high, low, open], gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_off)

    // Calculate the monthly range
    mrange = mh - ml

    // Create an array to store the last N monthly ranges
    var ranges = array.new_float(0)

    // Add the current range to the array
    if not na(mrange)
        array.unshift(ranges, mrange)

    // Keep only the lookback_period number of ranges
    while array.size(ranges) > lookback_period
        array.pop(ranges)

    // Calculate the sum of ranges
    sum_range = 0.0
    count = 0

    // Sum up the available ranges (up to lookback_period)
    for i = 0 to math.min(array.size(ranges) - 1, lookback_period - 1)
        range_val = array.get(ranges, i)
        if not na(range_val)
            sum_range := sum_range + range_val
            count := count + 1

    // Calculate the average
    avg_range = count > 0 ? sum_range / count : na

    // Return the average range
    avg_range

// Create a horizontal line
create_horizontal_line(price, color, style, x1, x2) =>
    // Check if the start bar is too far in the past
    if bar_index - x1 > 500  // Pine Script has a limit of about 500 bars for xloc.bar_index
        // Use time-based coordinates instead
        t1 = time[bar_index - x1]
        t2 = time[bar_index - x2]
        line.new(x1=t1, y1=price, x2=t2, y2=price, color=color, style=style, width=line_width, xloc=xloc.time)
    else
        // Use bar-based coordinates
        line.new(x1=x1, y1=price, x2=x2, y2=price, color=color, style=style, width=line_width, xloc=xloc.bar_index)

// Create a vertical line
create_vertical_line(bar_pos, color) =>
    // Check if the bar position is too far in the past
    if bar_index - bar_pos > 500  // Pine Script has a limit of about 500 bars for xloc.bar_index
        // Use time-based coordinates instead
        t = time[bar_index - bar_pos]
        line.new(x1=t, y1=low - (high-low)*0.5, x2=t, y2=high + (high-low)*0.5,
                 color=color, style=line.style_dashed, width=2, extend=extend.both, xloc=xloc.time)
    else
        // Use bar-based coordinates
        line.new(x1=bar_pos, y1=low - (high-low)*0.5, x2=bar_pos, y2=high + (high-low)*0.5,
                 color=color, style=line.style_dashed, width=2, extend=extend.both, xloc=xloc.bar_index)

// Create or update a label
create_or_update_label(label_obj, price, t, color, x_pos) =>
    if show_labels
        if na(label_obj) or na(label.get_x(label_obj))
            // Check if the position is too far in the past
            if bar_index - x_pos > 500  // Pine Script has a limit of about 500 bars for xloc.bar_index
                // Use time-based coordinates
                time_val = time[bar_index - x_pos]
                label.new(x=time_val, y=price + label_y_offset,
                         text=t + (show_price_in_label ? str.format(" ({0})", price) : ""),
                         style=label.style_label_left, size=label_size,
                         textcolor=color, color=color.new(color.black, 100), xloc=xloc.time)
            else
                // Use bar-based coordinates
                label.new(x=x_pos, y=price + label_y_offset,
                         text=t + (show_price_in_label ? str.format(" ({0})", price) : ""),
                         style=label.style_label_left, size=label_size,
                         textcolor=color, color=color.new(color.black, 100), xloc=xloc.bar_index)
        else
            // Check if we need to update the xloc type
            current_xloc = label.get_xloc(label_obj)
            if bar_index - x_pos > 500 and current_xloc == xloc.bar_index
                // Need to switch to time-based coordinates
                time_val = time[bar_index - x_pos]
                label.set_xloc(label_obj, xloc.time)
                label.set_x(label_obj, time_val)
            else if bar_index - x_pos <= 500 and current_xloc == xloc.time
                // Can switch back to bar-based coordinates
                label.set_xloc(label_obj, xloc.bar_index)
                label.set_x(label_obj, x_pos)
            else
                // Just update the position with the same xloc type
                label.set_x(label_obj, x_pos)

            label.set_y(label_obj, price + label_y_offset)
            label.set_text(label_obj, t + (show_price_in_label ? str.format(" ({0})", price) : ""))
            label.set_textcolor(label_obj, color)
            label_obj

// Delete a line safely
delete_line_safely(line_obj) =>
    if not na(line_obj)
        line.delete(line_obj)

// === MAIN LOGIC ===
// Define session for true month open detection
true_month_hour_str = true_month_open_hour < 10 ? "0" + str.tostring(true_month_open_hour) : str.tostring(true_month_open_hour)
true_month_next_hour = (true_month_open_hour + 1) % 24
true_month_next_hour_str = true_month_next_hour < 10 ? "0" + str.tostring(true_month_next_hour) : str.tostring(true_month_next_hour)
true_month_session = true_month_hour_str + "00-" + true_month_next_hour_str + "00"

// Determine if we're at a new month based on configurable true open time
is_true_month_start = not na(time(timeframe.period, true_month_session, "GMT+0")) and
                     na(time(timeframe.period, true_month_session, "GMT+0")[1]) and
                     dayofmonth(time, "GMT+0") == 1

// Use either exchange time or configurable true month open time
is_new_month = use_exchange_time ? timeframe.change("M") : is_true_month_start

// Calculate AMR value when needed
var float amr_value = na
if is_new_month or barstate.isfirst or last_amr_period != amr_period
    amr_value := calculate_amr(amr_period)

    // If AMR period changed, update all stored month data
    if last_amr_period != amr_period
        // Update AMR values for all months in the array
        for i = 0 to array.size(month_data_array) - 1
            month_data = array.get(month_data_array, i)
            month_data.amr_value := amr_value

            // Delete existing lines to force redraw
            delete_line_safely(month_data.tmo_line)
            delete_line_safely(month_data.full_up_line)
            delete_line_safely(month_data.full_down_line)
            delete_line_safely(month_data.one_third_up_line)
            delete_line_safely(month_data.one_third_down_line)
            delete_line_safely(month_data.two_third_up_line)
            delete_line_safely(month_data.two_third_down_line)
            delete_line_safely(month_data.half_up_line)
            delete_line_safely(month_data.half_down_line)

            // Reset line references to force creation of new lines
            month_data.tmo_line := na
            month_data.full_up_line := na
            month_data.full_down_line := na
            month_data.one_third_up_line := na
            month_data.one_third_down_line := na
            month_data.two_third_up_line := na
            month_data.two_third_down_line := na
            month_data.half_up_line := na
            month_data.half_down_line := na

            array.set(month_data_array, i, month_data)

    last_amr_period := amr_period

// Handle true month open
if is_new_month or barstate.isfirst
    // Get the true month open price
    true_month_open_price = barstate.isfirst ? close : open

    // Create a new month data object
    month_data = MonthData.new()
    month_data.timestamp := int(time)
    month_data.open_price := true_month_open_price
    month_data.amr_value := amr_value
    month_data.start_bar := bar_index
    month_data.end_bar := bar_index + 5  // Default end bar, will be updated later

    // Draw vertical line at month open if needed
    if show_vertical_month
        month_data.vertical_line := create_vertical_line(bar_index, true_month_open_color)

    // Add the new month data to the array
    array.unshift(month_data_array, month_data)

    // Update end bar for previous month if it exists
    if array.size(month_data_array) > 1
        prev_month = array.get(month_data_array, 1)
        prev_month.end_bar := bar_index
        array.set(month_data_array, 1, prev_month)

    // Manage array size
    if array.size(month_data_array) > max_periods
        oldest_month = array.pop(month_data_array)

        // Delete all lines for the oldest month
        delete_line_safely(oldest_month.vertical_line)
        delete_line_safely(oldest_month.tmo_line)
        delete_line_safely(oldest_month.full_up_line)
        delete_line_safely(oldest_month.full_down_line)
        delete_line_safely(oldest_month.one_third_up_line)
        delete_line_safely(oldest_month.one_third_down_line)
        delete_line_safely(oldest_month.two_third_up_line)
        delete_line_safely(oldest_month.two_third_down_line)
        delete_line_safely(oldest_month.half_up_line)
        delete_line_safely(oldest_month.half_down_line)

// Draw or update lines on the last bar
if barstate.islast and array.size(month_data_array) > 0
    // Update the end bar for the current month
    current_month = array.get(month_data_array, 0)
    current_month.end_bar := bar_index + 5
    array.set(month_data_array, 0, current_month)

    // Draw lines for each month
    for i = 0 to math.min(array.size(month_data_array) - 1, max_periods - 1)
        month_data = array.get(month_data_array, i)

        // Only draw if we have valid data
        if not na(month_data.open_price) and not na(month_data.amr_value)
            // Find the start bar for this month if not already set
            if na(month_data.start_bar)
                month_data.start_bar := bar_index
                for j = 0 to math.min(bar_index, 9999)  // Limit to 9999 to avoid history-referencing error
                    if time[j] <= month_data.timestamp
                        month_data.start_bar := bar_index - j
                        break

            // Draw true month open line
            if na(month_data.tmo_line)
                month_data.tmo_line := create_horizontal_line(month_data.open_price, true_month_open_color, line_style, month_data.start_bar, month_data.end_bar)
            else
                line.set_x1(month_data.tmo_line, month_data.start_bar)
                line.set_x2(month_data.tmo_line, month_data.end_bar)

            // Draw true month open label (only for current month)
            if i == 0
                tmo_label := create_or_update_label(tmo_label, month_data.open_price, "TMO", true_month_open_color, month_data.end_bar)

            // Calculate AMR levels
            float full_up = month_data.open_price + month_data.amr_value
            float full_down = month_data.open_price - month_data.amr_value
            float one_third_up = month_data.open_price + (month_data.amr_value / 3)
            float one_third_down = month_data.open_price - (month_data.amr_value / 3)
            float two_third_up = month_data.open_price + (month_data.amr_value * 2 / 3)
            float two_third_down = month_data.open_price - (month_data.amr_value * 2 / 3)
            float half_up = month_data.open_price + (month_data.amr_value / 2)
            float half_down = month_data.open_price - (month_data.amr_value / 2)

            // Draw full AMR levels
            if show_full_amr
                // Draw full AMR+ line
                if na(month_data.full_up_line)
                    month_data.full_up_line := create_horizontal_line(full_up, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.full_up_line, month_data.start_bar)
                    line.set_x2(month_data.full_up_line, month_data.end_bar)
                    line.set_y1(month_data.full_up_line, full_up)
                    line.set_y2(month_data.full_up_line, full_up)

                // Draw full AMR+ label (only for current month)
                if i == 0
                    full_up_label := create_or_update_label(full_up_label, full_up, "AMR+", amr_color, month_data.end_bar)

                // Draw full AMR- line
                if na(month_data.full_down_line)
                    month_data.full_down_line := create_horizontal_line(full_down, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.full_down_line, month_data.start_bar)
                    line.set_x2(month_data.full_down_line, month_data.end_bar)
                    line.set_y1(month_data.full_down_line, full_down)
                    line.set_y2(month_data.full_down_line, full_down)

                // Draw full AMR- label (only for current month)
                if i == 0
                    full_down_label := create_or_update_label(full_down_label, full_down, "AMR-", amr_color, month_data.end_bar)

            // Draw 1/3 AMR levels
            if show_one_third_amr
                // Draw 1/3 AMR+ line
                if na(month_data.one_third_up_line)
                    month_data.one_third_up_line := create_horizontal_line(one_third_up, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.one_third_up_line, month_data.start_bar)
                    line.set_x2(month_data.one_third_up_line, month_data.end_bar)
                    line.set_y1(month_data.one_third_up_line, one_third_up)
                    line.set_y2(month_data.one_third_up_line, one_third_up)

                // Draw 1/3 AMR+ label (only for current month)
                if i == 0
                    one_third_up_label := create_or_update_label(one_third_up_label, one_third_up, "1/3+", amr_color, month_data.end_bar)

                // Draw 1/3 AMR- line
                if na(month_data.one_third_down_line)
                    month_data.one_third_down_line := create_horizontal_line(one_third_down, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.one_third_down_line, month_data.start_bar)
                    line.set_x2(month_data.one_third_down_line, month_data.end_bar)
                    line.set_y1(month_data.one_third_down_line, one_third_down)
                    line.set_y2(month_data.one_third_down_line, one_third_down)

                // Draw 1/3 AMR- label (only for current month)
                if i == 0
                    one_third_down_label := create_or_update_label(one_third_down_label, one_third_down, "1/3-", amr_color, month_data.end_bar)

            // Draw 2/3 AMR levels
            if show_two_third_amr
                // Draw 2/3 AMR+ line
                if na(month_data.two_third_up_line)
                    month_data.two_third_up_line := create_horizontal_line(two_third_up, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.two_third_up_line, month_data.start_bar)
                    line.set_x2(month_data.two_third_up_line, month_data.end_bar)
                    line.set_y1(month_data.two_third_up_line, two_third_up)
                    line.set_y2(month_data.two_third_up_line, two_third_up)

                // Draw 2/3 AMR+ label (only for current month)
                if i == 0
                    two_third_up_label := create_or_update_label(two_third_up_label, two_third_up, "2/3+", amr_color, month_data.end_bar)

                // Draw 2/3 AMR- line
                if na(month_data.two_third_down_line)
                    month_data.two_third_down_line := create_horizontal_line(two_third_down, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.two_third_down_line, month_data.start_bar)
                    line.set_x2(month_data.two_third_down_line, month_data.end_bar)
                    line.set_y1(month_data.two_third_down_line, two_third_down)
                    line.set_y2(month_data.two_third_down_line, two_third_down)

                // Draw 2/3 AMR- label (only for current month)
                if i == 0
                    two_third_down_label := create_or_update_label(two_third_down_label, two_third_down, "2/3-", amr_color, month_data.end_bar)

            // Draw 1/2 AMR levels
            if show_half_amr
                // Draw 1/2 AMR+ line
                if na(month_data.half_up_line)
                    month_data.half_up_line := create_horizontal_line(half_up, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.half_up_line, month_data.start_bar)
                    line.set_x2(month_data.half_up_line, month_data.end_bar)
                    line.set_y1(month_data.half_up_line, half_up)
                    line.set_y2(month_data.half_up_line, half_up)

                // Draw 1/2 AMR+ label (only for current month)
                if i == 0
                    half_up_label := create_or_update_label(half_up_label, half_up, "1/2+", amr_color, month_data.end_bar)

                // Draw 1/2 AMR- line
                if na(month_data.half_down_line)
                    month_data.half_down_line := create_horizontal_line(half_down, amr_color, line_style, month_data.start_bar, month_data.end_bar)
                else
                    line.set_x1(month_data.half_down_line, month_data.start_bar)
                    line.set_x2(month_data.half_down_line, month_data.end_bar)
                    line.set_y1(month_data.half_down_line, half_down)
                    line.set_y2(month_data.half_down_line, half_down)

                // Draw 1/2 AMR- label (only for current month)
                if i == 0
                    half_down_label := create_or_update_label(half_down_label, half_down, "1/2-", amr_color, month_data.end_bar)

            // Update the month data in the array
            array.set(month_data_array, i, month_data)
