//@version=5
indicator("Advanced Time Technique (ATT) with HTF Box Candles", "", true, max_boxes_count = 500, max_lines_count = 500, max_labels_count = 500, max_bars_back = 5000)

// ---------------------------------------------------------------------------------------------- //
// Inputs  -------------------------------------------------------------------------------------- //

// HTF Box Candle Settings
group_candle = 'HTF Candle Settings'
htfCndl1  = input.bool(true, '1st HTF Candle', inline='TYP', group=group_candle)
cndlType1 = input.string('Standard', '', options=['Standard', 'Heikin Ashi'], inline='TYP', group=group_candle)
htfUser1  = input.string('1 Day', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP', group=group_candle)
htfCndl2  = input.bool(false, '2nd HTF Candle', inline='TYP3', group=group_candle)
cndlType2 = input.string('Standard', '', options=['Standard', 'Heikin Ashi'], inline='TYP3', group=group_candle)
htfUser2  = input.string('1 Week', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP3', group=group_candle)
bullC     = input.color(#26a69a, 'Candle : Bull', inline='CNDL', group=group_candle)
bearC     = input.color(#ef5350, 'Bear', inline='CNDL', group=group_candle)
trans     = input.int(85, 'Transp', inline='CNDL', minval=65, maxval=95, group=group_candle)
lineWidth = input.int(1, 'Line Width', inline='CNDL', minval=1, maxval=4, group=group_candle)

// ATT Settings
group_att = 'Advanced Time Technique (ATT) Settings'
showATT = input.bool(true, 'Show ATT Markers', group=group_att)
attColor = input.color(#f7cb4d, 'ATT Label Color', group=group_att)
attHighColor = input.color(#00ff00, 'ATT High Marker Color', group=group_att)
attLowColor = input.color(#ff0000, 'ATT Low Marker Color', group=group_att)
showLabels = input.bool(true, 'Show ATT Labels', group=group_att)
labelSize = input.string('Small', 'Marker & Label Size', options=['Tiny', 'Small', 'Normal'], group=group_att)
showSwingPoints = input.bool(true, 'Highlight Swing Points Only', tooltip='When enabled, only shows markers at swing highs/lows', group=group_att)
lookbackBars = input.int(3, 'Swing Point Lookback Bars', minval=1, maxval=10, group=group_att)

// ATT Minutes to highlight
group_att_minutes = 'ATT Minutes'
show_3min = input.bool(true, '3', inline='min1', group=group_att_minutes)
show_11min = input.bool(true, '11', inline='min1', group=group_att_minutes)
show_17min = input.bool(true, '17', inline='min1', group=group_att_minutes)
show_29min = input.bool(true, '29', inline='min2', group=group_att_minutes)
show_41min = input.bool(true, '41', inline='min2', group=group_att_minutes)
show_47min = input.bool(true, '47', inline='min3', group=group_att_minutes)
show_53min = input.bool(true, '53', inline='min3', group=group_att_minutes)
show_59min = input.bool(true, '59', inline='min3', group=group_att_minutes)

// ---------------------------------------------------------------------------------------------- //
// Functions  ----------------------------------------------------------------------------------- //

checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 :  _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)

    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0.,     htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c]

f_haTicker(_htf) =>
    haTicker = ticker.heikinashi(syminfo.tickerid)
    [ha_o, ha_h, ha_l, ha_c] = request.security(haTicker, _htf, [open, high, low, close])
    ha_ox = ta.valuewhen(ta.change(time(_htf)), ha_o, 0)
    ha_hx = ta.valuewhen(ta.change(time(_htf)), ha_h, 0)
    ha_lx = ta.valuewhen(ta.change(time(_htf)), ha_l, 0)
    ha_cx = ta.valuewhen(ta.change(time(_htf)), ha_c, 0)

    [ha_ox, ha_hx, ha_lx, ha_cx, ha_o, ha_h, ha_l, ha_c]

f_getTF(_htf) =>
    htf = _htf == '3 Mins' ? '3' : _htf == '5 Mins' ? '5' : _htf == '10 Mins' ? '10' : _htf == '15 Mins' ? '15' : _htf == '30 Mins' ? '30' : _htf == '45 Mins' ? '45' : _htf == '1 Hour' ? '60' : _htf == '2 Hours' ? '120' : _htf == '3 Hours' ? '180' : _htf == '4 Hours' ? '240' : _htf == '1 Day' ? 'D' : _htf == '1 Week' ? 'W' : _htf == '1 Month' ? 'M' : _htf == '3 Months' ? '3M' : _htf == '6 Months' ? '6M' : _htf == '1 Year' ? '12M' : na
    htf

f_processCandles(_show, _htf, _cndl, _bullC, _bearC, _trans, _width) =>
    if _show
        [O1 , H1 , L1 , C1 , O0 , H0 , L0 , C0 ] = f_htf_ohlc(_htf)
        [hO1, hH1, hL1, hC1, hO0, hH0, hL0, hC0] = f_haTicker(_htf)

        O0 := _cndl == 'Heikin Ashi' ? hO0 : O0, O1 := _cndl == 'Heikin Ashi' ? hO1 : O1
        H0 := _cndl == 'Heikin Ashi' ? hH0 : H0, H1 := _cndl == 'Heikin Ashi' ? hH1 : H1
        L0 := _cndl == 'Heikin Ashi' ? hL0 : L0, L1 := _cndl == 'Heikin Ashi' ? hL1 : L1
        C0 := _cndl == 'Heikin Ashi' ? hC0 : C0, C1 := _cndl == 'Heikin Ashi' ? hC1 : C1

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)
                true
            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)
                true

// Function to check if current bar is an ATT minute
isATTMinute(minute) =>
    result = false
    if show_3min and (minute == 3 or minute == 2 or minute == 4)
        result := true
    if show_11min and (minute == 11 or minute == 10 or minute == 12)
        result := true
    if show_17min and (minute == 17 or minute == 16 or minute == 18)
        result := true
    if show_29min and (minute == 29 or minute == 28 or minute == 30)
        result := true
    if show_41min and (minute == 41 or minute == 40 or minute == 42)
        result := true
    if show_47min and (minute == 47 or minute == 46 or minute == 48)
        result := true
    if show_53min and (minute == 53 or minute == 52 or minute == 54)
        result := true
    if show_59min and (minute == 59 or minute == 58 or minute == 0)  // Note: 60 becomes 0 in the next hour
        result := true
    result

// Function to detect swing points
isSwingHigh(lookback) =>
    high > ta.highest(high[1], lookback)

isSwingLow(lookback) =>
    low < ta.lowest(low[1], lookback)

// We can't use drawATTMarker function with plotshape since plotshape must be in global scope
// Instead, we'll use this function just for labels
drawATTLabels(isATT, minute, isSwingH, isSwingL) =>
    if isATT and showATT and showLabels
        labelText = str.tostring(minute)
        labelSizeValue = labelSize == 'Tiny' ? size.tiny : labelSize == 'Small' ? size.small : size.normal

        // Draw high label if showing all points or if it's a swing high
        if not showSwingPoints or isSwingH
            label.new(bar_index, high + (high * 0.001), labelText, xloc.bar_index, yloc.price,
                      color.new(color.black, 100), label.style_label_down, attHighColor, labelSizeValue)

        // Draw low label if showing all points or if it's a swing low
        if not showSwingPoints or isSwingL
            label.new(bar_index, low - (low * 0.001), labelText, xloc.bar_index, yloc.price,
                      color.new(color.black, 100), label.style_label_up, attLowColor, labelSizeValue)

// ---------------------------------------------------------------------------------------------- //
// Main Logic ---------------------------------------------------------------------------------- //

htf1 = f_getTF(htfUser1)
htf2 = f_getTF(htfUser2)
supported1 = checkIf(timeframe.period, htf1)
supported2 = checkIf(timeframe.period, htf2)

// Process HTF Box Candles
if chart.is_standard
    if supported1
        f_processCandles(htfCndl1, htf1, cndlType1, bullC, bearC, trans, lineWidth)
    if supported2
        f_processCandles(htfCndl2, htf2, cndlType2, bullC, bearC, trans, lineWidth)

// Get ATT status for current bar - using variables in global scope for plotshape
var bool isATT = false
var int currentMinute = 0
var bool isSwingH = false
var bool isSwingL = false

// Reset values on each bar to ensure proper calculation
isATT := false
isSwingH := false
isSwingL := false

if timeframe.isintraday and timeframe.in_seconds() <= 60 * 60  // Only apply to intraday charts up to 1 hour
    currentMinute := minute(time)
    isATT := isATTMinute(currentMinute)
    isSwingH := isSwingHigh(lookbackBars)
    isSwingL := isSwingLow(lookbackBars)

    // Draw labels only (circles are drawn with plotshape)
    if isATT
        drawATTLabels(isATT, currentMinute, isSwingH, isSwingL)

// Plot ATT markers as circles - these must be in global scope
// We need separate plotshape calls for each size since Pine Script requires const string for size parameter

// Tiny size
plotshape(isATT and showATT and (showSwingPoints ? isSwingH : true) and labelSize == 'Tiny', title="ATT High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT and showATT and (showSwingPoints ? isSwingL : true) and labelSize == 'Tiny', title="ATT Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// Small size
plotshape(isATT and showATT and (showSwingPoints ? isSwingH : true) and labelSize == 'Small', title="ATT High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT and showATT and (showSwingPoints ? isSwingL : true) and labelSize == 'Small', title="ATT Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// Normal size
plotshape(isATT and showATT and (showSwingPoints ? isSwingH : true) and labelSize == 'Normal', title="ATT High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT and showATT and (showSwingPoints ? isSwingL : true) and labelSize == 'Normal', title="ATT Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)
