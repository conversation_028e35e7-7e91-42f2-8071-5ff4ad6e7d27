//@version=5
indicator("Multi Deviation Scaled Moving Average [Ravi] - Extended", "Multi DSMA - [Ravi] - Ext", overlay=true)

// ---------------------------------------------------------------------------------------------------------------------
// USER INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

int period = input.int(30, title="Period")
int step = 100 - input.int(60, "Sensitivity", minval = 0, maxval = 100, tooltip = "The Lower input Lower sensitivity")
series float src = hlc3

// Timeframe Inputs for DSMA
bool show_tf1 = input.bool(true, title="Show 3-Minute DSMA")
tf1 = input.timeframe('3', "3-Minute Timeframe")

bool show_tf2 = input.bool(true, title="Show 5-Minute DSMA")
tf2 = input.timeframe('5', "5-Minute Timeframe")

bool show_tf3 = input.bool(true, title="Show 15-Minute DSMA")
tf3 = input.timeframe('15', "15-Minute Timeframe")

bool show_tf4 = input.bool(true, title="Show 1-Hour DSMA")
tf4 = input.timeframe('60', "1-Hour Timeframe")

bool show_tf5 = input.bool(true, title="Show 4-Hour DSMA")
tf5 = input.timeframe('240', "4-Hour Timeframe")

bool show_tf6 = input.bool(true, title="Show 1-Day DSMA")
tf6 = input.timeframe('D', "1-Day Timeframe")

// Configurable Colors for upper and lower for each timeframe
color upper_color1 = input.color(#41a1ce, "3-Minute Upper Color")
color down_color1 = input.color(#ce8541, "3-Minute Down Color")
color upper_color2 = input.color(#41a1ce, "5-Minute Upper Color")
color down_color2 = input.color(#ce8541, "5-Minute Down Color")
color upper_color3 = input.color(#41a1ce, "15-Minute Upper Color")
color down_color3 = input.color(#ce8541, "15-Minute Down Color")
color upper_color4 = input.color(#41a1ce, "1-Hour Upper Color")
color down_color4 = input.color(#ce8541, "1-Hour Down Color")
color upper_color5 = input.color(#41a1ce, "4-Hour Upper Color")
color down_color5 = input.color(#ce8541, "4-Hour Down Color")
color upper_color6 = input.color(#41a1ce, "1-Day Upper Color")
color down_color6 = input.color(#ce8541, "1-Day Down Color")

// Colors for each timeframe's DSMA
color dsma1_color = color.new(#1f77b4, 0) // 3-Minute DSMA Color
color dsma2_color = color.new(#ff7f0e, 0) // 5-Minute DSMA Color
color dsma3_color = color.new(#2ca02c, 0) // 15-Minute DSMA Color
color dsma4_color = color.new(#d62728, 0) // 1-Hour DSMA Color
color dsma5_color = color.new(#9467bd, 0) // 4-Hour DSMA Color
color dsma6_color = color.new(#8c564b, 0) // 1-Day DSMA Color

// ---------------------------------------------------------------------------------------------------------------------
// INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

// Function to calculate the Deviation Scaled Moving Average (DSMA)
dsma(src, int period)=>
    var float a1 = 0.0
    var float b1 = 0.0
    var float c1 = 0.0
    var float c2 = 0.0
    var float c3 = 0.0
    var float filt = 0.0
    var float dsma = 0.0
    var float s = 0.0

    if barstate.isfirst
        pi = 3.1415926535897932
        g = math.sqrt(2)
        s := 2 * pi / period
        a1 := math.exp(-g * pi / (0.5 * period))
        b1 := 2 * a1 * math.cos(g * s / (0.5 * period))
        c2 := b1
        c3 := -a1 * a1
        c1 := 1 - c2 - c3

    zeros = (close - close[2])
    filt := c1 * (zeros + zeros[1]) / 2 + c2 * nz(filt[1]) + c3 * nz(filt[2])

    rms = math.sqrt(ta.ema(math.pow(filt, 2), period))
    scaled_filt = rms != 0 ? filt / rms : 0
    alpha1 = math.abs(scaled_filt) * 5 / period
    dsma := alpha1 * close + (1 - alpha1) * nz(dsma[1])

    dsma

// Function to calculate trend percentage, color, and average DSMA
percent_trend(src, period, step, upper_color, down_color)=>
    dsma_arr = array.new<float>()

    length = period
    dsma1 = dsma(src, length)
    length += step 
    dsma2 = dsma(src, length)
    length += step 
    dsma3 = dsma(src, length)
    length += step 
    dsma4 = dsma(src, length)
    length += step 
    dsma5 = dsma(src, length)
    length += step 
    dsma6 = dsma(src, length)
    length += step 
    dsma7 = dsma(src, length)
    length += step 
    dsma8 = dsma(src, length)

    array.push(dsma_arr, dsma1)
    array.push(dsma_arr, dsma2)
    array.push(dsma_arr, dsma3)
    array.push(dsma_arr, dsma4)
    array.push(dsma_arr, dsma5)
    array.push(dsma_arr, dsma6)
    array.push(dsma_arr, dsma7)
    array.push(dsma_arr, dsma8)

    val = 0.14285714285714285714285714285714

    score = 0.
    for i = 0 to array.size(dsma_arr) - 1
        dsma = array.get(dsma_arr, i)
        if dsma > array.get(dsma_arr, 7)
            score += val

    color =  score > 0.5 
             ? color.from_gradient(score, 0.5, 1, na, upper_color) 
             : color.from_gradient(score, 0, 0.5, down_color, na)

    [score, array.avg(dsma_arr), color]

// Apply the multi-timeframe logic using `request.security` for all timeframes
[score1, ma1, color1] = request.security(syminfo.tickerid, tf1, percent_trend(src, period, step, upper_color1, down_color1))
[score2, ma2, color2] = request.security(syminfo.tickerid, tf2, percent_trend(src, period, step, upper_color2, down_color2))
[score3, ma3, color3] = request.security(syminfo.tickerid, tf3, percent_trend(src, period, step, upper_color3, down_color3))
[score4, ma4, color4] = request.security(syminfo.tickerid, tf4, percent_trend(src, period, step, upper_color4, down_color4))
[score5, ma5, color5] = request.security(syminfo.tickerid, tf5, percent_trend(src, period, step, upper_color5, down_color5))
[score6, ma6, color6] = request.security(syminfo.tickerid, tf6, percent_trend(src, period, step, upper_color6, down_color6))

// Detect crossovers for signal generation
cross_up1 = ta.crossover(score1, 0.3)
cross_dn1 = ta.crossunder(score1, 0.7)

cross_up2 = ta.crossover(score2, 0.3)
cross_dn2 = ta.crossunder(score2, 0.7)

cross_up3 = ta.crossover(score3, 0.3)
cross_dn3 = ta.crossunder(score3, 0.7)

cross_up4 = ta.crossover(score4, 0.3)
cross_dn4 = ta.crossunder(score4, 0.7)

cross_up5 = ta.crossover(score5, 0.3)
cross_dn5 = ta.crossunder(score5, 0.7)

cross_up6 = ta.crossover(score6, 0.3)
cross_dn6 = ta.crossunder(score6, 0.7)

// ---------------------------------------------------------------------------------------------------------------------
// VISUALIZATION
// ---------------------------------------------------------------------------------------------------------------------

// 3-Minute DSMA
plot(show_tf1 ? ma1 : na, color = color1, linewidth = 2, transp = 80)
plotshape(show_tf1 and cross_up1 ? ma1 : na, title="3-Min Up", location=location.absolute, color=color.new(upper_color1, 50), size=size.small, style=shape.diamond)
plotshape(show_tf1 and cross_dn1 ? ma1 : na, title="3-Min Down", location=location.absolute, color=color.new(down_color1, 50), size=size.small, style=shape.diamond)

// 5-Minute DSMA
plot(show_tf2 ? ma2 : na, color = color2, linewidth = 2, transp = 80)
plotshape(show_tf2 and cross_up2 ? ma2 : na, title="5-Min Up", location=location.absolute, color=color.new(upper_color2, 50), size=size.small, style=shape.diamond)
plotshape(show_tf2 and cross_dn2 ? ma2 : na, title="5-Min Down", location=location.absolute, color=color.new(down_color2, 50), size=size.small, style=shape.diamond)

// 15-Minute DSMA
plot(show_tf3 ? ma3 : na, color = color3, linewidth = 2, transp = 80)
plotshape(show_tf3 and cross_up3 ? ma3 : na, title="15-Min Up", location=location.absolute, color=color.new(upper_color3, 50), size=size.small, style=shape.diamond)
plotshape(show_tf3 and cross_dn3 ? ma3 : na, title="15-Min Down", location=location.absolute, color=color.new(down_color3, 50), size=size.small, style=shape.diamond)

// 1-Hour DSMA
plot(show_tf4 ? ma4 : na, color = color4, linewidth = 2, transp = 80)
plotshape(show_tf4 and cross_up4 ? ma4 : na, title="1-Hour Up", location=location.absolute, color=color.new(upper_color4, 50), size=size.small, style=shape.diamond)
plotshape(show_tf4 and cross_dn4 ? ma4 : na, title="1-Hour Down", location=location.absolute, color=color.new(down_color4, 50), size=size.small, style=shape.diamond)

// 4-Hour DSMA
plot(show_tf5 ? ma5 : na, color = color5, linewidth = 2, transp = 80)
plotshape(show_tf5 and cross_up5 ? ma5 : na, title="4-Hour Up", location=location.absolute, color=color.new(upper_color5, 50), size=size.small, style=shape.diamond)
plotshape(show_tf5 and cross_dn5 ? ma5 : na, title="4-Hour Down", location=location.absolute, color=color.new(down_color5, 50), size=size.small, style=shape.diamond)

// 1-Day DSMA
plot(show_tf6 ? ma6 : na, color = color6, linewidth = 2, transp = 80)
plotshape(show_tf6 and cross_up6 ? ma6 : na, title="1-Day Up", location=location.absolute, color=color.new(upper_color6, 50), size=size.small, style=shape.diamond)
plotshape(show_tf6 and cross_dn6 ? ma6 : na, title="1-Day Down", location=location.absolute, color=color.new(down_color6, 50), size=size.small, style=shape.diamond)
