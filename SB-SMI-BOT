// @version=5
indicator("Silver Bullet FVG Indicator", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// === Inputs ===
selectedTF         = input.string("1", "Selected FVG Timeframe", options=["1", "3", "5", "15"], group="Session")
showSB             = input.bool(true,     'Show 10–11 AM Session',        group="Session")
showHistoricalFVGs = input.bool(true,     "Show Historical FVGs",         group="Session")
extendLine         = input.bool(true,     'Extend Vertical Separators',   group="Session")
extendHLine        = input.bool(true,     'Extend Historical Lines',      group="Session & FVG")
boxShift           = input.int(1,         "Shift FVG Box Left", minval=0, group="Session")
historyDays        = input.int(50,        "Look-back (Days)", minval=1, maxval=500, group="Session & FVG")
labelSizeOpt       = input.string("Tiny",  "Session Label Size", options=["Auto","Tiny","Small","Normal","Large","Huge"], group="Session")
labelColor         = input.color(color.gray, "Session Label Color",       group="Session")
labelXOffset       = input.int(20,        "Session Label X Offset", minval=0,   group="Session")
labelYOffset       = input.float(0.0,      "Session Label Y Offset",           group="Session")
flipSource         = input.string("Wick",  "Flip Source", options=["Wick","Close"], group="FVG Style")
tradeLabelSize     = input.string("Normal", "Trade Label Size", options=["Tiny","Small","Normal","Large","Huge"], group="Labels")
debugLabels        = input.bool(false,     "Show Debug Labels", group="Debug")

// === FVG Styles ===
currentFVGCol = input.color(color.rgb(167, 250, 79, 48), title="Current FVG Color", group="Colors")
histFVGCol    = input.color(#4073f477, title="Historical FVG Color", group="Colors")
midlineColor  = input.color(#787b86, title="Midline Color", group="Colors")
invisBorder   = color.rgb(0, 0, 0, 100)

// === Label size mapping ===
labelSizeMap(x) =>
    switch x
        'Auto'   => size.auto
        'Tiny'   => size.tiny
        'Small'  => size.small
        'Normal' => size.normal
        'Large'  => size.large
        'Huge'   => size.huge

// === Session logic ===
timeSess(tf, s) => time(tf, s, "America/New_York")
SB_AM   = timeSess(timeframe.period, "1000-1100")
strAM   = SB_AM and not SB_AM[1]
n       = bar_index
minT    = syminfo.mintick
inAM    = timeSess(selectedTF, "1000-1100")
after10 = inAM and not strAM

// === Pull wick/close from selectedTF ===
[lf_low, lf_high, lf_close] = request.security(syminfo.tickerid, selectedTF, [low, high, close])

// === Session storage ===
var line    vLine        = na
var line[]  sessHLines   = array.new_line()
var int[]   sessHTimes   = array.new_int()
var float[] sessHPrices  = array.new_float()
var label[] sessHLabels  = array.new_label()

// === Session open line vars ===
var line   hLine   = na
var label  hLabel  = na
var float  hPrice  = na
var int    hStart  = na

// === FVG state ===
var box[]   fvgBoxes = array.new_box()
var line[]  fvgMids  = array.new_line()
var bool[]  fvgBull  = array.new_bool()
var int[]   fvgTimes = array.new_int()

var box    currBox  = na
var line   currMid  = na
var bool   drawn    = false
var bool   flipped  = false
var bool   swingConfirmed = false
var bool   tapped   = false
var bool   waitingForOpposite = false
var bool   oppositeTapped = false
var float  fvgTop   = na
var float  fvgBot   = na
var bool   isBull   = na

// ── New 10:00 AM session ──
if strAM and showSB
    vLine := line.new(n, close, n, close + minT, color=color.white, extend=extendLine ? extend.both : extend.none)
    if not na(hLine)
        line.set_extend(hLine, extend.none)
        line.set_x2(hLine, n)
        line.set_y2(hLine, hPrice)
        array.push(sessHLines, hLine)
        array.push(sessHTimes, time)
        array.push(sessHPrices, hPrice)
        array.push(sessHLabels, hLabel)
    hStart := n
    hPrice := open
    hLine := line.new(hStart, hPrice, n, hPrice, color=color.white, style=line.style_dotted, extend=extend.none)
    d = dayofmonth < 10 ? "0"+str.tostring(dayofmonth) : str.tostring(dayofmonth)
    m = month < 10 ? "0"+str.tostring(month) : str.tostring(month)
    y = (year % 100) < 10 ? "0"+str.tostring(year % 100) : str.tostring(year % 100)
    dateStr = d + "/" + m + "/" + y
    hLabel := label.new(x=n+labelXOffset, y=hPrice+labelYOffset, text=dateStr, xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_right, size=labelSizeMap(labelSizeOpt), textcolor=labelColor, color=color.new(color.white,100))

if not na(hLine)
    line.set_extend(hLine, extend.none)
    line.set_x2(hLine, n)
    line.set_y2(hLine, hPrice)
    label.set_x(hLabel, n + labelXOffset)
    label.set_y(hLabel, hPrice + labelYOffset)

// === FVG detection for selectedTF ===
fvgDetect() =>
    bull = low > high[2] and close[1] > high[2]
    bear = high < low[2] and close[1] < low[2]
    top  = bull ? low : bear ? low[2] : na
    bot  = bull ? high[2] : bear ? high : na
    [bull, bear, top, bot]
[bull, bear, top, bot] = request.security(syminfo.tickerid, selectedTF, fvgDetect())

// ── Reset on 10:00 AM ──
if strAM
    if not na(currBox)
        array.push(fvgBoxes, currBox)
        array.push(fvgMids, currMid)
        array.push(fvgBull, isBull)
        array.push(fvgTimes, time)
    drawn := false
    flipped := false
    swingConfirmed := false
    tapped := false
    waitingForOpposite := false
    oppositeTapped := false
    currBox := na
    currMid := na
    fvgTop := na
    fvgBot := na
    isBull := na

// ── Draw FVG ──
if after10 and not drawn and (bull or bear)
    isBull := bull
    fvgTop := top
    fvgBot := bot
    currBox := box.new(n-boxShift, fvgTop, n, fvgBot, border_color=invisBorder, bgcolor=currentFVGCol)
    currMid := line.new(n-boxShift, (fvgTop+fvgBot)/2, n, (fvgTop+fvgBot)/2, color=midlineColor, style=line.style_dashed)
    drawn := true
    flipped := false
    if debugLabels
        label.new(bar_index, (fvgTop+fvgBot)/2, isBull ? "Bull FVG" : "Bear FVG", color=isBull ? color.blue : color.orange, style=label.style_label_center, textcolor=color.white, size=labelSizeMap("Small"))

// ── Extend and Flip ──
if drawn and not na(currBox)
    box.set_right(currBox, n)
    line.set_x2(currMid, n)
    if not flipped
        srcLow = flipSource == "Wick" ? lf_low : lf_close
        srcHigh = flipSource == "Wick" ? lf_high : lf_close
        if isBull and srcLow < fvgBot
            box.set_bgcolor(currBox, currentFVGCol)
            flipped := true
        else if not isBull and srcHigh > fvgTop
            box.set_bgcolor(currBox, currentFVGCol)
            flipped := true



// ── Draw historical FVGs ──
if array.size(fvgBoxes) > 0 and showHistoricalFVGs
    cutoffTime = time - historyDays * 86400000
    for i = 0 to array.size(fvgBoxes) - 1
        if array.get(fvgTimes, i) >= cutoffTime
            b = array.get(fvgBoxes, i)
            m = array.get(fvgMids, i)
            box.set_right(b, n)
            box.set_bgcolor(b, histFVGCol)
            line.set_x2(m, n)

// ── Extend historical open lines ──
if array.size(sessHLines) > 0 and extendHLine
    cutoffSess = time - historyDays * 86400000
    for i = 0 to array.size(sessHLines) - 1
        if array.get(sessHTimes, i) >= cutoffSess
            ln = array.get(sessHLines, i)
            price = array.get(sessHPrices, i)
            lb = array.get(sessHLabels, i)
            line.set_extend(ln, extend.none)
            line.set_x2(ln, n)
            line.set_y2(ln, price)
            label.set_x(lb, n + labelXOffset)
            label.set_y(lb, price + labelYOffset)

// === Extension: First FVG after 10 AM per day with DD/MM ===
var box[]   dailyBoxes = array.new_box()
var line[]  dailyMids  = array.new_line()
var label[] dailyLabels = array.new_label()
var int[]   dailyTimes = array.new_int()
var int     lastDayTracked = na

fvgStartTime = timestamp("America/New_York", year, month, dayofmonth, 10, 0)
if time >= fvgStartTime and (bull or bear)
    dayofyear = dayofmonth +(month == 1  ? 0 : month == 2  ? 31 : month == 3  ? 59 : month == 4  ? 90 : month == 5  ? 120 : month == 6  ? 151 : month == 7  ? 181 : month == 8  ? 212 : month == 9  ? 243 : month == 10 ? 273 : month == 11 ? 304 : month == 12 ? 334 : 0)
    curDay = dayofyear
    if curDay != lastDayTracked
        lastDayTracked := curDay
        fvgTopD = bull ? top : low[2]
        fvgBotD = bull ? high[2] : bot
        b = box.new(n-boxShift, fvgTopD, n, fvgBotD, bgcolor=histFVGCol, border_color=invisBorder)
        m = line.new(n-boxShift, (fvgTopD+fvgBotD)/2, n, (fvgTopD+fvgBotD)/2, color=midlineColor, style=line.style_dashed)
        labelText = str.tostring(dayofmonth, "00") + "/" + str.tostring(month, "00")
        lb = label.new(n, fvgTopD, labelText, xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_right, size=labelSizeMap("Tiny"), textcolor=color.white)
        array.push(dailyBoxes, b)
        array.push(dailyMids, m)
        array.push(dailyLabels, lb)
        array.push(dailyTimes, time)
        if array.size(dailyBoxes) > historyDays
            box.delete(array.shift(dailyBoxes))
            line.delete(array.shift(dailyMids))
            label.delete(array.shift(dailyLabels))
            array.shift(dailyTimes)

if showHistoricalFVGs and array.size(dailyBoxes) > 0
    for i = 0 to array.size(dailyBoxes) - 1
        box.set_right(array.get(dailyBoxes, i), n)
        line.set_x2(array.get(dailyMids, i), n)
        label.set_x(array.get(dailyLabels, i), n)

//smi
x = input.int(25, "Index Period", minval = 1)
rr = input.int(14, "Volume Flow Period", minval = 1)
peakslen = input.int(500, "Normalization Period", minval = 1)
thr = input.float(0.9, "High Interest Threshold", minval = 0.01, maxval = 0.99)
green = input.color(#00ffbb, "Up Color")
red = input.color(#ff1100, "Down Color")

dumb = ta.pvi-ta.ema(ta.pvi,255)
smart = ta.nvi-ta.ema(ta.nvi,255)

drsi = ta.rsi(dumb, rr)
srsi = ta.rsi(smart, rr)

r = srsi/drsi //ratio shows if smart money is buying from dumb money selling and vice versa

sums = math.sum(r, x)
peak = ta.highest(sums, peakslen)

index = sums/peak

condition = index > thr
// barcolor(condition ? green : na)

// Main Panel Arrows
plotshape(series= condition ? 1 : na, title="High Smart Money Interest", color=color.rgb(233, 239, 233), style=shape.arrowup, size=size.normal, location=location.belowbar, force_overlay=true)

// Alert condition
alertcondition(condition=condition, title="High Smart Money Interest Alert", message="High Smart Money Interest detected!")



