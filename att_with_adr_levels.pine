//@version=5
indicator("ATT with ADR Levels", overlay=true, max_boxes_count=500, max_labels_count=500, max_lines_count=500)

// ===== ATT Settings =====
var g_att = "ATT Settings"
show_att_numbers = input.bool(true, "Show ATT Numbers", group=g_att)
show_att_markers = input.bool(true, "Show ATT Markers", group=g_att)
show_att_labels = input.bool(true, "Show ATT Labels", group=g_att)
wait_for_new_hour = input.bool(true, "Wait for New Hour", tooltip="Only start ATT at the beginning of a new hour", group=g_att)
market_hours_only = input.bool(true, "Market Hours Only", tooltip="Only show ATT during market hours", group=g_att)
market_open_time = input.session("0930-1600", "Market Hours", group=g_att)

// ATT Numbers Selection
var g_att_numbers = "ATT Numbers"
use_3 = input.bool(true, "Use 3", inline="att_3", group=g_att_numbers)
att_3_color = input.color(color.yellow, "", inline="att_3", group=g_att_numbers)

use_11 = input.bool(true, "Use 11", inline="att_11", group=g_att_numbers)
att_11_color = input.color(color.yellow, "", inline="att_11", group=g_att_numbers)

use_17 = input.bool(true, "Use 17", inline="att_17", group=g_att_numbers)
att_17_color = input.color(color.yellow, "", inline="att_17", group=g_att_numbers)

use_29 = input.bool(true, "Use 29", inline="att_29", group=g_att_numbers)
att_29_color = input.color(color.yellow, "", inline="att_29", group=g_att_numbers)

use_41 = input.bool(true, "Use 41", inline="att_41", group=g_att_numbers)
att_41_color = input.color(color.yellow, "", inline="att_41", group=g_att_numbers)

use_47 = input.bool(true, "Use 47", inline="att_47", group=g_att_numbers)
att_47_color = input.color(color.yellow, "", inline="att_47", group=g_att_numbers)

use_53 = input.bool(true, "Use 53", inline="att_53", group=g_att_numbers)
att_53_color = input.color(color.yellow, "", inline="att_53", group=g_att_numbers)

use_59 = input.bool(true, "Use 59", inline="att_59", group=g_att_numbers)
att_59_color = input.color(color.yellow, "", inline="att_59", group=g_att_numbers)

// ATT Appearance Settings
var g_att_appearance = "ATT Appearance Settings"
high_color = input.color(color.lime, "High Color", group=g_att_appearance)
low_color = input.color(color.red, "Low Color", group=g_att_appearance)
reentry_color = input.color(color.yellow, "Re-entry Color", group=g_att_appearance)
marker_size = input.string("Small", "Marker Size", options=["Tiny", "Small", "Normal", "Large"], group=g_att_appearance)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=g_att_appearance)
label_offset = input.float(0.0003, "Label Offset", minval=0.0001, maxval=0.01, step=0.0001, group=g_att_appearance)

// Swing Detection Settings
var g_swing = "Swing Detection Settings"
swing_length = input.int(3, "Swing Detection Length", minval=2, maxval=10, group=g_swing)
swing_strength = input.int(2, "Swing Strength", minval=1, maxval=5, tooltip="Higher values require stronger swings", group=g_swing)
allow_one_bar_offset = input.bool(true, "Allow 1-Bar Offset", tooltip="Allow ATT to form 1 bar before or after the exact ATT minute", group=g_swing)

// ===== ADR Settings =====
var g_adr = "ADR Settings"
show_adr = input.bool(true, "Show ADR Levels", group=g_adr)
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group=g_adr)
show_full_adr = input.bool(true, "Show Full ADR", group=g_adr)
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group=g_adr)
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group=g_adr)
show_half_adr = input.bool(false, "Show 1/2 ADR", group=g_adr)

// ADR Anchor Settings
var g_adr_anchor = "ADR Anchor Settings"
daily_anchor_type = input.string("New York Midnight", "Daily Anchor Type", options=["New York Midnight", "UTC Time", "Session Open"], group=g_adr_anchor)
daily_anchor_hour = input.int(4, "Daily Anchor Hour (UTC)", minval=0, maxval=23, group=g_adr_anchor, tooltip="For UTC Time anchor type. Default is 4 for NY midnight")
daily_anchor_minute = input.int(0, "Daily Anchor Minute", minval=0, maxval=59, group=g_adr_anchor)
show_true_day_separator = input.bool(true, "Show True Day Open Separator", group=g_adr_anchor)
true_day_separator_color = input.color(color.new(color.green, 50), "Separator Color", group=g_adr_anchor)

// ADR Line Settings
var g_adr_line = "ADR Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=4, group=g_adr_line)
line_style = input.string("Solid", "Line Style", options=["Solid", "Dashed", "Dotted"], group=g_adr_line)
extend_right = input.int(50, "Extend Right", minval=0, maxval=250, group=g_adr_line, tooltip="Maximum allowed is 250 bars to avoid Pine Script's 500-bar future limit")
extend_left = input.int(200, "Extend Left", minval=0, maxval=500, group=g_adr_line, tooltip="How many bars to the left the ADR levels should extend")

// ADR Color Settings
var g_adr_color = "ADR Color Settings"
adr_full_color = input.color(#ffffff, "Full ADR", group=g_adr_color)
adr_one_third_color = input.color(#ffffff, "1/3 ADR", group=g_adr_color)
adr_two_third_color = input.color(#ffffff, "2/3 ADR", group=g_adr_color)
adr_half_color = input.color(#ffffff, "1/2 ADR", group=g_adr_color)

// ===== ATT Numbers =====
// Create an array of ATT numbers based on user selection
var att_numbers = array.new_int()
var att_colors = array.new_color()

if barstate.isfirst
    // Clear arrays
    array.clear(att_numbers)
    array.clear(att_colors)
    
    // Add selected ATT numbers and their colors
    if use_3
        array.push(att_numbers, 3)
        array.push(att_colors, att_3_color)
    if use_11
        array.push(att_numbers, 11)
        array.push(att_colors, att_11_color)
    if use_17
        array.push(att_numbers, 17)
        array.push(att_colors, att_17_color)
    if use_29
        array.push(att_numbers, 29)
        array.push(att_colors, att_29_color)
    if use_41
        array.push(att_numbers, 41)
        array.push(att_colors, att_41_color)
    if use_47
        array.push(att_numbers, 47)
        array.push(att_colors, att_47_color)
    if use_53
        array.push(att_numbers, 53)
        array.push(att_colors, att_53_color)
    if use_59
        array.push(att_numbers, 59)
        array.push(att_colors, att_59_color)

// ===== Time Calculations =====
// Get current minute within the hour
current_minute = minute(time)
current_hour = hour(time)

// Check if we're in market hours
in_market_hours = not na(time(timeframe.period, market_open_time))

// Check if we're at the start of a new hour
is_new_hour = hour(time) != hour(time[1]) or na(time[1])

// Track if we've seen a new hour since the market opened
var seen_new_hour = false
if is_new_hour and in_market_hours
    seen_new_hour := true

// Function to check if current bar is an ATT number
isAttNumber(int min) =>
    result = false
    for i = 0 to array.size(att_numbers) - 1
        if min == array.get(att_numbers, i)
            result := true
    result

// Function to get color for a specific ATT number
getAttColor(int min) =>
    color result = color.yellow
    for i = 0 to array.size(att_numbers) - 1
        if min == array.get(att_numbers, i)
            result := array.get(att_colors, i)
    result

// Check if current bar is an ATT minute
is_att_minute = isAttNumber(current_minute)

// Check if previous or next bar is an ATT minute (for offset)
is_prev_att_minute = isAttNumber(current_minute[1])
is_next_att_minute = isAttNumber(current_minute == 59 ? 0 : current_minute + 1)

// ===== Swing Detection =====
// More robust swing detection
bool swingHighCondition = false
bool swingLowCondition = false

// Check if current bar is a swing high
if high == ta.highest(high, swing_length)
    int highCount = 0
    for i = 1 to swing_length
        if high > high[i]
            highCount += 1
    swingHighCondition := highCount >= swing_strength

// Check if current bar is a swing low
if low == ta.lowest(low, swing_length)
    int lowCount = 0
    for i = 1 to swing_length
        if low < low[i]
            lowCount += 1
    swingLowCondition := lowCount >= swing_strength

// ===== ATT Point Classification =====
// Determine if this ATT point is a high, low, or re-entry
is_att_high = is_att_minute and swingHighCondition
is_att_low = is_att_minute and swingLowCondition
is_att_reentry = is_att_minute and not swingHighCondition and not swingLowCondition

// Handle 1-bar offset if enabled
if allow_one_bar_offset
    // Check if previous bar was an ATT number and this bar forms a swing
    if is_prev_att_minute and not is_att_minute
        is_att_high := is_att_high or swingHighCondition
        is_att_low := is_att_low or swingLowCondition
        is_att_reentry := is_att_reentry or (not swingHighCondition and not swingLowCondition)
    
    // Check if next bar is an ATT number and this bar forms a swing
    if is_next_att_minute and not is_att_minute
        is_att_high := is_att_high or swingHighCondition
        is_att_low := is_att_low or swingLowCondition
        is_att_reentry := is_att_reentry or (not swingHighCondition and not swingLowCondition)

// ===== ADR Calculations =====
// Function to detect daily anchor time
isDailyAnchor() =>
    int current_hour = hour(time)
    int current_minute = minute(time)

    if daily_anchor_type == "New York Midnight"
        current_hour == daily_anchor_hour and current_minute == daily_anchor_minute
    else if daily_anchor_type == "UTC Time"
        current_hour == daily_anchor_hour and current_minute == daily_anchor_minute
    else if daily_anchor_type == "Session Open"
        // Session open is typically at the start of the trading day
        current_hour == 9 and current_minute == 30  // Default to market open at 9:30 AM

// Function to calculate the Average Range for a given timeframe
calculateAverageRange(tf, lookback_period) =>
    // Get high and low values for the lookback period
    high_array = request.security(syminfo.tickerid, tf, high, lookahead=barmerge.lookahead_on)
    low_array = request.security(syminfo.tickerid, tf, low, lookahead=barmerge.lookahead_on)

    // Calculate the sum of ranges
    float range_sum = 0.0
    for i = 1 to lookback_period
        if not na(high_array[i]) and not na(low_array[i])
            range_sum += (high_array[i] - low_array[i])

    // Return the average
    range_sum / lookback_period

// Store the true day open bar index for drawing lines from the anchor time
var int true_day_bar_index = na

// Global variables to store true day open values
var float daily_true_day_open = na

// Arrays to store timestamps of when ADR levels were calculated
var int[] daily_timestamps = array.new_int()

// Check if we're at the daily anchor time
if isDailyAnchor()
    // Update true day open bar index
    true_day_bar_index := bar_index
    daily_true_day_open := open

    // Store the current timestamp for this day's ADR levels
    array.unshift(daily_timestamps, int(time))
    // Keep only the most recent timestamp
    while array.size(daily_timestamps) > 1
        array.pop(daily_timestamps)

    // Draw separator line if enabled
    if show_true_day_separator
        line.new(x1=bar_index, y1=high * 1.05, x2=bar_index, y2=low * 0.95, color=true_day_separator_color, style=line.style_dashed, width=1, extend=extend.both)

// Calculate range values for daily timeframe
daily_range = calculateAverageRange("D", adr_days)

// Store ADR levels
var float daily_full_up = na
var float daily_full_down = na
var float daily_one_third_up = na
var float daily_one_third_down = na
var float daily_two_third_up = na
var float daily_two_third_down = na
var float daily_half_up = na
var float daily_half_down = na

// Arrays to store historical lines
var line[] daily_full_up_lines = array.new_line()
var line[] daily_full_down_lines = array.new_line()
var line[] daily_one_third_up_lines = array.new_line()
var line[] daily_one_third_down_lines = array.new_line()
var line[] daily_two_third_up_lines = array.new_line()
var line[] daily_two_third_down_lines = array.new_line()
var line[] daily_half_up_lines = array.new_line()
var line[] daily_half_down_lines = array.new_line()

// Convert line style input to Pine style
var lineStyleValue = line.style_solid
if line_style == "Dashed"
    lineStyleValue := line.style_dashed
else if line_style == "Dotted"
    lineStyleValue := line.style_dotted

// Function to clear old lines
clearOldLines(line_array) =>
    if array.size(line_array) > 0
        for i = 0 to array.size(line_array) - 1
            line.delete(array.get(line_array, i))
        array.clear(line_array)

// Function to draw ADR level and return the line
drawADRLevel(price_level, color_value, label_text) =>
    if na(price_level)
        line.new(na, na, na, na)
    else
        // Calculate start and end bar positions
        int startBar = math.max(0, bar_index - extend_left)  // Start at current bar minus extend_left, but not less than 0
        int endBar = math.min(bar_index + extend_right, bar_index + 499)  // Limit to 499 bars into the future (Pine Script limit is 500)

        // Create the line
        line result = line.new(
             x1=startBar,
             y1=price_level,
             x2=endBar,
             y2=price_level,
             color=color_value,
             style=lineStyleValue,
             width=line_width,
             xloc=xloc.bar_index)

        // Add label at the right side of the line
        label.new(
            x=endBar,
            y=price_level,
            text=label_text,
            style=label.style_label_left,
            color=color.rgb(0, 0, 0, 0),
            textcolor=color_value,
            size=size.small,
            xloc=xloc.bar_index
        )
        
        result

// Calculate ADR levels on the last bar only
if barstate.islast and not na(daily_true_day_open) and show_adr
    // Calculate Daily ADR levels
    daily_full_up := daily_true_day_open + daily_range
    daily_full_down := daily_true_day_open - daily_range
    daily_one_third_up := daily_true_day_open + (daily_range / 3)
    daily_one_third_down := daily_true_day_open - (daily_range / 3)
    daily_two_third_up := daily_true_day_open + (daily_range * 2 / 3)
    daily_two_third_down := daily_true_day_open - (daily_range * 2 / 3)
    daily_half_up := daily_true_day_open + (daily_range / 2)
    daily_half_down := daily_true_day_open - (daily_range / 2)

    // Clear all existing lines first
    clearOldLines(daily_full_up_lines)
    clearOldLines(daily_full_down_lines)
    clearOldLines(daily_one_third_up_lines)
    clearOldLines(daily_one_third_down_lines)
    clearOldLines(daily_two_third_up_lines)
    clearOldLines(daily_two_third_down_lines)
    clearOldLines(daily_half_up_lines)
    clearOldLines(daily_half_down_lines)

    // Draw Full ADR levels
    if show_full_adr
        line daily_full_up_line = drawADRLevel(daily_full_up, adr_full_color, "ADR+")
        line daily_full_down_line = drawADRLevel(daily_full_down, adr_full_color, "ADR-")
        if not na(daily_full_up_line)
            array.push(daily_full_up_lines, daily_full_up_line)
        if not na(daily_full_down_line)
            array.push(daily_full_down_lines, daily_full_down_line)

    // Draw 1/3 ADR levels
    if show_one_third_adr
        line daily_one_third_up_line = drawADRLevel(daily_one_third_up, adr_one_third_color, "1/3 ADR+")
        line daily_one_third_down_line = drawADRLevel(daily_one_third_down, adr_one_third_color, "1/3 ADR-")
        if not na(daily_one_third_up_line)
            array.push(daily_one_third_up_lines, daily_one_third_up_line)
        if not na(daily_one_third_down_line)
            array.push(daily_one_third_down_lines, daily_one_third_down_line)

    // Draw 2/3 ADR levels
    if show_two_third_adr
        line daily_two_third_up_line = drawADRLevel(daily_two_third_up, adr_two_third_color, "2/3 ADR+")
        line daily_two_third_down_line = drawADRLevel(daily_two_third_down, adr_two_third_color, "2/3 ADR-")
        if not na(daily_two_third_up_line)
            array.push(daily_two_third_up_lines, daily_two_third_up_line)
        if not na(daily_two_third_down_line)
            array.push(daily_two_third_down_lines, daily_two_third_down_line)

    // Draw 1/2 ADR levels
    if show_half_adr
        line daily_half_up_line = drawADRLevel(daily_half_up, adr_half_color, "1/2 ADR+")
        line daily_half_down_line = drawADRLevel(daily_half_down, adr_half_color, "1/2 ADR-")
        if not na(daily_half_up_line)
            array.push(daily_half_up_lines, daily_half_up_line)
        if not na(daily_half_down_line)
            array.push(daily_half_down_lines, daily_half_down_line)

// ===== Visualization =====
// Only show ATT if we're in market hours and have seen a new hour (if those options are enabled)
show_att = (not market_hours_only or in_market_hours) and 
           (not wait_for_new_hour or seen_new_hour) and
           show_att_numbers

// Convert size string to actual size value
var size_map = array.from(size.tiny, size.small, size.normal, size.large)
var size_names = array.from("Tiny", "Small", "Normal", "Large")

getSize(string size_str) =>
    int index = 1  // Default to small
    for i = 0 to array.size(size_names) - 1
        if size_str == array.get(size_names, i)
            index := i
    array.get(size_map, index)

marker_size_value = getSize(marker_size)
label_size_value = getSize(label_size)

// Plot ATT markers
plotshape(
    show_att and is_att_high and show_att_markers,
    title="ATT High",
    style=shape.circle,
    location=location.abovebar,
    color=high_color,
    size=marker_size_value
)

plotshape(
    show_att and is_att_low and show_att_markers,
    title="ATT Low",
    style=shape.circle,
    location=location.belowbar,
    color=low_color,
    size=marker_size_value
)

plotshape(
    show_att and is_att_reentry and not is_att_high and not is_att_low and show_att_markers,
    title="ATT Re-entry",
    style=shape.circle,
    location=location.absolute,
    color=reentry_color,
    size=marker_size_value,
    y=close
)

// Add labels for ATT numbers
if show_att and show_att_labels and is_att_minute
    label_text = str.tostring(current_minute)
    label_color = getAttColor(current_minute)
    
    if is_att_high
        label.new(
            bar_index,
            high + (high * label_offset),
            label_text,
            color=color.new(label_color, 80),
            style=label.style_label_down,
            textcolor=high_color,
            size=label_size_value
        )
    else if is_att_low
        label.new(
            bar_index,
            low - (low * label_offset),
            label_text,
            color=color.new(label_color, 80),
            style=label.style_label_up,
            textcolor=low_color,
            size=label_size_value
        )
    else
        label.new(
            bar_index,
            close,
            label_text,
            color=color.new(label_color, 80),
            style=label.style_label_right,
            textcolor=reentry_color,
            size=label_size_value
        )

// ===== Tracking ATT Bias =====
// Track if ATT is consistently forming highs or lows to determine market bias
var int consecutive_highs = 0
var int consecutive_lows = 0

if is_att_high
    consecutive_highs := consecutive_highs + 1
    consecutive_lows := 0
else if is_att_low
    consecutive_lows := consecutive_lows + 1
    consecutive_highs := 0
else if is_att_reentry
    // Re-entry points don't reset the counters
    consecutive_highs := consecutive_highs
    consecutive_lows := consecutive_lows

// Determine market bias based on consecutive ATT points
string market_bias = "Neutral"
if consecutive_highs >= 3
    market_bias := "Bearish"  // ATT forming highs suggests shorting opportunities
else if consecutive_lows >= 3
    market_bias := "Bullish"  // ATT forming lows suggests long opportunities

// Display market bias on the chart
if barstate.islast and show_att
    label.new(
        bar_index,
        high + (high * label_offset * 10),
        "ATT Bias: " + market_bias + "\nConsecutive Highs: " + str.tostring(consecutive_highs) + "\nConsecutive Lows: " + str.tostring(consecutive_lows),
        color=color.new(color.gray, 20),
        style=label.style_label_right,
        textcolor=color.white,
        size=size.small
    )

// ===== Alerts =====
// Alert conditions for ATT points
alertcondition(is_att_high, "ATT High", "ATT High formed at minute {{current_minute}}")
alertcondition(is_att_low, "ATT Low", "ATT Low formed at minute {{current_minute}}")
alertcondition(is_att_reentry, "ATT Re-entry", "ATT Re-entry formed at minute {{current_minute}}")
alertcondition(consecutive_highs == 3, "ATT Bearish Bias", "ATT showing bearish bias with 3 consecutive highs")
alertcondition(consecutive_lows == 3, "ATT Bullish Bias", "ATT showing bullish bias with 3 consecutive lows")
