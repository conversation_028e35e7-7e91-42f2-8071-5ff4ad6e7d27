// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// © Ravi

//@version=5
indicator("Multi-Anchor VWAP Position Table [Ravi]", "VWAP Anchor Table", overlay = true)

//---------------------------------------------------------------------------------------------------------------------}
//User Inputs
//---------------------------------------------------------------------------------------------------------------------{
// VWAP Anchor Settings
group1 = "VWAP Anchors"
show_session = input.bool(true, title="Daily/Session VWAP", group=group1)
show_week = input.bool(true, title="Weekly VWAP", group=group1)
show_month = input.bool(true, title="Monthly VWAP", group=group1)
show_quarter = input.bool(true, title="Quarterly VWAP", group=group1)
show_year = input.bool(false, title="Yearly VWAP", group=group1)

// VWAP Settings
group2 = "VWAP Settings"
src = input(title = "Source", defval = hlc3, group = group2)
hideonDWM = input(false, title="Hide VWAP on 1D or Above", group=group2)
offset = input.int(0, title="Offset", group=group2, minval=0)

// VWAP Colors
group3 = "VWAP Line Colors"
session_color = input.color(color.rgb(155, 244, 137), title="Daily VWAP Color", group=group3)
week_color = input.color(color.rgb(244, 155, 137), title="Weekly VWAP Color", group=group3)
month_color = input.color(color.rgb(137, 155, 244), title="Monthly VWAP Color", group=group3)
quarter_color = input.color(color.rgb(244, 137, 155), title="Quarterly VWAP Color", group=group3)
year_color = input.color(color.rgb(137, 244, 155), title="Yearly VWAP Color", group=group3)

// Style Settings
green = input.color(#089981, title = "Above VWAP Color", group = "Style")
red = input.color(#f23645, title = "Below VWAP Color", group = "Style")
silver = input.color(color.silver, title = "Neutral Color", group = "Style")

showDash = input.bool(true,title = "Show Dashboard", group = "Dashboard")
dashLoc  = str.lower(str.replace_all(input.string("Top Right",title =  "Location", options = ["Top Right", "Bottom Right", "Bottom Left"] , group = "Dashboard")," ","_"))
textSize = str.lower(input.string("Small", title = "Size", options = ["Tiny", "Small", "Normal"], group = "Dashboard"))
vert = input.string("Vertical", title = "Orientation", group = "Dashboard", options = ["Vertical", "Horizontal"]) == "Vertical"

//---------------------------------------------------------------------------------------------------------------------}
//Functions
//---------------------------------------------------------------------------------------------------------------------{

//For table colors - Green if above VWAP, Red if below
get_col(_val) => _val ? green : red

//---------------------------------------------------------------------------------------------------------------------}
//Calculations
//---------------------------------------------------------------------------------------------------------------------{

// Check for volume data
cumVolume = ta.cum(volume)
if barstate.islast and cumVolume == 0
    runtime.error("No volume is provided by the data vendor.")

// Define new period conditions for each anchor period
isNewSession = timeframe.change("D")
isNewWeek = timeframe.change("W")
isNewMonth = timeframe.change("M")
isNewQuarter = timeframe.change("3M")
isNewYear = timeframe.change("12M")

// Handle data gaps
if na(src[1])
    isNewSession := true
    isNewWeek := true
    isNewMonth := true
    isNewQuarter := true
    isNewYear := true

// Initialize VWAP variables
float sessionVwap = na
float weekVwap = na
float monthVwap = na
float quarterVwap = na
float yearVwap = na

// Calculate VWAPs for each selected anchor period if not on daily or higher timeframe when hidden
if not (hideonDWM and timeframe.isdwm)
    // Session VWAP
    if show_session
        var float sumSrc = 0.0
        var float sumVol = 0.0

        if isNewSession
            sumSrc := src * volume
            sumVol := volume
        else
            sumSrc += src * volume
            sumVol += volume

        sessionVwap := sumVol != 0 ? sumSrc / sumVol : na

    // Week VWAP
    if show_week
        var float sumSrcWeek = 0.0
        var float sumVolWeek = 0.0

        if isNewWeek
            sumSrcWeek := src * volume
            sumVolWeek := volume
        else
            sumSrcWeek += src * volume
            sumVolWeek += volume

        weekVwap := sumVolWeek != 0 ? sumSrcWeek / sumVolWeek : na

    // Month VWAP
    if show_month
        var float sumSrcMonth = 0.0
        var float sumVolMonth = 0.0

        if isNewMonth
            sumSrcMonth := src * volume
            sumVolMonth := volume
        else
            sumSrcMonth += src * volume
            sumVolMonth += volume

        monthVwap := sumVolMonth != 0 ? sumSrcMonth / sumVolMonth : na

    // Quarter VWAP
    if show_quarter
        var float sumSrcQuarter = 0.0
        var float sumVolQuarter = 0.0

        if isNewQuarter
            sumSrcQuarter := src * volume
            sumVolQuarter := volume
        else
            sumSrcQuarter += src * volume
            sumVolQuarter += volume

        quarterVwap := sumVolQuarter != 0 ? sumSrcQuarter / sumVolQuarter : na

    // Year VWAP
    if show_year
        var float sumSrcYear = 0.0
        var float sumVolYear = 0.0

        if isNewYear
            sumSrcYear := src * volume
            sumVolYear := volume
        else
            sumSrcYear += src * volume
            sumVolYear += volume

        yearVwap := sumVolYear != 0 ? sumSrcYear / sumVolYear : na

// Calculate position relative to each VWAP (true = above, false = below)
sessionAbove = show_session and not na(sessionVwap) ? close > sessionVwap : false
weekAbove = show_week and not na(weekVwap) ? close > weekVwap : false
monthAbove = show_month and not na(monthVwap) ? close > monthVwap : false
quarterAbove = show_quarter and not na(quarterVwap) ? close > quarterVwap : false
yearAbove = show_year and not na(yearVwap) ? close > yearVwap : false

//---------------------------------------------------------------------------------------------------------------------}
//Plot VWAP Lines
//---------------------------------------------------------------------------------------------------------------------{

// Plot each VWAP line with its own color
plot(show_session ? sessionVwap : na, title = "Daily VWAP", color = session_color, offset = offset, linewidth = 2)
plot(show_week ? weekVwap : na, title = "Weekly VWAP", color = week_color, offset = offset, linewidth = 2)
plot(show_month ? monthVwap : na, title = "Monthly VWAP", color = month_color, offset = offset, linewidth = 2)
plot(show_quarter ? quarterVwap : na, title = "Quarterly VWAP", color = quarter_color, offset = offset, linewidth = 2)
plot(show_year ? yearVwap : na, title = "Yearly VWAP", color = year_color, offset = offset, linewidth = 2)

//---------------------------------------------------------------------------------------------------------------------}
//VWAP Labels
//---------------------------------------------------------------------------------------------------------------------{

// Variables to store labels and prevent clutter
var label sessionLabel = na
var label weekLabel = na
var label monthLabel = na
var label quarterLabel = na
var label yearLabel = na

// Update labels only when needed to prevent clutter
if barstate.islast
    if show_session and not na(sessionVwap)
        if not na(sessionLabel)
            label.delete(sessionLabel)
        sessionLabel := label.new(bar_index + 1, sessionVwap, "Daily VWAP\n" + str.tostring(sessionVwap, "#.##"),
                      xloc.bar_index, yloc.price,
                      style = label.style_label_left,
                      textcolor = color.white,
                      color = color.new(session_color, 20),
                      size = size.small)

    if show_week and not na(weekVwap)
        if not na(weekLabel)
            label.delete(weekLabel)
        weekLabel := label.new(bar_index + 1, weekVwap, "Weekly VWAP\n" + str.tostring(weekVwap, "#.##"),
                      xloc.bar_index, yloc.price,
                      style = label.style_label_left,
                      textcolor = color.white,
                      color = color.new(week_color, 20),
                      size = size.small)

    if show_month and not na(monthVwap)
        if not na(monthLabel)
            label.delete(monthLabel)
        monthLabel := label.new(bar_index + 1, monthVwap, "Monthly VWAP\n" + str.tostring(monthVwap, "#.##"),
                      xloc.bar_index, yloc.price,
                      style = label.style_label_left,
                      textcolor = color.white,
                      color = color.new(month_color, 20),
                      size = size.small)

    if show_quarter and not na(quarterVwap)
        if not na(quarterLabel)
            label.delete(quarterLabel)
        quarterLabel := label.new(bar_index + 1, quarterVwap, "Quarterly VWAP\n" + str.tostring(quarterVwap, "#.##"),
                      xloc.bar_index, yloc.price,
                      style = label.style_label_left,
                      textcolor = color.white,
                      color = color.new(quarter_color, 20),
                      size = size.small)

    if show_year and not na(yearVwap)
        if not na(yearLabel)
            label.delete(yearLabel)
        yearLabel := label.new(bar_index + 1, yearVwap, "Yearly VWAP\n" + str.tostring(yearVwap, "#.##"),
                      xloc.bar_index, yloc.price,
                      style = label.style_label_left,
                      textcolor = color.white,
                      color = color.new(year_color, 20),
                      size = size.small)

//---------------------------------------------------------------------------------------------------------------------}
//Table
//---------------------------------------------------------------------------------------------------------------------{

//Table setup
var tb = table.new(dashLoc, 5, 5
  , bgcolor      = #1e222d
  , border_color = #373a46
  , border_width = 1
  , frame_color  = #373a46
  , frame_width  = 1)

//Getting the widths for each display style - bigger rectangles
vert_width = textSize == "normal" ? 1.5 : 0.8
flat_width = textSize == "normal" ? 4 : 2

//Sending everything to the table
if showDash
    row = 0

    if show_session
        tb.cell((vert?0:row),(vert?row:1), "Daily",text_color = color.white, text_size = textSize)
        tb.cell((vert?1:row),(vert?row:0), "", bgcolor = get_col(sessionAbove), height = 1.5, width = (vert?vert_width:flat_width))
        row += 1

    if show_week
        tb.cell((vert?0:row),(vert?row:1), "Weekly",text_color = color.white, text_size = textSize)
        tb.cell((vert?1:row),(vert?row:0), "", bgcolor = get_col(weekAbove), height = 1.5, width = (vert?vert_width:flat_width))
        row += 1

    if show_month
        tb.cell((vert?0:row),(vert?row:1), "Monthly",text_color = color.white, text_size = textSize)
        tb.cell((vert?1:row),(vert?row:0), "", bgcolor = get_col(monthAbove), height = 1.5, width = (vert?vert_width:flat_width))
        row += 1

    if show_quarter
        tb.cell((vert?0:row),(vert?row:1), "Quarterly",text_color = color.white, text_size = textSize)
        tb.cell((vert?1:row),(vert?row:0), "", bgcolor = get_col(quarterAbove), height = 1.5, width = (vert?vert_width:flat_width))
        row += 1

    if show_year
        tb.cell((vert?0:row),(vert?row:1), "Yearly",text_color = color.white, text_size = textSize)
        tb.cell((vert?1:row),(vert?row:0), "", bgcolor = get_col(yearAbove), height = 1.5, width = (vert?vert_width:flat_width))

//---------------------------------------------------------------------------------------------------------------------}
