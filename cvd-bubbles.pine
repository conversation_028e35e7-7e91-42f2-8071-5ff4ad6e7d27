//@version=5
indicator("CVD Bubbles - Leviathan Style", overlay=true, max_labels_count=500)

// Input Groups
g1 = 'CVD Settings'
g2 = 'Bubble Appearance'
g3 = 'Threshold Settings'

// CVD Settings (matching original)
mode = input.string("VWAP", "Mode", options=["EMA", "VWAP"], group=g1)
ema_cvd_length = input.int(30, "EMA/CVD Length", minval=1, group=g1)
stdev_length = input.int(150, "Standard Deviation Length", minval=1, group=g1)
level_calc_mode = input.string("MODE 1", "Level Calculation Mode", options=["MODE 1", "MODE 2", "MODE 3"], group=g1)

// Bubble Appearance
buy_bubble_color = input.color(color.new(color.blue, 30), "Buy Orders Color", group=g2)
sell_bubble_color = input.color(color.new(color.red, 30), "Sell Orders Color", group=g2)
show_bubbles = input.bool(true, "Show CVD Bubbles", group=g2)

// Threshold Settings (STDEV Multipliers) - matching original
small_bubble_mult = input.float(3, "Small CVD Bubble (STDEV MULT.)", minval=0.1, step=0.1, group=g3)
medium_bubble_mult = input.float(5, "Medium CVD Bubble (STDEV MULT.)", minval=0.1, step=0.1, group=g3)
large_bubble_mult = input.float(8, "Large CVD Bubble (STDEV MULT.)", minval=0.1, step=0.1, group=g3)

// === CVD Calculation (Leviathan Method) ===
// Simple volume delta based on candle direction
buy_vol = close > open ? volume : 0
sell_vol = close < open ? volume : 0
delta_vol = buy_vol - sell_vol

// Cumulative Volume Delta
cum_delta_vol = ta.cum(delta_vol)

// Calculate standard deviation of absolute delta volume for thresholds
abs_delta = math.abs(delta_vol)
delta_stdev = ta.stdev(abs_delta, stdev_length)

// Level calculation modes affect sensitivity
mode_multiplier = level_calc_mode == "MODE 1" ? 1.0 :
                 level_calc_mode == "MODE 2" ? 0.75 : 0.5

// Calculate threshold levels (smaller multiplier = more sensitive)
small_threshold = delta_stdev * small_bubble_mult * mode_multiplier
medium_threshold = delta_stdev * medium_bubble_mult * mode_multiplier
large_threshold = delta_stdev * large_bubble_mult * mode_multiplier

// === Bubble Detection Logic ===
// Check if absolute delta volume exceeds thresholds
show_large_bubble = abs_delta >= large_threshold and show_bubbles
show_medium_bubble = abs_delta >= medium_threshold and abs_delta < large_threshold and show_bubbles
show_small_bubble = abs_delta >= small_threshold and abs_delta < medium_threshold and show_bubbles

// Determine bubble direction
is_buy_bubble = delta_vol > 0  // More buying than selling
is_sell_bubble = delta_vol < 0  // More selling than buying

// === Plot CVD Bubbles ===
// Large Bubbles (highest volume activity)
plotshape(
    series = show_large_bubble and is_buy_bubble,
    title = "Large Buy CVD Bubble",
    style = shape.circle,
    location = location.abovebar,
    color = buy_bubble_color,
    size = size.large
)

plotshape(
    series = show_large_bubble and is_sell_bubble,
    title = "Large Sell CVD Bubble",
    style = shape.circle,
    location = location.belowbar,
    color = sell_bubble_color,
    size = size.large
)

// Medium Bubbles
plotshape(
    series = show_medium_bubble and is_buy_bubble,
    title = "Medium Buy CVD Bubble",
    style = shape.circle,
    location = location.abovebar,
    color = buy_bubble_color,
    size = size.normal
)

plotshape(
    series = show_medium_bubble and is_sell_bubble,
    title = "Medium Sell CVD Bubble",
    style = shape.circle,
    location = location.belowbar,
    color = sell_bubble_color,
    size = size.normal
)

// Small Bubbles
plotshape(
    series = show_small_bubble and is_buy_bubble,
    title = "Small Buy CVD Bubble",
    style = shape.circle,
    location = location.abovebar,
    color = buy_bubble_color,
    size = size.small
)

plotshape(
    series = show_small_bubble and is_sell_bubble,
    title = "Small Sell CVD Bubble",
    style = shape.circle,
    location = location.belowbar,
    color = sell_bubble_color,
    size = size.small
)



// === Optional Display Features ===
// CVD Line (for reference)
plot_cvd_line = input.bool(false, "Show CVD Line", group=g1)
plot(plot_cvd_line ? cum_delta_vol : na, "CVD", color=color.gray, linewidth=1)

// Delta Volume Histogram
show_delta_histogram = input.bool(false, "Show Delta Volume Histogram", group=g1)
bgcolor(show_delta_histogram ? (delta_vol > 0 ? color.new(color.green, 90) : color.new(color.red, 90)) : na)

// === Debug Information ===
show_debug = input.bool(false, "Show Debug Info", group=g3)
if show_debug and barstate.islast
    var debug_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)
    table.cell(debug_table, 0, 0, "Metric", text_color=color.black, bgcolor=color.gray)
    table.cell(debug_table, 1, 0, "Value", text_color=color.black, bgcolor=color.gray)
    table.cell(debug_table, 0, 1, "Mode", text_color=color.black)
    table.cell(debug_table, 1, 1, mode, text_color=color.black)
    table.cell(debug_table, 0, 2, "Delta Vol", text_color=color.black)
    table.cell(debug_table, 1, 2, str.tostring(delta_vol), text_color=color.black)
    table.cell(debug_table, 0, 3, "Abs Delta", text_color=color.black)
    table.cell(debug_table, 1, 3, str.tostring(abs_delta), text_color=color.black)
    table.cell(debug_table, 0, 4, "Delta StDev", text_color=color.black)
    table.cell(debug_table, 1, 4, str.tostring(delta_stdev), text_color=color.black)
    table.cell(debug_table, 0, 5, "Small Threshold", text_color=color.black)
    table.cell(debug_table, 1, 5, str.tostring(small_threshold), text_color=color.black)
    table.cell(debug_table, 0, 6, "Medium Threshold", text_color=color.black)
    table.cell(debug_table, 1, 6, str.tostring(medium_threshold), text_color=color.black)
    table.cell(debug_table, 0, 7, "Large Threshold", text_color=color.black)
    table.cell(debug_table, 1, 7, str.tostring(large_threshold), text_color=color.black)
