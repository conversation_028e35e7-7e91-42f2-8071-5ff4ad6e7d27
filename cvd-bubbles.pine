//@version=5
indicator("CVD Bubbles", overlay=true, max_labels_count=500)

// Input Groups
g1 = 'CVD Settings'
g2 = 'Bubble Appearance'
g3 = 'Threshold Settings'

// CVD Settings
ema_cvd_length = input.int(30, "EMA/CVD Length", minval=1, group=g1)
ltf_timeframe = input.timeframe('1', 'Lower Timeframe for Volume', group=g1)
stdev_length = input.int(150, "Standard Deviation Length", minval=1, group=g1)

// Bubble Appearance
buy_bubble_color = input.color(color.new(color.blue, 30), "Buy Orders Color", group=g2)
sell_bubble_color = input.color(color.new(color.red, 30), "Sell Orders Color", group=g2)
show_bubbles = input.bool(true, "Show CVD Bubbles", group=g2)

// Threshold Settings (STDEV Multipliers)
small_bubble_mult = input.float(3, "Small CVD Bubble (STDEV MULT.)", minval=0.1, step=0.1, group=g3)
medium_bubble_mult = input.float(5, "Medium CVD Bubble (STDEV MULT.)", minval=0.1, step=0.1, group=g3)
large_bubble_mult = input.float(8, "Large CVD Bubble (STDEV MULT.)", minval=0.1, step=0.1, group=g3)

// CVD Calculation
[buy_volume, sell_volume] = request.security_lower_tf(syminfo.tickerid, ltf_timeframe, [close > open ? volume : 0, close < open ? volume : 0])

buy_vol = array.sum(buy_volume)
sell_vol = array.sum(sell_volume)
delta_vol = buy_vol - sell_vol
cum_delta_vol = ta.cum(delta_vol)

// Calculate CVD EMA for smoothing
cvd_ema = ta.ema(cum_delta_vol, ema_cvd_length)

// Calculate standard deviation for thresholds
cvd_stdev = ta.stdev(delta_vol, stdev_length)
cvd_mean = ta.sma(delta_vol, stdev_length)

// Normalize delta volume for threshold calculation
normalized_delta = math.abs(delta_vol - cvd_mean) / cvd_stdev

// Determine bubble conditions
show_large_bubble = normalized_delta >= large_bubble_mult and show_bubbles
show_medium_bubble = normalized_delta >= medium_bubble_mult and normalized_delta < large_bubble_mult and show_bubbles
show_small_bubble = normalized_delta >= small_bubble_mult and normalized_delta < medium_bubble_mult and show_bubbles

// Determine bubble type (buy or sell)
is_buy_bubble = delta_vol > 0
is_sell_bubble = delta_vol < 0

// Plot Large CVD Bubbles
plotshape(series = show_large_bubble and is_buy_bubble, title = "Large Buy CVD Bubble", style = shape.circle, location = location.abovebar, color = buy_bubble_color, size = size.large)
plotshape(series = show_large_bubble and is_sell_bubble, title = "Large Sell CVD Bubble", style = shape.circle, location = location.belowbar, color = sell_bubble_color, size = size.large)

// Plot Medium CVD Bubbles
plotshape(series = show_medium_bubble and is_buy_bubble, title = "Medium Buy CVD Bubble", style = shape.circle, location = location.abovebar, color = buy_bubble_color, size = size.normal)
plotshape(series = show_medium_bubble and is_sell_bubble, title = "Medium Sell CVD Bubble", style = shape.circle, location = location.belowbar, color = sell_bubble_color, size = size.normal)

// Plot Small CVD Bubbles
plotshape(series = show_small_bubble and is_buy_bubble, title = "Small Buy CVD Bubble", style = shape.circle, location = location.abovebar, color = buy_bubble_color, size = size.small)
plotshape(series = show_small_bubble and is_sell_bubble, title = "Small Sell CVD Bubble", style = shape.circle, location = location.belowbar, color = sell_bubble_color, size = size.small)



// Optional: Plot CVD line for reference (can be disabled)
plot_cvd_line = input.bool(false, "Show CVD Line", group=g1)
plot(plot_cvd_line ? cum_delta_vol : na, "CVD", color=color.gray, linewidth=1)

// Optional: Show delta volume histogram in separate pane
show_delta_histogram = input.bool(false, "Show Delta Volume Histogram", group=g1)
bgcolor(show_delta_histogram ? (delta_vol > 0 ? color.new(color.green, 90) : color.new(color.red, 90)) : na)

// Debug information (can be enabled for testing)
show_debug = input.bool(false, "Show Debug Info", group=g3)
if show_debug and barstate.islast
    var debug_table = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
    table.cell(debug_table, 0, 0, "Metric", text_color=color.black, bgcolor=color.gray)
    table.cell(debug_table, 1, 0, "Value", text_color=color.black, bgcolor=color.gray)
    table.cell(debug_table, 0, 1, "Current Delta", text_color=color.black)
    table.cell(debug_table, 1, 1, str.tostring(delta_vol), text_color=color.black)
    table.cell(debug_table, 0, 2, "CVD StDev", text_color=color.black)
    table.cell(debug_table, 1, 2, str.tostring(cvd_stdev), text_color=color.black)
    table.cell(debug_table, 0, 3, "Normalized", text_color=color.black)
    table.cell(debug_table, 1, 3, str.tostring(normalized_delta), text_color=color.black)
    table.cell(debug_table, 0, 4, "Bubble Size", text_color=color.black)
    table.cell(debug_table, 1, 4, bubble_size == size.large ? "Large" : bubble_size == size.normal ? "Medium" : "Small", text_color=color.black)
    table.cell(debug_table, 0, 5, "Show Bubble", text_color=color.black)
    table.cell(debug_table, 1, 5, str.tostring(show_bubble), text_color=color.black)
