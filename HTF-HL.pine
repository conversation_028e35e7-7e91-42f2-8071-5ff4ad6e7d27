//@version=5
indicator(title='Higher Time Frame-High & Low [Ravi]',
          shorttitle='High Time Frame H/L',
          overlay=true,
          max_lines_count = 500,
          max_labels_count = 500
          )

// Master toggles for each component to reduce plot count
show_htf_hl = input.bool(true, "Show HTF High/Low/Open Levels", group="Component Toggles")

// Arrays to store lines and labels
// For high lines
var dailyHighLine = array.new_line(1, na)
var weeklyHighLine = array.new_line(1, na)
var monthlyHighLine = array.new_line(1, na)
var quarterlyHighLine = array.new_line(1, na)
var yearlyHighLine = array.new_line(1, na)

// For low lines
var dailyLowLine = array.new_line(1, na)
var weeklyLowLine = array.new_line(1, na)
var monthlyLowLine = array.new_line(1, na)
var quarterlyLowLine = array.new_line(1, na)
var yearlyLowLine = array.new_line(1, na)

// For open lines
var dailyOpenLine = array.new_line(1, na)
var weeklyOpenLine = array.new_line(1, na)
var monthlyOpenLine = array.new_line(1, na)
var quarterlyOpenLine = array.new_line(1, na)
var yearlyOpenLine = array.new_line(1, na)

// For labels
var dailyLabels = array.new_label()
var weeklyLabels = array.new_label()
var monthlyLabels = array.new_label()
var quarterlyLabels = array.new_label()
var yearlyLabels = array.new_label()
var combinedLabels = array.new_label() // Array to store combined level labels

// Function to clear old lines
clearLines(linesArray) =>
    if array.size(linesArray) > 0
        for i = 0 to array.size(linesArray) - 1
            line.delete(array.get(linesArray, i))
        array.clear(linesArray)

// Function to clear old labels
clearLabels(labelsArray) =>
    if array.size(labelsArray) > 0
        for i = 0 to array.size(labelsArray) - 1
            label.delete(array.get(labelsArray, i))
        array.clear(labelsArray)

// Function to manage line history - keep only the latest line
manageLine(linesArray, newLine) =>
    // Delete all existing lines from the chart
    if array.size(linesArray) > 0
        for i = 0 to array.size(linesArray) - 1
            line.delete(array.get(linesArray, i))
    // Clear the array
    array.clear(linesArray)
    // Add the new line
    array.push(linesArray, newLine)

// Function to check if two price levels are close to each other
isLevelsClose(level1, level2) =>
    // Calculate the percentage difference between levels
    // If levels are within 0.1% of each other, consider them close
    math.abs(level1 - level2) / level1 < 0.001

// ---------------------------------------------------------------------------------------------------------------------}
// Inputs for each timeframe
// ---------------------------------------------------------------------------------------------------------------------{


// Timeframe Settings
show_daily = input.bool(true, "Show Daily Levels", group="Timeframe Settings")
show_weekly = input.bool(true, "Show Weekly Levels", group="Timeframe Settings")
show_monthly = input.bool(true, "Show Monthly Levels", group="Timeframe Settings")
show_quarterly = input.bool(true, "Show Quarterly Levels", group="Timeframe Settings")
show_yearly = input.bool(true, "Show Yearly Levels", group="Timeframe Settings")

// Open Level Settings
show_daily_open = input.bool(true, "Show Daily Open", group="Open Levels")
show_weekly_open = input.bool(true, "Show Weekly Open", group="Open Levels")
show_monthly_open = input.bool(true, "Show Monthly Open", group="Open Levels")
show_quarterly_open = input.bool(true, "Show Quarterly Open", group="Open Levels")
show_yearly_open = input.bool(true, "Show Yearly Open", group="Open Levels")

extension = input.int(50, "Extension to the right", group="General Settings", minval=1, maxval=500)
show_break = input.bool(false, "Show Breakout Labels", group="General Settings")
show_trend = input.bool(false, "Show Trend Lines", group="General Settings")

// High/Low Color settings
daily_high_color = input.color(color.white, "Daily High", group="High/Low Colors")
daily_low_color = input.color(color.white, "Daily Low", group="High/Low Colors")
weekly_high_color = input.color(color.white, "Weekly High", group="High/Low Colors")
weekly_low_color = input.color(color.white, "Weekly Low", group="High/Low Colors")
monthly_high_color = input.color(color.white, "Monthly High", group="High/Low Colors")
monthly_low_color = input.color(color.white, "Monthly Low", group="High/Low Colors")
quarterly_high_color = input.color(color.white, "Quarterly High", group="High/Low Colors")
quarterly_low_color = input.color(color.white, "Quarterly Low", group="High/Low Colors")
yearly_high_color = input.color(color.white, "Yearly High", group="High/Low Colors")
yearly_low_color = input.color(color.white, "Yearly Low", group="High/Low Colors")

// Open Level Color settings
daily_open_color = input.color(color.white, "Daily Open", group="Open Colors")
weekly_open_color = input.color(color.white, "Weekly Open", group="Open Colors")
monthly_open_color = input.color(color.white, "Monthly Open", group="Open Colors")
quarterly_open_color = input.color(color.white, "Quarterly Open", group="Open Colors")
yearly_open_color = input.color(color.white, "Yearly Open", group="Open Colors")

// ---------------------------------------------------------------------------------------------------------------------}
// Functions
// ---------------------------------------------------------------------------------------------------------------------{
getLevels(timeframe) =>
    // For high and low, we want the previous period's values
    prev_high_tf = request.security(syminfo.tickerid, timeframe, high[1], lookahead=barmerge.lookahead_off)
    prev_low_tf = request.security(syminfo.tickerid, timeframe, low[1], lookahead=barmerge.lookahead_off)

    // For open, we want the current period's value
    curr_open_tf = request.security(syminfo.tickerid, timeframe, open, lookahead=barmerge.lookahead_off)

    [prev_high_tf, prev_low_tf, curr_open_tf]

// Global variables to track all level values and their labels
var float[] allLevels = array.new_float()
var string[] allLevelTexts = array.new_string()

// Function to find existing level index
findExistingLevelIndex(level) =>
    result = -1
    if array.size(allLevels) > 0
        for i = 0 to array.size(allLevels) - 1
            if isLevelsClose(level, array.get(allLevels, i))
                result := i
                break
    result

// Function to add or update level
addOrUpdateLevel(level, t) =>
    existingIndex = findExistingLevelIndex(level)
    if existingIndex >= 0
        // Update existing level t
        currentText = array.get(allLevelTexts, existingIndex)
        if not str.contains(currentText, t)
            array.set(allLevelTexts, existingIndex, currentText + " & " + t)
    else
        // Add new level
        array.push(allLevels, level)
        array.push(allLevelTexts, t)

drawLevels(show, tf_high, tf_low, high_color, low_color, tf_name, highLineArray, lowLineArray, labelsArray) =>
    if show and barstate.islast
        // Clear old labels
        clearLabels(labelsArray)

        // Delete existing high line if it exists
        if not na(array.get(highLineArray, 0))
            line.delete(array.get(highLineArray, 0))

        // Draw high line - keep only the latest
        high_line = line.new(x1=bar_index, y1=tf_high, x2=bar_index + extension, y2=tf_high, color=high_color, width=2)
        array.set(highLineArray, 0, high_line)

        // Add or update high level with appropriate label
        if tf_name == "Daily"
            addOrUpdateLevel(tf_high, "PDH")  // Previous Day High
        else if tf_name == "Weekly"
            addOrUpdateLevel(tf_high, "PWH")  // Previous Week High
        else if tf_name == "Monthly"
            addOrUpdateLevel(tf_high, "PMH")  // Previous Month High
        else if tf_name == "Quarterly"
            addOrUpdateLevel(tf_high, "PQH")  // Previous Quarter High
        else if tf_name == "Yearly"
            addOrUpdateLevel(tf_high, "PYH")  // Previous Year High

        // Delete existing low line if it exists
        if not na(array.get(lowLineArray, 0))
            line.delete(array.get(lowLineArray, 0))

        // Draw low line - keep only the latest
        low_line = line.new(x1=bar_index, y1=tf_low, x2=bar_index + extension, y2=tf_low, color=low_color, width=2)
        array.set(lowLineArray, 0, low_line)

        // Add or update low level with appropriate label
        if tf_name == "Daily"
            addOrUpdateLevel(tf_low, "PDL")  // Previous Day Low
        else if tf_name == "Weekly"
            addOrUpdateLevel(tf_low, "PWL")  // Previous Week Low
        else if tf_name == "Monthly"
            addOrUpdateLevel(tf_low, "PML")  // Previous Month Low
        else if tf_name == "Quarterly"
            addOrUpdateLevel(tf_low, "PQL")  // Previous Quarter Low
        else if tf_name == "Yearly"
            addOrUpdateLevel(tf_low, "PYL")  // Previous Year Low

drawOpenLevel(show, tf_open, open_color, tf_name, openLineArray, labelsArray) =>
    if show and barstate.islast
        // Clear old labels
        clearLabels(labelsArray)

        // Delete existing open line if it exists
        if not na(array.get(openLineArray, 0))
            line.delete(array.get(openLineArray, 0))

        // Draw open line - keep only the latest
        open_line = line.new(x1=bar_index, y1=tf_open, x2=bar_index + extension, y2=tf_open, color=open_color, width=2, style=line.style_dashed)
        array.set(openLineArray, 0, open_line)

        // Add or update open level with appropriate label
        if tf_name == "Daily"
            addOrUpdateLevel(tf_open, "CDO")  // Current Day Open
        else if tf_name == "Weekly"
            addOrUpdateLevel(tf_open, "CWO")  // Current Week Open
        else if tf_name == "Monthly"
            addOrUpdateLevel(tf_open, "CMO")  // Current Month Open
        else if tf_name == "Quarterly"
            addOrUpdateLevel(tf_open, "CQO")  // Current Quarter Open
        else if tf_name == "Yearly"
            addOrUpdateLevel(tf_open, "CYO")  // Current Year Open

// ---------------------------------------------------------------------------------------------------------------------}
// Get levels for each timeframe
// ---------------------------------------------------------------------------------------------------------------------{
[daily_high, daily_low, daily_open] = getLevels("D")
[weekly_high, weekly_low, weekly_open] = getLevels("W")
[monthly_high, monthly_low, monthly_open] = getLevels("M")
[quarterly_high, quarterly_low, quarterly_open] = getLevels("3M")
[yearly_high, yearly_low, yearly_open] = getLevels("12M")

// Calculate change conditions
daily_changed = not ta.change(daily_high) or not ta.change(daily_low)
weekly_changed = not ta.change(weekly_high) or not ta.change(weekly_low)
monthly_changed = not ta.change(monthly_high) or not ta.change(monthly_low)
quarterly_changed = not ta.change(quarterly_high) or not ta.change(quarterly_low)
yearly_changed = not ta.change(yearly_high) or not ta.change(yearly_low)

// Calculate open change conditions
daily_open_changed = not ta.change(daily_open)
weekly_open_changed = not ta.change(weekly_open)
monthly_open_changed = not ta.change(monthly_open)
quarterly_open_changed = not ta.change(quarterly_open)
yearly_open_changed = not ta.change(yearly_open)

// Plot high/low levels in global scope
plot(show_htf_hl and show_daily and not show_trend and daily_changed ? daily_high : na, "Daily High", style=plot.style_linebr, linewidth=1, color=daily_high_color)
plot(show_htf_hl and show_daily and not show_trend and daily_changed ? daily_low : na, "Daily Low", style=plot.style_linebr, linewidth=1, color=daily_low_color)

plot(show_htf_hl and show_weekly and not show_trend and weekly_changed ? weekly_high : na, "Weekly High", style=plot.style_linebr, linewidth=2, color=weekly_high_color)
plot(show_htf_hl and show_weekly and not show_trend and weekly_changed ? weekly_low : na, "Weekly Low", style=plot.style_linebr, linewidth=2, color=weekly_low_color)

plot(show_htf_hl and show_monthly and not show_trend and monthly_changed ? monthly_high : na, "Monthly High", style=plot.style_linebr, linewidth=3, color=monthly_high_color)
plot(show_htf_hl and show_monthly and not show_trend and monthly_changed ? monthly_low : na, "Monthly Low", style=plot.style_linebr, linewidth=3, color=monthly_low_color)

plot(show_htf_hl and show_quarterly and not show_trend and quarterly_changed ? quarterly_high : na, "Quarterly High", style=plot.style_linebr, linewidth=3, color=quarterly_high_color)
plot(show_htf_hl and show_quarterly and not show_trend and quarterly_changed ? quarterly_low : na, "Quarterly Low", style=plot.style_linebr, linewidth=3, color=quarterly_low_color)

plot(show_htf_hl and show_yearly and not show_trend and yearly_changed ? yearly_high : na, "Yearly High", style=plot.style_linebr, linewidth=3, color=yearly_high_color)
plot(show_htf_hl and show_yearly and not show_trend and yearly_changed ? yearly_low : na, "Yearly Low", style=plot.style_linebr, linewidth=3, color=yearly_low_color)

// Plot open levels in global scope
plot(show_htf_hl and show_daily_open and daily_open_changed ? daily_open : na, "Daily Open", style=plot.style_linebr, linewidth=1, color=daily_open_color)
plot(show_htf_hl and show_weekly_open and weekly_open_changed ? weekly_open : na, "Weekly Open", style=plot.style_linebr, linewidth=2, color=weekly_open_color)
plot(show_htf_hl and show_monthly_open and monthly_open_changed ? monthly_open : na, "Monthly Open", style=plot.style_linebr, linewidth=3, color=monthly_open_color)
plot(show_htf_hl and show_quarterly_open and quarterly_open_changed ? quarterly_open : na, "Quarterly Open", style=plot.style_linebr, linewidth=3, color=quarterly_open_color)
plot(show_htf_hl and show_yearly_open and yearly_open_changed ? yearly_open : na, "Yearly Open", style=plot.style_linebr, linewidth=3, color=yearly_open_color)

// Draw extended high/low lines and labels
if show_htf_hl
    drawLevels(show_daily, daily_high, daily_low, daily_high_color, daily_low_color, "Daily", dailyHighLine, dailyLowLine, dailyLabels)
    drawLevels(show_weekly, weekly_high, weekly_low, weekly_high_color, weekly_low_color, "Weekly", weeklyHighLine, weeklyLowLine, weeklyLabels)
    drawLevels(show_monthly, monthly_high, monthly_low, monthly_high_color, monthly_low_color, "Monthly", monthlyHighLine, monthlyLowLine, monthlyLabels)
    drawLevels(show_quarterly, quarterly_high, quarterly_low, quarterly_high_color, quarterly_low_color, "Quarterly", quarterlyHighLine, quarterlyLowLine, quarterlyLabels)
    drawLevels(show_yearly, yearly_high, yearly_low, yearly_high_color, yearly_low_color, "Yearly", yearlyHighLine, yearlyLowLine, yearlyLabels)

    // Draw extended open lines and labels
    drawOpenLevel(show_daily_open, daily_open, daily_open_color, "Daily", dailyOpenLine, dailyLabels)
    drawOpenLevel(show_weekly_open, weekly_open, weekly_open_color, "Weekly", weeklyOpenLine, weeklyLabels)
    drawOpenLevel(show_monthly_open, monthly_open, monthly_open_color, "Monthly", monthlyOpenLine, monthlyLabels)
    drawOpenLevel(show_quarterly_open, quarterly_open, quarterly_open_color, "Quarterly", quarterlyOpenLine, quarterlyLabels)
    drawOpenLevel(show_yearly_open, yearly_open, yearly_open_color, "Yearly", yearlyOpenLine, yearlyLabels)

// Create combined labels for all levels
if barstate.islast and show_htf_hl
    // Clear all existing arrays first
    array.clear(allLevels)
    array.clear(allLevelTexts)

    // Clear old combined labels to prevent cluttering
    clearLabels(combinedLabels)

    // Process all high/low levels - only the latest ones
    if show_daily
        addOrUpdateLevel(daily_high, "PDH")  // Previous Day High
        addOrUpdateLevel(daily_low, "PDL")   // Previous Day Low
    if show_weekly
        addOrUpdateLevel(weekly_high, "PWH")  // Previous Week High
        addOrUpdateLevel(weekly_low, "PWL")   // Previous Week Low
    if show_monthly
        addOrUpdateLevel(monthly_high, "PMH")  // Previous Month High
        addOrUpdateLevel(monthly_low, "PML")   // Previous Month Low
    if show_quarterly
        addOrUpdateLevel(quarterly_high, "PQH")  // Previous Quarter High
        addOrUpdateLevel(quarterly_low, "PQL")   // Previous Quarter Low
    if show_yearly
        addOrUpdateLevel(yearly_high, "PYH")  // Previous Year High
        addOrUpdateLevel(yearly_low, "PYL")   // Previous Year Low

    // Process all open levels - only the latest ones
    if show_daily_open
        addOrUpdateLevel(daily_open, "CDO")  // Current Day Open
    if show_weekly_open
        addOrUpdateLevel(weekly_open, "CWO")  // Current Week Open
    if show_monthly_open
        addOrUpdateLevel(monthly_open, "CMO")  // Current Month Open
    if show_quarterly_open
        addOrUpdateLevel(quarterly_open, "CQO")  // Current Quarter Open
    if show_yearly_open
        addOrUpdateLevel(yearly_open, "CYO")  // Current Year Open

    // Create labels for all tracked levels - only the latest ones
    if array.size(allLevels) > 0
        for i = 0 to array.size(allLevels) - 1
            level = array.get(allLevels, i)
            t = array.get(allLevelTexts, i)
            newLabel = label.new(x=bar_index + extension, y=level, text=t, style=label.style_label_left, color=color.new(color.black, 100), textcolor=color.white, size=size.normal)
            array.push(combinedLabels, newLabel)

// Alert conditions for high/low breakouts
// Daily
alertcondition(ta.crossover(high, daily_high), title="Daily High Breakout", message="Price broke above the Daily High")
alertcondition(ta.crossunder(low, daily_low), title="Daily Low Breakout", message="Price broke below the Daily Low")

// Weekly
alertcondition(ta.crossover(high, weekly_high), title="Weekly High Breakout", message="Price broke above the Weekly High")
alertcondition(ta.crossunder(low, weekly_low), title="Weekly Low Breakout", message="Price broke below the Weekly Low")

// Monthly
alertcondition(ta.crossover(high, monthly_high), title="Monthly High Breakout", message="Price broke above the Monthly High")
alertcondition(ta.crossunder(low, monthly_low), title="Monthly Low Breakout", message="Price broke below the Monthly Low")

// Quarterly
alertcondition(ta.crossover(high, quarterly_high), title="Quarterly High Breakout", message="Price broke above the Quarterly High")
alertcondition(ta.crossunder(low, quarterly_low), title="Quarterly Low Breakout", message="Price broke below the Quarterly Low")

// Yearly
alertcondition(ta.crossover(high, yearly_high), title="Yearly High Breakout", message="Price broke above the Yearly High")
alertcondition(ta.crossunder(low, yearly_low), title="Yearly Low Breakout", message="Price broke below the Yearly Low")

// Alert conditions for open level breakouts
alertcondition(ta.cross(close, daily_open), title="Daily Open Breakout", message="Price crossed the Daily Open level")
alertcondition(ta.cross(close, weekly_open), title="Weekly Open Breakout", message="Price crossed the Weekly Open level")
alertcondition(ta.cross(close, monthly_open), title="Monthly Open Breakout", message="Price crossed the Monthly Open level")
alertcondition(ta.cross(close, quarterly_open), title="Quarterly Open Breakout", message="Price crossed the Quarterly Open level")
alertcondition(ta.cross(close, yearly_open), title="Yearly Open Breakout", message="Price crossed the Yearly Open level")

