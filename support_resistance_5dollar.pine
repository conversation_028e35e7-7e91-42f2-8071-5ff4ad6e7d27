// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © AlgoAlpha

//@version=5
indicator("$5 Support & Resistance Lines", overlay=true, max_lines_count=500, max_labels_count=500)

// Input parameters
line_color = input.color(#787B86, "Line Color")
line_transparency = input.int(50, "Line Transparency", minval=0, maxval=100)
line_style = input.string("Dashed", "Line Style", options=["Solid", "Dashed", "Dotted"])
show_labels = input.bool(true, "Show Price Labels")
extend_right = input.int(20, "Extend Right (bars)", minval=0)
lookback = input.int(500, "Look Back Period", minval=1)

// Convert line style input to Pine style
var line_style_dict = line.style_solid
if line_style == "Dashed"
    line_style_dict := line.style_dashed
else if line_style == "Dotted"
    line_style_dict := line.style_dotted

// Store lines and labels in arrays
var line[] lines_array = array.new_line()
var label[] labels_array = array.new_label()

// Draw support and resistance lines
f_draw_sr_lines() =>
    price_ceil = math.ceil(high / 5) * 5
    price_floor = math.floor(low / 5) * 5
    
    // Clear previous lines and labels
    if array.size(lines_array) > 0
        for i = array.size(lines_array) - 1 to 0
            line.delete(array.get(lines_array, i))
        array.clear(lines_array)
    
    if array.size(labels_array) > 0
        for i = array.size(labels_array) - 1 to 0
            label.delete(array.get(labels_array, i))
        array.clear(labels_array)
    
    // Draw new lines and labels
    for price = price_floor to price_ceil by 5
        new_line = line.new(bar_index - lookback, price, 
             bar_index + extend_right, price, 
             extend=extend.right,
             color=color.new(line_color, line_transparency),
             style=line_style_dict)
        array.push(lines_array, new_line)
             
        if show_labels
            new_label = label.new(bar_index + extend_right + 2, price, 
                 str.tostring(price),
                 color=color.new(line_color, 100),
                 textcolor=color.new(line_color, 0),
                 style=label.style_label_left,
                 size=size.small)
            array.push(labels_array, new_label)

// Draw new lines
f_draw_sr_lines()