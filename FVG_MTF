//@version=5
indicator("Multi-Timeframe Fair Value Gap [Ravi]", "MTF FVG", overlay = true, max_lines_count = 500, max_boxes_count = 500)

//------------------------------------------------------------------------------
//Settings
//-----------------------------------------------------------------------------{
thresholdPer = input.float(0, "Threshold %", minval = 0, maxval = 100, step = .1)
showLast = input.int(0, 'Unmitigated Levels', minval = 0)
mitigationLevels = input.bool(false, 'Mitigation Levels')

// Multiple timeframe settings
tf1 = input.timeframe('', "Timeframe 1")
tf2 = input.timeframe('', "Timeframe 2")
tf3 = input.timeframe('', "Timeframe 3")

show_tf1 = input.bool(true, "Show Timeframe 1")
show_tf2 = input.bool(true, "Show Timeframe 2")
show_tf3 = input.bool(true, "Show Timeframe 3")

//Style
extend = input.int(20, 'Extend', minval = 0, group = 'Style')

// Colors for different timeframes
tf1_bull = input.color(color.new(#089981, 70), "TF1 Bullish FVG", group = 'Style')
tf1_bear = input.color(color.new(#f23645, 70), "TF1 Bearish FVG", group = 'Style')
tf2_bull = input.color(color.new(#089981, 50), "TF2 Bullish FVG", group = 'Style')
tf2_bear = input.color(color.new(#f23645, 50), "TF2 Bearish FVG", group = 'Style')
tf3_bull = input.color(color.new(#089981, 30), "TF3 Bullish FVG", group = 'Style')
tf3_bear = input.color(color.new(#f23645, 30), "TF3 Bearish FVG", group = 'Style')

//-----------------------------------------------------------------------------}
//UDT's
//-----------------------------------------------------------------------------{
type fvg
    float max
    float min
    bool isbull
    int t = time
    string tf

//-----------------------------------------------------------------------------}
//Methods/Functions
//-----------------------------------------------------------------------------{
n = bar_index

detect(timeframe)=>
    var new_fvg = fvg.new(na, na, na, na, '')
    threshold = thresholdPer / 100

    bull_fvg = low > high[2] and close[1] > high[2] and (low - high[2]) / high[2] > threshold
    bear_fvg = high < low[2] and close[1] < low[2] and (low[2] - high) / high > threshold
    
    if bull_fvg
        new_fvg := fvg.new(low, high[2], true, time, timeframe)
    else if bear_fvg
        new_fvg := fvg.new(low[2], high, false, time, timeframe)

    [bull_fvg, bear_fvg, new_fvg]

get_color(is_bull, timeframe) =>
    if timeframe == tf1
        is_bull ? tf1_bull : tf1_bear
    else if timeframe == tf2
        is_bull ? tf2_bull : tf2_bear
    else
        is_bull ? tf3_bull : tf3_bear

//-----------------------------------------------------------------------------}
//FVG's detection/display
//-----------------------------------------------------------------------------{
var fvg_records = array.new<fvg>(0)
var fvg_areas = array.new<box>(0)
var last_times = array.new<int>(3, 0)

// Get FVG data for all timeframes
[bull_fvg1, bear_fvg1, new_fvg1] = request.security(syminfo.tickerid, tf1, detect(tf1))
[bull_fvg2, bear_fvg2, new_fvg2] = request.security(syminfo.tickerid, tf2, detect(tf2))
[bull_fvg3, bear_fvg3, new_fvg3] = request.security(syminfo.tickerid, tf3, detect(tf3))

formatTfText(tf) =>
    timeframeS = tf
    if str.contains(tf, "D")
        str.replace(tf, "D", "D ")
    else if str.contains(tf, "W")
        str.replace(tf, "W", "W ")
    else if str.contains(tf, "M")
        str.replace(tf, "M", "M ")
    else
        seconds = timeframe.in_seconds(tf)
        if seconds >= 3600
            hourCount = str.tostring(seconds / 3600)
            hourCount + "hr "
        else
            tf + "m "

// Process timeframe 1
if show_tf1 and (bull_fvg1 or bear_fvg1) and new_fvg1.t != array.get(last_times, 0)
    fvg_color = get_color(new_fvg1.isbull, tf1)
    box_text = formatTfText(tf1) + "FVG"
    fvg_areas.unshift(box.new(n-2, new_fvg1.max, n+extend, new_fvg1.min, na, bgcolor = fvg_color, text = box_text, text_color = color.white, text_halign = text.align_center, text_size=size.small))
    fvg_records.unshift(new_fvg1)
    array.set(last_times, 0, new_fvg1.t)

// Process timeframe 2
if show_tf2 and (bull_fvg2 or bear_fvg2) and new_fvg2.t != array.get(last_times, 1)
    fvg_color = get_color(new_fvg2.isbull, tf2)
    box_text = formatTfText(tf2) + "FVG"
    fvg_areas.unshift(box.new(n-2, new_fvg2.max, n+extend, new_fvg2.min, na, bgcolor = fvg_color, text = box_text, text_color = color.white, text_halign = text.align_center, text_size=size.small))
    fvg_records.unshift(new_fvg2)
    array.set(last_times, 1, new_fvg2.t)

// Process timeframe 3
if show_tf3 and (bull_fvg3 or bear_fvg3) and new_fvg3.t != array.get(last_times, 2)
    fvg_color = get_color(new_fvg3.isbull, tf3)
    box_text = formatTfText(tf3) + "FVG"
    fvg_areas.unshift(box.new(n-2, new_fvg3.max, n+extend, new_fvg3.min, na, bgcolor = fvg_color, text = box_text, text_color = color.white, text_halign = text.align_center, text_size=size.small))
    fvg_records.unshift(new_fvg3)
    array.set(last_times, 2, new_fvg3.t)

//-----------------------------------------------------------------------------}
//Mitigation Check
//-----------------------------------------------------------------------------{
if fvg_records.size() > 0
    for i = fvg_records.size()-1 to 0
        get = fvg_records.get(i)
        
        is_mitigated = get.isbull ? close < get.min : close > get.max
        
        if is_mitigated
            if mitigationLevels
                fvg_color = get_color(get.isbull, get.tf)
                level = get.isbull ? get.min : get.max
                
                line.new(get.t
                  , level
                  , time
                  , level
                  , xloc.bar_time
                  , color = fvg_color
                  , style = line.style_dashed)

            area = fvg_areas.remove(i)
            area.delete()
            fvg_records.remove(i)

//-----------------------------------------------------------------------------}
//Unmitigated Lines
//-----------------------------------------------------------------------------{
var unmitigated = array.new<line>(0)

if barstate.islast and showLast > 0 and fvg_records.size() > 0
    if unmitigated.size() > 0 
        for element in unmitigated
            element.delete()
        unmitigated.clear()

    for i = 0 to math.min(showLast-1, fvg_records.size()-1)
        get = fvg_records.get(i)
        fvg_color = get_color(get.isbull, get.tf)
        
        unmitigated.push(line.new(get.t
          , get.isbull ? get.min : get.max 
          , time
          , get.isbull ? get.min : get.max
          , xloc.bar_time
          , color = fvg_color))

//-----------------------------------------------------------------------------}
//Alerts
//-----------------------------------------------------------------------------{
// Variables to track FVG touches
var bool tf1_bull_touch = false
var bool tf1_bear_touch = false
var bool tf2_bull_touch = false
var bool tf2_bear_touch = false
var bool tf3_bull_touch = false
var bool tf3_bear_touch = false

if show_tf1 and bull_fvg1
    tf1_bull_touch := false
if show_tf1 and bear_fvg1
    tf1_bear_touch := false
if show_tf2 and bull_fvg2
    tf2_bull_touch := false
if show_tf2 and bear_fvg2
    tf2_bear_touch := false
if show_tf3 and bull_fvg3
    tf3_bull_touch := false
if show_tf3 and bear_fvg3
    tf3_bear_touch := false

// Check for FVG touches
if fvg_records.size() > 0
    for i = 0 to fvg_records.size() - 1
        get = fvg_records.get(i)
        if get.isbull
            if low <= get.max and high >= get.min // Price is within the FVG range
                if get.tf == tf1
                    tf1_bull_touch := true
                else if get.tf == tf2
                    tf2_bull_touch := true
                else if get.tf == tf3
                    tf3_bull_touch := true
        else
            if low <= get.max and high >= get.min // Price is within the FVG range
                if get.tf == tf1
                    tf1_bear_touch := true
                else if get.tf == tf2
                    tf2_bear_touch := true
                else if get.tf == tf3
                    tf3_bear_touch := true

// Alert conditions for new FVGs
alertcondition(show_tf1 and bull_fvg1, "TF1 Bullish FVG", "New Bullish FVG in ")
alertcondition(show_tf1 and bear_fvg1, "TF1 Bearish FVG", "New Bearish FVG in ")
alertcondition(show_tf2 and bull_fvg2, "TF2 Bullish FVG", "New Bullish FVG in ")
alertcondition(show_tf2 and bear_fvg2, "TF2 Bearish FVG", "New Bearish FVG in ")
alertcondition(show_tf3 and bull_fvg3, "TF3 Bullish FVG", "New Bearish FVG in ")
alertcondition(show_tf3 and bear_fvg3, "TF3 Bearish FVG", "New Bearish FVG in ")

// Alert conditions for FVG touches
alertcondition(tf1_bull_touch, "TF1 Bullish FVG Touch", "Price touched Bullish FVG in ")
alertcondition(tf1_bear_touch, "TF1 Bearish FVG Touch", "Price touched Bearish FVG in ")
alertcondition(tf2_bull_touch, "TF2 Bullish FVG Touch", "Price touched Bullish FVG in ")
alertcondition(tf2_bear_touch, "TF2 Bearish FVG Touch", "Price touched Bearish FVG in ")
alertcondition(tf3_bull_touch, "TF3 Bullish FVG Touch", "Price touched Bullish FVG in ")
alertcondition(tf3_bear_touch, "TF3 Bearish FVG Touch", "Price touched Bearish FVG in ")

// Alert for any FVG mitigation
alertcondition(ta.change(fvg_records.size()) < 0, "FVG Mitigation", "FVG has been mitigated")