//@version=5
indicator("Advanced Session Sweeps [LuxAlgo]", overlay=true, max_lines_count=500, max_boxes_count=500)

//------------------------------------------------------------------------------
// Input Parameters
//------------------------------------------------------------------------------
// Session settings from original indicator
asianSession = input.session("2000-0000", "Asian Session", group="Sessions")
londonOpen = input.session("0200-0500", "London Open", group="Sessions")
nyOpen = input.session("0830-1100", "NY Open", group="Sessions")
londonClose = input.session("1330-1600", "London Close", group="Sessions")

// Sweep Detection Settings
var GRP1 = "Sweep Detection Settings"
sweepLength = input.int(10, "Sweep Detection Length", minval=1, group=GRP1)
sweepThreshold = input.float(1.5, "Sweep ATR Multiple", minval=0.5, group=GRP1)
confirmationBars = input.int(3, "Confirmation Bars", minval=1, group=GRP1)

// Sweep Type Settings
var GRP2 = "Sweep Types"
checkSwingPoints = input(true, "Swing Points Sweep", group=GRP2)
checkRoundNumbers = input(true, "Round Numbers Sweep", group=GRP2)
checkStopClusters = input(true, "Stop Clusters Sweep", group=GRP2)
checkDailyLevels = input(true, "Daily Levels Sweep", group=GRP2)

// Round Number Settings
var GRP3 = "Round Number Settings"
roundNumberSpacing = input.float(50.0, "Round Number Spacing", group=GRP3)
roundNumberThreshold = input.float(0.1, "Round Number Threshold %", group=GRP3)

// Stop Cluster Settings
var GRP4 = "Stop Cluster Settings"
stopClusterPeriod = input.int(20, "Stop Cluster Look-back", group=GRP4)
stopClusterThreshold = input.float(1.2, "Stop Cluster ATR Multiple", group=GRP4)

//------------------------------------------------------------------------------
// Functions
//------------------------------------------------------------------------------
// Function to detect swing point sweeps
detectSwingPointSweep(high, low, close, prevHigh, prevLow, atr) =>
    bullSweep = low < prevLow and close > prevLow and close - low > atr * sweepThreshold
    bearSweep = high > prevHigh and close < prevHigh and high - close > atr * sweepThreshold
    [bullSweep, bearSweep]

// Function to detect round number sweeps
detectRoundNumberSweep(price, roundLevel, threshold) =>
    distance = math.abs(price - roundLevel)
    sweep = distance <= threshold
    [sweep, roundLevel]

// Function to detect stop cluster sweeps
detectStopClusterSweep(high, low, close, atr) =>
    highCluster = ta.highest(high[1], stopClusterPeriod)
    lowCluster = ta.lowest(low[1], stopClusterPeriod)
    
    bullSweep = low < lowCluster and close > lowCluster and close - low > atr * stopClusterThreshold
    bearSweep = high > highCluster and close < highCluster and high - close > atr * stopClusterThreshold
    [bullSweep, bearSweep, highCluster, lowCluster]

// Function to detect daily level sweeps
detectDailyLevelSweep(high, low, close, dailyHigh, dailyLow, atr) =>
    bullSweep = low < dailyLow and close > dailyLow and close - low > atr * sweepThreshold
    bearSweep = high > dailyHigh and close < dailyHigh and high - close > atr * sweepThreshold
    [bullSweep, bearSweep]

//------------------------------------------------------------------------------
// Calculations
//------------------------------------------------------------------------------
// Session windows
inAsianSession = not na(time(timeframe.period, asianSession, "UTC-5"))
inLondonOpen = not na(time(timeframe.period, londonOpen, "UTC-5"))
inNYOpen = not na(time(timeframe.period, nyOpen, "UTC-5"))
inLondonClose = not na(time(timeframe.period, londonClose, "UTC-5"))

// Get ATR
atr = ta.atr(14)

// Get swing levels
swingHigh = ta.highest(high, sweepLength)
swingLow = ta.lowest(low, sweepLength)

// Get daily levels
dailyHigh = request.security(syminfo.tickerid, "D", high[1], lookahead=barmerge.lookahead_off)
dailyLow = request.security(syminfo.tickerid, "D", low[1], lookahead=barmerge.lookahead_off)

// Calculate round numbers
currentPrice = close
roundBase = math.floor(currentPrice / roundNumberSpacing) * roundNumberSpacing
roundAbove = roundBase + roundNumberSpacing
roundBelow = roundBase
roundThreshold = currentPrice * roundNumberThreshold / 100

//------------------------------------------------------------------------------
// Sweep Detection
//------------------------------------------------------------------------------
// Initialize sweep variables
var bool swingPointBullSweep = false
var bool swingPointBearSweep = false
var bool roundNumberSweep = false
var bool stopClusterBullSweep = false
var bool stopClusterBearSweep = false
var bool dailyLevelBullSweep = false
var bool dailyLevelBearSweep = false

// Detect sweeps based on enabled types
if inAsianSession or inLondonOpen or inNYOpen or inLondonClose
    // Swing Point Sweeps
    if checkSwingPoints
        [spBull, spBear] = detectSwingPointSweep(high, low, close, swingHigh[1], swingLow[1], atr)
        swingPointBullSweep := spBull
        swingPointBearSweep := spBear

    // Round Number Sweeps
    if checkRoundNumbers
        [aboveSweep, _] = detectRoundNumberSweep(high, roundAbove, roundThreshold)
        [belowSweep, _] = detectRoundNumberSweep(low, roundBelow, roundThreshold)
        roundNumberSweep := aboveSweep or belowSweep

    // Stop Cluster Sweeps
    if checkStopClusters
        [scBull, scBear, _, _] = detectStopClusterSweep(high, low, close, atr)
        stopClusterBullSweep := scBull
        stopClusterBearSweep := scBear

    // Daily Level Sweeps
    if checkDailyLevels
        [dlBull, dlBear] = detectDailyLevelSweep(high, low, close, dailyHigh, dailyLow, atr)
        dailyLevelBullSweep := dlBull
        dailyLevelBearSweep := dlBear

// Detect combined sweeps
combinedBullSweep = swingPointBullSweep and stopClusterBullSweep
combinedBearSweep = swingPointBearSweep and stopClusterBearSweep

//------------------------------------------------------------------------------
// Plotting
//------------------------------------------------------------------------------
// Plot swing point sweeps
plotshape(swingPointBullSweep, "Swing Point Bull Sweep", shape.triangleup, location.belowbar, color.green, size=size.small)
plotshape(swingPointBearSweep, "Swing Point Bear Sweep", shape.triangledown, location.abovebar, color.red, size=size.small)

// Plot round number sweeps
plotshape(roundNumberSweep, "Round Number Sweep", shape.circle, location.abovebar, color.yellow, size=size.small)

// Plot stop cluster sweeps
plotshape(stopClusterBullSweep, "Stop Cluster Bull Sweep", shape.diamond, location.belowbar, color.green, size=size.small)
plotshape(stopClusterBearSweep, "Stop Cluster Bear Sweep", shape.diamond, location.abovebar, color.red, size=size.small)

// Plot daily level sweeps
plotshape(dailyLevelBullSweep, "Daily Level Bull Sweep", shape.square, location.belowbar, color.lime, size=size.small)
plotshape(dailyLevelBearSweep, "Daily Level Bear Sweep", shape.square, location.abovebar, color.maroon, size=size.small)

// Plot combined sweeps with labels
if combinedBullSweep
    label.new(bar_index, low, "Combined\nBull Sweep", color=color.green, style=label.style_label_up, textcolor=color.white)

if combinedBearSweep
    label.new(bar_index, high, "Combined\nBear Sweep", color=color.red, style=label.style_label_down, textcolor=color.white)

//------------------------------------------------------------------------------
// Alerts
//------------------------------------------------------------------------------
// Swing Point Sweeps
alertcondition(swingPointBullSweep, "Swing Point Bullish Sweep", "Bullish sweep of swing low detected")
alertcondition(swingPointBearSweep, "Swing Point Bearish Sweep", "Bearish sweep of swing high detected")

// Round Number Sweeps
alertcondition(roundNumberSweep, "Round Number Sweep", "Round number level swept")

// Stop Cluster Sweeps
alertcondition(stopClusterBullSweep, "Stop Cluster Bullish Sweep", "Bullish sweep of stop cluster detected")
alertcondition(stopClusterBearSweep, "Stop Cluster Bearish Sweep", "Bearish sweep of stop cluster detected")

// Daily Level Sweeps
alertcondition(dailyLevelBullSweep, "Daily Level Bullish Sweep", "Bullish sweep of daily low detected")
alertcondition(dailyLevelBearSweep, "Daily Level Bearish Sweep", "Bearish sweep of daily high detected")

// Combined Sweeps
alertcondition(combinedBullSweep, "Combined Bullish Sweep", "Both swing point and stop cluster bullish sweeps detected")
alertcondition(combinedBearSweep, "Combined Bearish Sweep", "Both swing point and stop cluster bearish sweeps detected")