//@version=5
indicator("ICT ADR Levels (LuxAlgo-style)", overlay=true, max_lines_count=500, max_labels_count=500, dynamic_requests=true)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
show_only_intraday = input.bool(true, "Show Only on Intraday Timeframes", group=group_settings)
timezone = input.string("America/New_York", "Time Zone", group=group_settings)
max_days = input.int(3, "Days to Display", minval=1, maxval=10, group=group_settings)
show_table = input.bool(true, "Show Data Table", group=group_settings)

// Line Extension Settings
extend_right = input.int(50, "Extend Right", minval=0, maxval=250, group=group_settings, tooltip="Maximum allowed is 250 bars to avoid Pine Script's 500-bar future limit")
extend_left = input.int(200, "Extend Left", minval=0, maxval=500, group=group_settings, tooltip="How many bars to the left the AR levels should extend")

// Daily Settings
group_adr = "Daily Settings"
show_daily = input.bool(true, "Show Daily Levels", group=group_adr)
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group=group_adr)
daily_anchor_type = input.string("Midnight", "Daily Anchor Type", options=["Midnight", "Session Open", "Custom Time"], group=group_adr, tooltip="Midnight is the traditional 'True Day Open' in ICT methodology")
daily_custom_hour = input.int(0, "Custom Hour (if Custom Time selected)", minval=0, maxval=23, group=group_adr)
daily_custom_minute = input.int(0, "Custom Minute (if Custom Time selected)", minval=0, maxval=59, group=group_adr)

// Daily Level Types
group_daily_levels = "Daily Level Types"
show_full_adr = input.bool(true, "Show Full ADR", group=group_daily_levels)
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group=group_daily_levels)
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group=group_daily_levels)
show_half_adr = input.bool(false, "Show 1/2 ADR", group=group_daily_levels)

// Weekly Settings
group_weekly = "Weekly Settings"
show_weekly = input.bool(true, "Show Weekly Levels", group=group_weekly)
weekly_days = input.int(5, "Weeks to Average", minval=1, maxval=10, group=group_weekly)
weekly_anchor_type = input.string("Monday Open", "Weekly Anchor Type", options=["Monday Open", "Tuesday Open", "Custom Day"], group=group_weekly, tooltip="Tuesday Open is the traditional 'True Week Open' in ICT methodology")
weekly_custom_day = input.string("Monday", "Custom Day (if Custom Day selected)", options=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], group=group_weekly)
weekly_custom_hour = input.int(0, "Custom Hour (if Custom Day selected)", minval=0, maxval=23, group=group_weekly)

// Weekly Level Types
group_weekly_levels = "Weekly Level Types"
show_full_awr = input.bool(true, "Show Full AWR", group=group_weekly_levels)
show_one_third_awr = input.bool(true, "Show 1/3 AWR", group=group_weekly_levels)
show_two_third_awr = input.bool(false, "Show 2/3 AWR", group=group_weekly_levels)
show_half_awr = input.bool(false, "Show 1/2 AWR", group=group_weekly_levels)

// Monthly Settings
group_monthly = "Monthly Settings"
show_monthly = input.bool(true, "Show Monthly Levels", group=group_monthly)
monthly_months = input.int(3, "Months to Average", minval=1, maxval=12, group=group_monthly)
monthly_anchor_type = input.string("First Day", "Monthly Anchor Type", options=["First Day", "Second Week", "Custom Day"], group=group_monthly)
monthly_custom_day = input.int(1, "Custom Day of Month (if Custom Day selected)", minval=1, maxval=31, group=group_monthly)
monthly_custom_hour = input.int(0, "Custom Hour (if Custom Day selected)", minval=0, maxval=23, group=group_monthly)

// Monthly Level Types
group_monthly_levels = "Monthly Level Types"
show_full_amr = input.bool(true, "Show Full AMR", group=group_monthly_levels)
show_one_third_amr = input.bool(true, "Show 1/3 AMR", group=group_monthly_levels)
show_two_third_amr = input.bool(false, "Show 2/3 AMR", group=group_monthly_levels)
show_half_amr = input.bool(false, "Show 1/2 AMR", group=group_monthly_levels)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Day Open", group=group_line)

// Color Settings
group_color = "Color Settings"
true_day_open_color = input.color(color.orange, "True Day Open Line", group=group_color)
full_adr_color = input.color(color.white, "Full ADR", group=group_color)
one_third_adr_color = input.color(color.aqua, "1/3 ADR", group=group_color)
two_third_adr_color = input.color(color.yellow, "2/3 ADR", group=group_color)
half_adr_color = input.color(color.purple, "1/2 ADR", group=group_color)

// Weekly Color Settings
group_weekly_color = "Weekly Color Settings"
weekly_true_open_color = input.color(color.blue, "Weekly Open Line", group=group_weekly_color)
weekly_full_color = input.color(color.white, "Full AWR", group=group_weekly_color)
weekly_one_third_color = input.color(color.aqua, "1/3 AWR", group=group_weekly_color)
weekly_two_third_color = input.color(color.yellow, "2/3 AWR", group=group_weekly_color)
weekly_half_color = input.color(color.purple, "1/2 AWR", group=group_weekly_color)

// Monthly Color Settings
group_monthly_color = "Monthly Color Settings"
monthly_true_open_color = input.color(color.red, "Monthly Open Line", group=group_monthly_color)
monthly_full_color = input.color(color.white, "Full AMR", group=group_monthly_color)
monthly_one_third_color = input.color(color.aqua, "1/3 AMR", group=group_monthly_color)
monthly_two_third_color = input.color(color.yellow, "2/3 AMR", group=group_monthly_color)
monthly_half_color = input.color(color.purple, "1/2 AMR", group=group_monthly_color)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)
labels_on_right_side_only = input.bool(true, "Labels on Right Side Only", tooltip="When enabled, labels will only appear on the right side of the chart", group=group_label)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === TIME LOGIC ===
// Function to get daily anchor time based on settings
get_daily_anchor_hour() =>
    if daily_anchor_type == "Midnight"
        0
    else if daily_anchor_type == "Session Open"
        session.ismarket ? 9 : 0  // Use 9 AM for market session, midnight otherwise
    else // Custom Time
        daily_custom_hour

get_daily_anchor_minute() =>
    if daily_anchor_type == "Midnight"
        0
    else if daily_anchor_type == "Session Open"
        session.ismarket ? 30 : 0  // Use 9:30 AM for market session, midnight otherwise
    else // Custom Time
        daily_custom_minute

// Calculate daily anchor timestamp
daily_anchor_hour = get_daily_anchor_hour()
daily_anchor_minute = get_daily_anchor_minute()
start_of_day = timestamp(timezone, year, month, dayofmonth, daily_anchor_hour, daily_anchor_minute)
end_of_day = timestamp(timezone, year, month, dayofmonth, 23, 59)
is_new_day = time == start_of_day
in_current_day = time >= start_of_day and time <= end_of_day

// Function to detect weekly anchor time
is_weekly_anchor() =>
    int dow = dayofweek
    int target_dow = 1  // Default to Monday

    if weekly_anchor_type == "Monday Open"
        target_dow := 1
    else if weekly_anchor_type == "Tuesday Open"
        target_dow := 2
    else if weekly_anchor_type == "Custom Day"
        if weekly_custom_day == "Monday"
            target_dow := 1
        else if weekly_custom_day == "Tuesday"
            target_dow := 2
        else if weekly_custom_day == "Wednesday"
            target_dow := 3
        else if weekly_custom_day == "Thursday"
            target_dow := 4
        else if weekly_custom_day == "Friday"
            target_dow := 5
        else if weekly_custom_day == "Saturday"
            target_dow := 6
        else if weekly_custom_day == "Sunday"
            target_dow := 7

    // Check if current time matches the anchor
    // For 1-minute charts, we need to be more flexible with the detection
    // Using timeframe.change to detect the start of a new week
    bool is_anchor = false
    if timeframe.isintraday
        // For intraday charts, check if this is the first bar of the target day
        is_anchor := dow == target_dow and hour(time) == weekly_custom_hour and minute(time) == 0
        // Additional check for 1-minute charts
        if timeframe.in_seconds() <= 60
            // Also check if we've just changed to a new week
            is_anchor := is_anchor or (dow == target_dow and not na(time) and na(time[1]))
    else
        // For daily and above charts
        is_anchor := dow == target_dow

    is_anchor

// Function to detect monthly anchor time
is_monthly_anchor() =>
    bool is_anchor = false

    if timeframe.isintraday
        if monthly_anchor_type == "First Day"
            is_anchor := dayofmonth(time) == 1 and hour(time) == 0 and minute(time) == 0
        else if monthly_anchor_type == "Second Week"
            // Second Monday of the month
            is_anchor := dayofweek == 1 and dayofmonth(time) > 7 and dayofmonth(time) <= 14 and hour(time) == 0 and minute(time) == 0
        else if monthly_anchor_type == "Custom Day"
            is_anchor := dayofmonth(time) == monthly_custom_day and hour(time) == monthly_custom_hour and minute(time) == 0

        // Additional check for 1-minute charts
        if timeframe.in_seconds() <= 60
            // Also check if we've just changed to a new month
            if monthly_anchor_type == "First Day"
                is_anchor := is_anchor or (dayofmonth(time) == 1 and not na(time) and na(time[1]))
            else if monthly_anchor_type == "Second Week"
                is_anchor := is_anchor or (dayofweek == 1 and dayofmonth(time) > 7 and dayofmonth(time) <= 14 and not na(time) and na(time[1]))
            else if monthly_anchor_type == "Custom Day"
                is_anchor := is_anchor or (dayofmonth(time) == monthly_custom_day and not na(time) and na(time[1]))
    else
        // For daily and above charts
        if monthly_anchor_type == "First Day"
            is_anchor := dayofmonth(time) == 1
        else if monthly_anchor_type == "Second Week"
            is_anchor := dayofweek == 1 and dayofmonth(time) > 7 and dayofmonth(time) <= 14
        else if monthly_anchor_type == "Custom Day"
            is_anchor := dayofmonth(time) == monthly_custom_day

    is_anchor

// Function to calculate the Average Range for a given timeframe
calculateAverageRange(tf, lookback_period) =>
    // Use a simplified approach with fewer request calls
    // Request the high and low values for the current bar
    float range_sum = 0.0
    int count = 0

    // Use a single request for the current bar's range
    [h0, l0] = request.security(syminfo.tickerid, tf, [high, low], lookahead=barmerge.lookahead_on)

    // Ensure we have valid values even on lower timeframes
    if na(h0) or na(l0)
        h0 := high
        l0 := low

    range_sum += (h0 - l0)
    count += 1

    // Use a single request for the previous bar's range
    [h1, l1] = request.security(syminfo.tickerid, tf, [high[1], low[1]], lookahead=barmerge.lookahead_on)

    // Ensure we have valid values even on lower timeframes
    if lookback_period > 1
        if na(h1) or na(l1)
            h1 := high[1]
            l1 := low[1]

        range_sum += (h1 - l1)
        count += 1

    // Use a single request for bar 2's range
    [h2, l2] = request.security(syminfo.tickerid, tf, [high[2], low[2]], lookahead=barmerge.lookahead_on)

    // Ensure we have valid values even on lower timeframes
    if lookback_period > 2
        if na(h2) or na(l2)
            h2 := high[2]
            l2 := low[2]

        range_sum += (h2 - l2)
        count += 1

    // Use a single request for bar 3's range
    [h3, l3] = request.security(syminfo.tickerid, tf, [high[3], low[3]], lookahead=barmerge.lookahead_on)

    // Ensure we have valid values even on lower timeframes
    if lookback_period > 3
        if na(h3) or na(l3)
            h3 := high[3]
            l3 := low[3]

        range_sum += (h3 - l3)
        count += 1

    // Use a single request for bar 4's range
    [h4, l4] = request.security(syminfo.tickerid, tf, [high[4], low[4]], lookahead=barmerge.lookahead_on)

    // Ensure we have valid values even on lower timeframes
    if lookback_period > 4
        if na(h4) or na(l4)
            h4 := high[4]
            l4 := low[4]

        range_sum += (h4 - l4)
        count += 1

    // Return the average (use max to ensure we don't divide by zero)
    range_sum / math.max(count, 1)

// Function to get the daily range for a specific day
get_daily_range(day_offset) =>
    float result = 0.0

    // Use a single request with array syntax to get high and low
    if day_offset == 0
        [h, l] = request.security(syminfo.tickerid, "D", [high, low], lookahead=barmerge.lookahead_on)
        result := h - l
    else if day_offset == 1
        [h, l] = request.security(syminfo.tickerid, "D", [high[1], low[1]], lookahead=barmerge.lookahead_on)
        result := h - l
    else if day_offset == 2
        [h, l] = request.security(syminfo.tickerid, "D", [high[2], low[2]], lookahead=barmerge.lookahead_on)
        result := h - l
    else if day_offset == 3
        [h, l] = request.security(syminfo.tickerid, "D", [high[3], low[3]], lookahead=barmerge.lookahead_on)
        result := h - l
    else if day_offset == 4
        [h, l] = request.security(syminfo.tickerid, "D", [high[4], low[4]], lookahead=barmerge.lookahead_on)
        result := h - l

    result

// Function to get the weekly range for a specific week
get_weekly_range(week_offset) =>
    float result = 0.0

    // Use a single request with array syntax to get high and low
    if week_offset == 0
        [h, l] = request.security(syminfo.tickerid, "W", [high, low], lookahead=barmerge.lookahead_on)
        result := h - l
    else if week_offset == 1
        [h, l] = request.security(syminfo.tickerid, "W", [high[1], low[1]], lookahead=barmerge.lookahead_on)
        result := h - l
    else if week_offset == 2
        [h, l] = request.security(syminfo.tickerid, "W", [high[2], low[2]], lookahead=barmerge.lookahead_on)
        result := h - l
    else if week_offset == 3
        [h, l] = request.security(syminfo.tickerid, "W", [high[3], low[3]], lookahead=barmerge.lookahead_on)
        result := h - l

    result

// Function to get the monthly range for a specific month
get_monthly_range(month_offset) =>
    float result = 0.0

    // Use a single request with array syntax to get high and low
    if month_offset == 0
        [h, l] = request.security(syminfo.tickerid, "M", [high, low], lookahead=barmerge.lookahead_on)
        result := h - l
    else if month_offset == 1
        [h, l] = request.security(syminfo.tickerid, "M", [high[1], low[1]], lookahead=barmerge.lookahead_on)
        result := h - l
    else if month_offset == 2
        [h, l] = request.security(syminfo.tickerid, "M", [high[2], low[2]], lookahead=barmerge.lookahead_on)
        result := h - l

    result

// === STATE VARIABLES ===
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// True Day Open variables
var line true_day_open_line = na
var label true_day_open_label = na
var line true_day_vertical = na
var float true_day_open_price = na
var bool true_day_line_active = false

// ADR level lines
var line full_adr_up_line = na
var line full_adr_down_line = na
var line one_third_adr_up_line = na
var line one_third_adr_down_line = na
var line two_third_adr_up_line = na
var line two_third_adr_down_line = na
var line half_adr_up_line = na
var line half_adr_down_line = na

// ADR level labels
var label full_adr_up_label = na
var label full_adr_down_label = na
var label one_third_adr_up_label = na
var label one_third_adr_down_label = na
var label two_third_adr_up_label = na
var label two_third_adr_down_label = na
var label half_adr_up_label = na
var label half_adr_down_label = na

// ADR level values
var float daily_range = na
var float full_adr_up = na
var float full_adr_down = na
var float one_third_adr_up = na
var float one_third_adr_down = na
var float two_third_adr_up = na
var float two_third_adr_down = na
var float half_adr_up = na
var float half_adr_down = na

// Arrays to store historical ADR values
var float[] daily_range_history = array.new_float()
var float[] daily_actual_range_history = array.new_float()
var float[] weekly_range_history = array.new_float()
var float[] weekly_actual_range_history = array.new_float()
var float[] monthly_range_history = array.new_float()
var float[] monthly_actual_range_history = array.new_float()

// Arrays to store timestamps of when AR levels were calculated
var int[] daily_timestamps = array.new_int()
var int[] weekly_timestamps = array.new_int()
var int[] monthly_timestamps = array.new_int()

// Arrays to store lines for each level type
// Daily lines
var line[] daily_full_up_lines = array.new_line()
var line[] daily_full_down_lines = array.new_line()
var line[] daily_one_third_up_lines = array.new_line()
var line[] daily_one_third_down_lines = array.new_line()
var line[] daily_two_third_up_lines = array.new_line()
var line[] daily_two_third_down_lines = array.new_line()
var line[] daily_half_up_lines = array.new_line()
var line[] daily_half_down_lines = array.new_line()

// Weekly lines
var line[] weekly_full_up_lines = array.new_line()
var line[] weekly_full_down_lines = array.new_line()
var line[] weekly_one_third_up_lines = array.new_line()
var line[] weekly_one_third_down_lines = array.new_line()
var line[] weekly_two_third_up_lines = array.new_line()
var line[] weekly_two_third_down_lines = array.new_line()
var line[] weekly_half_up_lines = array.new_line()
var line[] weekly_half_down_lines = array.new_line()

// Monthly lines
var line[] monthly_full_up_lines = array.new_line()
var line[] monthly_full_down_lines = array.new_line()
var line[] monthly_one_third_up_lines = array.new_line()
var line[] monthly_one_third_down_lines = array.new_line()
var line[] monthly_two_third_up_lines = array.new_line()
var line[] monthly_two_third_down_lines = array.new_line()
var line[] monthly_half_up_lines = array.new_line()
var line[] monthly_half_down_lines = array.new_line()

// Weekly variables
var line weekly_true_open_line = na
var label weekly_true_open_label = na
var line weekly_vertical = na
var float weekly_true_open_price = na
var bool weekly_line_active = false
var float weekly_range = na
var float weekly_anchor = na

// Weekly level lines
var line weekly_full_up_line = na
var line weekly_full_down_line = na
var line weekly_one_third_up_line = na
var line weekly_one_third_down_line = na
var line weekly_two_third_up_line = na
var line weekly_two_third_down_line = na
var line weekly_half_up_line = na
var line weekly_half_down_line = na

// Weekly level labels
var label weekly_full_up_label = na
var label weekly_full_down_label = na
var label weekly_one_third_up_label = na
var label weekly_one_third_down_label = na
var label weekly_two_third_up_label = na
var label weekly_two_third_down_label = na
var label weekly_half_up_label = na
var label weekly_half_down_label = na

// Weekly level values
var float weekly_full_up = na
var float weekly_full_down = na
var float weekly_one_third_up = na
var float weekly_one_third_down = na
var float weekly_two_third_up = na
var float weekly_two_third_down = na
var float weekly_half_up = na
var float weekly_half_down = na

// Monthly variables
var line monthly_true_open_line = na
var label monthly_true_open_label = na
var line monthly_vertical = na
var float monthly_true_open_price = na
var bool monthly_line_active = false
var float monthly_range = na
var float monthly_anchor = na

// Monthly level lines
var line monthly_full_up_line = na
var line monthly_full_down_line = na
var line monthly_one_third_up_line = na
var line monthly_one_third_down_line = na
var line monthly_two_third_up_line = na
var line monthly_two_third_down_line = na
var line monthly_half_up_line = na
var line monthly_half_down_line = na

// Monthly level labels
var label monthly_full_up_label = na
var label monthly_full_down_label = na
var label monthly_one_third_up_label = na
var label monthly_one_third_down_label = na
var label monthly_two_third_up_label = na
var label monthly_two_third_down_label = na
var label monthly_half_up_label = na
var label monthly_half_down_label = na

// Monthly level values
var float monthly_full_up = na
var float monthly_full_down = na
var float monthly_one_third_up = na
var float monthly_one_third_down = na
var float monthly_two_third_up = na
var float monthly_two_third_down = na
var float monthly_half_up = na
var float monthly_half_down = na



// === MAIN LOGIC ===
// Calculate ADR value
float current_adr = calculateAverageRange("D", adr_days)
float current_awr = calculateAverageRange("W", weekly_days)
float current_amr = calculateAverageRange("M", monthly_months)

// Initialize weekly and monthly levels on the first bar
var bool initialized_weekly = false
var bool initialized_monthly = false

// Store weekly and monthly anchor prices and ranges
var float stored_weekly_anchor = na
var float stored_weekly_range = na
var float stored_monthly_anchor = na
var float stored_monthly_range = na

// We'll initialize values in barstate.isfirst after function definitions

// Get daily anchor price and calculate ADR
getDailyAnchorAndADR() =>
    // Get the daily open price at the anchor time
    anchor_time = timestamp(timezone, year, month, dayofmonth, daily_anchor_hour, daily_anchor_minute)
    is_anchor = time("D") == anchor_time

    // Get the open price at the anchor time
    anchor_open = request.security(syminfo.tickerid, "D", open, lookahead=barmerge.lookahead_on)

    // Calculate ADR
    adr_value = calculateAverageRange("D", adr_days)

    [anchor_open, adr_value, is_anchor]

// Get weekly anchor price and calculate AWR
getWeeklyAnchorAndAWR() =>
    // Determine the day of week for the anchor
    int anchor_dow = 1  // Default to Monday
    if weekly_anchor_type == "Monday Open"
        anchor_dow := 1
    else if weekly_anchor_type == "Tuesday Open"
        anchor_dow := 2
    else if weekly_anchor_type == "Custom Day"
        if weekly_custom_day == "Monday"
            anchor_dow := 1
        else if weekly_custom_day == "Tuesday"
            anchor_dow := 2
        else if weekly_custom_day == "Wednesday"
            anchor_dow := 3
        else if weekly_custom_day == "Thursday"
            anchor_dow := 4
        else if weekly_custom_day == "Friday"
            anchor_dow := 5
        else if weekly_custom_day == "Saturday"
            anchor_dow := 6
        else if weekly_custom_day == "Sunday"
            anchor_dow := 7

    // Check if current bar is at the weekly anchor using our improved detection
    is_anchor = is_weekly_anchor()

    // Get the weekly open price directly from the current symbol
    // For 1-minute charts, we need to be more careful with how we get the weekly open
    float anchor_open = na

    if timeframe.in_seconds() <= 60
        // For 1-minute charts, use a more reliable method
        // First try to get the open of the current week
        [w_open, w_high, w_low] = request.security(syminfo.tickerid, "W", [open, high, low], lookahead=barmerge.lookahead_on)
        anchor_open := w_open

        // If that fails, try to calculate it from the current price
        if na(anchor_open) and is_anchor
            anchor_open := open
        else if na(anchor_open)
            anchor_open := close
    else
        // For higher timeframes, the standard approach works well
        anchor_open := request.security(syminfo.tickerid, "W", open, lookahead=barmerge.lookahead_on)

        // Fallback if we still don't have a valid value
        if na(anchor_open)
            anchor_open := close

    // Calculate AWR - use a more robust method for 1-minute charts
    float awr_value = na

    if timeframe.in_seconds() <= 60
        // For 1-minute charts, calculate AWR using a more direct approach
        // We need to avoid using loop variables in request.security calls
        float range_sum = 0.0
        int count = 0

        // Get weekly ranges for the past few weeks - using individual calls instead of a loop
        if weekly_days >= 1
            [h1, l1] = request.security(syminfo.tickerid, "W", [high[1], low[1]], lookahead=barmerge.lookahead_on)
            if not na(h1) and not na(l1)
                range_sum += (h1 - l1)
                count += 1

        if weekly_days >= 2
            [h2, l2] = request.security(syminfo.tickerid, "W", [high[2], low[2]], lookahead=barmerge.lookahead_on)
            if not na(h2) and not na(l2)
                range_sum += (h2 - l2)
                count += 1

        if weekly_days >= 3
            [h3, l3] = request.security(syminfo.tickerid, "W", [high[3], low[3]], lookahead=barmerge.lookahead_on)
            if not na(h3) and not na(l3)
                range_sum += (h3 - l3)
                count += 1

        if weekly_days >= 4
            [h4, l4] = request.security(syminfo.tickerid, "W", [high[4], low[4]], lookahead=barmerge.lookahead_on)
            if not na(h4) and not na(l4)
                range_sum += (h4 - l4)
                count += 1

        if weekly_days >= 5
            [h5, l5] = request.security(syminfo.tickerid, "W", [high[5], low[5]], lookahead=barmerge.lookahead_on)
            if not na(h5) and not na(l5)
                range_sum += (h5 - l5)
                count += 1

        // Calculate average
        if count > 0
            awr_value := range_sum / count
    else
        // For higher timeframes, use the standard calculation
        awr_value := calculateAverageRange("W", weekly_days)

    // Ensure we have a valid AWR value
    if na(awr_value) or awr_value <= 0
        awr_value := high - low

    [anchor_open, awr_value, is_anchor]

// Get monthly anchor price and calculate AMR
getMonthlyAnchorAndAMR() =>
    // Determine the anchor day based on settings
    int anchor_day = 1  // Default to first day of month
    if monthly_anchor_type == "First Day"
        anchor_day := 1
    else if monthly_anchor_type == "Second Week"
        // Find the second Monday of the month
        // This is simplified - in a real implementation we'd need to calculate this dynamically
        anchor_day := 8  // Approximate - second Monday is usually around the 8th
    else if monthly_anchor_type == "Custom Day"
        anchor_day := monthly_custom_day

    // Check if current bar is at the monthly anchor using our improved detection
    is_anchor = is_monthly_anchor()

    // Get the monthly open price directly from the current symbol
    // For 1-minute charts, we need to be more careful with how we get the monthly open
    float anchor_open = na

    if timeframe.in_seconds() <= 60
        // For 1-minute charts, use a more reliable method
        // First try to get the open of the current month
        [m_open, m_high, m_low] = request.security(syminfo.tickerid, "M", [open, high, low], lookahead=barmerge.lookahead_on)
        anchor_open := m_open

        // If that fails, try to calculate it from the current price
        if na(anchor_open) and is_anchor
            anchor_open := open
        else if na(anchor_open)
            anchor_open := close
    else
        // For higher timeframes, the standard approach works well
        anchor_open := request.security(syminfo.tickerid, "M", open, lookahead=barmerge.lookahead_on)

        // Fallback if we still don't have a valid value
        if na(anchor_open)
            anchor_open := close

    // Calculate AMR - use a more robust method for 1-minute charts
    float amr_value = na

    if timeframe.in_seconds() <= 60
        // For 1-minute charts, calculate AMR using a more direct approach
        // We need to avoid using loop variables in request.security calls
        float range_sum = 0.0
        int count = 0

        // Get monthly ranges for the past few months - using individual calls instead of a loop
        if monthly_months >= 1
            [h1, l1] = request.security(syminfo.tickerid, "M", [high[1], low[1]], lookahead=barmerge.lookahead_on)
            if not na(h1) and not na(l1)
                range_sum += (h1 - l1)
                count += 1

        if monthly_months >= 2
            [h2, l2] = request.security(syminfo.tickerid, "M", [high[2], low[2]], lookahead=barmerge.lookahead_on)
            if not na(h2) and not na(l2)
                range_sum += (h2 - l2)
                count += 1

        if monthly_months >= 3
            [h3, l3] = request.security(syminfo.tickerid, "M", [high[3], low[3]], lookahead=barmerge.lookahead_on)
            if not na(h3) and not na(l3)
                range_sum += (h3 - l3)
                count += 1

        if monthly_months >= 4
            [h4, l4] = request.security(syminfo.tickerid, "M", [high[4], low[4]], lookahead=barmerge.lookahead_on)
            if not na(h4) and not na(l4)
                range_sum += (h4 - l4)
                count += 1

        if monthly_months >= 5
            [h5, l5] = request.security(syminfo.tickerid, "M", [high[5], low[5]], lookahead=barmerge.lookahead_on)
            if not na(h5) and not na(l5)
                range_sum += (h5 - l5)
                count += 1

        if monthly_months >= 6
            [h6, l6] = request.security(syminfo.tickerid, "M", [high[6], low[6]], lookahead=barmerge.lookahead_on)
            if not na(h6) and not na(l6)
                range_sum += (h6 - l6)
                count += 1

        // Calculate average
        if count > 0
            amr_value := range_sum / count
    else
        // For higher timeframes, use the standard calculation
        amr_value := calculateAverageRange("M", monthly_months)

    // Ensure we have a valid AMR value
    if na(amr_value) or amr_value <= 0
        amr_value := high - low

    [anchor_open, amr_value, is_anchor]

// Calculate weekly levels
getWeeklyLevels() =>
    // Get weekly anchor and AWR
    [weekly_anchor, weekly_awr, _] = getWeeklyAnchorAndAWR()

    // Calculate weekly levels
    float w_full_up = weekly_anchor + weekly_awr
    float w_full_down = weekly_anchor - weekly_awr
    float w_one_third_up = weekly_anchor + (weekly_awr / 3)
    float w_one_third_down = weekly_anchor - (weekly_awr / 3)
    float w_two_third_up = weekly_anchor + (weekly_awr * 2 / 3)
    float w_two_third_down = weekly_anchor - (weekly_awr * 2 / 3)
    float w_half_up = weekly_anchor + (weekly_awr / 2)
    float w_half_down = weekly_anchor - (weekly_awr / 2)

    [weekly_anchor, w_full_up, w_full_down, w_one_third_up, w_one_third_down, w_two_third_up, w_two_third_down, w_half_up, w_half_down]

// Calculate monthly levels
getMonthlyLevels() =>
    // Get monthly anchor and AMR
    [monthly_anchor, monthly_amr, _] = getMonthlyAnchorAndAMR()

    // Calculate monthly levels
    float m_full_up = monthly_anchor + monthly_amr
    float m_full_down = monthly_anchor - monthly_amr
    float m_one_third_up = monthly_anchor + (monthly_amr / 3)
    float m_one_third_down = monthly_anchor - (monthly_amr / 3)
    float m_two_third_up = monthly_anchor + (monthly_amr * 2 / 3)
    float m_two_third_down = monthly_anchor - (monthly_amr * 2 / 3)
    float m_half_up = monthly_anchor + (monthly_amr / 2)
    float m_half_down = monthly_anchor - (monthly_amr / 2)

    [monthly_anchor, m_full_up, m_full_down, m_one_third_up, m_one_third_down, m_two_third_up, m_two_third_down, m_half_up, m_half_down]

// Calculate daily levels
getDailyLevels() =>
    // Get daily anchor and ADR
    [daily_anchor, daily_adr, _] = getDailyAnchorAndADR()

    // Calculate daily levels
    float d_full_up = daily_anchor + daily_adr
    float d_full_down = daily_anchor - daily_adr
    float d_one_third_up = daily_anchor + (daily_adr / 3)
    float d_one_third_down = daily_anchor - (daily_adr / 3)
    float d_two_third_up = daily_anchor + (daily_adr * 2 / 3)
    float d_two_third_down = daily_anchor - (daily_adr * 2 / 3)
    float d_half_up = daily_anchor + (daily_adr / 2)
    float d_half_down = daily_anchor - (daily_adr / 2)

    [daily_anchor, d_full_up, d_full_down, d_one_third_up, d_one_third_down, d_two_third_up, d_two_third_down, d_half_up, d_half_down]

// Check if we're at the start of a new day
if is_new_day
    // Store the current ADR value in history and for today's use
    array.unshift(daily_range_history, current_adr)
    if array.size(daily_range_history) > adr_days
        array.pop(daily_range_history)

    // Store the actual daily range for the previous day
    float yesterday_range = get_daily_range(1)
    array.unshift(daily_actual_range_history, yesterday_range)
    if array.size(daily_actual_range_history) > adr_days
        array.pop(daily_actual_range_history)

    // Update the daily range value for today
    daily_range := current_adr

    // Update true day open price
    true_day_open_price := open
    true_day_line_active := true

    // Calculate ADR levels at the daily anchor point
    full_adr_up := true_day_open_price + daily_range
    full_adr_down := true_day_open_price - daily_range
    one_third_adr_up := true_day_open_price + (daily_range / 3)
    one_third_adr_down := true_day_open_price - (daily_range / 3)
    two_third_adr_up := true_day_open_price + (daily_range * 2 / 3)
    two_third_adr_down := true_day_open_price - (daily_range * 2 / 3)
    half_adr_up := true_day_open_price + (daily_range / 2)
    half_adr_down := true_day_open_price - (daily_range / 2)

    if show_daily
        // Create true day open line
        true_day_open_line := line.new(bar_index - extend_left, true_day_open_price, bar_index + extend_right, true_day_open_price, color=true_day_open_color, width=line_width, style=get_line_style(line_style))

        // When labels_on_right_side_only is true, labels will be created in the barstate.islast block
        // We don't create labels here anymore to prevent cluttering

        // Create vertical line at day open
        if show_vertical
            true_day_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_day_open_color, style=line.style_dashed)

        // Create ADR level lines
        if show_full_adr
            // Full ADR up line
            full_adr_up_line := line.new(bar_index - extend_left, full_adr_up, bar_index + extend_right, full_adr_up, color=full_adr_color, width=line_width, style=line_style_value)
            array.unshift(daily_full_up_lines, full_adr_up_line)

            // Full ADR up label - only created in barstate.islast block now

            // Full ADR down line
            full_adr_down_line := line.new(bar_index - extend_left, full_adr_down, bar_index + extend_right, full_adr_down, color=full_adr_color, width=line_width, style=line_style_value)
            array.unshift(daily_full_down_lines, full_adr_down_line)

            // Full ADR down label - only created in barstate.islast block now

    // Create 1/3 ADR level lines
    if show_one_third_adr
        // 1/3 ADR up line
        one_third_adr_up_line := line.new(bar_index - extend_left, one_third_adr_up, bar_index + extend_right, one_third_adr_up, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(daily_one_third_up_lines, one_third_adr_up_line)

        // 1/3 ADR up label - only created in barstate.islast block now

        // 1/3 ADR down line
        one_third_adr_down_line := line.new(bar_index - extend_left, one_third_adr_down, bar_index + extend_right, one_third_adr_down, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(daily_one_third_down_lines, one_third_adr_down_line)

        // 1/3 ADR down label - only created in barstate.islast block now

    // Create 2/3 ADR level lines
    if show_two_third_adr
        // 2/3 ADR up line
        two_third_adr_up_line := line.new(bar_index - extend_left, two_third_adr_up, bar_index + extend_right, two_third_adr_up, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(daily_two_third_up_lines, two_third_adr_up_line)

        // 2/3 ADR up label - only created in barstate.islast block now

        // 2/3 ADR down line
        two_third_adr_down_line := line.new(bar_index - extend_left, two_third_adr_down, bar_index + extend_right, two_third_adr_down, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(daily_two_third_down_lines, two_third_adr_down_line)

        // 2/3 ADR down label - only created in barstate.islast block now

    // Create 1/2 ADR level lines
    if show_half_adr
        // 1/2 ADR up line
        half_adr_up_line := line.new(bar_index - extend_left, half_adr_up, bar_index + extend_right, half_adr_up, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(daily_half_up_lines, half_adr_up_line)

        // 1/2 ADR up label - only created in barstate.islast block now

        // 1/2 ADR down line
        half_adr_down_line := line.new(bar_index - extend_left, half_adr_down, bar_index + extend_right, half_adr_down, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(daily_half_down_lines, half_adr_down_line)

        // 1/2 ADR down label - only created in barstate.islast block now



// Check if we're at the weekly anchor time
if is_weekly_anchor()
    // Store the current AWR value in history
    array.unshift(weekly_range_history, current_awr)
    if array.size(weekly_range_history) > weekly_days
        array.pop(weekly_range_history)

    // Store the actual weekly range for the previous week
    float last_week_range = get_weekly_range(1)
    array.unshift(weekly_actual_range_history, last_week_range)
    if array.size(weekly_actual_range_history) > weekly_days
        array.pop(weekly_actual_range_history)

    // Update the weekly range value
    weekly_range := current_awr

    // Update true week open price
    weekly_true_open_price := open
    weekly_line_active := true

    // Calculate AWR levels at the weekly anchor point
    weekly_full_up := weekly_true_open_price + weekly_range
    weekly_full_down := weekly_true_open_price - weekly_range
    weekly_one_third_up := weekly_true_open_price + (weekly_range / 3)
    weekly_one_third_down := weekly_true_open_price - (weekly_range / 3)
    weekly_two_third_up := weekly_true_open_price + (weekly_range * 2 / 3)
    weekly_two_third_down := weekly_true_open_price - (weekly_range * 2 / 3)
    weekly_half_up := weekly_true_open_price + (weekly_range / 2)
    weekly_half_down := weekly_true_open_price - (weekly_range / 2)

    // Store timestamps for historical tracking
    array.unshift(weekly_timestamps, int(time))

    // For weekly levels, we want to store them for the entire week
    // but only show lines for the last max_days
    // We'll keep more timestamps than max_days to track the entire week
    int max_weekly_timestamps = 20 // Store up to 20 weeks of data
    while array.size(weekly_timestamps) > max_weekly_timestamps
        array.pop(weekly_timestamps)

    // But we'll only keep max_days worth of lines to display
    if array.size(weekly_full_up_lines) > max_days
        line.delete(array.get(weekly_full_up_lines, array.size(weekly_full_up_lines) - 1))
        array.pop(weekly_full_up_lines)

    if array.size(weekly_full_down_lines) > max_days
        line.delete(array.get(weekly_full_down_lines, array.size(weekly_full_down_lines) - 1))
        array.pop(weekly_full_down_lines)

    if array.size(weekly_one_third_up_lines) > max_days
        line.delete(array.get(weekly_one_third_up_lines, array.size(weekly_one_third_up_lines) - 1))
        array.pop(weekly_one_third_up_lines)

    if array.size(weekly_one_third_down_lines) > max_days
        line.delete(array.get(weekly_one_third_down_lines, array.size(weekly_one_third_down_lines) - 1))
        array.pop(weekly_one_third_down_lines)

// Removed duplicate getWeeklyLevels() function

// Removed duplicate getMonthlyAnchorAndAMR() function

// Check if we're at the monthly anchor time
if is_monthly_anchor()
    // Store the current AMR value in history
    array.unshift(monthly_range_history, current_amr)
    if array.size(monthly_range_history) > monthly_months
        array.pop(monthly_range_history)

    // Store the actual monthly range for the previous month
    float last_month_range = get_monthly_range(1)
    array.unshift(monthly_actual_range_history, last_month_range)
    if array.size(monthly_actual_range_history) > monthly_months
        array.pop(monthly_actual_range_history)

    // Update the monthly range value
    monthly_range := current_amr

    // Update true month open price
    monthly_true_open_price := open
    monthly_line_active := true

    // Calculate AMR levels at the monthly anchor point
    monthly_full_up := monthly_true_open_price + monthly_range
    monthly_full_down := monthly_true_open_price - monthly_range
    monthly_one_third_up := monthly_true_open_price + (monthly_range / 3)
    monthly_one_third_down := monthly_true_open_price - (monthly_range / 3)
    monthly_two_third_up := monthly_true_open_price + (monthly_range * 2 / 3)
    monthly_two_third_down := monthly_true_open_price - (monthly_range * 2 / 3)
    monthly_half_up := monthly_true_open_price + (monthly_range / 2)
    monthly_half_down := monthly_true_open_price - (monthly_range / 2)

    // Store timestamps for historical tracking
    array.unshift(monthly_timestamps, int(time))
    // Keep only the timestamps for the months we want to display
    int max_monthly_timestamps = 12 // Store up to 12 months of data
    while array.size(monthly_timestamps) > max_monthly_timestamps
        array.pop(monthly_timestamps)

    // But we'll only keep max_days worth of lines to display
    if array.size(monthly_full_up_lines) > max_days
        line.delete(array.get(monthly_full_up_lines, array.size(monthly_full_up_lines) - 1))
        array.pop(monthly_full_up_lines)

    if array.size(monthly_full_down_lines) > max_days
        line.delete(array.get(monthly_full_down_lines, array.size(monthly_full_down_lines) - 1))
        array.pop(monthly_full_down_lines)

    if array.size(monthly_one_third_up_lines) > max_days
        line.delete(array.get(monthly_one_third_up_lines, array.size(monthly_one_third_up_lines) - 1))
        array.pop(monthly_one_third_up_lines)

    if array.size(monthly_one_third_down_lines) > max_days
        line.delete(array.get(monthly_one_third_down_lines, array.size(monthly_one_third_down_lines) - 1))
        array.pop(monthly_one_third_down_lines)





// Calculate and draw weekly levels on every real-time bar
if barstate.isrealtime and show_weekly
    // Get weekly anchor and AWR
    [w_anchor, w_awr, _] = getWeeklyAnchorAndAWR()

    // Calculate weekly levels - update global variables
    weekly_anchor := w_anchor
    weekly_full_up := w_anchor + w_awr
    weekly_full_down := w_anchor - w_awr
    weekly_one_third_up := w_anchor + (w_awr / 3)
    weekly_one_third_down := w_anchor - (w_awr / 3)
    weekly_two_third_up := w_anchor + (w_awr * 2 / 3)
    weekly_two_third_down := w_anchor - (w_awr * 2 / 3)
    weekly_half_up := w_anchor + (w_awr / 2)
    weekly_half_down := w_anchor - (w_awr / 2)

    // Only create new lines if we don't have them yet or if we're at a new weekly anchor
    if not initialized_weekly or is_weekly_anchor()
        // Delete previous weekly lines
        line.delete(weekly_true_open_line)
        line.delete(weekly_full_up_line)
        line.delete(weekly_full_down_line)
        line.delete(weekly_one_third_up_line)
        line.delete(weekly_one_third_down_line)
        line.delete(weekly_two_third_up_line)
        line.delete(weekly_two_third_down_line)
        line.delete(weekly_half_up_line)
        line.delete(weekly_half_down_line)

        // Create weekly lines
        // Weekly true open line
        weekly_true_open_line := line.new(bar_index - extend_left, weekly_anchor, bar_index + extend_right, weekly_anchor, color=weekly_true_open_color, width=line_width, style=get_line_style(line_style))

        // Weekly level lines
        if show_full_awr
            weekly_full_up_line := line.new(bar_index - extend_left, weekly_full_up, bar_index + extend_right, weekly_full_up, color=weekly_full_color, width=line_width, style=line_style_value)
            weekly_full_down_line := line.new(bar_index - extend_left, weekly_full_down, bar_index + extend_right, weekly_full_down, color=weekly_full_color, width=line_width, style=line_style_value)

        if show_one_third_awr
            weekly_one_third_up_line := line.new(bar_index - extend_left, weekly_one_third_up, bar_index + extend_right, weekly_one_third_up, color=weekly_one_third_color, width=line_width, style=line_style_value)
            weekly_one_third_down_line := line.new(bar_index - extend_left, weekly_one_third_down, bar_index + extend_right, weekly_one_third_down, color=weekly_one_third_color, width=line_width, style=line_style_value)

        if show_two_third_awr
            weekly_two_third_up_line := line.new(bar_index - extend_left, weekly_two_third_up, bar_index + extend_right, weekly_two_third_up, color=weekly_two_third_color, width=line_width, style=line_style_value)
            weekly_two_third_down_line := line.new(bar_index - extend_left, weekly_two_third_down, bar_index + extend_right, weekly_two_third_down, color=weekly_two_third_color, width=line_width, style=line_style_value)

        if show_half_awr
            weekly_half_up_line := line.new(bar_index - extend_left, weekly_half_up, bar_index + extend_right, weekly_half_up, color=weekly_half_color, width=line_width, style=line_style_value)
            weekly_half_down_line := line.new(bar_index - extend_left, weekly_half_down, bar_index + extend_right, weekly_half_down, color=weekly_half_color, width=line_width, style=line_style_value)
    else
        // Update existing lines
        if not na(weekly_true_open_line)
            line.set_x2(weekly_true_open_line, bar_index + extend_right)

        if show_full_awr
            if not na(weekly_full_up_line)
                line.set_x2(weekly_full_up_line, bar_index + extend_right)
            if not na(weekly_full_down_line)
                line.set_x2(weekly_full_down_line, bar_index + extend_right)

        if show_one_third_awr
            if not na(weekly_one_third_up_line)
                line.set_x2(weekly_one_third_up_line, bar_index + extend_right)
            if not na(weekly_one_third_down_line)
                line.set_x2(weekly_one_third_down_line, bar_index + extend_right)

        if show_two_third_awr
            if not na(weekly_two_third_up_line)
                line.set_x2(weekly_two_third_up_line, bar_index + extend_right)
            if not na(weekly_two_third_down_line)
                line.set_x2(weekly_two_third_down_line, bar_index + extend_right)

        if show_half_awr
            if not na(weekly_half_up_line)
                line.set_x2(weekly_half_up_line, bar_index + extend_right)
            if not na(weekly_half_down_line)
                line.set_x2(weekly_half_down_line, bar_index + extend_right)

    // Values are already stored in global variables

    // Mark as initialized
    initialized_weekly := true
    weekly_line_active := true

// Calculate and draw monthly levels on every real-time bar
if barstate.isrealtime and show_monthly
    // Get monthly anchor and AMR
    [m_anchor, m_amr, _] = getMonthlyAnchorAndAMR()

    // Calculate monthly levels - update global variables
    monthly_anchor := m_anchor
    monthly_full_up := m_anchor + m_amr
    monthly_full_down := m_anchor - m_amr
    monthly_one_third_up := m_anchor + (m_amr / 3)
    monthly_one_third_down := m_anchor - (m_amr / 3)
    monthly_two_third_up := m_anchor + (m_amr * 2 / 3)
    monthly_two_third_down := m_anchor - (m_amr * 2 / 3)
    monthly_half_up := m_anchor + (m_amr / 2)
    monthly_half_down := m_anchor - (m_amr / 2)

    // Only create new lines if we don't have them yet or if we're at a new monthly anchor
    if not initialized_monthly or is_monthly_anchor()
        // Delete previous monthly lines
        line.delete(monthly_true_open_line)
        line.delete(monthly_full_up_line)
        line.delete(monthly_full_down_line)
        line.delete(monthly_one_third_up_line)
        line.delete(monthly_one_third_down_line)
        line.delete(monthly_two_third_up_line)
        line.delete(monthly_two_third_down_line)
        line.delete(monthly_half_up_line)
        line.delete(monthly_half_down_line)

        // Create monthly lines
        // Monthly true open line
        monthly_true_open_line := line.new(bar_index - extend_left, monthly_anchor, bar_index + extend_right, monthly_anchor, color=monthly_true_open_color, width=line_width, style=get_line_style(line_style))

        // Monthly level lines
        if show_full_amr
            monthly_full_up_line := line.new(bar_index - extend_left, monthly_full_up, bar_index + extend_right, monthly_full_up, color=monthly_full_color, width=line_width, style=line_style_value)
            monthly_full_down_line := line.new(bar_index - extend_left, monthly_full_down, bar_index + extend_right, monthly_full_down, color=monthly_full_color, width=line_width, style=line_style_value)

        if show_one_third_amr
            monthly_one_third_up_line := line.new(bar_index - extend_left, monthly_one_third_up, bar_index + extend_right, monthly_one_third_up, color=monthly_one_third_color, width=line_width, style=line_style_value)
            monthly_one_third_down_line := line.new(bar_index - extend_left, monthly_one_third_down, bar_index + extend_right, monthly_one_third_down, color=monthly_one_third_color, width=line_width, style=line_style_value)

        if show_two_third_amr
            monthly_two_third_up_line := line.new(bar_index - extend_left, monthly_two_third_up, bar_index + extend_right, monthly_two_third_up, color=monthly_two_third_color, width=line_width, style=line_style_value)
            monthly_two_third_down_line := line.new(bar_index - extend_left, monthly_two_third_down, bar_index + extend_right, monthly_two_third_down, color=monthly_two_third_color, width=line_width, style=line_style_value)

        if show_half_amr
            monthly_half_up_line := line.new(bar_index - extend_left, monthly_half_up, bar_index + extend_right, monthly_half_up, color=monthly_half_color, width=line_width, style=line_style_value)
            monthly_half_down_line := line.new(bar_index - extend_left, monthly_half_down, bar_index + extend_right, monthly_half_down, color=monthly_half_color, width=line_width, style=line_style_value)
    else
        // Update existing lines
        if not na(monthly_true_open_line)
            line.set_x2(monthly_true_open_line, bar_index + extend_right)

        if show_full_amr
            if not na(monthly_full_up_line)
                line.set_x2(monthly_full_up_line, bar_index + extend_right)
            if not na(monthly_full_down_line)
                line.set_x2(monthly_full_down_line, bar_index + extend_right)

        if show_one_third_amr
            if not na(monthly_one_third_up_line)
                line.set_x2(monthly_one_third_up_line, bar_index + extend_right)
            if not na(monthly_one_third_down_line)
                line.set_x2(monthly_one_third_down_line, bar_index + extend_right)

        if show_two_third_amr
            if not na(monthly_two_third_up_line)
                line.set_x2(monthly_two_third_up_line, bar_index + extend_right)
            if not na(monthly_two_third_down_line)
                line.set_x2(monthly_two_third_down_line, bar_index + extend_right)

        if show_half_amr
            if not na(monthly_half_up_line)
                line.set_x2(monthly_half_up_line, bar_index + extend_right)
            if not na(monthly_half_down_line)
                line.set_x2(monthly_half_down_line, bar_index + extend_right)

    // Values are already stored in global variables

    // Mark as initialized
    initialized_monthly := true
    monthly_line_active := true

// No need to update weekly and monthly lines on each bar
// as we're recreating them on every bar

// Initialize on the first bar to ensure we have values even on 1-minute charts
if barstate.isfirst
    // Get weekly and monthly open prices
    [w_anchor, w_awr, _] = getWeeklyAnchorAndAWR()
    [m_anchor, m_amr, _] = getMonthlyAnchorAndAMR()

    // Store these values
    stored_weekly_anchor := w_anchor
    stored_weekly_range := w_awr
    stored_monthly_anchor := m_anchor
    stored_monthly_range := m_amr

    // Calculate weekly levels
    weekly_anchor := w_anchor
    weekly_full_up := w_anchor + w_awr
    weekly_full_down := w_anchor - w_awr
    weekly_one_third_up := w_anchor + (w_awr / 3)
    weekly_one_third_down := w_anchor - (w_awr / 3)
    weekly_two_third_up := w_anchor + (w_awr * 2 / 3)
    weekly_two_third_down := w_anchor - (w_awr * 2 / 3)
    weekly_half_up := w_anchor + (w_awr / 2)
    weekly_half_down := w_anchor - (w_awr / 2)

    // Calculate monthly levels
    monthly_anchor := m_anchor
    monthly_full_up := m_anchor + m_amr
    monthly_full_down := m_anchor - m_amr
    monthly_one_third_up := m_anchor + (m_amr / 3)
    monthly_one_third_down := m_anchor - (m_amr / 3)
    monthly_two_third_up := m_anchor + (m_amr * 2 / 3)
    monthly_two_third_down := m_anchor - (m_amr * 2 / 3)
    monthly_half_up := m_anchor + (m_amr / 2)
    monthly_half_down := m_anchor - (m_amr / 2)

    // Mark as initialized to prevent recreation on every bar
    initialized_weekly := true
    initialized_monthly := true

// Draw all levels on the last bar
if barstate.islast
    // Always clear all existing labels to prevent clutter
    // This ensures we only have one set of labels at a time
    // Delete all existing lines and labels
    line.delete(true_day_open_line)
    label.delete(true_day_open_label)
    line.delete(full_adr_up_line)
    line.delete(full_adr_down_line)
    label.delete(full_adr_up_label)
    label.delete(full_adr_down_label)
    line.delete(one_third_adr_up_line)
    line.delete(one_third_adr_down_line)
    label.delete(one_third_adr_up_label)
    label.delete(one_third_adr_down_label)
    line.delete(two_third_adr_up_line)
    line.delete(two_third_adr_down_line)
    label.delete(two_third_adr_up_label)
    label.delete(two_third_adr_down_label)
    line.delete(half_adr_up_line)
    line.delete(half_adr_down_line)
    label.delete(half_adr_up_label)
    label.delete(half_adr_down_label)

    // Clear only the labels for weekly and monthly levels, not the lines
    // We keep the lines because they're managed separately
    label.delete(weekly_true_open_label)
    label.delete(weekly_full_up_label)
    label.delete(weekly_full_down_label)
    label.delete(weekly_one_third_up_label)
    label.delete(weekly_one_third_down_label)
    label.delete(weekly_two_third_up_label)
    label.delete(weekly_two_third_down_label)
    label.delete(weekly_half_up_label)
    label.delete(weekly_half_down_label)

    label.delete(monthly_true_open_label)
    label.delete(monthly_full_up_label)
    label.delete(monthly_full_down_label)
    label.delete(monthly_one_third_up_label)
    label.delete(monthly_one_third_down_label)
    label.delete(monthly_two_third_up_label)
    label.delete(monthly_two_third_down_label)
    label.delete(monthly_half_up_label)
    label.delete(monthly_half_down_label)

    // Get daily levels
    [daily_anchor, d_full_up, d_full_down, d_one_third_up, d_one_third_down, d_two_third_up, d_two_third_down, d_half_up, d_half_down] = getDailyLevels()

    // Draw daily levels if enabled
    if show_daily
        // Draw true day open line
        line.new(bar_index - extend_left, daily_anchor, bar_index + extend_right, daily_anchor,
             color=true_day_open_color, width=line_width, style=line_style_value)

        // Draw true day open label
        if show_labels
            true_day_open_label := label.new(bar_index + extend_right, daily_anchor + label_y_offset,
                 "TDO" + (show_price_in_label ? str.format(" ({0})", daily_anchor) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=true_day_open_color, color=color.new(color.black, 100))

        // Draw ADR levels
        if show_full_adr
            // Full ADR up line
            line.new(bar_index - extend_left, d_full_up, bar_index + extend_right, d_full_up,
                 color=full_adr_color, width=line_width, style=line_style_value)

            // Full ADR up label
            if show_labels
                full_adr_up_label := label.new(bar_index + extend_right, d_full_up + label_y_offset,
                     "ADR+" + (show_price_in_label ? str.format(" ({0})", d_full_up) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=full_adr_color, color=color.new(color.black, 100))

            // Full ADR down line
            line.new(bar_index - extend_left, d_full_down, bar_index + extend_right, d_full_down,
                 color=full_adr_color, width=line_width, style=line_style_value)

            // Full ADR down label
            if show_labels
                full_adr_down_label := label.new(bar_index + extend_right, d_full_down + label_y_offset,
                     "ADR-" + (show_price_in_label ? str.format(" ({0})", d_full_down) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=full_adr_color, color=color.new(color.black, 100))

        // Draw 1/3 ADR levels
        if show_one_third_adr
            // 1/3 ADR up line
            line.new(bar_index - extend_left, d_one_third_up, bar_index + extend_right, d_one_third_up,
                 color=one_third_adr_color, width=line_width, style=line_style_value)

            // 1/3 ADR up label
            if show_labels
                one_third_adr_up_label := label.new(bar_index + extend_right, d_one_third_up + label_y_offset,
                     "1/3 ADR+" + (show_price_in_label ? str.format(" ({0})", d_one_third_up) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=one_third_adr_color, color=color.new(color.black, 100))

            // 1/3 ADR down line
            line.new(bar_index - extend_left, d_one_third_down, bar_index + extend_right, d_one_third_down,
                 color=one_third_adr_color, width=line_width, style=line_style_value)

            // 1/3 ADR down label
            if show_labels
                one_third_adr_down_label := label.new(bar_index + extend_right, d_one_third_down + label_y_offset,
                     "1/3 ADR-" + (show_price_in_label ? str.format(" ({0})", d_one_third_down) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=one_third_adr_color, color=color.new(color.black, 100))

        // Draw 2/3 ADR levels
        if show_two_third_adr
            // 2/3 ADR up line
            line.new(bar_index - extend_left, d_two_third_up, bar_index + extend_right, d_two_third_up,
                 color=two_third_adr_color, width=line_width, style=line_style_value)

            // 2/3 ADR up label
            if show_labels
                two_third_adr_up_label := label.new(bar_index + extend_right, d_two_third_up + label_y_offset,
                     "2/3 ADR+" + (show_price_in_label ? str.format(" ({0})", d_two_third_up) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=two_third_adr_color, color=color.new(color.black, 100))

            // 2/3 ADR down line
            line.new(bar_index - extend_left, d_two_third_down, bar_index + extend_right, d_two_third_down,
                 color=two_third_adr_color, width=line_width, style=line_style_value)

            // 2/3 ADR down label
            if show_labels
                two_third_adr_down_label := label.new(bar_index + extend_right, d_two_third_down + label_y_offset,
                     "2/3 ADR-" + (show_price_in_label ? str.format(" ({0})", d_two_third_down) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=two_third_adr_color, color=color.new(color.black, 100))

        // Draw 1/2 ADR levels
        if show_half_adr
            // 1/2 ADR up line
            line.new(bar_index - extend_left, d_half_up, bar_index + extend_right, d_half_up,
                 color=half_adr_color, width=line_width, style=line_style_value)

            // 1/2 ADR up label
            if show_labels
                half_adr_up_label := label.new(bar_index + extend_right, d_half_up + label_y_offset,
                     "1/2 ADR+" + (show_price_in_label ? str.format(" ({0})", d_half_up) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=half_adr_color, color=color.new(color.black, 100))

            // 1/2 ADR down line
            line.new(bar_index - extend_left, d_half_down, bar_index + extend_right, d_half_down,
                 color=half_adr_color, width=line_width, style=line_style_value)

            // 1/2 ADR down label
            if show_labels
                half_adr_down_label := label.new(bar_index + extend_right, d_half_down + label_y_offset,
                     "1/2 ADR-" + (show_price_in_label ? str.format(" ({0})", d_half_down) : ""),
                     style=label.style_label_right, size=label_size_value,
                     textcolor=half_adr_color, color=color.new(color.black, 100))

    // Create weekly labels if initialized
    if show_weekly and initialized_weekly
        // Draw weekly true open label
        if show_labels
            weekly_true_open_label := label.new(bar_index + extend_right, weekly_anchor + label_y_offset,
                 "Weekly Open" + (show_price_in_label ? str.format(" ({0})", weekly_anchor) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_true_open_color, color=color.new(color.black, 100))

        // Draw AWR level labels
        if show_full_awr and show_labels
            // Full AWR up label
            weekly_full_up_label := label.new(bar_index + extend_right, weekly_full_up + label_y_offset,
                 "AWR+" + (show_price_in_label ? str.format(" ({0})", weekly_full_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_full_color, color=color.new(color.black, 100))

            // Full AWR down label
            weekly_full_down_label := label.new(bar_index + extend_right, weekly_full_down + label_y_offset,
                 "AWR-" + (show_price_in_label ? str.format(" ({0})", weekly_full_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_full_color, color=color.new(color.black, 100))

        // Draw 1/3 AWR level labels
        if show_one_third_awr and show_labels
            // 1/3 AWR up label
            weekly_one_third_up_label := label.new(bar_index + extend_right, weekly_one_third_up + label_y_offset,
                 "1/3 AWR+" + (show_price_in_label ? str.format(" ({0})", weekly_one_third_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_one_third_color, color=color.new(color.black, 100))

            // 1/3 AWR down label
            weekly_one_third_down_label := label.new(bar_index + extend_right, weekly_one_third_down + label_y_offset,
                 "1/3 AWR-" + (show_price_in_label ? str.format(" ({0})", weekly_one_third_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_one_third_color, color=color.new(color.black, 100))

        // Draw 2/3 AWR level labels
        if show_two_third_awr and show_labels
            // 2/3 AWR up label
            weekly_two_third_up_label := label.new(bar_index + extend_right, weekly_two_third_up + label_y_offset,
                 "2/3 AWR+" + (show_price_in_label ? str.format(" ({0})", weekly_two_third_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_two_third_color, color=color.new(color.black, 100))

            // 2/3 AWR down label
            weekly_two_third_down_label := label.new(bar_index + extend_right, weekly_two_third_down + label_y_offset,
                 "2/3 AWR-" + (show_price_in_label ? str.format(" ({0})", weekly_two_third_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_two_third_color, color=color.new(color.black, 100))

        // Draw 1/2 AWR level labels
        if show_half_awr and show_labels
            // 1/2 AWR up label
            weekly_half_up_label := label.new(bar_index + extend_right, weekly_half_up + label_y_offset,
                 "1/2 AWR+" + (show_price_in_label ? str.format(" ({0})", weekly_half_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_half_color, color=color.new(color.black, 100))

            // 1/2 AWR down label
            weekly_half_down_label := label.new(bar_index + extend_right, weekly_half_down + label_y_offset,
                 "1/2 AWR-" + (show_price_in_label ? str.format(" ({0})", weekly_half_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=weekly_half_color, color=color.new(color.black, 100))

    // Create monthly labels if initialized
    if show_monthly and initialized_monthly
        // Draw monthly true open label
        if show_labels
            monthly_true_open_label := label.new(bar_index + extend_right, monthly_anchor + label_y_offset,
                 "Monthly Open" + (show_price_in_label ? str.format(" ({0})", monthly_anchor) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_true_open_color, color=color.new(color.black, 100))

        // Draw AMR level labels
        if show_full_amr and show_labels
            // Full AMR up label
            monthly_full_up_label := label.new(bar_index + extend_right, monthly_full_up + label_y_offset,
                 "AMR+" + (show_price_in_label ? str.format(" ({0})", monthly_full_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_full_color, color=color.new(color.black, 100))

            // Full AMR down label
            monthly_full_down_label := label.new(bar_index + extend_right, monthly_full_down + label_y_offset,
                 "AMR-" + (show_price_in_label ? str.format(" ({0})", monthly_full_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_full_color, color=color.new(color.black, 100))

        // Draw 1/3 AMR level labels
        if show_one_third_amr and show_labels
            // 1/3 AMR up label
            monthly_one_third_up_label := label.new(bar_index + extend_right, monthly_one_third_up + label_y_offset,
                 "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", monthly_one_third_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_one_third_color, color=color.new(color.black, 100))

            // 1/3 AMR down label
            monthly_one_third_down_label := label.new(bar_index + extend_right, monthly_one_third_down + label_y_offset,
                 "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", monthly_one_third_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_one_third_color, color=color.new(color.black, 100))

        // Draw 2/3 AMR level labels
        if show_two_third_amr and show_labels
            // 2/3 AMR up label
            monthly_two_third_up_label := label.new(bar_index + extend_right, monthly_two_third_up + label_y_offset,
                 "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", monthly_two_third_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_two_third_color, color=color.new(color.black, 100))

            // 2/3 AMR down label
            monthly_two_third_down_label := label.new(bar_index + extend_right, monthly_two_third_down + label_y_offset,
                 "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", monthly_two_third_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_two_third_color, color=color.new(color.black, 100))

        // Draw 1/2 AMR level labels
        if show_half_amr and show_labels
            // 1/2 AMR up label
            monthly_half_up_label := label.new(bar_index + extend_right, monthly_half_up + label_y_offset,
                 "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", monthly_half_up) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_half_color, color=color.new(color.black, 100))

            // 1/2 AMR down label
            monthly_half_down_label := label.new(bar_index + extend_right, monthly_half_down + label_y_offset,
                 "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", monthly_half_down) : ""),
                 style=label.style_label_right, size=label_size_value,
                 textcolor=monthly_half_color, color=color.new(color.black, 100))

// Update lines during the current day
if true_day_line_active
    // Update true day open line
    line.set_x2(true_day_open_line, bar_index)
    line.set_y2(true_day_open_line, true_day_open_price)

    // Update true day open label
    if show_labels
        label.set_x(true_day_open_label, bar_index + extend_right)
        label.set_y(true_day_open_label, true_day_open_price + label_y_offset)

    // Update ADR level lines
    if show_full_adr
        line.set_x2(full_adr_up_line, bar_index)
        line.set_y2(full_adr_up_line, full_adr_up)
        line.set_x2(full_adr_down_line, bar_index)
        line.set_y2(full_adr_down_line, full_adr_down)

        if show_labels
            label.set_x(full_adr_up_label, bar_index + extend_right)
            label.set_y(full_adr_up_label, full_adr_up + label_y_offset)
            label.set_x(full_adr_down_label, bar_index + extend_right)
            label.set_y(full_adr_down_label, full_adr_down + label_y_offset)

    if show_one_third_adr
        line.set_x2(one_third_adr_up_line, bar_index)
        line.set_y2(one_third_adr_up_line, one_third_adr_up)
        line.set_x2(one_third_adr_down_line, bar_index)
        line.set_y2(one_third_adr_down_line, one_third_adr_down)

        if show_labels
            label.set_x(one_third_adr_up_label, bar_index + extend_right)
            label.set_y(one_third_adr_up_label, one_third_adr_up + label_y_offset)
            label.set_x(one_third_adr_down_label, bar_index + extend_right)
            label.set_y(one_third_adr_down_label, one_third_adr_down + label_y_offset)

    if show_two_third_adr
        line.set_x2(two_third_adr_up_line, bar_index)
        line.set_y2(two_third_adr_up_line, two_third_adr_up)
        line.set_x2(two_third_adr_down_line, bar_index)
        line.set_y2(two_third_adr_down_line, two_third_adr_down)

        if show_labels
            label.set_x(two_third_adr_up_label, bar_index + extend_right)
            label.set_y(two_third_adr_up_label, two_third_adr_up + label_y_offset)
            label.set_x(two_third_adr_down_label, bar_index + extend_right)
            label.set_y(two_third_adr_down_label, two_third_adr_down + label_y_offset)

    if show_half_adr
        line.set_x2(half_adr_up_line, bar_index)
        line.set_y2(half_adr_up_line, half_adr_up)
        line.set_x2(half_adr_down_line, bar_index)
        line.set_y2(half_adr_down_line, half_adr_down)

        if show_labels
            label.set_x(half_adr_up_label, bar_index + extend_right)
            label.set_y(half_adr_up_label, half_adr_up + label_y_offset)
            label.set_x(half_adr_down_label, bar_index + extend_right)
            label.set_y(half_adr_down_label, half_adr_down + label_y_offset)

// Update weekly lines
if weekly_line_active and show_weekly
    // Update weekly true open line
    line.set_x2(weekly_true_open_line, bar_index)
    line.set_y2(weekly_true_open_line, weekly_true_open_price)

    // Update weekly true open label
    if show_labels
        label.set_x(weekly_true_open_label, bar_index + extend_right)
        label.set_y(weekly_true_open_label, weekly_true_open_price + label_y_offset)

    // Update weekly AWR level lines
    line.set_x2(weekly_full_up_line, bar_index)
    line.set_y2(weekly_full_up_line, weekly_full_up)
    line.set_x2(weekly_full_down_line, bar_index)
    line.set_y2(weekly_full_down_line, weekly_full_down)

    if show_labels
        label.set_x(weekly_full_up_label, bar_index + extend_right)
        label.set_y(weekly_full_up_label, weekly_full_up + label_y_offset)
        label.set_x(weekly_full_down_label, bar_index + extend_right)
        label.set_y(weekly_full_down_label, weekly_full_down + label_y_offset)

    line.set_x2(weekly_one_third_up_line, bar_index)
    line.set_y2(weekly_one_third_up_line, weekly_one_third_up)
    line.set_x2(weekly_one_third_down_line, bar_index)
    line.set_y2(weekly_one_third_down_line, weekly_one_third_down)

    if show_labels
        label.set_x(weekly_one_third_up_label, bar_index + extend_right)
        label.set_y(weekly_one_third_up_label, weekly_one_third_up + label_y_offset)
        label.set_x(weekly_one_third_down_label, bar_index + extend_right)
        label.set_y(weekly_one_third_down_label, weekly_one_third_down + label_y_offset)

    if show_two_third_awr
        line.set_x2(weekly_two_third_up_line, bar_index)
        line.set_y2(weekly_two_third_up_line, weekly_two_third_up)
        line.set_x2(weekly_two_third_down_line, bar_index)
        line.set_y2(weekly_two_third_down_line, weekly_two_third_down)

        if show_labels
            label.set_x(weekly_two_third_up_label, bar_index + extend_right)
            label.set_y(weekly_two_third_up_label, weekly_two_third_up + label_y_offset)
            label.set_x(weekly_two_third_down_label, bar_index + extend_right)
            label.set_y(weekly_two_third_down_label, weekly_two_third_down + label_y_offset)

    if show_half_awr
        line.set_x2(weekly_half_up_line, bar_index)
        line.set_y2(weekly_half_up_line, weekly_half_up)
        line.set_x2(weekly_half_down_line, bar_index)
        line.set_y2(weekly_half_down_line, weekly_half_down)

        if show_labels
            label.set_x(weekly_half_up_label, bar_index + extend_right)
            label.set_y(weekly_half_up_label, weekly_half_up + label_y_offset)
            label.set_x(weekly_half_down_label, bar_index + extend_right)
            label.set_y(weekly_half_down_label, weekly_half_down + label_y_offset)

// Update monthly lines
if monthly_line_active and show_monthly
    // Update monthly true open line
    line.set_x2(monthly_true_open_line, bar_index)
    line.set_y2(monthly_true_open_line, monthly_true_open_price)

    // Update monthly true open label
    if show_labels
        label.set_x(monthly_true_open_label, bar_index + extend_right)
        label.set_y(monthly_true_open_label, monthly_true_open_price + label_y_offset)

    // Update monthly AMR level lines
    line.set_x2(monthly_full_up_line, bar_index)
    line.set_y2(monthly_full_up_line, monthly_full_up)
    line.set_x2(monthly_full_down_line, bar_index)
    line.set_y2(monthly_full_down_line, monthly_full_down)

    if show_labels
        label.set_x(monthly_full_up_label, bar_index + extend_right)
        label.set_y(monthly_full_up_label, monthly_full_up + label_y_offset)
        label.set_x(monthly_full_down_label, bar_index + extend_right)
        label.set_y(monthly_full_down_label, monthly_full_down + label_y_offset)

    line.set_x2(monthly_one_third_up_line, bar_index)
    line.set_y2(monthly_one_third_up_line, monthly_one_third_up)
    line.set_x2(monthly_one_third_down_line, bar_index)
    line.set_y2(monthly_one_third_down_line, monthly_one_third_down)

    if show_labels
        label.set_x(monthly_one_third_up_label, bar_index + extend_right)
        label.set_y(monthly_one_third_up_label, monthly_one_third_up + label_y_offset)
        label.set_x(monthly_one_third_down_label, bar_index + extend_right)
        label.set_y(monthly_one_third_down_label, monthly_one_third_down + label_y_offset)

    if show_two_third_amr
        line.set_x2(monthly_two_third_up_line, bar_index)
        line.set_y2(monthly_two_third_up_line, monthly_two_third_up)
        line.set_x2(monthly_two_third_down_line, bar_index)
        line.set_y2(monthly_two_third_down_line, monthly_two_third_down)

        if show_labels
            label.set_x(monthly_two_third_up_label, bar_index + extend_right)
            label.set_y(monthly_two_third_up_label, monthly_two_third_up + label_y_offset)
            label.set_x(monthly_two_third_down_label, bar_index + extend_right)
            label.set_y(monthly_two_third_down_label, monthly_two_third_down + label_y_offset)

    if show_half_amr
        line.set_x2(monthly_half_up_line, bar_index)
        line.set_y2(monthly_half_up_line, monthly_half_up)
        line.set_x2(monthly_half_down_line, bar_index)
        line.set_y2(monthly_half_down_line, monthly_half_down)

        if show_labels
            label.set_x(monthly_half_up_label, bar_index + extend_right)
            label.set_y(monthly_half_up_label, monthly_half_up + label_y_offset)
            label.set_x(monthly_half_down_label, bar_index + extend_right)
            label.set_y(monthly_half_down_label, monthly_half_down + label_y_offset)

// === TIMEFRAME RESTRICTION ===
// Handle daily lines - only delete if daily levels are not enabled or if timeframe restriction applies
if not show_daily or (show_only_intraday and timeframe.isdwm)
    // Delete daily lines and labels
    line.delete(true_day_open_line)
    label.delete(true_day_open_label)
    line.delete(true_day_vertical)

    if show_full_adr
        line.delete(full_adr_up_line)
        line.delete(full_adr_down_line)
        label.delete(full_adr_up_label)
        label.delete(full_adr_down_label)

    if show_one_third_adr
        line.delete(one_third_adr_up_line)
        line.delete(one_third_adr_down_line)
        label.delete(one_third_adr_up_label)
        label.delete(one_third_adr_down_label)

    if show_two_third_adr
        line.delete(two_third_adr_up_line)
        line.delete(two_third_adr_down_line)
        label.delete(two_third_adr_up_label)
        label.delete(two_third_adr_down_label)

    if show_half_adr
        line.delete(half_adr_up_line)
        line.delete(half_adr_down_line)
        label.delete(half_adr_up_label)
        label.delete(half_adr_down_label)

    true_day_line_active := false

// Handle weekly lines separately - only delete if weekly levels are not enabled
if not show_weekly
    line.delete(weekly_true_open_line)
    label.delete(weekly_true_open_label)
    line.delete(weekly_vertical)

    if show_full_awr
        line.delete(weekly_full_up_line)
        line.delete(weekly_full_down_line)
        label.delete(weekly_full_up_label)
        label.delete(weekly_full_down_label)

    if show_one_third_awr
        line.delete(weekly_one_third_up_line)
        line.delete(weekly_one_third_down_line)
        label.delete(weekly_one_third_up_label)
        label.delete(weekly_one_third_down_label)

    if show_two_third_awr
        line.delete(weekly_two_third_up_line)
        line.delete(weekly_two_third_down_line)
        label.delete(weekly_two_third_up_label)
        label.delete(weekly_two_third_down_label)

    if show_half_awr
        line.delete(weekly_half_up_line)
        line.delete(weekly_half_down_line)
        label.delete(weekly_half_up_label)
        label.delete(weekly_half_down_label)

    weekly_line_active := false

// Handle monthly lines separately - only delete if monthly levels are not enabled
if not show_monthly
    line.delete(monthly_true_open_line)
    label.delete(monthly_true_open_label)
    line.delete(monthly_vertical)

    if show_full_amr
        line.delete(monthly_full_up_line)
        line.delete(monthly_full_down_line)
        label.delete(monthly_full_up_label)
        label.delete(monthly_full_down_label)

    if show_one_third_amr
        line.delete(monthly_one_third_up_line)
        line.delete(monthly_one_third_down_line)
        label.delete(monthly_one_third_up_label)
        label.delete(monthly_one_third_down_label)

    if show_two_third_amr
        line.delete(monthly_two_third_up_line)
        line.delete(monthly_two_third_down_line)
        label.delete(monthly_two_third_up_label)
        label.delete(monthly_two_third_down_label)

    if show_half_amr
        line.delete(monthly_half_up_line)
        line.delete(monthly_half_down_line)
        label.delete(monthly_half_up_label)
        label.delete(monthly_half_down_label)

    monthly_line_active := false

// === ALERT CONDITIONS ===
// Alert conditions for price touching ADR levels
alertcondition(ta.crossover(high, full_adr_up) or ta.crossunder(low, full_adr_up), "Price touched Full ADR+", "Price touched Full ADR+ level")
alertcondition(ta.crossover(high, full_adr_down) or ta.crossunder(low, full_adr_down), "Price touched Full ADR-", "Price touched Full ADR- level")
alertcondition(ta.crossover(high, one_third_adr_up) or ta.crossunder(low, one_third_adr_up), "Price touched 1/3 ADR+", "Price touched 1/3 ADR+ level")
alertcondition(ta.crossover(high, one_third_adr_down) or ta.crossunder(low, one_third_adr_down), "Price touched 1/3 ADR-", "Price touched 1/3 ADR- level")

// Alert conditions for price touching AWR levels
alertcondition(ta.crossover(high, weekly_full_up) or ta.crossunder(low, weekly_full_up), "Price touched Full AWR+", "Price touched Full AWR+ level")
alertcondition(ta.crossover(high, weekly_full_down) or ta.crossunder(low, weekly_full_down), "Price touched Full AWR-", "Price touched Full AWR- level")
alertcondition(ta.crossover(high, weekly_one_third_up) or ta.crossunder(low, weekly_one_third_up), "Price touched 1/3 AWR+", "Price touched 1/3 AWR+ level")
alertcondition(ta.crossover(high, weekly_one_third_down) or ta.crossunder(low, weekly_one_third_down), "Price touched 1/3 AWR-", "Price touched 1/3 AWR- level")
alertcondition(ta.crossover(high, weekly_two_third_up) or ta.crossunder(low, weekly_two_third_up), "Price touched 2/3 AWR+", "Price touched 2/3 AWR+ level")
alertcondition(ta.crossover(high, weekly_two_third_down) or ta.crossunder(low, weekly_two_third_down), "Price touched 2/3 AWR-", "Price touched 2/3 AWR- level")
alertcondition(ta.crossover(high, weekly_half_up) or ta.crossunder(low, weekly_half_up), "Price touched 1/2 AWR+", "Price touched 1/2 AWR+ level")
alertcondition(ta.crossover(high, weekly_half_down) or ta.crossunder(low, weekly_half_down), "Price touched 1/2 AWR-", "Price touched 1/2 AWR- level")

// Alert conditions for price touching AMR levels
alertcondition(ta.crossover(high, monthly_full_up) or ta.crossunder(low, monthly_full_up), "Price touched Full AMR+", "Price touched Full AMR+ level")
alertcondition(ta.crossover(high, monthly_full_down) or ta.crossunder(low, monthly_full_down), "Price touched Full AMR-", "Price touched Full AMR- level")
alertcondition(ta.crossover(high, monthly_one_third_up) or ta.crossunder(low, monthly_one_third_up), "Price touched 1/3 AMR+", "Price touched 1/3 AMR+ level")
alertcondition(ta.crossover(high, monthly_one_third_down) or ta.crossunder(low, monthly_one_third_down), "Price touched 1/3 AMR-", "Price touched 1/3 AMR- level")
alertcondition(ta.crossover(high, monthly_two_third_up) or ta.crossunder(low, monthly_two_third_up), "Price touched 2/3 AMR+", "Price touched 2/3 AMR+ level")
alertcondition(ta.crossover(high, monthly_two_third_down) or ta.crossunder(low, monthly_two_third_down), "Price touched 2/3 AMR-", "Price touched 2/3 AMR- level")
alertcondition(ta.crossover(high, monthly_half_up) or ta.crossunder(low, monthly_half_up), "Price touched 1/2 AMR+", "Price touched 1/2 AMR+ level")
alertcondition(ta.crossover(high, monthly_half_down) or ta.crossunder(low, monthly_half_down), "Price touched 1/2 AMR-", "Price touched 1/2 AMR- level")

// Alert conditions for price touching ADR levels
alertcondition(ta.crossover(high, two_third_adr_up) or ta.crossunder(low, two_third_adr_up), "Price touched 2/3 ADR+", "Price touched 2/3 ADR+ level")
alertcondition(ta.crossover(high, two_third_adr_down) or ta.crossunder(low, two_third_adr_down), "Price touched 2/3 ADR-", "Price touched 2/3 ADR- level")
alertcondition(ta.crossover(high, half_adr_up) or ta.crossunder(low, half_adr_up), "Price touched 1/2 ADR+", "Price touched 1/2 ADR+ level")
alertcondition(ta.crossover(high, half_adr_down) or ta.crossunder(low, half_adr_down), "Price touched 1/2 ADR-", "Price touched 1/2 ADR- level")
