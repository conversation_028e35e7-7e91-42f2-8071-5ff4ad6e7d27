//@version=5
indicator("AM Silver Bullet Line Only", shorttitle='SB 10–11AM', overlay=true)

// === Inputs ===
showSB     = input.bool(true, 'Show 10–11AM Session')
col_SB     = input.color(color.rgb(229, 233, 244, 7), 'Session Line Color')
extendLine = input.bool(true, 'Extend vertical line')

// === Session logic ===
timeSess(tf, sessionStr) => time(tf, sessionStr, "America/New_York")
SB_AM_per  = timeSess(timeframe.period, "1000-1100")
strAM      = SB_AM_per and not SB_AM_per[1]
n          = bar_index
minT       = syminfo.mintick

// === Persistent variables ===
var line   vLine = na
var line   hLine = na
var int    hStartBar = na
var float  hPrice    = na

// === On each 10 AM NY session start ===
if strAM and showSB
    // Draw vertical line at session start
    vLine := line.new(n, close, n, close + minT, color=col_SB, extend=extendLine ? extend.both : extend.none)

    // Close previous horizontal line
    if not na(hLine)
        line.set_x2(hLine, n)  // end it at current bar
        hLine := na

    // Create new horizontal line
    hStartBar := n
    hPrice := open
    hLine := line.new(x1=hStartBar, y1=hPrice, x2=n, y2=hPrice, color=col_SB, style=line.style_dashed, extend=extend.none)

// === Update horizontal line each bar until next 10AM session ===
if not na(hLine)
    line.set_x2(hLine, n)
    line.set_y2(hLine, hPrice)

// === Timeframe warning ===
var table tab = table.new(position=position.top_right, columns=1, rows=1, bgcolor=color(na), border_width=1)
if barstate.islast and timeframe.in_seconds(timeframe.period) > 15 * 60
    table.cell(tab, 0, 0, text='Use timeframe <= 15 min', text_color=color.red)


