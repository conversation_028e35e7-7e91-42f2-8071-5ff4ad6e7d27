//@version=5
indicator("CVD Candles (Extracted - Original Color Logic)", overlay = true, format = format.volume)

// Timeframe for Buy/Sell Volume Calculation (Important: Keep this consistent with the original script)
tf = input.timeframe('1', 'LTF Timeframe') // Or whatever the original script used

// --- CVD Calculation (Extracted from the original script) ---
[buy_volume, sell_volume] = request.security_lower_tf(syminfo.tickerid, tf, [close>open ? volume : 0, close<open ? volume : 0])

buy_vol       = array.sum(buy_volume)
sell_vol      = array.sum(sell_volume)
delta_vol     = buy_vol-sell_vol
cum_delta_vol = ta.cum(delta_vol)

// CVD Candle Calculations (Open, High, Low, Close)
cvd_open = cum_delta_vol[1]  // Previous CVD value
cvd_high = math.max(cum_delta_vol, cvd_open) // High is the max of current and previous CVD
cvd_low  = math.min(cum_delta_vol, cvd_open) // Low is the min of current and previous CVD
cvd_close = cum_delta_vol       // Current CVD value


// --- CVD Candle Plotting (Extracted with Original Color Logic) ---

// Original Colors (Preserved)
upcol    = color.new(#d1d4dc, 0) // Light gray, fully opaque
downcol  = color.new(#9598a1f6, 0) // Slightly darker gray, fully opaque

// CVD Candle Color (Using Original Logic - Based on delta_vol)
cvd_color = delta_vol > 0 ? upcol : downcol


// Plot CVD Candles
plotcandle(cvd_open, cvd_high, cvd_low, cvd_close, color = cvd_color, wickcolor = cvd_color, bordercolor = cvd_color, title = 'CVD')

// Optional: Plot CVD as a line for comparison (Extracted)
// plot(cum_delta_vol, color=cvd_color, title="CVD Line")