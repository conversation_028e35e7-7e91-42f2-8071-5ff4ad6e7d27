//@version=5

indicator("Multi-Timeframe Fibonacci Levels", overlay=true, max_lines_count=500, max_labels_count=500, dynamic_requests=true)

// Arrays to store labels and lines for each timeframe
var dailyLabelsFib = array.new_label()
var weeklyLabelsFib = array.new_label()
var monthlyLabelsFib = array.new_label()
var quarterlyLabelsFib = array.new_label()
var yearlyLabelsFib = array.new_label()

var dailyLinesFib = array.new_line()
var weeklyLinesFib = array.new_line()
var monthlyLinesFib = array.new_line()
var quarterlyLinesFib = array.new_line()
var yearlyLinesFib = array.new_line()

// Arrays to store open price labels and lines
var dailyOpenLabelsFib = array.new_label()
var weeklyOpenLabelsFib = array.new_label()
var monthlyOpenLabelsFib = array.new_label()

var dailyOpenLinesFib = array.new_line()
var weeklyOpenLinesFib = array.new_line()
var monthlyOpenLinesFib = array.new_line()

// Array to store all price levels for overlap detection
var allPriceLevelsFib = array.new_float()
var allLabelTextsFib = array.new_string()

// Arrays to store historical Fibonacci levels
var dailyHistoricalLinesFib = array.new_line(0)
var weeklyHistoricalLinesFib = array.new_line(0)
var monthlyHistoricalLinesFib = array.new_line(0)
var quarterlyHistoricalLinesFib = array.new_line(0)
var yearlyHistoricalLinesFib = array.new_line(0)

// Arrays to store historical open price levels
var dailyOpenHistoricalLinesFib = array.new_line(0)
var weeklyOpenHistoricalLinesFib = array.new_line(0)
var monthlyOpenHistoricalLinesFib = array.new_line(0)

// Arrays to store vertical day open lines
var dayOpenVerticalLines = array.new_line(0)

// Variables to track timeframe changes
var int lastDayOpenTime = 0
var bool newDayDetectedFib = false

// Arrays to store historical high/low values and their bar indices
var dailyHighsFib = array.new_float()
var dailyLowsFib = array.new_float()
var dailyBarsFib = array.new_int()

var weeklyHighsFib = array.new_float()
var weeklyLowsFib = array.new_float()
var weeklyBarsFib = array.new_int()

var monthlyHighsFib = array.new_float()
var monthlyLowsFib = array.new_float()
var monthlyBarsFib = array.new_int()

var quarterlyHighsFib = array.new_float()
var quarterlyLowsFib = array.new_float()
var quarterlyBarsFib = array.new_int()

var yearlyHighsFib = array.new_float()
var yearlyLowsFib = array.new_float()
var yearlyBarsFib = array.new_int()

// Variables to track timeframe changes
var int lastDailyBarTime = 0
var int lastWeeklyBarTime = 0
var int lastMonthlyBarTime = 0
var int lastQuarterlyBarTime = 0
var int lastYearlyBarTime = 0

// Function to clear old labels with safety limit
clearLabelsFib(labelsArrayFib) =>
    if array.size(labelsArrayFib) > 0
        // Limit the number of iterations to prevent potential issues
        maxSize = math.min(array.size(labelsArrayFib), 100)
        for iFib = 0 to maxSize - 1
            label.delete(array.get(labelsArrayFib, iFib))
        array.clear(labelsArrayFib)

// Function to clear old lines with safety limit
clearLinesFib(linesArrayFib) =>
    if array.size(linesArrayFib) > 0
        // Limit the number of iterations to prevent potential issues
        maxSize = math.min(array.size(linesArrayFib), 100)
        for iFib = 0 to maxSize - 1
            line.delete(array.get(linesArrayFib, iFib))
        array.clear(linesArrayFib)

// Function to check if a price level is close to an existing level
isOverlappingFib(priceFib, thresholdFib) =>
    result = false
    existingLabelFib = ""
    overlapIndexFib = -1

    if array.size(allPriceLevelsFib) > 0
        // Limit the number of iterations to prevent potential issues
        maxSize = math.min(array.size(allPriceLevelsFib), 100)
        for iFib = 0 to maxSize - 1
            existingPriceFib = array.get(allPriceLevelsFib, iFib)
            // Check if prices are within threshold (0.05% of current price)
            if math.abs(priceFib - existingPriceFib) <= thresholdFib
                result := true
                existingLabelFib := array.get(allLabelTextsFib, iFib)
                overlapIndexFib := iFib
                break

    [result, existingLabelFib, overlapIndexFib]

// Function to add a price level to the tracking arrays with size limit
addPriceLevelFib(priceFib, labelFib) =>
    // Limit array size to prevent overflow
    if array.size(allPriceLevelsFib) >= 100
        array.shift(allPriceLevelsFib)
        array.shift(allLabelTextsFib)

    array.push(allPriceLevelsFib, priceFib)
    array.push(allLabelTextsFib, labelFib)

// Function to clear price level tracking arrays
clearPriceLevelsFib() =>
    array.clear(allPriceLevelsFib)
    array.clear(allLabelTextsFib)
// Master enable/disable for all Fibonacci drawings
enableFibDrawingsFib = input.bool(true, "Enable Fibonacci Drawings", group="Main Settings")

// Enable/disable historical Fibonacci levels
showHistoricalLevelsFib = input.bool(true, "Show Historical Fibonacci Levels", group="Main Settings")
maxHistoricalPeriodsFib = input.int(10, "Max Historical Periods to Show", minval=1, maxval=50, group="Main Settings")

// Enable/disable day open vertical lines
showDayOpenLinesFib = input.bool(true, "Show Day Open Vertical Lines", group="Main Settings")
dayOpenLineColorFib = input.color(color.new(#FFFFFF, 70), "Day Open Line Color", group="Main Settings")

// Enable/disable open price levels
showOpenLevelsFib = input.bool(true, "Enable Open Price Levels", group="Main Settings")
showDailyOpenFib = input.bool(true, "Show Daily Open", group="Open Levels")
showWeeklyOpenFib = input.bool(true, "Show Weekly Open", group="Open Levels")
showMonthlyOpenFib = input.bool(true, "Show Monthly Open", group="Open Levels")

// Inputs for enabling/disabling timeframes and lookback periods
showDailyFib = input.bool(true, "Show Daily Levels", group="Timeframe Settings")
dailyLookbackFib = input.int(500, "Daily Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showWeeklyFib = input.bool(true, "Show Weekly Levels", group="Timeframe Settings")
weeklyLookbackFib = input.int(500, "Weekly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showMonthlyFib = input.bool(true, "Show Monthly Levels", group="Timeframe Settings")
monthlyLookbackFib = input.int(500, "Monthly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showQuarterlyFib = input.bool(true, "Show Quarterly Levels", group="Timeframe Settings")
quarterlyLookbackFib = input.int(500, "Quarterly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showYearlyFib = input.bool(true, "Show Yearly Levels", group="Timeframe Settings")
yearlyLookbackFib = input.int(500, "Yearly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")


// Input for right and left extension
dailyExtendRightFib = input.int(50, "Daily Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
dailyExtendLeftFib = input.int(500, "Daily Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

weeklyExtendRightFib = input.int(50, "Weekly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
weeklyExtendLeftFib = input.int(500, "Weekly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

monthlyExtendRightFib = input.int(50, "Monthly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
monthlyExtendLeftFib = input.int(500, "Monthly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

quarterlyExtendRightFib = input.int(50, "Quarterly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
quarterlyExtendLeftFib = input.int(500, "Quarterly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

yearlyExtendRightFib = input.int(50, "Yearly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
yearlyExtendLeftFib = input.int(500, "Yearly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

// Define Fibonacci levels and their labels from the screenshot
fibLevelsFib = array.from(0, 0.236, 0.382, 0.5, 0.618, 0.764, 1.0)
levelLabelsFib = array.from("High", "23.6", "38.2", "50.0", "61.8", "76.4", "Low")

// Toggle inputs for each Fibonacci level
show0Fib = input.bool(true, "Show High (0.00) Level", group="Fibonacci Levels")
show236Fib = input.bool(true, "Show 23.6 Level", group="Fibonacci Levels")
show382Fib = input.bool(true, "Show 38.2 Level", group="Fibonacci Levels")
show50Fib = input.bool(true, "Show 50.0 Level", group="Fibonacci Levels")
show618Fib = input.bool(true, "Show 61.8 Level", group="Fibonacci Levels")
show764Fib = input.bool(true, "Show 76.4 Level", group="Fibonacci Levels")
show100Fib = input.bool(true, "Show Low (100) Level", group="Fibonacci Levels")

// Color inputs for each Fibonacci level
color0Fib = input.color(#ffffff, "High (0.00) Level Color", group="Color Settings")
color236Fib = input.color(#ffffff, "23.6 Level Color", group="Color Settings")
color382Fib = input.color(#ffffff, "38.2 Level Color", group="Color Settings")
color50Fib = input.color(#ffffff, "50.0 Level Color", group="Color Settings")
color618Fib = input.color(#ffffff, "61.8 Level Color", group="Color Settings")
color764Fib = input.color(#ffffff, "76.4 Level Color", group="Color Settings")
color100Fib = input.color(#ffffff, "Low (100) Level Color", group="Color Settings")

fibColorsFib = array.from(color0Fib, color236Fib, color382Fib, color50Fib, color618Fib, color764Fib, color100Fib)
fibShowLevelsFib = array.from(show0Fib, show236Fib, show382Fib, show50Fib, show618Fib, show764Fib, show100Fib)

// Color inputs for open price levels
dailyOpenColorFib = input.color(#00FFFF, "Daily Open Color", group="Open Level Colors")
weeklyOpenColorFib = input.color(#FF00FF, "Weekly Open Color", group="Open Level Colors")
monthlyOpenColorFib = input.color(#FFFF00, "Monthly Open Color", group="Open Level Colors")

// Line style configuration
lineWidthFib = input.int(2, "Line Width", minval=1, maxval=5, group="Style Settings")
lineStyleFib = input.string("Dashed", "Line Style", options=["Solid", "Dotted", "Dashed"], group="Style Settings")

// Function to convert string style to Pine line style
getLineStyleFib(styleFib) =>
    if styleFib == "Solid"
        line.style_solid
    else if styleFib == "Dotted"
        line.style_dotted
    else
        line.style_dashed

// Function to calculate pivot highs and lows for a given timeframe
getPivotsFib(resFib) =>
    highPriceFib = request.security(syminfo.tickerid, resFib, high[1], lookahead=barmerge.lookahead_on)
    lowPriceFib = request.security(syminfo.tickerid, resFib, low[1], lookahead=barmerge.lookahead_on)
    [highPriceFib, lowPriceFib]

// Function to get open price for a given timeframe
getOpenPriceFib(resFib) =>
    openPriceFib = request.security(syminfo.tickerid, resFib, open, lookahead=barmerge.lookahead_on)
    openPriceFib

// Function to get time for a given timeframe
getTimeForTimeframeFib(resFib) =>
    timeFib = request.security(syminfo.tickerid, resFib, time, lookahead=barmerge.lookahead_on)
    timeFib

// Function to detect new day open and draw vertical line
detectNewDayOpenFib(lastTimeRef) =>
    dayTime = getTimeForTimeframeFib("D")
    newDay = false
    newLastTime = lastTimeRef

    if dayTime != lastTimeRef and not na(dayTime)
        newDay := true
        newLastTime := dayTime

        // Draw vertical line at day open if enabled
        if showDayOpenLinesFib and barstate.islast
            // Limit the number of vertical lines to prevent overflow
            if array.size(dayOpenVerticalLines) >= maxHistoricalPeriodsFib
                if array.size(dayOpenVerticalLines) > 0
                    line.delete(array.shift(dayOpenVerticalLines))

            // Create vertical line at day open
            dayOpenLine = line.new(x1=bar_index, y1=low - (high - low) * 0.5, x2=bar_index, y2=high + (high - low) * 0.5,
                                 color=dayOpenLineColorFib, width=1, style=line.style_dashed)
            array.push(dayOpenVerticalLines, dayOpenLine)

    [newDay, newLastTime]

// Function to store historical Fibonacci levels
storeHistoricalFibLevelsFib(highValFib, lowValFib, timeframeFib, historicalLinesArrayFib) =>
    if showHistoricalLevelsFib and not na(highValFib) and not na(lowValFib)
        priceRangeFib = lowValFib - highValFib

        // Store each Fibonacci level as a historical line
        for iFib = 0 to array.size(fibLevelsFib) - 1
            if iFib < array.size(fibShowLevelsFib) and array.get(fibShowLevelsFib, iFib)
                levelFib = array.get(fibLevelsFib, iFib)
                priceLevelFib = highValFib + priceRangeFib * levelFib
                lineColorFib = array.get(fibColorsFib, iFib)

                // Limit array size to prevent overflow
                if array.size(historicalLinesArrayFib) >= maxHistoricalPeriodsFib * 7  // 7 Fibonacci levels per period
                    if array.size(historicalLinesArrayFib) > 0
                        line.delete(array.shift(historicalLinesArrayFib))

                // Create historical line from current bar to right edge
                histLineFib = line.new(x1=bar_index, y1=priceLevelFib, x2=bar_index + 500, y2=priceLevelFib,
                                     color=lineColorFib, style=getLineStyleFib(lineStyleFib), width=lineWidthFib,
                                     xloc=xloc.bar_index, extend=extend.none)
                array.push(historicalLinesArrayFib, histLineFib)

// Get pivots for each timeframe
[dHighFib, dLowFib] = getPivotsFib("D")
[wHighFib, wLowFib] = getPivotsFib("W")
[mHighFib, mLowFib] = getPivotsFib("M")
[qHighFib, qLowFib] = getPivotsFib("3M")
[yHighFib, yLowFib] = getPivotsFib("12M")

// Get open prices for each timeframe
dOpenFib = getOpenPriceFib("D")
wOpenFib = getOpenPriceFib("W")
mOpenFib = getOpenPriceFib("M")

// Detect new day open
// Using direct assignment to avoid variable redefinition
[newDayDetectedFib, lastDayOpenTime] = detectNewDayOpenFib(lastDayOpenTime)

// Function to plot Fibonacci levels with labels
plotFibLevelsFib(highValFib, lowValFib, showLevelsFib, suffixFib, lookbackFib, rightExtendFib, leftExtendFib, labelsArrayFib, linesArrayFib, historicalLinesArrayFib) =>
    if enableFibDrawingsFib and showLevelsFib and not na(highValFib) and not na(lowValFib)
        // Store historical levels when a new day is detected
        if newDayDetectedFib and showHistoricalLevelsFib
            // Store current Fibonacci levels as historical before clearing
            for i = 0 to array.size(linesArrayFib) - 1
                currentLine = array.get(linesArrayFib, i)
                if not na(currentLine)
                    // Get the price level of the current line
                    priceLevelFib = line.get_y1(currentLine)

                    // Create a new historical line that ends at the current bar
                    if array.size(historicalLinesArrayFib) >= maxHistoricalPeriodsFib * 7
                        if array.size(historicalLinesArrayFib) > 0
                            line.delete(array.shift(historicalLinesArrayFib))

                    // Use the same color and style as the original line
                    // Pine Script doesn't have line.get_color, so we'll use a fixed color
                    startBarFib = line.get_x1(currentLine)

                    // Create historical line from start to current bar
                    histLineFib = line.new(x1=startBarFib, y1=priceLevelFib, x2=bar_index, y2=priceLevelFib,
                                         color=color.gray, style=getLineStyleFib(lineStyleFib), width=lineWidthFib,
                                         xloc=xloc.bar_index, extend=extend.none)
                    array.push(historicalLinesArrayFib, histLineFib)

        // Clear old labels and lines when on the last bar to prevent clutter
        if barstate.islast
            clearLabelsFib(labelsArrayFib)
            clearLinesFib(linesArrayFib)

        priceRangeFib = lowValFib - highValFib
        // Limit the number of Fibonacci levels to process to prevent array overflow
        maxLevels = math.min(array.size(fibLevelsFib), 7)  // We have 7 Fibonacci levels

        for iFib = 0 to maxLevels - 1
            // Check if this specific Fibonacci level is enabled
            if iFib < array.size(fibShowLevelsFib) and array.get(fibShowLevelsFib, iFib)
                levelFib = array.get(fibLevelsFib, iFib)
                priceLevelFib = highValFib + priceRangeFib * levelFib
                labelTextFib = array.get(levelLabelsFib, iFib) + suffixFib
                lineColorFib = array.get(fibColorsFib, iFib)

                // Draw line for lookback period with left extension
                startBarFib = math.max(0, bar_index - lookbackFib - leftExtendFib)
                endBarFib = bar_index + rightExtendFib

                // Create the line and store it in the array
                // Limit array size to prevent overflow
                if array.size(linesArrayFib) >= 100
                    if array.size(linesArrayFib) > 0
                        line.delete(array.shift(linesArrayFib))

                fibLineFib = line.new(x1=startBarFib,y1=priceLevelFib,x2=endBarFib,y2=priceLevelFib,color=lineColorFib,style=getLineStyleFib(lineStyleFib),width=lineWidthFib,xloc=xloc.bar_index,extend=extend.none)
                array.push(linesArrayFib, fibLineFib)

                // Check for overlapping levels
                thresholdFib = priceLevelFib * 0.0005  // 0.05% threshold for overlap detection
                [isOverlapFib, existingLabelFib, overlapIndexFib] = isOverlappingFib(priceLevelFib, thresholdFib)

                // Add label at the right side of the chart only on the last bar
                if barstate.islast
                    finalLabelTextFib = labelTextFib

                    // If overlapping with another level, combine the labels
                    if isOverlapFib
                        finalLabelTextFib := existingLabelFib + " + " + labelTextFib
                        // Update the existing label in the tracking array
                        if overlapIndexFib >= 0
                            array.set(allLabelTextsFib, overlapIndexFib, finalLabelTextFib)
                    else
                        // Add this level to the tracking arrays
                        addPriceLevelFib(priceLevelFib, labelTextFib)

                    // Limit label array size to prevent overflow
                    if array.size(labelsArrayFib) >= 100
                        if array.size(labelsArrayFib) > 0
                            label.delete(array.shift(labelsArrayFib))

                    newLabelFib = label.new(x=endBarFib,y=priceLevelFib,text=finalLabelTextFib,style=label.style_label_left,color=color.rgb(0, 0, 0, 100),textcolor=lineColorFib,size=size.normal)
                    // Store the label in the array for future management
                    array.push(labelsArrayFib, newLabelFib)

// Function to plot open price levels with labels
plotOpenLevelFib(openPriceFib, showLevelFib, labelTextFib, lookbackFib, rightExtendFib, leftExtendFib, labelsArrayFib, linesArrayFib, colorFib, historicalLinesArrayFib) =>
    if showOpenLevelsFib and showLevelFib and not na(openPriceFib)
        // Store historical open levels when a new day is detected
        if newDayDetectedFib and showHistoricalLevelsFib
            // Store current open price levels as historical before clearing
            for i = 0 to array.size(linesArrayFib) - 1
                currentLine = array.get(linesArrayFib, i)
                if not na(currentLine)
                    // Get the price level of the current line
                    priceLevelFib = line.get_y1(currentLine)

                    // Create a new historical line that ends at the current bar
                    if array.size(historicalLinesArrayFib) >= maxHistoricalPeriodsFib
                        if array.size(historicalLinesArrayFib) > 0
                            line.delete(array.shift(historicalLinesArrayFib))

                    // Use the same color and style as the original line
                    // Pine Script doesn't have line.get_color, so we'll use the array color
                    startBarFib = line.get_x1(currentLine)

                    // Create historical line from start to current bar
                    histLineFib = line.new(x1=startBarFib, y1=priceLevelFib, x2=bar_index, y2=priceLevelFib,
                                         color=colorFib, style=getLineStyleFib(lineStyleFib), width=lineWidthFib,
                                         xloc=xloc.bar_index, extend=extend.none)
                    array.push(historicalLinesArrayFib, histLineFib)

        // Clear old labels and lines when on the last bar to prevent clutter
        if barstate.islast
            clearLabelsFib(labelsArrayFib)
            clearLinesFib(linesArrayFib)

        // Draw line for lookback period with left extension
        startBarFib = math.max(0, bar_index - lookbackFib - leftExtendFib)
        endBarFib = bar_index + rightExtendFib

        // Create the line and store it in the array
        // Limit array size to prevent overflow
        if array.size(linesArrayFib) >= 100
            if array.size(linesArrayFib) > 0
                line.delete(array.shift(linesArrayFib))

        openLineFib = line.new(x1=startBarFib, y1=openPriceFib, x2=endBarFib, y2=openPriceFib,
                             color=colorFib, style=getLineStyleFib(lineStyleFib), width=lineWidthFib,
                             xloc=xloc.bar_index, extend=extend.none)
        array.push(linesArrayFib, openLineFib)

        // Check for overlapping levels
        thresholdFib = openPriceFib * 0.0005  // 0.05% threshold for overlap detection
        [isOverlapFib, existingLabelFib, overlapIndexFib] = isOverlappingFib(openPriceFib, thresholdFib)

        // Add label at the right side of the chart only on the last bar
        if barstate.islast
            finalLabelTextFib = labelTextFib

            // If overlapping with another level, combine the labels
            if isOverlapFib
                finalLabelTextFib := existingLabelFib + " + " + labelTextFib
                // Update the existing label in the tracking array
                if overlapIndexFib >= 0
                    array.set(allLabelTextsFib, overlapIndexFib, finalLabelTextFib)
            else
                // Add this level to the tracking arrays
                addPriceLevelFib(openPriceFib, labelTextFib)

            // Limit label array size to prevent overflow
            if array.size(labelsArrayFib) >= 100
                if array.size(labelsArrayFib) > 0
                    label.delete(array.shift(labelsArrayFib))

            newLabelFib = label.new(x=endBarFib, y=openPriceFib, text=finalLabelTextFib,
                                  style=label.style_label_left, color=color.rgb(0, 0, 0, 100),
                                  textcolor=colorFib, size=size.normal)
            // Store the label in the array for future management
            array.push(labelsArrayFib, newLabelFib)

// Clear price level tracking arrays at the start of each bar
if barstate.islast
    clearPriceLevelsFib()

// Plot all Fibonacci levels with 500 candles lookback
plotFibLevelsFib(dHighFib, dLowFib, showDailyFib, " D", dailyLookbackFib, dailyExtendRightFib, dailyExtendLeftFib, dailyLabelsFib, dailyLinesFib, dailyHistoricalLinesFib)
plotFibLevelsFib(wHighFib, wLowFib, showWeeklyFib, " W", weeklyLookbackFib, weeklyExtendRightFib, weeklyExtendLeftFib, weeklyLabelsFib, weeklyLinesFib, weeklyHistoricalLinesFib)
plotFibLevelsFib(mHighFib, mLowFib, showMonthlyFib, " M", monthlyLookbackFib, monthlyExtendRightFib, monthlyExtendLeftFib, monthlyLabelsFib, monthlyLinesFib, monthlyHistoricalLinesFib)
plotFibLevelsFib(qHighFib, qLowFib, showQuarterlyFib, " Q", quarterlyLookbackFib, quarterlyExtendRightFib, quarterlyExtendLeftFib, quarterlyLabelsFib, quarterlyLinesFib, quarterlyHistoricalLinesFib)
plotFibLevelsFib(yHighFib, yLowFib, showYearlyFib, " Y", yearlyLookbackFib, yearlyExtendRightFib, yearlyExtendLeftFib, yearlyLabelsFib, yearlyLinesFib, yearlyHistoricalLinesFib)

// Plot open price levels
plotOpenLevelFib(dOpenFib, showDailyOpenFib, "DO", dailyLookbackFib, dailyExtendRightFib, dailyExtendLeftFib, dailyOpenLabelsFib, dailyOpenLinesFib, dailyOpenColorFib, dailyOpenHistoricalLinesFib)
plotOpenLevelFib(wOpenFib, showWeeklyOpenFib, "WO", weeklyLookbackFib, weeklyExtendRightFib, weeklyExtendLeftFib, weeklyOpenLabelsFib, weeklyOpenLinesFib, weeklyOpenColorFib, weeklyOpenHistoricalLinesFib)
plotOpenLevelFib(mOpenFib, showMonthlyOpenFib, "MO", monthlyLookbackFib, monthlyExtendRightFib, monthlyExtendLeftFib, monthlyOpenLabelsFib, monthlyOpenLinesFib, monthlyOpenColorFib, monthlyOpenHistoricalLinesFib)