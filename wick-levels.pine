//@version=5
indicator("HTF Large Wick Levels", "HTF Wick Levels", overlay=true, max_lines_count=500, max_labels_count=500)

// === TIMEFRAME SETTINGS ===
show_4h = input.bool(true, "Show 4H Wick Levels", group="Timeframes")
show_daily = input.bool(true, "Show Daily Wick Levels", group="Timeframes")
show_weekly = input.bool(true, "Show Weekly Wick Levels", group="Timeframes")
show_monthly = input.bool(true, "Show Monthly Wick Levels", group="Timeframes")
show_quarterly = input.bool(false, "Show Quarterly Wick Levels", group="Timeframes")

// === DETECTION PARAMETERS ===
wick_threshold_4h = input.float(2.0, "4H Wick Threshold (ATR multiplier)", minval=0.1, maxval=10.0, group="Detection Settings")
wick_threshold_daily = input.float(2.5, "Daily Wick Threshold (ATR multiplier)", minval=0.1, maxval=10.0, group="Detection Settings")
wick_threshold_weekly = input.float(3.0, "Weekly Wick Threshold (ATR multiplier)", minval=0.1, maxval=10.0, group="Detection Settings")
wick_threshold_monthly = input.float(3.5, "Monthly Wick Threshold (ATR multiplier)", minval=0.1, maxval=10.0, group="Detection Settings")
wick_threshold_quarterly = input.float(4.0, "Quarterly Wick Threshold (ATR multiplier)", minval=0.1, maxval=10.0, group="Detection Settings")
atr_length = input.int(14, "ATR Length", minval=1, maxval=100, group="Detection Settings")
max_wicks_per_tf = input.int(5, "Max Wicks per Timeframe", minval=1, maxval=20, group="Detection Settings")

// === DRAWING PARAMETERS ===
line_width = input.int(1, "Line Width", minval=1, maxval=5, group="Style Settings")
line_style = input.string("Solid", "Line Style", options=["Solid", "Dashed", "Dotted"], group="Style Settings")
extend_right = input.int(100, "Extend Lines Right", minval=0, maxval=500, group="Style Settings")
extend_left = input.int(20, "Extend Lines Left", minval=0, maxval=100, group="Style Settings")
show_labels = input.bool(true, "Show Labels", group="Style Settings")
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group="Style Settings")

// === TIMEFRAME COLORS ===
tf_4h_color = input.color(color.blue, "4H Levels Color", group="Timeframe Colors")
tf_daily_color = input.color(color.green, "Daily Levels Color", group="Timeframe Colors")
tf_weekly_color = input.color(color.orange, "Weekly Levels Color", group="Timeframe Colors")
tf_monthly_color = input.color(color.red, "Monthly Levels Color", group="Timeframe Colors")
tf_quarterly_color = input.color(color.purple, "Quarterly Levels Color", group="Timeframe Colors")

// === FUNCTIONS ===
// Convert string style to Pine line style
getLineStyle(style) =>
    switch style
        "Solid" => line.style_solid
        "Dotted" => line.style_dotted
        "Dashed" => line.style_dashed
        => line.style_solid

// Convert label size string to Pine label size
getLabelSize(size) =>
    switch size
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        => size.small

// Get HTF data for wick detection
getHTFData(tf) =>
    htf_high = request.security(syminfo.tickerid, tf, high, lookahead=barmerge.lookahead_off)
    htf_low = request.security(syminfo.tickerid, tf, low, lookahead=barmerge.lookahead_off)
    htf_open = request.security(syminfo.tickerid, tf, open, lookahead=barmerge.lookahead_off)
    htf_close = request.security(syminfo.tickerid, tf, close, lookahead=barmerge.lookahead_off)
    htf_atr = request.security(syminfo.tickerid, tf, ta.atr(atr_length), lookahead=barmerge.lookahead_off)
    htf_time = request.security(syminfo.tickerid, tf, time, lookahead=barmerge.lookahead_off)
    [htf_high, htf_low, htf_open, htf_close, htf_atr, htf_time]

// Detect large wicks for HTF
detectHTFLargeWick(htf_high, htf_low, htf_open, htf_close, htf_atr, threshold) =>
    upper_wick = htf_high - math.max(htf_open, htf_close)
    lower_wick = math.min(htf_open, htf_close) - htf_low

    large_upper_wick = upper_wick > (htf_atr * threshold)
    large_lower_wick = lower_wick > (htf_atr * threshold)

    [large_upper_wick, large_lower_wick, upper_wick, lower_wick]

// Draw HTF wick levels
drawHTFWickLevels(is_upper_wick, wick_start, wick_end, tf_name, tf_color, htf_time) =>
    // Calculate level prices
    level_0 = wick_start
    level_25 = wick_start + (wick_end - wick_start) * 0.25
    level_50 = wick_start + (wick_end - wick_start) * 0.50
    level_75 = wick_start + (wick_end - wick_start) * 0.75
    level_100 = wick_end

    // Calculate line positions
    start_bar = math.max(0, bar_index - extend_left)
    end_bar = bar_index + extend_right

    line_style_value = getLineStyle(line_style)
    label_size_value = getLabelSize(label_size)

    // Create different transparency levels for different levels
    level_0_color_tf = color.new(tf_color, 0)
    level_25_color_tf = color.new(tf_color, 20)
    level_50_color_tf = color.new(tf_color, 10)
    level_75_color_tf = color.new(tf_color, 20)
    level_100_color_tf = color.new(tf_color, 0)

    // Draw level lines
    line.new(start_bar, level_0, end_bar, level_0,
             color=level_0_color_tf, width=line_width, style=line_style_value, extend=extend.right)
    line.new(start_bar, level_25, end_bar, level_25,
             color=level_25_color_tf, width=line_width, style=line_style_value, extend=extend.right)
    line.new(start_bar, level_50, end_bar, level_50,
             color=level_50_color_tf, width=line_width + 1, style=line_style_value, extend=extend.right)
    line.new(start_bar, level_75, end_bar, level_75,
             color=level_75_color_tf, width=line_width, style=line_style_value, extend=extend.right)
    line.new(start_bar, level_100, end_bar, level_100,
             color=level_100_color_tf, width=line_width, style=line_style_value, extend=extend.right)

    // Draw labels if enabled
    if show_labels
        wick_type = is_upper_wick ? "U" : "L"
        label.new(end_bar, level_0, tf_name + " 0 (" + str.tostring(level_0, "#.#####") + ")",
                 style=label.style_label_left, color=color.new(color.black, 100),
                 textcolor=tf_color, size=label_size_value)
        label.new(end_bar, level_25, tf_name + " 0.25 (" + str.tostring(level_25, "#.#####") + ")",
                 style=label.style_label_left, color=color.new(color.black, 100),
                 textcolor=tf_color, size=label_size_value)
        label.new(end_bar, level_50, tf_name + " 0.5 (" + str.tostring(level_50, "#.#####") + ")",
                 style=label.style_label_left, color=color.new(color.black, 100),
                 textcolor=tf_color, size=label_size_value)
        label.new(end_bar, level_75, tf_name + " 0.75 (" + str.tostring(level_75, "#.#####") + ")",
                 style=label.style_label_left, color=color.new(color.black, 100),
                 textcolor=tf_color, size=label_size_value)
        label.new(end_bar, level_100, tf_name + " 1 (" + str.tostring(level_100, "#.#####") + ")",
                 style=label.style_label_left, color=color.new(color.black, 100),
                 textcolor=tf_color, size=label_size_value)

// Process HTF wick detection
processHTFWicks(tf, tf_name, tf_color, threshold, show_tf) =>
    if show_tf
        [htf_high, htf_low, htf_open, htf_close, htf_atr, htf_time] = getHTFData(tf)
        [large_upper, large_lower, upper_wick_size, lower_wick_size] = detectHTFLargeWick(htf_high, htf_low, htf_open, htf_close, htf_atr, threshold)

        // Check if this is a new HTF candle
        htf_time_changed = ta.change(htf_time) != 0

        // Process large upper wicks
        if large_upper and htf_time_changed
            body_top = math.max(htf_open, htf_close)
            wick_start = body_top
            wick_end = htf_high
            drawHTFWickLevels(true, wick_start, wick_end, tf_name, tf_color, htf_time)

        // Process large lower wicks
        if large_lower and htf_time_changed
            body_bottom = math.min(htf_open, htf_close)
            wick_start = htf_low
            wick_end = body_bottom
            drawHTFWickLevels(false, wick_start, wick_end, tf_name, tf_color, htf_time)

// === MAIN LOGIC ===
// Process each timeframe
processHTFWicks("240", "4H", tf_4h_color, wick_threshold_4h, show_4h)
processHTFWicks("1D", "D", tf_daily_color, wick_threshold_daily, show_daily)
processHTFWicks("1W", "W", tf_weekly_color, wick_threshold_weekly, show_weekly)
processHTFWicks("1M", "M", tf_monthly_color, wick_threshold_monthly, show_monthly)
processHTFWicks("3M", "Q", tf_quarterly_color, wick_threshold_quarterly, show_quarterly)

// === INFORMATION TABLE ===
if barstate.islast
    var table info_table = table.new(position.top_right, 3, 8, bgcolor=color.white, border_width=1)

    // Header
    table.cell(info_table, 0, 0, "HTF Wick Levels", text_color=color.black, text_size=size.normal)
    table.cell(info_table, 1, 0, "Threshold", text_color=color.black, text_size=size.normal)
    table.cell(info_table, 2, 0, "Status", text_color=color.black, text_size=size.normal)

    // 4H Row
    table.cell(info_table, 0, 1, "4H", text_color=tf_4h_color, text_size=size.small)
    table.cell(info_table, 1, 1, str.tostring(wick_threshold_4h, "#.#"), text_color=color.black, text_size=size.small)
    table.cell(info_table, 2, 1, show_4h ? "ON" : "OFF", text_color=show_4h ? color.green : color.red, text_size=size.small)

    // Daily Row
    table.cell(info_table, 0, 2, "Daily", text_color=tf_daily_color, text_size=size.small)
    table.cell(info_table, 1, 2, str.tostring(wick_threshold_daily, "#.#"), text_color=color.black, text_size=size.small)
    table.cell(info_table, 2, 2, show_daily ? "ON" : "OFF", text_color=show_daily ? color.green : color.red, text_size=size.small)

    // Weekly Row
    table.cell(info_table, 0, 3, "Weekly", text_color=tf_weekly_color, text_size=size.small)
    table.cell(info_table, 1, 3, str.tostring(wick_threshold_weekly, "#.#"), text_color=color.black, text_size=size.small)
    table.cell(info_table, 2, 3, show_weekly ? "ON" : "OFF", text_color=show_weekly ? color.green : color.red, text_size=size.small)

    // Monthly Row
    table.cell(info_table, 0, 4, "Monthly", text_color=tf_monthly_color, text_size=size.small)
    table.cell(info_table, 1, 4, str.tostring(wick_threshold_monthly, "#.#"), text_color=color.black, text_size=size.small)
    table.cell(info_table, 2, 4, show_monthly ? "ON" : "OFF", text_color=show_monthly ? color.green : color.red, text_size=size.small)

    // Quarterly Row
    table.cell(info_table, 0, 5, "Quarterly", text_color=tf_quarterly_color, text_size=size.small)
    table.cell(info_table, 1, 5, str.tostring(wick_threshold_quarterly, "#.#"), text_color=color.black, text_size=size.small)
    table.cell(info_table, 2, 5, show_quarterly ? "ON" : "OFF", text_color=show_quarterly ? color.green : color.red, text_size=size.small)

    // Current ATR info
    current_atr = ta.atr(atr_length)
    table.cell(info_table, 0, 6, "Current ATR:", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 6, str.tostring(current_atr, "#.#####"), text_color=color.black, text_size=size.small)
    table.cell(info_table, 2, 6, "", text_color=color.black, text_size=size.small)

    // Legend
    table.cell(info_table, 0, 7, "Levels: 0, 0.25, 0.5, 0.75, 1", text_color=color.gray, text_size=size.tiny)
    table.cell(info_table, 1, 7, "", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 2, 7, "", text_color=color.black, text_size=size.tiny)



// you have to consider the sourrounding candle and then detect the large wick candle among them and then store those levels in an array and draw them on 1 min time frame . as the price moves the lines should also move. also learn bit about the ict wick theory and use those standars