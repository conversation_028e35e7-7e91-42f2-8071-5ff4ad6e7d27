// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © augmentcode

//@version=5
indicator("True Day Open (4:00 UTC)", "True Day Open (4:00 UTC)", overlay=true, max_lines_count=500)

// ---------------------------------------- Inputs --------------------------------------------------
var g_SETTINGS = "Settings"
max_days = input.int(30, "Maximum Lines", 1, tooltip = "Maximum number of vertical lines to display", group = g_SETTINGS)
gmt_tz = input.string('GMT+0', "Timezone", options = ['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], tooltip = "Select timezone for reference (True Day Open is fixed at 4:00 UTC)", group = g_SETTINGS)
show_labels = input.bool(true, "Show Date Labels", tooltip = "Display date labels at the top of each vertical line", group = g_SETTINGS)
label_size = input.string('Small', "Label Size", options = ['Tiny', 'Small', 'Normal', 'Large', 'Huge'], tooltip = "Size of date labels", group = g_SETTINGS)
label_color = input.color(color.white, "Label Color", tooltip = "Color of date labels", group = g_SETTINGS)

var g_LINE = "Line Style"
line_color = input.color(color.blue, "Line Color", tooltip = "Color of the vertical day open lines", group = g_LINE)
line_style = input.string("Solid", "Line Style", options = ["Solid", "Dotted", "Dashed"], tooltip = "Style of the vertical day open lines", group = g_LINE)
line_width = input.int(1, "Line Width", minval=1, maxval=4, tooltip = "Width of the vertical day open lines", group = g_LINE)

// ---------------------------------------- Functions --------------------------------------------------
get_line_style(style) =>
    result = switch style
        'Solid' => line.style_solid
        'Dotted' => line.style_dotted
        'Dashed' => line.style_dashed
    result

get_label_size(size) =>
    result = switch size
        'Tiny' => size.tiny
        'Small' => size.small
        'Normal' => size.normal
        'Large' => size.large
        'Huge' => size.huge
    result

// ---------------------------------------- Variables & Constants --------------------------------------------------
var line_style_setting = get_line_style(line_style)
var label_size_setting = get_label_size(label_size)
var day_open_lines = array.new_line()
var day_open_labels = array.new_label()

// ---------------------------------------- Core Logic --------------------------------------------------
// Check if we're at the true day open (4:00 UTC)
is_new_day = not na(time("D", "0400-0401", "GMT+0")) and na(time("D", "0400-0401", "GMT+0"))[1]

// Draw vertical line at true day open (4:00 UTC)
if is_new_day
    // Create vertical line
    line_obj = line.new(
        x1=bar_index,
        y1=low * 0.999,
        x2=bar_index,
        y2=high * 1.001,
        color=line_color,
        style=line_style_setting,
        width=line_width,
        extend=extend.both
    )
    array.unshift(day_open_lines, line_obj)

    // Create date label if enabled
    if show_labels
        current_date = str.format("{0}", dayofmonth) + "/" + str.format("{0}", month) + " (4:00 UTC)"
        label_obj = label.new(
            x=bar_index,
            y=high * 1.002,
            text=current_date,
            color=color.new(color.black, 100),
            style=label.style_label_down,
            textcolor=label_color,
            size=label_size_setting
        )
        array.unshift(day_open_labels, label_obj)

    // Remove old lines if we exceed the maximum
    if array.size(day_open_lines) > max_days
        array.pop(day_open_lines).delete()

        if show_labels and array.size(day_open_labels) > max_days
            array.pop(day_open_labels).delete()

// Plot a small dot at 4:00 UTC for debugging/reference
plotchar(is_new_day, char="•", location=location.top, color=line_color, size=size.tiny, text="4:00 UTC", textcolor=label_color)
