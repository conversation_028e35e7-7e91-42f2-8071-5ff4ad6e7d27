//@version=5
 indicator("Smart Money Interest Index [Ravi]"
   , overlay=true  Ensure the script is drawn on the main chart
   , max_boxes_count = 500
   , max_labels_count = 500
   , max_lines_count = 500)

x = input.int(25, "Index Period", minval = 1)
rr = input.int(14, "Volume Flow Period", minval = 1)
peakslen = input.int(500, "Normalization Period", minval = 1)
thr = input.float(0.9, "High Interest Threshold", minval = 0.01, maxval = 0.99)
green = input.color(#00ffbb, "Up Color")
red = input.color(#ff1100, "Down Color")

dumb = ta.pvi-ta.ema(ta.pvi,255)
smart = ta.nvi-ta.ema(ta.nvi,255)

drsi = ta.rsi(dumb, rr)
srsi = ta.rsi(smart, rr)

r = srsi/drsi //ratio shows if smart money is buying from dumb money selling and vice versa

sums = math.sum(r, x)
peak = ta.highest(sums, peakslen)

index = sums/peak

condition = index > thr
// barcolor(condition ? green : na)

// Main Panel Arrows
plotshape(series= condition ? 1 : na, title="High Smart Money Interest", color=color.rgb(233, 239, 233), style=shape.arrowup, size=size.normal, location=location.belowbar, force_overlay=true)

// Alert condition
alertcondition(condition=condition, title="High Smart Money Interest Alert", message="High Smart Money Interest detected!")