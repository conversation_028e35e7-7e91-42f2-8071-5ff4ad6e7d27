
//@version=5
const bool DEBUG = false
const int maxBoxesCount = 500
const float overlapThresholdPercentage = 0
const int maxDistanceToLastBar = 1750 // Affects Running Time
const int maxOrderBlocks = 30

indicator(title = 'SMC-VOB-S&R [Ravi]', overlay = true, max_boxes_count = maxBoxesCount, max_labels_count = maxBoxesCount, max_lines_count = maxBoxesCount, max_bars_back = 5000)

showInvalidated = input.bool(true, "Show Historic Zones", group = "General Configuration", display = display.none)
OBsEnabled = true
orderBlockVolumetricInfo = input.bool(true, "Volumetric Info", group = "General Configuration", inline="EV", display = display.none)
obEndMethod = input.string("Wick", "Zone Invalidation", options = ["Wick", "Close"],  group = "General Configuration", display = display.none)
combineOBs = DEBUG ? input.bool(true, "Combine Zones", group = "General Configuration", display = display.none) : true
maxATRMult = DEBUG ? input.float(3.5,"Max Atr Multiplier", group = "General Configuration") : 3.5
swingLength = input.int(10, 'Swing Length', minval = 3, tooltip="Swing length is used when finding order block formations. Smaller values will result in finding smaller order blocks.",group = "General Configuration", display = display.none)
zoneCount = input.string("Low", 'Zone Count', options = ["High", "Medium", "Low", "One"], tooltip = "Number of Order Block Zones to be rendered. Higher options will result in older Order Blocks shown.",  group = "General Configuration", display = display.none)
bullOrderBlockColor = input(#08998180, 'Bullish', inline = 'obColor', group = 'General Configuration', display = display.none)
bearOrderBlockColor = input(#f2364680, 'Bearish', inline = 'obColor', group = 'General Configuration', display = display.none)

bullishOrderBlocks = zoneCount == "One" ? 1 : zoneCount == "Low" ? 3 : zoneCount == "Medium" ? 5 : 10
bearishOrderBlocks = zoneCount == "One" ? 1 : zoneCount == "Low" ? 3 : zoneCount == "Medium" ? 5 : 10

timeframe1Enabled = true
timeframe1 = ""

textColor = input.color(#ffffff80, "Text Color", group = "Style")
extendZonesBy = DEBUG ? input.int(15, "Extend Zones", group = "Style", minval = 1, maxval = 30, inline = "ExtendZones") : 15
extendZonesDynamic = DEBUG ? input.bool(true, "Dynamic", group = "Style", inline = "ExtendZones") : true
combinedText = DEBUG ? input.bool(false, "Combined Text", group = "Style", inline = "CombinedColor") : false
volumeBarsPlace = DEBUG ? input.string("Left", "Show Volume Bars At", options = ["Left", "Right"], group = "Style", inline = "volumebars") : "Left"
mirrorVolumeBars = DEBUG ? input.bool(true, "Mirror Volume Bars", group = "Style", inline = "volumebars") : true

volumeBarsLeftSide = (volumeBarsPlace == "Left")
extendZonesByTime = extendZonesBy * timeframe.in_seconds(timeframe.period) * 1000

atr = ta.atr(10)

type orderBlockInfo
    float top
    float bottom
    float obVolume
    string obType
    int startTime
    float bbVolume
    float obLowVolume
    float obHighVolume
    bool breaker
    int breakTime
    string timeframeStr
    bool disabled = false
    string combinedTimeframesStr = na
    bool combined = false

type orderBlock
    orderBlockInfo info
    bool isRendered = false

    box orderBox = na
    box breakerBox = na

    line orderBoxLineTop = na
    line orderBoxLineBottom = na
    line breakerBoxLineTop = na
    line breakerBoxLineBottom = na
    //
    box orderBoxText = na
    box orderBoxPositive = na
    box orderBoxNegative = na

    line orderSeperator = na
    line orderTextSeperator = na

createOrderBlock (orderBlockInfo orderBlockInfoF) =>
    orderBlock newOrderBlock = orderBlock.new(orderBlockInfoF)
    newOrderBlock

safeDeleteOrderBlock (orderBlock orderBlockF) =>
    orderBlockF.isRendered := false

    box.delete(orderBlockF.orderBox)
    box.delete(orderBlockF.breakerBox)
    box.delete(orderBlockF.orderBoxText)
    box.delete(orderBlockF.orderBoxPositive)
    box.delete(orderBlockF.orderBoxNegative)

    line.delete(orderBlockF.orderBoxLineTop)
    line.delete(orderBlockF.orderBoxLineBottom)
    line.delete(orderBlockF.breakerBoxLineTop)
    line.delete(orderBlockF.breakerBoxLineBottom)
    line.delete(orderBlockF.orderSeperator)
    line.delete(orderBlockF.orderTextSeperator)

type timeframeInfo
    int index = na
    string timeframeStr = na
    bool isEnabled = false

    orderBlockInfo[] bullishOrderBlocksList = na
    orderBlockInfo[] bearishOrderBlocksList = na

newTimeframeInfo (index, timeframeStr, isEnabled) =>
    newTFInfo = timeframeInfo.new()
    newTFInfo.index := index
    newTFInfo.isEnabled := isEnabled
    newTFInfo.timeframeStr := timeframeStr

    newTFInfo

type obSwing
    int x = na    
    float y = na
    float swingVolume = na
    bool crossed = false

// ____ TYPES END ____

var timeframeInfo[] timeframeInfos = array.from(newTimeframeInfo(1, timeframe1, timeframe1Enabled))
var bullishOrderBlocksList = array.new<orderBlockInfo>(0)
var bearishOrderBlocksList = array.new<orderBlockInfo>(0)

var allOrderBlocksList = array.new<orderBlock>(0)

moveLine(_line, _x, _y, _x2) =>
    line.set_xy1(_line, _x,  _y)
    line.set_xy2(_line, _x2, _y)

moveBox (_box, _topLeftX, _topLeftY, _bottomRightX, _bottomRightY) =>
    box.set_lefttop(_box, _topLeftX, _topLeftY)
    box.set_rightbottom(_box, _bottomRightX, _bottomRightY)

isTimeframeLower (timeframe1F, timeframe2F) =>
    timeframe.in_seconds(timeframe1F) < timeframe.in_seconds(timeframe2F)

getMinTimeframe (timeframe1F, timeframe2F) =>
    if isTimeframeLower(timeframe1F, timeframe2F)
        timeframe1F
    else
        timeframe2F

getMaxTimeframe (timeframe1F, timeframe2F) =>
    if isTimeframeLower(timeframe1F, timeframe2F)
        timeframe2F
    else
        timeframe1F

formatTimeframeString (formatTimeframe) =>
    timeframeF = formatTimeframe == "" ? timeframe.period : formatTimeframe
    
    if str.contains(timeframeF, "D") or str.contains(timeframeF, "W") or str.contains(timeframeF, "S") or str.contains(timeframeF, "M")
        timeframeF
    else
        seconds = timeframe.in_seconds(timeframeF)
        if seconds >= 3600
            hourCount = int(seconds / 3600)
            str.tostring(hourCount) + " Hour" + (hourCount > 1 ? "s" : "")
        else
            timeframeF + " Min"

betterCross(s1, s2) =>
    string ret = na
    if s1 >= s2 and s1[1] < s2
        ret := "Bull"
    if s1 < s2 and s1[1] >= s2
        ret := "Bear"
    ret

colorWithTransparency (colorF, transparencyX) =>
    color.new(colorF, color.t(colorF) * transparencyX)

createOBBox (boxColor, transparencyX = 1.0, xlocType = xloc.bar_time) =>
    box.new(na, na, na, na, text_size = size.normal, xloc = xlocType, extend = extend.none, bgcolor = colorWithTransparency(boxColor, transparencyX), text_color = textColor, text_halign = text.align_center, border_color = #00000000)

renderOrderBlock (orderBlock ob) =>
    orderBlockInfo info = ob.info
    ob.isRendered := true
    orderColor = ob.info.obType == "Bull" ? bullOrderBlockColor : bearOrderBlockColor

    if OBsEnabled and (not false or not (false and info.breaker)) and not (not showInvalidated and info.breaker)
        ob.orderBox := createOBBox(orderColor, 1.5)
        if ob.info.combined
            ob.orderBox.set_bgcolor(colorWithTransparency(orderColor, 1.1))
        ob.orderBoxText := createOBBox(color.new(color.white, 100))
        if orderBlockVolumetricInfo
            ob.orderBoxPositive := createOBBox(bullOrderBlockColor)
            ob.orderBoxNegative := createOBBox(bearOrderBlockColor)
            ob.orderSeperator := line.new(na,na,na,na,xloc.bar_time,extend.none,textColor,line.style_dashed,1)
            ob.orderTextSeperator := line.new(na,na,na,na,xloc.bar_time,extend.none,textColor,line.style_solid,1)

        zoneSize = extendZonesDynamic ? na(info.breakTime) ? extendZonesByTime : (info.breakTime - info.startTime) : extendZonesByTime
        if na(info.breakTime)
            zoneSize := (time + 1) - info.startTime

        startX = volumeBarsLeftSide ? info.startTime : info.startTime + zoneSize - zoneSize / 3
        maxEndX = volumeBarsLeftSide ? info.startTime + zoneSize / 3 : info.startTime + zoneSize

        moveBox(ob.orderBox, info.startTime, info.top, info.startTime + zoneSize, info.bottom)
        moveBox(ob.orderBoxText, volumeBarsLeftSide ? maxEndX : info.startTime, info.top, volumeBarsLeftSide ? info.startTime + zoneSize : startX, info.bottom)

        percentage = int((math.min(info.obHighVolume, info.obLowVolume) / math.max(info.obHighVolume, info.obLowVolume)) * 100.0)
        OBText = (na(ob.info.combinedTimeframesStr) ? formatTimeframeString(ob.info.timeframeStr) : ob.info.combinedTimeframesStr) + " OB"
        box.set_text(ob.orderBoxText, (orderBlockVolumetricInfo ? str.tostring(ob.info.obVolume, format.volume) + " (" + str.tostring(percentage) + "%)\n" : "") + (combinedText and ob.info.combined ? "[Combined]\n" : "") + OBText)

        if orderBlockVolumetricInfo
            showHighLowBoxText = false

            curEndXHigh = int(math.ceil((info.obHighVolume / info.obVolume) * (maxEndX - startX) + startX))
            curEndXLow = int(math.ceil((info.obLowVolume / info.obVolume) * (maxEndX - startX) + startX))

            moveBox(ob.orderBoxPositive, mirrorVolumeBars ? startX : curEndXLow, info.top, mirrorVolumeBars ? curEndXHigh : maxEndX, (info.bottom + info.top) / 2)
            box.set_text(ob.orderBoxPositive, showHighLowBoxText ? str.tostring(info.obHighVolume, format.volume) : "")

            moveBox(ob.orderBoxNegative, mirrorVolumeBars ? startX : curEndXHigh, info.bottom, mirrorVolumeBars ? curEndXLow : maxEndX, (info.bottom + info.top) / 2)
            box.set_text(ob.orderBoxNegative, showHighLowBoxText ? str.tostring(info.obLowVolume, format.volume) : "")

            moveLine(ob.orderSeperator, volumeBarsLeftSide ? startX : maxEndX, (info.bottom + info.top) / 2, volumeBarsLeftSide ? maxEndX : startX)

            line.set_xy1(ob.orderTextSeperator, volumeBarsLeftSide ? maxEndX : startX, info.top)
            line.set_xy2(ob.orderTextSeperator, volumeBarsLeftSide ? maxEndX : startX, info.bottom)

findOBSwings(len) =>
    var swingType = 0
    var obSwing top = obSwing.new(na, na)
    var obSwing bottom = obSwing.new(na, na)
    
    upper = ta.highest(len)
    lower = ta.lowest(len)

    swingType := high[len] > upper ? 0 : low[len] < lower ? 1 : swingType

    if swingType == 0 and swingType[1] != 0
        top := obSwing.new(bar_index[len], high[len], volume[len])
    
    if swingType == 1 and swingType[1] != 1
        bottom := obSwing.new(bar_index[len], low[len], volume[len])

    [top, bottom]

findOrderBlocks () =>
    if bar_index > last_bar_index - maxDistanceToLastBar
        [top, btm] = findOBSwings(swingLength)
        useBody = false
        max = useBody ? math.max(close, open) : high
        min = useBody ? math.min(close, open) : low

        // Bullish Order Block
        bullishBreaked = 0

        if bullishOrderBlocksList.size() > 0
            for i = bullishOrderBlocksList.size() - 1 to 0
                currentOB = bullishOrderBlocksList.get(i)
            
                if not currentOB.breaker 
                    if (obEndMethod == "Wick" ? low : math.min(open, close)) < currentOB.bottom
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if high > currentOB.top
                        bullishOrderBlocksList.remove(i)
                    else if i < bullishOrderBlocks and top.y < currentOB.top and top.y > currentOB.bottom 
                        bullishBreaked := 1

        if close > top.y and not top.crossed
            top.crossed := true

            boxBtm = max[1]
            boxTop = min[1]
            boxLoc = time[1]

            for i = 1 to (bar_index - top.x) - 1
                boxBtm := math.min(min[i], boxBtm)
                boxTop := boxBtm == min[i] ? max[i] : boxTop
                boxLoc := boxBtm == min[i] ? time[i] : boxLoc

            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bull", boxLoc)
            newOrderBlockInfo.obLowVolume := volume[2]
            newOrderBlockInfo.obHighVolume := volume + volume[1]
            
            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr * maxATRMult
                bullishOrderBlocksList.unshift(newOrderBlockInfo)
                if bullishOrderBlocksList.size() > maxOrderBlocks
                    bullishOrderBlocksList.pop()

        // Bearish Order Block
        
        bearishBreaked = 0

        if bearishOrderBlocksList.size() > 0
            for i = bearishOrderBlocksList.size() - 1 to 0
                currentOB = bearishOrderBlocksList.get(i)

                if not currentOB.breaker 
                    if (obEndMethod == "Wick" ? high : math.max(open, close)) > currentOB.top
                        currentOB.breaker := true
                        currentOB.breakTime := time
                        currentOB.bbVolume := volume
                else
                    if low < currentOB.bottom
                        bearishOrderBlocksList.remove(i)
                    else if i < bearishOrderBlocks and btm.y > currentOB.bottom and btm.y < currentOB.top 
                        bearishBreaked := 1

        if close < btm.y and not btm.crossed
            btm.crossed := true

            boxBtm = min[1]
            boxTop = max[1]
            boxLoc = time[1]

            for i = 1 to (bar_index - btm.x) - 1
                boxTop := math.max(max[i], boxTop)
                boxBtm := boxTop == max[i] ? min[i] : boxBtm
                boxLoc := boxTop == max[i] ? time[i] : boxLoc

            newOrderBlockInfo = orderBlockInfo.new(boxTop, boxBtm, volume + volume[1] + volume[2], "Bear", boxLoc)
            newOrderBlockInfo.obLowVolume := volume + volume[1]
            newOrderBlockInfo.obHighVolume := volume[2]

            obSize = math.abs(newOrderBlockInfo.top - newOrderBlockInfo.bottom)
            if obSize <= atr * maxATRMult
                bearishOrderBlocksList.unshift(newOrderBlockInfo)
                if bearishOrderBlocksList.size() > maxOrderBlocks
                    bearishOrderBlocksList.pop()
    true

areaOfOB (orderBlockInfo OBInfoF) =>
    float XA1 = OBInfoF.startTime
    float XA2 = na(OBInfoF.breakTime) ? time + 1 : OBInfoF.breakTime
    float YA1 = OBInfoF.top
    float YA2 = OBInfoF.bottom
    float edge1 = math.sqrt((XA2 - XA1) * (XA2 - XA1) + (YA2 - YA2) * (YA2 - YA2))
    float edge2 = math.sqrt((XA2 - XA2) * (XA2 - XA2) + (YA2 - YA1) * (YA2 - YA1))
    float totalArea = edge1 * edge2
    totalArea

doOBsTouch (orderBlockInfo OBInfo1, orderBlockInfo OBInfo2) =>
    float XA1 = OBInfo1.startTime
    float XA2 = na(OBInfo1.breakTime) ? time + 1 : OBInfo1.breakTime
    float YA1 = OBInfo1.top
    float YA2 = OBInfo1.bottom

    float XB1 = OBInfo2.startTime
    float XB2 = na(OBInfo2.breakTime) ? time + 1 : OBInfo2.breakTime
    float YB1 = OBInfo2.top
    float YB2 = OBInfo2.bottom
    float intersectionArea = math.max(0, math.min(XA2, XB2) - math.max(XA1, XB1)) * math.max(0, math.min(YA1, YB1) - math.max(YA2, YB2))
    float unionArea = areaOfOB(OBInfo1) + areaOfOB(OBInfo2) - intersectionArea
    
    float overlapPercentage = (intersectionArea / unionArea) * 100.0

    if overlapPercentage > overlapThresholdPercentage
        true
    else
        false

isOBValid (orderBlockInfo OBInfo) =>
    valid = true
    if OBInfo.disabled
        valid := false
    valid

combineOBsFunc () =>
    if allOrderBlocksList.size() > 0
        lastCombinations = 999
        while lastCombinations > 0
            lastCombinations := 0
            for i = 0 to allOrderBlocksList.size() - 1
                curOB1 = allOrderBlocksList.get(i)
                for j = 0 to allOrderBlocksList.size() - 1
                    curOB2 = allOrderBlocksList.get(j)
                    if i == j
                        continue
                    if not isOBValid(curOB1.info) or not isOBValid(curOB2.info)
                        continue
                    if curOB1.info.obType != curOB2.info.obType
                        continue
                    if doOBsTouch(curOB1.info, curOB2.info)
                        curOB1.info.disabled := true
                        curOB2.info.disabled := true
                        orderBlock newOB = createOrderBlock(orderBlockInfo.new(math.max(curOB1.info.top, curOB2.info.top), math.min(curOB1.info.bottom, curOB2.info.bottom), curOB1.info.obVolume + curOB2.info.obVolume, curOB1.info.obType))
                        newOB.info.startTime := math.min(curOB1.info.startTime, curOB2.info.startTime)
                        newOB.info.breakTime := math.max(nz(curOB1.info.breakTime), nz(curOB2.info.breakTime))
                        newOB.info.breakTime := newOB.info.breakTime == 0 ? na : newOB.info.breakTime
                        newOB.info.timeframeStr := curOB1.info.timeframeStr

                        newOB.info.obVolume := curOB1.info.obVolume + curOB2.info.obVolume
                        newOB.info.obLowVolume := curOB1.info.obLowVolume + curOB2.info.obLowVolume
                        newOB.info.obHighVolume := curOB1.info.obHighVolume + curOB2.info.obHighVolume
                        newOB.info.bbVolume := nz(curOB1.info.bbVolume, 0) + nz(curOB2.info.bbVolume, 0)
                        newOB.info.breaker := curOB1.info.breaker or curOB2.info.breaker
                        
                        newOB.info.combined := true
                        if timeframe.in_seconds(curOB1.info.timeframeStr) != timeframe.in_seconds(curOB2.info.timeframeStr)
                            newOB.info.combinedTimeframesStr := (na(curOB1.info.combinedTimeframesStr) ? formatTimeframeString(curOB1.info.timeframeStr) : curOB1.info.combinedTimeframesStr) + " & " + (na(curOB2.info.combinedTimeframesStr) ? formatTimeframeString(curOB2.info.timeframeStr) : curOB2.info.combinedTimeframesStr)
                        allOrderBlocksList.unshift(newOB)
                        lastCombinations += 1


reqSeq (timeframeStr) =>
    [bullishOrderBlocksListF, bearishOrderBlocksListF] = request.security(syminfo.tickerid, timeframeStr, [bullishOrderBlocksList, bearishOrderBlocksList])
    [bullishOrderBlocksListF, bearishOrderBlocksListF]

getTFData (timeframeInfo timeframeInfoF, timeframeStr) =>
    if not isTimeframeLower(timeframeInfoF.timeframeStr, timeframe.period) and timeframeInfoF.isEnabled
        [bullishOrderBlocksListF, bearishOrderBlocksListF] = reqSeq(timeframeStr)
        [bullishOrderBlocksListF, bearishOrderBlocksListF]
    else
        [na, na]

handleTimeframeInfo (timeframeInfo timeframeInfoF, bullishOrderBlocksListF, bearishOrderBlocksListF) =>
    if not isTimeframeLower(timeframeInfoF.timeframeStr, timeframe.period) and timeframeInfoF.isEnabled
        timeframeInfoF.bullishOrderBlocksList := bullishOrderBlocksListF
        timeframeInfoF.bearishOrderBlocksList := bearishOrderBlocksListF


handleOrderBlocksFinal () =>
    if DEBUG
        log.info("Bullish OB Count " + str.tostring(bullishOrderBlocksList.size()))
        log.info("Bearish OB Count " + str.tostring(bearishOrderBlocksList.size()))

    if allOrderBlocksList.size () > 0
        for i = 0 to allOrderBlocksList.size() - 1
            safeDeleteOrderBlock(allOrderBlocksList.get(i))
    allOrderBlocksList.clear()    

    for i = 0 to timeframeInfos.size() - 1
        curTimeframe = timeframeInfos.get(i)
        if not curTimeframe.isEnabled
            continue
        if curTimeframe.bullishOrderBlocksList.size() > 0
            for j = 0 to math.min(curTimeframe.bullishOrderBlocksList.size() - 1, bullishOrderBlocks - 1)
                orderBlockInfoF = curTimeframe.bullishOrderBlocksList.get(j)
                orderBlockInfoF.timeframeStr := curTimeframe.timeframeStr
                allOrderBlocksList.unshift(createOrderBlock(orderBlockInfo.copy(orderBlockInfoF)))

        if curTimeframe.bearishOrderBlocksList.size() > 0
            for j = 0 to math.min(curTimeframe.bearishOrderBlocksList.size() - 1, bearishOrderBlocks - 1)
                orderBlockInfoF = curTimeframe.bearishOrderBlocksList.get(j)
                orderBlockInfoF.timeframeStr := curTimeframe.timeframeStr
                allOrderBlocksList.unshift(createOrderBlock(orderBlockInfo.copy(orderBlockInfoF)))

    if combineOBs
        combineOBsFunc()    

    if allOrderBlocksList.size() > 0
        for i = 0 to allOrderBlocksList.size() - 1
            curOB = allOrderBlocksList.get(i)
            if isOBValid(curOB.info)
                renderOrderBlock(curOB)

findOrderBlocks()

[bullishOrderBlocksListTimeframe1, bearishOrderBlocksListTimeframe1] = getTFData(timeframeInfos.get(0), timeframe1)

if barstate.isconfirmed
    handleTimeframeInfo(timeframeInfos.get(0), bullishOrderBlocksListTimeframe1, bearishOrderBlocksListTimeframe1)
    handleOrderBlocksFinal()    


//@version=5
// ══════════════════════════════════════════════════════════════════════════════════════════════════ //
//# * ══════════════════════════════════════════════════════════════════════════════════════════════
//# *
//# * Study       : Price Action - Support & Resistance
//# *                - Support & Resistance levels, based on price and volume combination, additinally based on volume & volatility spikes 
//# *                - Sign of Exhaustion Indication - volume & volatility spikes
//# *                - Volume Profile - Common Interest & Bull/Bear Dominance 
//# *                - Supply & Demand Zones, based on traded volume over given period of time
//# *                - Volume Weighted Colored Bars
//# * Author      : © dgtrd
//# *
//# * Revision History
//# *  Release    : Jan 25, 2021
//# *  Update     : Jan 26, 2021  : User Request : Add price option for intruments with no volume data
//# *  Update     : Mar 18, 2021  : Ability to draw lines and cofigure alerts when volume spikes detected 
//# *  Update     : Mar 21, 2021  : Ability to draw lines and cofigure alerts when high volatility detected
//# *                                - line customization abaility 
//# *  Update     : Sep 03, 2021  : Ability to control number of lines drawn
//# *  Update     : Feb 21, 2022  : Added Volume Profile, inspired by Ildar Akhmetgaleev's 'Poor man's volume profile'
//# *  Update     : Mar 22, 2022  : Added Bull/Bear Dominance to Volume Profile
//# *  Update     : Apr 08, 2022  : Added Supply/Demand Zones, PoC for Volume Profile
//# *  Update     : Apr 26, 2022  : Value Area (VAL, VAH) addition
//# *  Update     : Jul 23, 2022  : visible range option added for S&R, VP and SD components 
//# *
//# * ══════════════════════════════════════════════════════════════════════════════════════════════
// ══════════════════════════════════════════════════════════════════════════════════════════════════ //

// indicator('Price Action - Support & Resistance by DGT', 'S&R ʙʏ DGT ☼☾', true, max_bars_back = 5000, max_lines_count = 500, max_boxes_count = 500)

// -Inputs ══════════════════════════════════════════════════════════════════════════════════════ //

// ---------------------------------------------------------------------------------------------- //
// Definitions ---------------------------------------------------------------------------------- //

group_support_and_resistance            = 'Consecutively Increasing Volume / Price'
tooltip_support_and_resistance          = 'Moments where\n' + 
                                          '- price is bullish or bearish consecutively for minimum 3 bars and on increasing volume with at least one bar\'s volume is above volume moving average\n' + 
                                          'or\n' + 
                                          '- price is bullish or bearish consecutively on increasing/decreasing price for minimum 3 bars'

group_volume_spike_sign_of_exhaustion   = 'Volume Spike - Sign of Exhaustion'
tooltip_volume_spike_sign_of_exhaustion = 'Moments where\n' + 
                                          'huge volume detected : current volume is grater than the product of the theshold value and volume moving average\n' + 
                                          'presents idea : huge volume may be a sign of exhaustion and may lead to sharp reversals'

group_high_volatility                   = 'High Volatility'
tooltip_high_volatility                 = 'Moments where\n' + 
                                           'price range of the current bar is grater than the product of the theshold value and average true range value of defined period'

group_volume_weighted_colored_bars      = 'Volume Weighted Colored Bars'
tooltip_volume_weighted_colored_bars    = 'Colors bars based on the bar\'s volume relative to volume moving average\n' + 
                                          'trading tip : a potential breakout trading opportunity may occur when price moves above a resistance level or moves below a support level on increasing volume'

tooltip_volume_moving_average           = 'Volume simple moving average, serves as reference to\n' + 
                                          '- Support and Resistance,\n' + 
                                          '- Volume Weighted Colored Bars,\n' + 
                                          '- Volume Spike - Sign of Exhaustion\ncalculations'

// User Input Declarations ---------------------------------------------------------------------- //

// ---------------------------------------------------------------------------------------------- //
// Consecutively Increasing Volume / Price  ----------------------------------------------------- //

srLookbackRange = input.string('Visible Range', 'Lookback Range', options = ['Fixed Range', 'Visible Range'])
i_lenLookback = input.int(360, 'Fixed Range : Lookback Interval (Bars)', minval=0, step=10)

i_sourceSnR = input.string('Volume', 'S & R Calculation Source', options=['Price', 'Volume'], group=group_support_and_resistance, tooltip=tooltip_support_and_resistance)
i_isSnR = input.bool(true, 'S & R Lines', inline='SR', group=group_support_and_resistance)
i_srLnColor = input.color(#4dd0e141, '' , inline='SR', group=group_support_and_resistance)
i_srLnWidth = input.int(3, ''           , inline='SR', group=group_support_and_resistance)
i_srLnStyle = input.string('Solid', '', options=['Dashed', 'Dotted', 'Solid'], inline='SR', group=group_support_and_resistance)

// ---------------------------------------------------------------------------------------------- //
// Volume Spike - Sign of Exhaustion ------------------------------------------------------------ //

i_vSpikeLb = input.bool(true, '🚦', inline='SRS1', group=group_volume_spike_sign_of_exhaustion, tooltip=tooltip_volume_spike_sign_of_exhaustion)
i_vSpikeThresh = input.float(4.669, 'Volume Spike Theshold', minval=.1, step=.1, inline='SRS1', group=group_volume_spike_sign_of_exhaustion)
i_isSnRSpike = input.bool(true, 'S & R Lines', inline='SRS2', group=group_volume_spike_sign_of_exhaustion)
i_spLnColor = input.color(#ffb74d69, ''      , inline='SRS2', group=group_volume_spike_sign_of_exhaustion)
i_spLnWidth = input.int(3, ''                , inline='SRS2', group=group_volume_spike_sign_of_exhaustion)
i_spLnStyle = input.string('Solid', '', options=['Dashed', 'Dotted', 'Solid']               , inline='SRS2', group=group_volume_spike_sign_of_exhaustion)
i_spLnBullLevel = input.string('Both', 'Levels : Bullish', options=['High', 'Close', 'Both'], inline='SRS3', group=group_volume_spike_sign_of_exhaustion)
i_spLnBearLevel = input.string('Both', ' Bearish', options=['Low', 'Close', 'Both']         , inline='SRS3', group=group_volume_spike_sign_of_exhaustion)

// ---------------------------------------------------------------------------------------------- //
// High Volatility ------------------------------------------------------------------------------ //

i_hATRLb = input.bool(true, '⚡', inline='ATR', group=group_high_volatility, tooltip=tooltip_high_volatility)
i_atrLength = input.int(11, 'ATR : Length', inline='ATR', group=group_high_volatility)
i_atrMult = input.float(2.718, 'Mult', minval=.1, step=.1, inline='ATR', group=group_high_volatility)
i_hATRLn = input.bool(true, 'S & R Lines'                , inline='AT1', group=group_high_volatility)
i_hATRLnColor = input.color(#81c78455, ''                , inline='AT1', group=group_high_volatility)
i_hATRLnWidth = input.int(3, ''                          , inline='AT1', group=group_high_volatility)
i_hATRLnStyle = input.string('Solid', '', options=['Dashed', 'Dotted', 'Solid']             , inline='AT1', group=group_high_volatility)
i_haLnBullLevel = input.string('Both', 'Levels : Bullish', options=['High', 'Close', 'Both'], inline='AT2', group=group_high_volatility)
i_haLnBearLevel = input.string('Both', ' Bearish', options=['Low', 'Close', 'Both']         , inline='AT2', group=group_high_volatility)

// Volume Moving Average : Base ----------------------------------------------------------------- //
nzVolume  = nz(volume)

i_vSMA = ta.sma(nzVolume, input.int(89, 'Volume Moving Average Length', group='General Settings', tooltip=tooltip_volume_moving_average))


// -Calculations ════════════════════════════════════════════════════════════════════════════════ //

// ---------------------------------------------------------------------------------------------- //
// Definitions ---------------------------------------------------------------------------------- //

risingVol = nzVolume >= nzVolume[1]

bullCandle = close > open
bearCandle = close < open

risingPrice  = close > close[1]
fallingPrice = close < close[1]

lwstPrice = ta.lowest (low , 3)
hstPrice  = ta.highest(high, 3)

weightedATR = i_atrMult * ta.atr(i_atrLength)
range_1     = math.abs(high - low)

x2 = timenow + 7 * math.round(ta.change(time))

var sProcessing = false
if srLookbackRange == 'Visible Range'
    sProcessing := time >= chart.left_visible_bar_time
else
    sProcessing := time > timenow - i_lenLookback * (timeframe.isintraday ? timeframe.multiplier * 86400000 / 1440 : timeframe.multiplier * 86400000)

// ---------------------------------------------------------------------------------------------- //
// Consecutively Increasing Volume / Price  ----------------------------------------------------- //

falling = if i_sourceSnR == 'Volume'
    bearCandle and bearCandle[1] and bearCandle[2] and nzVolume > i_vSMA and risingVol and risingVol[1]
else
    bearCandle and bearCandle[1] and bearCandle[2] and fallingPrice and fallingPrice[1] and fallingPrice[2]

rising = if i_sourceSnR == 'Volume'
    bullCandle and bullCandle[1] and bullCandle[2] and nzVolume > i_vSMA and risingVol and risingVol[1]
else
    bullCandle and bullCandle[1] and bullCandle[2] and risingPrice and risingPrice[1] and risingPrice[2]

y  = ta.valuewhen(falling or rising, falling ? lwstPrice : hstPrice, 0)
x1 = ta.valuewhen(falling or rising, time, 0)

// ---------------------------------------------------------------------------------------------- //
// Volume Spike - Sign of Exhaustion ------------------------------------------------------------ //

exhaustVol = nzVolume > i_vSpikeThresh * i_vSMA

x1V = ta.valuewhen(exhaustVol, time, 0)

// ---------------------------------------------------------------------------------------------- //
// High Volatility ------------------------------------------------------------------------------ //

highVolatility = range_1 > weightedATR

x1hV = ta.valuewhen(highVolatility, time, 0)

// ---------------------------------------------------------------------------------------------- //
// Volume Weighted Colored Bars ----------------------------------------------------------------- //


// -Plotting ════════════════════════════════════════════════════════════════════════════════════ //

f_getStyle(_s) =>
    _s == 'Solid' ? line.style_solid : _s == 'Dotted' ? line.style_dotted : line.style_dashed

// ---------------------------------------------------------------------------------------------- //
// Consecutively Increasing Volume / Price  ----------------------------------------------------- //

var line srLine = na

if i_isSnR and falling and sProcessing
    if falling == falling[1]
        line.delete(srLine[1])
        
    srLine := line.new(x1, y, x2, y, xloc.bar_time, extend.none, i_srLnColor, f_getStyle(i_srLnStyle), i_srLnWidth)
    srLine

if i_isSnR and rising and sProcessing
    if rising == rising[1]
        line.delete(srLine[1])
        
    srLine := line.new(x1, y, x2, y, xloc.bar_time, extend.none, i_srLnColor, f_getStyle(i_srLnStyle), i_srLnWidth)
    srLine

// ---------------------------------------------------------------------------------------------- //
// Volume Spike - Sign of Exhaustion ------------------------------------------------------------ //

var line spikeLine  = na
var line spikeLine1 = na
var line spikeLine2 = na
var line spikeLine3 = na

if i_isSnRSpike and exhaustVol and sProcessing

    if bullCandle
        if i_spLnBullLevel == 'High'
            if exhaustVol == exhaustVol[1] and not bearCandle[1]
                line.delete(spikeLine[1])
                
            spikeLine := line.new(x1V, high, x2, high, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine

        else if i_spLnBullLevel == 'Close'
            if exhaustVol == exhaustVol[1] and not bearCandle[1]
                line.delete(spikeLine[1])
                
            spikeLine := line.new(x1V, close, x2, close, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine
        else

            if exhaustVol == exhaustVol[1] and not bearCandle[1]
                line.delete(spikeLine1[1]), line.delete(spikeLine2[1]), line.delete(spikeLine3[1])
            
            spikeLine1 := line.new(x1V, close, x2, close, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine2 := line.new(x1V, math.avg(high, close), x2, math.avg(high, close), xloc.bar_time, extend.none, i_spLnColor, f_getStyle('Dotted'), i_spLnWidth - 1)
            spikeLine3 := line.new(x1V, high, x2, high, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine3

    if bearCandle
        if i_spLnBearLevel == 'Low'
            if exhaustVol == exhaustVol[1] and not bullCandle[1]
                line.delete(spikeLine[1])
                
            spikeLine := line.new(x1V, low, x2, low, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine

        else if i_spLnBearLevel == 'Close'
            if exhaustVol == exhaustVol[1] and not bullCandle[1]
                line.delete(spikeLine[1])
                
            spikeLine := line.new(x1V, close, x2, close, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine
        else

            if exhaustVol == exhaustVol[1] and not bullCandle[1]
                line.delete(spikeLine1[1]), line.delete(spikeLine2[1]), line.delete(spikeLine3[1])
                
            spikeLine1 := line.new(x1V, low, x2, low, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine2 := line.new(x1V, math.avg(low, close), x2, math.avg(low, close), xloc.bar_time, extend.none, i_spLnColor, f_getStyle('Dotted'), i_spLnWidth - 1)
            spikeLine3 := line.new(x1V, close, x2, close, xloc.bar_time, extend.none, i_spLnColor, f_getStyle(i_spLnStyle), i_spLnWidth)
            spikeLine3

plotchar(i_vSpikeLb and not na(nzVolume) and sProcessing ? exhaustVol : na, 'Exhaustion Bar', '🚦', location.abovebar, size=size.tiny)

// ---------------------------------------------------------------------------------------------- //
// High Volatility ------------------------------------------------------------------------------ //

var line volatileLine  = na
var line volatileLine1 = na
var line volatileLine2 = na
var line volatileLine3 = na

if i_hATRLn and highVolatility and sProcessing

    if bullCandle
        if i_haLnBullLevel == 'High'
            if highVolatility == highVolatility[1] and not bearCandle[1]
                line.delete(volatileLine[1])
                
            volatileLine := line.new(x1hV, high, x2, high, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine

        else if i_haLnBullLevel == 'Close'
            if highVolatility == highVolatility[1] and not bearCandle[1]
                line.delete(volatileLine[1])
                
            volatileLine := line.new(x1hV, close, x2, close, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine
        else

            if highVolatility == highVolatility[1] and not bearCandle[1]
                line.delete(volatileLine1[1]), line.delete(volatileLine2[1]), line.delete(volatileLine3[1])
                
            volatileLine1 := line.new(x1hV, close, x2, close, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine2 := line.new(x1hV, math.avg(high, close), x2, math.avg(high, close), xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle('Dotted'), i_hATRLnWidth - 1)
            volatileLine3 := line.new(x1hV, high, x2, high, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine3

    if bearCandle
        if i_haLnBearLevel == 'Low'
            if highVolatility == highVolatility[1] and not bullCandle[1]
                line.delete(volatileLine[1])
                
            volatileLine := line.new(x1hV, low, x2, low, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine

        else if i_haLnBearLevel == 'Close'
            if highVolatility == highVolatility[1] and not bullCandle[1]
                line.delete(volatileLine[1])
                
            volatileLine := line.new(x1hV, close, x2, close, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine
        else

            if highVolatility == highVolatility[1] and not bullCandle[1]
                line.delete(volatileLine1[1]), line.delete(volatileLine2[1]), line.delete(volatileLine3[1])
                
            volatileLine1 := line.new(x1hV, low, x2, low, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine2 := line.new(x1hV, math.avg(low, close), x2, math.avg(low, close), xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle('Dotted'), i_hATRLnWidth - 1)
            volatileLine3 := line.new(x1hV, close, x2, close, xloc.bar_time, extend.none, i_hATRLnColor, f_getStyle(i_hATRLnStyle), i_hATRLnWidth)
            volatileLine3

plotchar(i_hATRLb and sProcessing ? highVolatility : na, 'High Volatile Bar', '⚡', location.belowbar, size=size.tiny)
alertcondition(i_hATRLb and sProcessing, "High volatility bar detected", 'High Volatility on {{ticker}} detected at {{time}}')


// ---------------------------------------------------------------------------------------------- //
// Volume Profile (Price by Volume)  ------------------------------------------------------------ //

f_drawLabelX(_x, _y, _text, _xloc, _yloc, _color, _style, _textcolor, _size, _textalign, _tooltip) =>
    var id = label.new(_x, _y, _text, _xloc, _yloc, _color, _style, _textcolor, _size, _textalign, _tooltip)
    label.set_xy(id, _x, _y)
    label.set_text(id, _text)
    label.set_tooltip(id, _tooltip)
    label.set_textcolor(id, _textcolor)

f_drawLineX(_x1, _y1, _x2, _y2, _xloc, _extend, _color, _style, _width) =>
    var id = line.new(_x1, _y1, _x2, _y2, _xloc, _extend, _color, _style, _width)
    line.set_xy1(id, _x1, _y1)
    line.set_xy2(id, _x2, _y2)
    line.set_color(id, _color)

group_volume_profile    = 'Volume Profile / Price by Volume'
tooltip_volume_profile  = 'Volume Profile (also known as Price by Volume) is an charting study that displays trading activity over a specified time period at specific price levels'

volumeProfile     = input.bool(false, 'Volume Profile', group = group_volume_profile, tooltip = tooltip_volume_profile)
vpLookbackRange   = input.string('Visible Range', 'VP and SD Lookback Range', options = ['Fixed Range', 'Visible Range'], group = group_volume_profile)
lookbackLength    = input.int(360, 'VP and SD Fixed Range : Lookback Length', minval = 10, maxval = 5000, step = 10  , group = group_volume_profile)
avgVolNodeCol     = input.color(color.new(#787b86, 25), 'Volume Nodes : AVN'        , inline='col', group = group_volume_profile)
highVolNodeCol    = input.color(color.new(#f57c00, 50), 'HVN'                       , inline='col', group = group_volume_profile)
lowVolNodeCol     = input.color(color.new(#787b86, 75), 'LVN'                       , inline='col', group = group_volume_profile)
isValueArea       = input.float(68, "Value Area Volume %", minval = 0, maxval = 100               , group = group_volume_profile) / 100
valueAreaHigh     = input.bool(false, 'VAH'                                          , inline='VA' , group = group_volume_profile)
vahColor          = input.color(color.new(#ffeb3b, 0), ''                           , inline='VA' , group = group_volume_profile)
pointOfControl    = input.bool(false, 'POC'                                          , inline='VA' , group = group_volume_profile)
pocColor          = input.color(color.new(#ff0000, 0), ''                           , inline='VA' , group = group_volume_profile)
valueAreaLow      = input.bool(false, 'VAL'                                          , inline='VA' , group = group_volume_profile)
valColor          = input.color(color.new(#ffeb3b, 0), ''                           , inline='VA' , group = group_volume_profile)
priceLevels       = input.bool(false, 'Show Profile Price Levels     '               , inline='BBe', group = group_volume_profile)
labelColor        = input.color(color.new(#8c92a4, 0), ''                           , inline='BBe', group = group_volume_profile)
profileLevels     = input.int(100, 'Number of Rows' , minval = 10, maxval = 170 , step = 10       , group = group_volume_profile)
horizontalOffset  = input.int(13, 'Horizontal Offset', minval = 0 , maxval = 100                  , group = group_volume_profile)

tooltip_sd        = 'Defines the relationship between the price of a given asset and the willingness of traders to either buy or sell it'
group_supply_demand = 'Supply and Demand Settings'
supplyDemand      = input.bool(true, 'Supply and Demand Zones'                      , inline='low2', group = group_supply_demand, tooltip = tooltip_sd)
lowVolNodesVal    = input.int(15, '' , minval = 0, maxval = 50, step = 1            , inline='low2', group = group_supply_demand) / 100
supplyDemandCol   = input.color(color.new(#512da8, 80), ''                          , inline='low2', group = group_supply_demand)

priceHighestFR    = ta.highest(high, lookbackLength)
priceLowestFR     = ta.lowest (low , lookbackLength)

var startBarIndexX = 0
if time == chart.left_visible_bar_time
    startBarIndexX := bar_index

if vpLookbackRange == 'Visible Range'
    lookbackLength    := last_bar_index -  startBarIndexX

f_getHighLow() =>
    var htf_h  = 0., var htf_l  = 0.
    
    if vpLookbackRange == 'Visible Range'
        if time == chart.left_visible_bar_time
            htf_l := low 
            htf_h := high
        else if time > chart.left_visible_bar_time
            htf_l := math.min(low , htf_l)
            htf_h := math.max(high, htf_h)
    else
        htf_h := priceHighestFR
        htf_l := priceLowestFR

    [htf_h, htf_l]

[priceHighest, priceLowest] = f_getHighLow()
priceStep         = (priceHighest - priceLowest) / profileLevels
barPriceLow       = low
barPriceHigh      = high
var levelAbovePoc = 0
var levelBelowPoc = 0
var pocLevel      = 0

volumeStorage     = array.new_float(profileLevels + 1, 0.)
volumeStorageB    = array.new_float(profileLevels + 1, 0.)
var a_profile     = array.new_box()

if barstate.islast and not na(nzVolume)
    if array.size(a_profile) > 0
        for i = 1 to array.size(a_profile)
            box.delete(array.shift(a_profile))

    for barIndex = 0 to lookbackLength - 1
        level = 0
        for priceLevel = priceLowest to priceHighest by priceStep
            if barPriceHigh[barIndex] >= priceLevel and barPriceLow[barIndex] < priceLevel + priceStep
                array.set(volumeStorage, level, array.get(volumeStorage, level) + nzVolume[barIndex] * ((barPriceHigh[barIndex] - barPriceLow[barIndex]) == 0 ? 1 : priceStep / (barPriceHigh[barIndex] - barPriceLow[barIndex])) )

                if bullCandle[barIndex]
                    array.set(volumeStorageB, level, array.get(volumeStorageB, level) + nzVolume[barIndex] * ((barPriceHigh[barIndex] - barPriceLow[barIndex]) == 0 ? 1 : priceStep / (barPriceHigh[barIndex] - barPriceLow[barIndex])) )
            level += 1

    pocLevel  := array.indexof(volumeStorage, array.max(volumeStorage))
    totalVolumeTraded = array.sum(volumeStorage) * isValueArea
    valueArea  = array.get(volumeStorage, pocLevel)

    levelAbovePoc := pocLevel
    levelBelowPoc := pocLevel
    
    while valueArea < totalVolumeTraded
        if levelBelowPoc == 0 and levelAbovePoc == profileLevels - 1
            break

        volumeAbovePoc = 0.
        if levelAbovePoc < profileLevels - 1 
            volumeAbovePoc := array.get(volumeStorage, levelAbovePoc + 1)

        volumeBelowPoc = 0.
        if levelBelowPoc > 0
            volumeBelowPoc := array.get(volumeStorage, levelBelowPoc - 1)
        
        if volumeAbovePoc >= volumeBelowPoc
            valueArea     += volumeAbovePoc
            levelAbovePoc += 1
        else
            valueArea     += volumeBelowPoc
            levelBelowPoc -= 1

    f_drawLineX(bar_index - lookbackLength + 1, priceLowest + (levelAbovePoc + 1.00) * priceStep, volumeProfile ? bar_index + horizontalOffset + 50 : bar_index + 7, priceLowest + (levelAbovePoc + 1.00) * priceStep, xloc.bar_index, extend.none, valueAreaHigh  ? vahColor : #00000000, line.style_solid, 2)
    f_drawLineX(bar_index - lookbackLength + 1, priceLowest + (pocLevel      + 0.50) * priceStep, volumeProfile ? bar_index + horizontalOffset + 50 : bar_index + 7, priceLowest + (pocLevel      + 0.50) * priceStep, xloc.bar_index, extend.none, pointOfControl ? pocColor : #00000000, line.style_solid, 2)
    f_drawLineX(bar_index - lookbackLength + 1, priceLowest + (levelBelowPoc + 0.00) * priceStep, volumeProfile ? bar_index + horizontalOffset + 50 : bar_index + 7, priceLowest + (levelBelowPoc + 0.00) * priceStep, xloc.bar_index, extend.none, valueAreaLow   ? valColor : #00000000, line.style_solid, 2)

    if priceLevels
        f_drawLabelX(volumeProfile ? bar_index + horizontalOffset + 50 : bar_index + 7, priceHighest, str.tostring(priceHighest, format.mintick), xloc.bar_index, yloc.price, color.new(labelColor, 89), label.style_label_down, labelColor, size.normal, text.align_left, 'Profile High - during last ' + str.tostring(lookbackLength) + ' bars\n %' + str.tostring((priceHighest - priceLowest) / priceLowest  * 100, '#.##') + ' higher than the Profile Low')
        f_drawLabelX(volumeProfile ? bar_index + horizontalOffset + 50 : bar_index + 7, priceLowest , str.tostring(priceLowest , format.mintick), xloc.bar_index, yloc.price, color.new(labelColor, 89), label.style_label_up  , labelColor, size.normal, text.align_left, 'Profile Low - during last '  + str.tostring(lookbackLength) + ' bars\n %' + str.tostring((priceHighest - priceLowest) / priceHighest * 100, '#.##') + ' lower than the Profile High')
        f_drawLabelX(volumeProfile ? bar_index + horizontalOffset + 57 : bar_index + 13, priceLowest + (levelAbovePoc + 1.00) * priceStep, str.tostring(priceLowest + (levelAbovePoc + 1.00) * priceStep, format.mintick), xloc.bar_index, yloc.price, valueAreaHigh  ? color.new(labelColor, 89) : #00000000, label.style_label_left, valueAreaHigh  ? labelColor : #00000000, size.normal, text.align_left, 'Value Area High Price')
        f_drawLabelX(volumeProfile ? bar_index + horizontalOffset + 57 : bar_index + 13, priceLowest + (pocLevel      + 0.50) * priceStep, str.tostring(priceLowest + (pocLevel      + 0.50) * priceStep, format.mintick), xloc.bar_index, yloc.price, pointOfControl ? color.new(labelColor, 89) : #00000000, label.style_label_left, pointOfControl ? labelColor : #00000000, size.normal, text.align_left, 'Point Of Control Price')
        f_drawLabelX(volumeProfile ? bar_index + horizontalOffset + 57 : bar_index + 13, priceLowest + (levelBelowPoc + 0.00) * priceStep, str.tostring(priceLowest + (levelBelowPoc + 0.00) * priceStep, format.mintick), xloc.bar_index, yloc.price, valueAreaLow   ? color.new(labelColor, 89) : #00000000, label.style_label_left, valueAreaLow   ? labelColor : #00000000, size.normal, text.align_left, 'Value Area Low Price')

    for level = 0 to profileLevels - 1
        if volumeProfile
            levelColor = array.get(volumeStorage, level) / array.max(volumeStorage) > .8 ? highVolNodeCol : array.get(volumeStorage, level) / array.max(volumeStorage) < .2 ? lowVolNodeCol : avgVolNodeCol
            array.push(a_profile, box.new( bar_index + horizontalOffset + 49 - int( array.get(volumeStorage, level) / array.max(volumeStorage) * 41), priceLowest + (level + 0.25) * priceStep, 
                                       bar_index + horizontalOffset + 50, priceLowest + (level + 0.75) * priceStep, levelColor, bgcolor = levelColor ))
            bullBearPower  = 2 * array.get(volumeStorageB, level) - array.get(volumeStorage, level)
            array.push(a_profile, box.new(bar_index + horizontalOffset + 51 , priceLowest + (level + 0.25) * priceStep, 
                                      bar_index + horizontalOffset + 51 + (bullBearPower > 0 ? 1 : -1) * int( bullBearPower / array.max(volumeStorage) * 73), priceLowest + (level + 0.75) * priceStep, bullBearPower > 0 ? color.new(#26a69a, 50) : color.new(#ef5350, 50), bgcolor = bullBearPower > 0 ? color.new(#26a69a, 50) : color.new(#ef5350, 50) ))

        if supplyDemand
            if array.get(volumeStorage, level) / array.max(volumeStorage) < lowVolNodesVal
                array.push(a_profile, box.new(bar_index - lookbackLength + 1, priceLowest + (level + 0.00) * priceStep, bar_index + 7, priceLowest + (level + 1.00) * priceStep, #00000000, bgcolor = supplyDemandCol ))

    if volumeProfile
        array.push(a_profile, box.new(bar_index - lookbackLength + 1, priceLowest, bar_index + horizontalOffset + 50, priceHighest, color.new(color.gray, 37), 1, line.style_dotted, bgcolor=#00000000 ))

// Volume Profile (Price by Volume)  ------------------------------------------------------------ //
// ---------------------------------------------------------------------------------------------- //
// Volume Weighted Colored Bars ----------------------------------------------------------------- //

i_vwcb           = input.bool (true , ''                                     , inline='VWC', group=group_volume_weighted_colored_bars, tooltip=tooltip_volume_weighted_colored_bars)
i_vwcbHighThresh = input.float(1.618, 'Thesholds : High ', minval=1., step=.1, inline='VWC', group=group_volume_weighted_colored_bars)
i_vwcbLowThresh  = input.float(0.618, 'Low', minval=.1, step=.1              , inline='VWC', group=group_volume_weighted_colored_bars)
vwcbCol = nzVolume > i_vSMA * i_vwcbHighThresh ? bearCandle ? #910000 : #006400 : nzVolume < i_vSMA * i_vwcbLowThresh ? bearCandle ? #FF9800 : #7FFFD4 : na
barcolor(i_vwcb and not na(nzVolume) ? vwcbCol : na, title='Volume Weighted Colored Bars')

// Volume Weighted Colored Bars ----------------------------------------------------------------- //
// ---------------------------------------------------------------------------------------------- //
// Alerts --------------------------------------------------------------------------------------- //

priceTxt  = str.tostring(close, format.mintick)
tickerTxt = syminfo.ticker

if ta.cross(close, priceLowest + (pocLevel + .50) * priceStep) and pointOfControl
    alert(tickerTxt + ' Volume Profile : Price touches/crosses Point Of Control Line, price ' + priceTxt)

if ta.cross(close, priceLowest + (levelAbovePoc + 1.00) * priceStep) and valueAreaHigh
    alert(tickerTxt + ' Volume Profile : Price touches/crosses Value Area High Line, price '  + priceTxt)

if ta.cross(close, priceLowest + (levelBelowPoc + 0.00) * priceStep) and valueAreaLow
    alert(tickerTxt + ' Volume Profile : Price touches/crosses Value Area Low Line, price '   + priceTxt)
 
if nzVolume > i_vSMA * i_vwcbHighThresh and i_vwcb
    alert(tickerTxt + ' High Volume, price ' + priceTxt)

if nzVolume > i_vSMA * i_vSpikeThresh and i_vSpikeLb
    alert(tickerTxt + ' Volume Spike : sign of exhaustion, huge volume increase detected, price ' + priceTxt)

if ta.crossover(range_1, weightedATR) and i_hATRLb
    alert(tickerTxt + ' High Volatility detected, price ' + priceTxt)

// Alerts --------------------------------------------------------------------------------------- //
// ---------------------------------------------------------------------------------------------- //

var table logo = table.new(position.bottom_right, 1, 1)
if barstate.islast
    table.cell(logo, 0, 0, '☼☾  ', text_size=size.normal, text_color=color.teal)



//@version=5
// indicator("Smart Money Concepts [LuxAlgo]", "Smart Money Concepts [LuxAlgo]"
//   , overlay = true
//   , max_labels_count = 500
//   , max_lines_count = 500
//   , max_boxes_count = 500
//   , max_bars_back = 500)
//-----------------------------------------------------------------------------{
//Constants
//-----------------------------------------------------------------------------{
color TRANSP_CSS = #ffffff00

//Tooltips
string MODE_TOOLTIP          = 'Allows to display historical Structure or only the recent ones'
string STYLE_TOOLTIP         = 'Indicator color theme'
string COLOR_CANDLES_TOOLTIP = 'Display additional candles with a color reflecting the current trend detected by structure'
string SHOW_INTERNAL         = 'Display internal market structure'
string CONFLUENCE_FILTER     = 'Filter non significant internal structure breakouts'
string SHOW_SWING            = 'Display swing market Structure'
string SHOW_SWING_POINTS     = 'Display swing point as labels on the chart'
string SHOW_SWHL_POINTS      = 'Highlight most recent strong and weak high/low points on the chart'
string INTERNAL_OB           = 'Display internal order blocks on the chart\n\nNumber of internal order blocks to display on the chart'
string SWING_OB              = 'Display swing order blocks on the chart\n\nNumber of internal swing blocks to display on the chart'
string FILTER_OB             = 'Method used to filter out volatile order blocks \n\nIt is recommended to use the cumulative mean range method when a low amount of data is available'
string SHOW_EQHL             = 'Display equal highs and equal lows on the chart'
string EQHL_BARS             = 'Number of bars used to confirm equal highs and equal lows'
string EQHL_THRESHOLD        = 'Sensitivity threshold in a range (0, 1) used for the detection of equal highs & lows\n\nLower values will return fewer but more pertinent results'
string SHOW_FVG              = 'Display fair values gaps on the chart'
string AUTO_FVG              = 'Filter out non significant fair value gaps'
string FVG_TF                = 'Fair value gaps timeframe'
string EXTEND_FVG            = 'Determine how many bars to extend the Fair Value Gap boxes on chart'
string PED_ZONES             = 'Display premium, discount, and equilibrium zones on chart'

//-----------------------------------------------------------------------------{
//Settings
//-----------------------------------------------------------------------------{
//General
//----------------------------------------{
mode = input.string('Historical'
  , options = ['Historical', 'Present']
  , group = 'Smart Money Concepts'
  , tooltip = MODE_TOOLTIP)

style = input.string('Colored'
  , options = ['Colored', 'Monochrome']
  , group = 'Smart Money Concepts'
  , tooltip = STYLE_TOOLTIP)

show_trend = input(false, 'Color Candles'
  , group = 'Smart Money Concepts'
  , tooltip = COLOR_CANDLES_TOOLTIP)

//----------------------------------------}
//Internal Structure
//----------------------------------------{
show_internals = input(true, 'Show Internal Structure'
  , group = 'Real Time Internal Structure'
  , tooltip = SHOW_INTERNAL)

show_ibull = input.string('All', 'Bullish Structure'
  , options = ['All', 'BOS', 'CHoCH']
  , inline = 'ibull'
  , group = 'Real Time Internal Structure')

swing_ibull_css = input(#089981, ''
  , inline = 'ibull'
  , group = 'Real Time Internal Structure')

//Bear Structure
show_ibear = input.string('All', 'Bearish Structure'
  , options = ['All', 'BOS', 'CHoCH']
  , inline = 'ibear'
  , group = 'Real Time Internal Structure')

swing_ibear_css = input(#f23645, ''
  , inline = 'ibear'
  , group = 'Real Time Internal Structure')

ifilter_confluence = input(false, 'Confluence Filter'
  , group = 'Real Time Internal Structure'
  , tooltip = CONFLUENCE_FILTER)

internal_structure_size = input.string('Tiny', 'Internal Label Size'
  , options = ['Tiny', 'Small', 'Normal']
  , group = 'Real Time Internal Structure')

//----------------------------------------}
//Swing Structure
//----------------------------------------{
show_Structure = input(true, 'Show Swing Structure'
  , group = 'Real Time Swing Structure'
  , tooltip = SHOW_SWING)

//Bull Structure
show_bull = input.string('All', 'Bullish Structure'
  , options = ['All', 'BOS', 'CHoCH']
  , inline = 'bull'
  , group = 'Real Time Swing Structure')

swing_bull_css = input(#089981, ''
  , inline = 'bull'
  , group = 'Real Time Swing Structure')

//Bear Structure
show_bear = input.string('All', 'Bearish Structure'
  , options = ['All', 'BOS', 'CHoCH']
  , inline = 'bear'
  , group = 'Real Time Swing Structure')

swing_bear_css = input(#f23645, ''
  , inline = 'bear'
  , group = 'Real Time Swing Structure')

swing_structure_size = input.string('Small', 'Swing Label Size'
  , options = ['Tiny', 'Small', 'Normal']
  , group = 'Real Time Swing Structure')

//Swings
show_swings = input(false, 'Show Swings Points'
  , inline = 'swings'
  , group = 'Real Time Swing Structure'
  , tooltip = SHOW_SWING_POINTS)

length = input.int(50, ''
  , minval = 10
  , inline = 'swings'
  , group = 'Real Time Swing Structure')

show_hl_swings = input(true, 'Show Strong/Weak High/Low'
  , group = 'Real Time Swing Structure'
  , tooltip = SHOW_SWHL_POINTS)

//----------------------------------------}
//Order Blocks
//----------------------------------------{
show_iob = input(true, 'Internal Order Blocks'
  , inline = 'iob'
  , group = 'Order Blocks'
  , tooltip = INTERNAL_OB)

iob_showlast = input.int(5, ''
  , minval = 1
  , inline = 'iob'
  , group = 'Order Blocks')

show_ob = input(false, 'Swing Order Blocks'
  , inline = 'ob'
  , group = 'Order Blocks'
  , tooltip = SWING_OB)

ob_showlast = input.int(5, ''
  , minval = 1
  , inline = 'ob'
  , group = 'Order Blocks')

ob_filter = input.string('Atr', 'Order Block Filter'
  , options = ['Atr', 'Cumulative Mean Range']
  , group = 'Order Blocks'
  , tooltip = FILTER_OB)

ibull_ob_css = input.color(color.new(#3179f5, 80), 'Internal Bullish OB'
  , group = 'Order Blocks')

ibear_ob_css = input.color(color.new(#f77c80, 80), 'Internal Bearish OB'
  , group = 'Order Blocks')

bull_ob_css = input.color(color.new(#1848cc, 80), 'Bullish OB'
  , group = 'Order Blocks')

bear_ob_css = input.color(color.new(#b22833, 80), 'Bearish OB'
  , group = 'Order Blocks')

//----------------------------------------}
//EQH/EQL
//----------------------------------------{
show_eq = input(true, 'Equal High/Low'
  , group = 'EQH/EQL'
  , tooltip = SHOW_EQHL)

eq_len = input.int(3, 'Bars Confirmation'
  , minval = 1
  , group = 'EQH/EQL'
  , tooltip = EQHL_BARS)

eq_threshold = input.float(0.1, 'Threshold'
  , minval = 0
  , maxval = 0.5
  , step = 0.1
  , group = 'EQH/EQL'
  , tooltip = EQHL_THRESHOLD)

eq_size = input.string('Tiny', 'Label Size'
  , options = ['Tiny', 'Small', 'Normal']
  , group = 'EQH/EQL')

//----------------------------------------}
//Fair Value Gaps
//----------------------------------------{
show_fvg = input(false, 'Fair Value Gaps'
  , group = 'Fair Value Gaps'
  , tooltip = SHOW_FVG)
  
fvg_auto = input(true, "Auto Threshold"
  , group = 'Fair Value Gaps'
  , tooltip = AUTO_FVG)

fvg_tf = input.timeframe('', "Timeframe"
  , group = 'Fair Value Gaps'
  , tooltip = FVG_TF)

bull_fvg_css = input.color(color.new(#00ff68, 70), 'Bullish FVG'
  , group = 'Fair Value Gaps')

bear_fvg_css = input.color(color.new(#ff0008, 70), 'Bearish FVG'
  , group = 'Fair Value Gaps')

fvg_extend = input.int(1, "Extend FVG"
  , minval = 0
  , group = 'Fair Value Gaps'
  , tooltip = EXTEND_FVG)

//----------------------------------------}
//Previous day/week high/low
//----------------------------------------{
//Daily
show_pdhl = input(false, 'Daily'
  , inline = 'daily'
  , group = 'Highs & Lows MTF')

pdhl_style = input.string('⎯⎯⎯', ''
  , options = ['⎯⎯⎯', '----', '····']
  , inline = 'daily'
  , group = 'Highs & Lows MTF')

pdhl_css = input(#2157f3, ''
  , inline = 'daily'
  , group = 'Highs & Lows MTF')

//Weekly
show_pwhl = input(false, 'Weekly'
  , inline = 'weekly'
  , group = 'Highs & Lows MTF')

pwhl_style = input.string('⎯⎯⎯', ''
  , options = ['⎯⎯⎯', '----', '····']
  , inline = 'weekly'
  , group = 'Highs & Lows MTF')

pwhl_css = input(#2157f3, ''
  , inline = 'weekly'
  , group = 'Highs & Lows MTF')

//Monthly
show_pmhl = input(false, 'Monthly'
  , inline = 'monthly'
  , group = 'Highs & Lows MTF')

pmhl_style = input.string('⎯⎯⎯', ''
  , options = ['⎯⎯⎯', '----', '····']
  , inline = 'monthly'
  , group = 'Highs & Lows MTF')

pmhl_css = input(#2157f3, ''
  , inline = 'monthly'
  , group = 'Highs & Lows MTF')

//----------------------------------------}
//Premium/Discount zones
//----------------------------------------{
show_sd = input(false, 'Premium/Discount Zones'
  , group = 'Premium & Discount Zones'
  , tooltip = PED_ZONES)

premium_css = input.color(#f23645, 'Premium Zone'
  , group = 'Premium & Discount Zones')

eq_css = input.color(#b2b5be, 'Equilibrium Zone'
  , group = 'Premium & Discount Zones')

discount_css = input.color(#089981, 'Discount Zone'
  , group = 'Premium & Discount Zones')

//-----------------------------------------------------------------------------}
//Functions
//-----------------------------------------------------------------------------{
n = bar_index

smc_atr = ta.atr(200)
cmean_range = ta.cum(high - low) / n

//HL Output function
hl() => [high, low]

//Get ohlc values function
get_ohlc()=> [close[1], open[1], high, low, high[2], low[2]]

//Display Structure function
display_Structure(x, y, txt, css, dashed, down, lbl_size)=>
    structure_line = line.new(x, y, n, y
      , color = css
      , style = dashed ? line.style_dashed : line.style_solid)

    structure_lbl = label.new(int(math.avg(x, n)), y, txt
      , color = TRANSP_CSS
      , textcolor = css
      , style = down ? label.style_label_down : label.style_label_up
      , size = lbl_size)

    if mode == 'Present'
        line.delete(structure_line[1])
        label.delete(structure_lbl[1])

//Swings detection/measurements
swings(len)=>
    var os = 0
    
    upper = ta.highest(len)
    lower = ta.lowest(len)

    os := high[len] > upper ? 0 : low[len] < lower ? 1 : os[1]

    top = os == 0 and os[1] != 0 ? high[len] : 0
    btm = os == 1 and os[1] != 1 ? low[len] : 0

    [top, btm]

//Order block coordinates function
ob_coord(use_max, loc, target_top, target_btm, target_left, target_type)=>
    min = 99999999.
    max = 0.
    idx = 1

    ob_threshold = ob_filter == 'Atr' ? smc_atr : cmean_range 

    //Search for highest/lowest high within the structure interval and get range
    if use_max
        for i = 1 to (n - loc)-1
            if (high[i] - low[i]) < ob_threshold[i] * 2
                max := math.max(high[i], max)
                min := max == high[i] ? low[i] : min
                idx := max == high[i] ? i : idx
    else
        for i = 1 to (n - loc)-1
            if (high[i] - low[i]) < ob_threshold[i] * 2
                min := math.min(low[i], min)
                max := min == low[i] ? high[i] : max
                idx := min == low[i] ? i : idx

    array.unshift(target_top, max)
    array.unshift(target_btm, min)
    array.unshift(target_left, time[idx])
    array.unshift(target_type, use_max ? -1 : 1)

//Set order blocks
display_ob(boxes, target_top, target_btm, target_left, target_type, show_last, swing, size)=>
    for i = 0 to math.min(show_last-1, size-1)
        get_box = array.get(boxes, i)

        box.set_lefttop(get_box, array.get(target_left, i), array.get(target_top, i))
        box.set_rightbottom(get_box, array.get(target_left, i), array.get(target_btm, i))
        box.set_extend(get_box, extend.right)

        color css = na
        
        if swing 
            if style == 'Monochrome'
                css := array.get(target_type, i) == 1 ? color.new(#b2b5be, 80) : color.new(#5d606b, 80)
                border_css = array.get(target_type, i) == 1 ? #b2b5be : #5d606b
                box.set_border_color(get_box, border_css)
            else
                css := array.get(target_type, i) == 1 ? bull_ob_css : bear_ob_css
                box.set_border_color(get_box, css)

            box.set_bgcolor(get_box, css)
        else
            if style == 'Monochrome'
                css := array.get(target_type, i) == 1 ? color.new(#b2b5be, 80) : color.new(#5d606b, 80)
            else
                css := array.get(target_type, i) == 1 ? ibull_ob_css : ibear_ob_css
            
            box.set_border_color(get_box, css)
            box.set_bgcolor(get_box, css)
        
//Line Style function
get_line_style(style) =>
    out = switch style
        '⎯⎯⎯'  => line.style_solid
        '----' => line.style_dashed
        '····' => line.style_dotted

//Set line/labels function for previous high/lows
phl(h, l, tf, css)=>
    var line high_line = line.new(na,na,na,na
      , xloc = xloc.bar_time
      , color = css
      , style = get_line_style(pdhl_style))

    var label high_lbl = label.new(na,na
      , xloc = xloc.bar_time
      , text = str.format('P{0}H', tf)
      , color = TRANSP_CSS
      , textcolor = css
      , size = size.small
      , style = label.style_label_left)

    var line low_line = line.new(na,na,na,na
      , xloc = xloc.bar_time
      , color = css
      , style = get_line_style(pdhl_style))

    var label low_lbl = label.new(na,na
      , xloc = xloc.bar_time
      , text = str.format('P{0}L', tf)
      , color = TRANSP_CSS
      , textcolor = css
      , size = size.small
      , style = label.style_label_left)

    hy = ta.valuewhen(h != h[1], h, 1)
    hx = ta.valuewhen(h == high, time, 1)

    ly = ta.valuewhen(l != l[1], l, 1)
    lx = ta.valuewhen(l == low, time, 1)

    if barstate.islast
        ext = time + (time - time[1])*20

        //High
        line.set_xy1(high_line, hx, hy)
        line.set_xy2(high_line, ext, hy)

        label.set_xy(high_lbl, ext, hy)

        //Low
        line.set_xy1(low_line, lx, ly)
        line.set_xy2(low_line, ext, ly)

        label.set_xy(low_lbl, ext, ly)

//-----------------------------------------------------------------------------}
//Global variables
//-----------------------------------------------------------------------------{
var trend = 0, var itrend = 0

var top_y = 0., var top_x = 0
var btm_y = 0., var btm_x = 0

var itop_y = 0., var itop_x = 0
var ibtm_y = 0., var ibtm_x = 0

var trail_up = high, var trail_dn = low
var trail_up_x = 0,  var trail_dn_x = 0

var top_cross = true,  var btm_cross = true
var itop_cross = true, var ibtm_cross = true

var txt_top = '',  var txt_btm = ''

//Alerts
bull_choch_alert = false 
bull_bos_alert   = false 

bear_choch_alert = false 
bear_bos_alert   = false 

bull_ichoch_alert = false 
bull_ibos_alert   = false 

bear_ichoch_alert = false 
bear_ibos_alert   = false 

bull_iob_break = false 
bear_iob_break = false

bull_ob_break = false 
bear_ob_break = false

eqh_alert = false 
eql_alert = false 

//Structure colors
var bull_css = style == 'Monochrome' ? #b2b5be 
  : swing_bull_css

var bear_css = style == 'Monochrome' ? #b2b5be 
  : swing_bear_css

var ibull_css = style == 'Monochrome' ? #b2b5be 
  : swing_ibull_css

var ibear_css = style == 'Monochrome' ? #b2b5be 
  : swing_ibear_css

//Labels size
var internal_structure_lbl_size = internal_structure_size == 'Tiny' 
  ? size.tiny 
  : internal_structure_size == 'Small' 
  ? size.small 
  : size.normal 

var swing_structure_lbl_size = swing_structure_size == 'Tiny' 
  ? size.tiny 
  : swing_structure_size == 'Small' 
  ? size.small 
  : size.normal 

var eqhl_lbl_size = eq_size == 'Tiny' 
  ? size.tiny 
  : eq_size == 'Small' 
  ? size.small
  : size.normal 

//Swings
[top, btm] = swings(length)

[itop, ibtm] = swings(5)

//-----------------------------------------------------------------------------}
//Pivot High
//-----------------------------------------------------------------------------{
var line extend_top = na

var label extend_top_lbl = label.new(na, na
  , color = TRANSP_CSS
  , textcolor = bear_css
  , style = label.style_label_down
  , size = size.tiny)

if top
    top_cross := true
    txt_top := top > top_y ? 'HH' : 'LH'

    if show_swings
        top_lbl = label.new(n-length, top, txt_top
          , color = TRANSP_CSS
          , textcolor = bear_css
          , style = label.style_label_down
          , size = swing_structure_lbl_size)

        if mode == 'Present'
            label.delete(top_lbl[1])

    //Extend recent top to last bar
    line.delete(extend_top[1])
    extend_top := line.new(n-length, top, n, top
      , color = bear_css)

    top_y := top
    top_x := n - length

    trail_up := top
    trail_up_x := n - length

if itop
    itop_cross := true

    itop_y := itop
    itop_x := n - 5

//Trailing maximum
trail_up := math.max(high, trail_up)
trail_up_x := trail_up == high ? n : trail_up_x

//Set top extension label/line
if barstate.islast and show_hl_swings
    line.set_xy1(extend_top, trail_up_x, trail_up)
    line.set_xy2(extend_top, n + 20, trail_up)

    label.set_x(extend_top_lbl, n + 20)
    label.set_y(extend_top_lbl, trail_up)
    label.set_text(extend_top_lbl, trend < 0 ? 'Strong High' : 'Weak High')

//-----------------------------------------------------------------------------}
//Pivot Low
//-----------------------------------------------------------------------------{
var line extend_btm = na 

var label extend_btm_lbl = label.new(na, na
  , color = TRANSP_CSS
  , textcolor = bull_css
  , style = label.style_label_up
  , size = size.tiny)

if btm
    btm_cross := true
    txt_btm := btm < btm_y ? 'LL' : 'HL'
    
    if show_swings
        btm_lbl = label.new(n - length, btm, txt_btm
          , color = TRANSP_CSS
          , textcolor = bull_css
          , style = label.style_label_up
          , size = swing_structure_lbl_size)

        if mode == 'Present'
            label.delete(btm_lbl[1])
    
    //Extend recent btm to last bar
    line.delete(extend_btm[1])
    extend_btm := line.new(n - length, btm, n, btm
      , color = bull_css)

    btm_y := btm
    btm_x := n-length

    trail_dn := btm
    trail_dn_x := n-length

if ibtm
    ibtm_cross := true

    ibtm_y := ibtm
    ibtm_x := n - 5

//Trailing minimum
trail_dn := math.min(low, trail_dn)
trail_dn_x := trail_dn == low ? n : trail_dn_x

//Set btm extension label/line
if barstate.islast and show_hl_swings
    line.set_xy1(extend_btm, trail_dn_x, trail_dn)
    line.set_xy2(extend_btm, n + 20, trail_dn)

    label.set_x(extend_btm_lbl, n + 20)
    label.set_y(extend_btm_lbl, trail_dn)
    label.set_text(extend_btm_lbl, trend > 0 ? 'Strong Low' : 'Weak Low')

//-----------------------------------------------------------------------------}
//Order Blocks Arrays
//-----------------------------------------------------------------------------{
var iob_top = array.new_float(0)
var iob_btm = array.new_float(0)
var iob_left = array.new_int(0)
var iob_type = array.new_int(0)

var ob_top = array.new_float(0)
var ob_btm = array.new_float(0)
var ob_left = array.new_int(0)
var ob_type = array.new_int(0)

//-----------------------------------------------------------------------------}
//Pivot High BOS/CHoCH
//-----------------------------------------------------------------------------{
//Filtering
var bull_concordant = true

if ifilter_confluence
    bull_concordant := high - math.max(close, open) > math.min(close, open - low)

//Detect internal bullish Structure
if ta.crossover(close, itop_y) and itop_cross and top_y != itop_y and bull_concordant
    bool choch = na
    
    if itrend < 0
        choch := true
        bull_ichoch_alert := true
    else 
        bull_ibos_alert := true
    
    txt = choch ? 'CHoCH' : 'BOS'

    if show_internals
        if show_ibull == 'All' or (show_ibull == 'BOS' and not choch) or (show_ibull == 'CHoCH' and choch)
            display_Structure(itop_x, itop_y, txt, ibull_css, true, true, internal_structure_lbl_size)
    
    itop_cross := false
    itrend := 1
    
    //Internal Order Block
    if show_iob
        ob_coord(false, itop_x, iob_top, iob_btm, iob_left, iob_type)

//Detect bullish Structure
if ta.crossover(close, top_y) and top_cross
    bool choch = na
    
    if trend < 0
        choch := true
        bull_choch_alert := true
    else 
        bull_bos_alert := true

    txt = choch ? 'CHoCH' : 'BOS'
    
    if show_Structure
        if show_bull == 'All' or (show_bull == 'BOS' and not choch) or (show_bull == 'CHoCH' and choch)
            display_Structure(top_x, top_y, txt, bull_css, false, true, swing_structure_lbl_size)
    
    //Order Block
    if show_ob
        ob_coord(false, top_x, ob_top, ob_btm, ob_left, ob_type)

    top_cross := false
    trend := 1

//-----------------------------------------------------------------------------}
//Pivot Low BOS/CHoCH
//-----------------------------------------------------------------------------{
var bear_concordant = true

if ifilter_confluence
    bear_concordant := high - math.max(close, open) < math.min(close, open - low)

//Detect internal bearish Structure
if ta.crossunder(close, ibtm_y) and ibtm_cross and btm_y != ibtm_y and bear_concordant
    bool choch = false
    
    if itrend > 0
        choch := true
        bear_ichoch_alert := true
    else 
        bear_ibos_alert := true
    
    txt = choch ? 'CHoCH' : 'BOS'

    if show_internals
        if show_ibear == 'All' or (show_ibear == 'BOS' and not choch) or (show_ibear == 'CHoCH' and choch)
            display_Structure(ibtm_x, ibtm_y, txt, ibear_css, true, false, internal_structure_lbl_size)
    
    ibtm_cross := false
    itrend := -1
    
    //Internal Order Block
    if show_iob
        ob_coord(true, ibtm_x, iob_top, iob_btm, iob_left, iob_type)

//Detect bearish Structure
if ta.crossunder(close, btm_y) and btm_cross
    bool choch = na
    
    if trend > 0
        choch := true
        bear_choch_alert := true
    else 
        bear_bos_alert := true

    txt = choch ? 'CHoCH' : 'BOS'
    
    if show_Structure
        if show_bear == 'All' or (show_bear == 'BOS' and not choch) or (show_bear == 'CHoCH' and choch)
            display_Structure(btm_x, btm_y, txt, bear_css, false, false, swing_structure_lbl_size)
    
    //Order Block
    if show_ob
        ob_coord(true, btm_x, ob_top, ob_btm, ob_left, ob_type)

    btm_cross := false
    trend := -1

//-----------------------------------------------------------------------------}
//Order Blocks
//-----------------------------------------------------------------------------{
//Set order blocks
var iob_boxes = array.new_box(0)
var ob_boxes = array.new_box(0)

//Delete internal order blocks box coordinates if top/bottom is broken
for element in iob_type
    index = array.indexof(iob_type, element)

    if close < array.get(iob_btm, index) and element == 1
        array.remove(iob_top, index) 
        array.remove(iob_btm, index) 
        array.remove(iob_left, index) 
        array.remove(iob_type, index)
        bull_iob_break := true

    else if close > array.get(iob_top, index) and element == -1
        array.remove(iob_top, index) 
        array.remove(iob_btm, index)
        array.remove(iob_left, index) 
        array.remove(iob_type, index)
        bear_iob_break := true

//Delete internal order blocks box coordinates if top/bottom is broken
for element in ob_type
    index = array.indexof(ob_type, element)

    if close < array.get(ob_btm, index) and element == 1
        array.remove(ob_top, index) 
        array.remove(ob_btm, index) 
        array.remove(ob_left, index) 
        array.remove(ob_type, index)
        bull_ob_break := true

    else if close > array.get(ob_top, index) and element == -1
        array.remove(ob_top, index) 
        array.remove(ob_btm, index)
        array.remove(ob_left, index) 
        array.remove(ob_type, index)
        bear_ob_break := true

iob_size = array.size(iob_type)
ob_size = array.size(ob_type)

if barstate.isfirst
    if show_iob
        for i = 0 to iob_showlast-1
            array.push(iob_boxes, box.new(na,na,na,na, xloc = xloc.bar_time))
    if show_ob
        for i = 0 to ob_showlast-1
            array.push(ob_boxes, box.new(na,na,na,na, xloc = xloc.bar_time))

if iob_size > 0
    if barstate.islast
        display_ob(iob_boxes, iob_top, iob_btm, iob_left, iob_type, iob_showlast, false, iob_size)

if ob_size > 0
    if barstate.islast
        display_ob(ob_boxes, ob_top, ob_btm, ob_left, ob_type, ob_showlast, true, ob_size)

//-----------------------------------------------------------------------------}
//EQH/EQL
//-----------------------------------------------------------------------------{
var eq_prev_top = 0.
var eq_top_x = 0

var eq_prev_btm = 0.
var eq_btm_x = 0

if show_eq
    eq_top = ta.pivothigh(eq_len, eq_len)
    eq_btm = ta.pivotlow(eq_len, eq_len)

    if eq_top 
        max = math.max(eq_top, eq_prev_top)
        min = math.min(eq_top, eq_prev_top)
        
        if max < min + smc_atr * eq_threshold
            eqh_line = line.new(eq_top_x, eq_prev_top, n-eq_len, eq_top
              , color = bear_css
              , style = line.style_dotted)

            eqh_lbl = label.new(int(math.avg(n-eq_len, eq_top_x)), eq_top, 'EQH'
              , color = #00000000
              , textcolor = bear_css
              , style = label.style_label_down
              , size = eqhl_lbl_size)

            if mode == 'Present'
                line.delete(eqh_line[1])
                label.delete(eqh_lbl[1])
            
            eqh_alert := true

        eq_prev_top := eq_top
        eq_top_x := n-eq_len

    if eq_btm 
        max = math.max(eq_btm, eq_prev_btm)
        min = math.min(eq_btm, eq_prev_btm)
        
        if min > max - smc_atr * eq_threshold
            eql_line = line.new(eq_btm_x, eq_prev_btm, n-eq_len, eq_btm
              , color = bull_css
              , style = line.style_dotted)

            eql_lbl = label.new(int(math.avg(n-eq_len, eq_btm_x)), eq_btm, 'EQL'
              , color = #00000000
              , textcolor = bull_css
              , style = label.style_label_up
              , size = eqhl_lbl_size)

            eql_alert := true

            if mode == 'Present'
                line.delete(eql_line[1])
                label.delete(eql_lbl[1])

        eq_prev_btm := eq_btm
        eq_btm_x := n-eq_len

//-----------------------------------------------------------------------------}
//Fair Value Gaps
//-----------------------------------------------------------------------------{
var bullish_fvg_max = array.new_box(0)
var bullish_fvg_min = array.new_box(0)

var bearish_fvg_max = array.new_box(0)
var bearish_fvg_min = array.new_box(0)

float bullish_fvg_avg = na
float bearish_fvg_avg = na

bullish_fvg_cnd = false
bearish_fvg_cnd = false

[src_c1, src_o1, src_h, src_l, src_h2, src_l2] =
  request.security(syminfo.tickerid, fvg_tf, get_ohlc())

if show_fvg
    delta_per = (src_c1 - src_o1) / src_o1 * 100

    change_tf = timeframe.change(fvg_tf)

    threshold = fvg_auto ? ta.cum(math.abs(change_tf ? delta_per : 0)) / n * 2 
      : 0

    //FVG conditions
    bullish_fvg_cnd := src_l > src_h2
      and src_c1 > src_h2 
      and delta_per > threshold
      and change_tf

    bearish_fvg_cnd := src_h < src_l2 
      and src_c1 < src_l2 
      and -delta_per > threshold
      and change_tf

    //FVG Areas
    if bullish_fvg_cnd
        array.unshift(bullish_fvg_max, box.new(n-1, src_l, n + fvg_extend, math.avg(src_l, src_h2)
          , border_color = bull_fvg_css
          , bgcolor = bull_fvg_css))
        
        array.unshift(bullish_fvg_min, box.new(n-1, math.avg(src_l, src_h2), n + fvg_extend, src_h2
          , border_color = bull_fvg_css
          , bgcolor = bull_fvg_css))
    
    if bearish_fvg_cnd
        array.unshift(bearish_fvg_max, box.new(n-1, src_h, n + fvg_extend, math.avg(src_h, src_l2)
          , border_color = bear_fvg_css
          , bgcolor = bear_fvg_css))
        
        array.unshift(bearish_fvg_min, box.new(n-1, math.avg(src_h, src_l2), n + fvg_extend, src_l2
          , border_color = bear_fvg_css
          , bgcolor = bear_fvg_css))

    for bx in bullish_fvg_min
        if low < box.get_bottom(bx)
            box.delete(bx)
            box.delete(array.get(bullish_fvg_max, array.indexof(bullish_fvg_min, bx)))
    
    for bx in bearish_fvg_max
        if high > box.get_top(bx)
            box.delete(bx)
            box.delete(array.get(bearish_fvg_min, array.indexof(bearish_fvg_max, bx)))

//-----------------------------------------------------------------------------}
//Previous day/week high/lows
//-----------------------------------------------------------------------------{
//Daily high/low
[pdh, pdl] = request.security(syminfo.tickerid, 'D', hl()
  , lookahead = barmerge.lookahead_on)

//Weekly high/low
[pwh, pwl] = request.security(syminfo.tickerid, 'W', hl()
  , lookahead = barmerge.lookahead_on)

//Monthly high/low
[pmh, pml] = request.security(syminfo.tickerid, 'M', hl()
  , lookahead = barmerge.lookahead_on)

//Display Daily
if show_pdhl
    phl(pdh, pdl, 'D', pdhl_css)

//Display Weekly
if show_pwhl
    phl(pwh, pwl, 'W', pwhl_css)
    
//Display Monthly
if show_pmhl
    phl(pmh, pml, 'M', pmhl_css)

//-----------------------------------------------------------------------------}
//Premium/Discount/Equilibrium zones
//-----------------------------------------------------------------------------{
var premium = box.new(na, na, na, na
  , bgcolor = color.new(premium_css, 80)
  , border_color = na)

var premium_lbl = label.new(na, na
  , text = 'Premium'
  , color = TRANSP_CSS
  , textcolor = premium_css
  , style = label.style_label_down
  , size = size.small)

var eq = box.new(na, na, na, na
  , bgcolor = color.rgb(120, 123, 134, 80)
  , border_color = na)

var eq_lbl = label.new(na, na
  , text = 'Equilibrium'
  , color = TRANSP_CSS
  , textcolor = eq_css
  , style = label.style_label_left
  , size = size.small)

var discount = box.new(na, na, na, na
  , bgcolor = color.new(discount_css, 80)
  , border_color = na)

var discount_lbl = label.new(na, na
  , text = 'Discount'
  , color = TRANSP_CSS
  , textcolor = discount_css
  , style = label.style_label_up
  , size = size.small)

//Show Premium/Discount Areas
if barstate.islast and show_sd
    avg = math.avg(trail_up, trail_dn)

    box.set_lefttop(premium, math.max(top_x, btm_x), trail_up)
    box.set_rightbottom(premium, n, .95 * trail_up + .05 * trail_dn)

    label.set_xy(premium_lbl, int(math.avg(math.max(top_x, btm_x), n)), trail_up)

    box.set_lefttop(eq, math.max(top_x, btm_x), .525 * trail_up + .475*trail_dn)
    box.set_rightbottom(eq, n, .525 * trail_dn + .475 * trail_up)

    label.set_xy(eq_lbl, n, avg)
    
    box.set_lefttop(discount, math.max(top_x, btm_x), .95 * trail_dn + .05 * trail_up)
    box.set_rightbottom(discount, n, trail_dn)
    label.set_xy(discount_lbl, int(math.avg(math.max(top_x, btm_x), n)), trail_dn)

//-----------------------------------------------------------------------------}
//Trend
//-----------------------------------------------------------------------------{
var color trend_css = na

if show_trend
    if style == 'Colored'
        trend_css := itrend == 1 ? bull_css : bear_css
    else if style == 'Monochrome'
        trend_css := itrend == 1 ? #b2b5be : #5d606b

plotcandle(open, high, low, close
  , color = trend_css
  , wickcolor = trend_css
  , bordercolor = trend_css
  , editable = false)

//-----------------------------------------------------------------------------}
//Alerts
//-----------------------------------------------------------------------------{
//Internal Structure
alertcondition(bull_ibos_alert, 'Internal Bullish BOS', 'Internal Bullish BOS formed')
alertcondition(bull_ichoch_alert, 'Internal Bullish CHoCH', 'Internal Bullish CHoCH formed')

alertcondition(bear_ibos_alert, 'Internal Bearish BOS', 'Internal Bearish BOS formed')
alertcondition(bear_ichoch_alert, 'Internal Bearish CHoCH', 'Internal Bearish CHoCH formed')

//Swing Structure
alertcondition(bull_bos_alert, 'Bullish BOS', 'Internal Bullish BOS formed')
alertcondition(bull_choch_alert, 'Bullish CHoCH', 'Internal Bullish CHoCH formed')

alertcondition(bear_bos_alert, 'Bearish BOS', 'Bearish BOS formed')
alertcondition(bear_choch_alert, 'Bearish CHoCH', 'Bearish CHoCH formed')

//order Blocks
alertcondition(bull_iob_break, 'Bullish Internal OB Breakout', 'Price broke bullish internal OB')
alertcondition(bear_iob_break, 'Bearish Internal OB Breakout', 'Price broke bearish internal OB')

alertcondition(bull_ob_break, 'Bullish Swing OB Breakout', 'Price broke bullish swing OB')
alertcondition(bear_ob_break, 'Bearish Swing OB Breakout', 'Price broke bearish swing OB')

//EQH/EQL
alertcondition(eqh_alert, 'Equal Highs', 'Equal highs detected')
alertcondition(eql_alert, 'Equal Lows', 'Equal lows detected')

//FVG
alertcondition(bullish_fvg_cnd, 'Bullish FVG', 'Bullish FVG formed')
alertcondition(bearish_fvg_cnd, 'Bearish FVG', 'Bearish FVG formed')

//-----------------------------------------------------------------------------}

// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © fluxchart


// indicator(title = 'Volumized Breaker Blocks | Flux Charts', overlay = true, max_boxes_count = VBB_maxBoxesCount, max_labels_count = VBB_maxBoxesCount, max_lines_count = VBB_maxBoxesCount, max_bars_back = 5000)
//@version=5
const bool VBB_DEBUG = false
const int VBB_maxBoxesCount = 500
const float VBB_overlapThresholdPercentage = 0
const int VBB_maxDistanceToLastBar = 1750 // Affects Running Time
const int VBB_maxOrderBlocks = 60
var bool VBB_initRun = true



VBB_showInvalidated = input.bool(true, "Show Historic Zones", group = "General Configuration", display = display.none)
VBB_OBsEnabled = true
VBB_breakBlockVolumetricInfo = input.bool(false, "Volumetric Info", group = "General Configuration", inline="EV", display = display.none)
VBB_obEndMethod = input.string("Close", "Order Block Invalidation", options = ["Wick", "Close"],  group = "General Configuration", display = display.none)
VBB_bbEndMethod = input.string("Close", "Breaker Block Invalidation", options = ["Wick", "Close"],  group = "General Configuration", display = display.none)
VBB_combineOBs = VBB_DEBUG ? input.bool(true, "Combine Zones", group = "General Configuration", display = display.none) : true
VBB_maxATRMult = VBB_DEBUG ? input.float(3.5,"Max Atr Multiplier", group = "General Configuration") : 3.5
VBB_swingLength = input.int(10, 'Swing Length', minval = 3, tooltip="Swing length is used when finding breaker block formations. Smaller values will result in finding smaller breaker blocks.", group = "General Configuration", display = display.none)
VBB_zoneCount = input.string("Low", 'Zone Count', options = ["High", "Medium", "Low"], tooltip = "Number of Breaker Block Zones to be rendered. Higher options will result in older Breaker Blocks shown.",  group = "General Configuration", display = display.none)
VBB_bullishBreakerBlockColor = input(color.new(#2962ff, 75), "Bullish Breaker", inline = 'breakerColor', group = 'General Configuration', display = display.none)
VBB_bearishBreakerBlockColor = input(color.new(#ffeb3b, 75), "Bearish Breaker", inline = 'breakerColor', group = 'General Configuration', display = display.none)

VBB_bullishBreakerBlocks = VBB_zoneCount == "Low" ? 3 : VBB_zoneCount == "Medium" ? 5 : 10
VBB_bearishBreakerBlocks = VBB_zoneCount == "Low" ? 3 : VBB_zoneCount == "Medium" ? 5 : 10
VBB_bullishOrderBlocks = VBB_zoneCount == "Low" ? 15 : VBB_zoneCount == "Medium" ? 30 : 60
VBB_bearishOrderBlocks = VBB_zoneCount == "Low" ? 15 : VBB_zoneCount == "Medium" ? 30 : 60

VBB_BBsEnabled = true

VBB_timeframe1Enabled = true
VBB_timeframe1 = ""

VBB_breakersFull = VBB_DEBUG ? input.bool(true, "Breakers Full", group = "Style", display = display.none) : true
VBB_textColor = input.color(#ffffff80, "Text Color", group = "Style")
VBB_combinedText = VBB_DEBUG ? input.bool(false, "Combined Text", group = "Style", inline = "CombinedColor") : false

VBB_atr = ta.atr(10)

type VBB_orderBlockInfo
    float VBB_top
    float VBB_bottom
    float VBB_obVolume
    string VBB_obType
    int VBB_startTime
    float VBB_bbVolume
    float VBB_obLowVolume
    float VBB_obHighVolume
    bool VBB_breaker
    int VBB_breakTime
    string VBB_timeframeStr
    bool VBB_disabled = false
    string VBB_combinedTimeframesStr = na
    bool VBB_combined = false

type VBB_orderBlock
    VBB_orderBlockInfo VBB_info
    bool VBB_isRendered = false

    box VBB_orderBox = na
    box VBB_breakerBox = na

    line VBB_orderBoxLineTop = na
    line VBB_orderBoxLineBottom = na
    line VBB_breakerBoxLineTop = na
    line VBB_breakerBoxLineBottom = na
    //
    box VBB_orderBoxText = na
    box VBB_orderBoxPositive = na
    box VBB_orderBoxNegative = na

    line VBB_orderSeperator = na
    line VBB_orderTextSeperator = na

VBB_createOrderBlock (VBB_orderBlockInfo VBB_orderBlockInfoF) =>
    VBB_orderBlock VBB_newOrderBlock = VBB_orderBlock.new(VBB_orderBlockInfoF)
    VBB_newOrderBlock

VBB_safeDeleteOrderBlock (VBB_orderBlock VBB_orderBlockF) =>
    VBB_orderBlockF.VBB_isRendered := false

    box.delete(VBB_orderBlockF.VBB_orderBox)
    box.delete(VBB_orderBlockF.VBB_breakerBox)
    box.delete(VBB_orderBlockF.VBB_orderBoxText)
    box.delete(VBB_orderBlockF.VBB_orderBoxPositive)
    box.delete(VBB_orderBlockF.VBB_orderBoxNegative)

    line.delete(VBB_orderBlockF.VBB_orderBoxLineTop)
    line.delete(VBB_orderBlockF.VBB_orderBoxLineBottom)
    line.delete(VBB_orderBlockF.VBB_breakerBoxLineTop)
    line.delete(VBB_orderBlockF.VBB_breakerBoxLineBottom)
    line.delete(VBB_orderBlockF.VBB_orderSeperator)
    line.delete(VBB_orderBlockF.VBB_orderTextSeperator)

type VBB_timeframeInfo
    int VBB_index = na
    string VBB_timeframeStr = na
    bool VBB_isEnabled = false

    VBB_orderBlockInfo[] VBB_bullishOrderBlocksList = na
    VBB_orderBlockInfo[] VBB_bearishOrderBlocksList = na

VBB_newTimeframeInfo (VBB_index, VBB_timeframeStr, VBB_isEnabled) =>
    VBB_newTFInfo = VBB_timeframeInfo.new()
    VBB_newTFInfo.VBB_index := VBB_index
    VBB_newTFInfo.VBB_isEnabled := VBB_isEnabled
    VBB_newTFInfo.VBB_timeframeStr := VBB_timeframeStr

    VBB_newTFInfo

type VBB_obSwing
    int VBB_x = na    
    float VBB_y = na
    float VBB_swingVolume = na
    bool VBB_crossed = false

// ____ TYPES END ____

var VBB_timeframeInfo[] VBB_timeframeInfos = array.from(VBB_newTimeframeInfo(1, VBB_timeframe1, VBB_timeframe1Enabled))
var VBB_bullishOrderBlocksList = array.new<VBB_orderBlockInfo>(0)
var VBB_bearishOrderBlocksList = array.new<VBB_orderBlockInfo>(0)

var VBB_allOrderBlocksList = array.new<VBB_orderBlock>(0)

VBB_moveLine(VBB_line, VBB_x, VBB_y, VBB_x2) =>
    line.set_xy1(VBB_line, VBB_x,  VBB_y)
    line.set_xy2(VBB_line, VBB_x2, VBB_y)

VBB_moveBox (VBB_box, VBB_topLeftX, VBB_topLeftY, VBB_bottomRightX, VBB_bottomRightY) =>
    box.set_lefttop(VBB_box, VBB_topLeftX, VBB_topLeftY)
    box.set_rightbottom(VBB_box, VBB_bottomRightX, VBB_bottomRightY)

VBB_isTimeframeLower (VBB_timeframe1F, VBB_timeframe2F) =>
    timeframe.in_seconds(VBB_timeframe1F) < timeframe.in_seconds(VBB_timeframe2F)

VBB_getMinTimeframe (VBB_timeframe1F, VBB_timeframe2F) =>
    if VBB_isTimeframeLower(VBB_timeframe1F, VBB_timeframe2F)
        VBB_timeframe1F
    else
        VBB_timeframe2F

VBB_getMaxTimeframe (VBB_timeframe1F, VBB_timeframe2F) =>
    if VBB_isTimeframeLower(VBB_timeframe1F, VBB_timeframe2F)
        VBB_timeframe2F
    else
        VBB_timeframe1F

VBB_formatTimeframeString (VBB_formatTimeframe) =>
    VBB_timeframeF = VBB_formatTimeframe == "" ? timeframe.period : VBB_formatTimeframe
    
    if str.contains(VBB_timeframeF, "D") or str.contains(VBB_timeframeF, "W") or str.contains(VBB_timeframeF, "S") or str.contains(VBB_timeframeF, "M")
        VBB_timeframeF
    else
        VBB_seconds = timeframe.in_seconds(VBB_timeframeF)
        if VBB_seconds >= 3600
            VBB_hourCount = int(VBB_seconds / 3600)
            str.tostring(VBB_hourCount) + " Hour" + (VBB_hourCount > 1 ? "s" : "")
        else
            VBB_timeframeF + " Min"

VBB_betterCross(VBB_s1, VBB_s2) =>
    string VBB_ret = na
    if VBB_s1 >= VBB_s2 and VBB_s1[1] < VBB_s2
        VBB_ret := "Bull"
    if VBB_s1 < VBB_s2 and VBB_s1[1] >= VBB_s2
        VBB_ret := "Bear"
    VBB_ret

VBB_colorWithTransparency (VBB_colorF, VBB_transparencyX) =>
    color.new(VBB_colorF, color.t(VBB_colorF) * VBB_transparencyX)

VBB_createOBBox (VBB_boxColor, VBB_transparencyX = 1.0, VBB_xlocType = xloc.bar_time) =>
    box.new(na, na, na, na, text_size = size.normal, xloc = VBB_xlocType, extend = extend.none, bgcolor = VBB_colorWithTransparency(VBB_boxColor, VBB_transparencyX), text_color = VBB_textColor, text_halign = text.align_center, border_color = #00000000)

VBB_renderOrderBlock (VBB_orderBlock VBB_ob) =>
    VBB_orderBlockInfo VBB_info = VBB_ob.VBB_info
    VBB_ob.VBB_isRendered := true
    VBB_breakerBlockColor = VBB_ob.VBB_info.VBB_obType == "Bull" ? VBB_bearishBreakerBlockColor : VBB_bullishBreakerBlockColor

    if VBB_info.VBB_breaker and VBB_BBsEnabled
        VBB_startTime = (VBB_OBsEnabled and not VBB_breakersFull) ? VBB_info.VBB_breakTime : VBB_info.VBB_startTime
        VBB_ob.VBB_breakerBox := box.new(VBB_startTime, VBB_info.VBB_top, time + 1, VBB_info.VBB_bottom, na, bgcolor = VBB_breakerBlockColor, extend = extend.none, xloc = xloc.bar_time, text_color = VBB_textColor, text_size = size.normal)
        VBB_BBText = (na(VBB_ob.VBB_info.VBB_combinedTimeframesStr) ? VBB_formatTimeframeString(VBB_ob.VBB_info.VBB_timeframeStr) : VBB_ob.VBB_info.VBB_combinedTimeframesStr) + " BB"
        box.set_text(VBB_ob.VBB_breakerBox, (VBB_breakBlockVolumetricInfo ? str.tostring(VBB_ob.VBB_info.VBB_bbVolume, format.volume) + "\n" : "") + (VBB_combinedText and VBB_ob.VBB_info.VBB_combined ? "[Combined]\n" : "") + VBB_BBText)
        VBB_ob.VBB_breakerBoxLineTop := line.new(VBB_startTime, VBB_info.VBB_top, time + 1, VBB_info.VBB_top, xloc.bar_time, extend.none, VBB_colorWithTransparency(VBB_breakerBlockColor, 0), line.style_dashed)
        VBB_ob.VBB_breakerBoxLineBottom := line.new(VBB_startTime, VBB_info.VBB_bottom, time + 1, VBB_info.VBB_bottom, xloc.bar_time, extend.none, VBB_colorWithTransparency(VBB_breakerBlockColor, 0), line.style_dashed)


VBB_findOBSwings(VBB_len) =>
    var VBB_swingType = 0
    var VBB_obSwing VBB_top = VBB_obSwing.new(na, na)
    var VBB_obSwing VBB_bottom = VBB_obSwing.new(na, na)
    
    VBB_upper = ta.highest(VBB_len)
    VBB_lower = ta.lowest(VBB_len)

    VBB_swingType := high[VBB_len] > VBB_upper ? 0 : low[VBB_len] < VBB_lower ? 1 : VBB_swingType

    if VBB_swingType == 0 and VBB_swingType[1] != 0
        VBB_top := VBB_obSwing.new(bar_index[VBB_len], high[VBB_len], volume[VBB_len])
    
    if VBB_swingType == 1 and VBB_swingType[1] != 1
        VBB_bottom := VBB_obSwing.new(bar_index[VBB_len], low[VBB_len], volume[VBB_len])

    [VBB_top, VBB_bottom]

VBB_findOrderBlocks () =>
    if bar_index > last_bar_index - VBB_maxDistanceToLastBar
        [VBB_top, VBB_btm] = VBB_findOBSwings(VBB_swingLength)
        VBB_useBody = false
        VBB_max = VBB_useBody ? math.max(close, open) : high
        VBB_min = VBB_useBody ? math.min(close, open) : low

        // Bullish Order Block
        VBB_bullishBreaked = 0

        if VBB_bullishOrderBlocksList.size() > 0
            for VBB_i = VBB_bullishOrderBlocksList.size() - 1 to 0
                VBB_currentOB = VBB_bullishOrderBlocksList.get(VBB_i)
            
                if not VBB_currentOB.VBB_breaker 
                    if ((VBB_obEndMethod == "Wick" ? low : close) < VBB_currentOB.VBB_bottom)
                        VBB_currentOB.VBB_breaker := true
                        VBB_currentOB.VBB_breakTime := time
                        VBB_currentOB.VBB_bbVolume := volume
                else
                    if (VBB_bbEndMethod == "Wick" ? high : close) > VBB_currentOB.VBB_top
                        VBB_bullishOrderBlocksList.remove(VBB_i)
                    else if VBB_i < VBB_bullishOrderBlocks and VBB_top.VBB_y < VBB_currentOB.VBB_top and VBB_top.VBB_y > VBB_currentOB.VBB_bottom 
                        VBB_bullishBreaked := 1

        if close > VBB_top.VBB_y and not VBB_top.VBB_crossed
            VBB_top.VBB_crossed := true

            VBB_boxBtm = VBB_max[1]
            VBB_boxTop = VBB_min[1]
            VBB_boxLoc = time[1]

            for VBB_i = 1 to (bar_index - VBB_top.VBB_x) - 1
                VBB_boxBtm := math.min(VBB_min[VBB_i], VBB_boxBtm)
                VBB_boxTop := VBB_boxBtm == VBB_min[VBB_i] ? VBB_max[VBB_i] : VBB_boxTop
                VBB_boxLoc := VBB_boxBtm == VBB_min[VBB_i] ? time[VBB_i] : VBB_boxLoc

            VBB_newOrderBlockInfo = VBB_orderBlockInfo.new(VBB_boxTop, VBB_boxBtm, volume + volume[1] + volume[2], "Bull", VBB_boxLoc)
            VBB_newOrderBlockInfo.VBB_obLowVolume := volume[2]
            VBB_newOrderBlockInfo.VBB_obHighVolume := volume + volume[1]
            VBB_obSize = math.abs(VBB_newOrderBlockInfo.VBB_top - VBB_newOrderBlockInfo.VBB_bottom)
            if VBB_obSize <= VBB_atr * VBB_maxATRMult
                VBB_bullishOrderBlocksList.unshift(VBB_newOrderBlockInfo)
                if VBB_bullishOrderBlocksList.size() > VBB_maxOrderBlocks
                    VBB_bullishOrderBlocksList.pop()

        // Bearish Order Block
        
        VBB_bearishBreaked = 0

        if VBB_bearishOrderBlocksList.size() > 0
            for VBB_i = VBB_bearishOrderBlocksList.size() - 1 to 0
                VBB_currentOB = VBB_bearishOrderBlocksList.get(VBB_i)

                if not VBB_currentOB.VBB_breaker 
                    if (VBB_obEndMethod == "Wick" ? high : close) > VBB_currentOB.VBB_top
                        VBB_currentOB.VBB_breaker := true
                        VBB_currentOB.VBB_breakTime := time
                        VBB_currentOB.VBB_bbVolume := volume
                else
                    if (VBB_bbEndMethod == "Wick" ? low : close) < VBB_currentOB.VBB_bottom
                        VBB_bearishOrderBlocksList.remove(VBB_i)
                    else if VBB_i < VBB_bearishOrderBlocks and VBB_btm.VBB_y > VBB_currentOB.VBB_bottom and VBB_btm.VBB_y < VBB_currentOB.VBB_top 
                        VBB_bearishBreaked := 1

        if close < VBB_btm.VBB_y and not VBB_btm.VBB_crossed
            VBB_btm.VBB_crossed := true

            VBB_boxBtm = VBB_min[1]
            VBB_boxTop = VBB_max[1]
            VBB_boxLoc = time[1]

            for VBB_i = 1 to (bar_index - VBB_btm.VBB_x) - 1
                VBB_boxTop := math.max(VBB_max[VBB_i], VBB_boxTop)
                VBB_boxBtm := VBB_boxTop == VBB_max[VBB_i] ? VBB_min[VBB_i] : VBB_boxBtm
                VBB_boxLoc := VBB_boxTop == VBB_max[VBB_i] ? time[VBB_i] : VBB_boxLoc

            VBB_newOrderBlockInfo = VBB_orderBlockInfo.new(VBB_boxTop, VBB_boxBtm, volume + volume[1] + volume[2], "Bear", VBB_boxLoc)
            VBB_newOrderBlockInfo.VBB_obLowVolume := volume + volume[1]
            VBB_newOrderBlockInfo.VBB_obHighVolume := volume[2]
            VBB_obSize = math.abs(VBB_newOrderBlockInfo.VBB_top - VBB_newOrderBlockInfo.VBB_bottom)
            if VBB_obSize <= VBB_atr * VBB_maxATRMult
                VBB_bearishOrderBlocksList.unshift(VBB_newOrderBlockInfo)
                if VBB_bearishOrderBlocksList.size() > VBB_maxOrderBlocks
                    VBB_bearishOrderBlocksList.pop()
    true

VBB_areaOfOB (VBB_orderBlockInfo VBB_OBInfoF) =>
    float VBB_XA1 = VBB_OBInfoF.VBB_startTime
    float VBB_XA2 = na(VBB_OBInfoF.VBB_breakTime) ? time + 1 : VBB_OBInfoF.VBB_breakTime
    float VBB_YA1 = VBB_OBInfoF.VBB_top
    float VBB_YA2 = VBB_OBInfoF.VBB_bottom
    float VBB_edge1 = math.sqrt((VBB_XA2 - VBB_XA1) * (VBB_XA2 - VBB_XA1) + (VBB_YA2 - VBB_YA2) * (VBB_YA2 - VBB_YA2))
    float VBB_edge2 = math.sqrt((VBB_XA2 - VBB_XA2) * (VBB_XA2 - VBB_XA2) + (VBB_YA2 - VBB_YA1) * (VBB_YA2 - VBB_YA1))
    float VBB_totalArea = VBB_edge1 * VBB_edge2
    VBB_totalArea

VBB_doOBsTouch (VBB_orderBlockInfo VBB_OBInfo1, VBB_orderBlockInfo VBB_OBInfo2) =>
    float VBB_XA1 = VBB_OBInfo1.VBB_startTime
    float VBB_XA2 = na(VBB_OBInfo1.VBB_breakTime) ? time + 1 : VBB_OBInfo1.VBB_breakTime
    float VBB_YA1 = VBB_OBInfo1.VBB_top
    float VBB_YA2 = VBB_OBInfo1.VBB_bottom

    float VBB_XB1 = VBB_OBInfo2.VBB_startTime
    float VBB_XB2 = na(VBB_OBInfo2.VBB_breakTime) ? time + 1 : VBB_OBInfo2.VBB_breakTime
    float VBB_YB1 = VBB_OBInfo2.VBB_top
    float VBB_YB2 = VBB_OBInfo2.VBB_bottom
    float VBB_intersectionArea = math.max(0, math.min(VBB_XA2, VBB_XB2) - math.max(VBB_XA1, VBB_XB1)) * math.max(0, math.min(VBB_YA1, VBB_YB1) - math.max(VBB_YA2, VBB_YB2))
    float VBB_unionArea = VBB_areaOfOB(VBB_OBInfo1) + VBB_areaOfOB(VBB_OBInfo2) - VBB_intersectionArea
    
    float VBB_overlapPercentage = (VBB_intersectionArea / VBB_unionArea) * 100.0

    if VBB_overlapPercentage > VBB_overlapThresholdPercentage
        true
    else
        false

VBB_isOBValid (VBB_orderBlockInfo VBB_OBInfo) =>
    VBB_valid = true
    if VBB_OBInfo.VBB_disabled
        VBB_valid := false
    VBB_valid

VBB_combineOBsFunc () =>
    if VBB_allOrderBlocksList.size() > 0
        VBB_lastCombinations = 999
        while VBB_lastCombinations > 0
            VBB_lastCombinations := 0
            for VBB_i = 0 to VBB_allOrderBlocksList.size() - 1
                VBB_curOB1 = VBB_allOrderBlocksList.get(VBB_i)
                for VBB_j = 0 to VBB_allOrderBlocksList.size() - 1
                    VBB_curOB2 = VBB_allOrderBlocksList.get(VBB_j)
                    if VBB_i == VBB_j
                        continue
                    if not VBB_isOBValid(VBB_curOB1.VBB_info) or not VBB_isOBValid(VBB_curOB2.VBB_info)
                        continue
                    if VBB_curOB1.VBB_info.VBB_obType != VBB_curOB2.VBB_info.VBB_obType
                        continue
                    if VBB_doOBsTouch(VBB_curOB1.VBB_info, VBB_curOB2.VBB_info)
                        VBB_curOB1.VBB_info.VBB_disabled := true
                        VBB_curOB2.VBB_info.VBB_disabled := true
                        VBB_orderBlock VBB_newOB = VBB_createOrderBlock(VBB_orderBlockInfo.new(math.max(VBB_curOB1.VBB_info.VBB_top, VBB_curOB2.VBB_info.VBB_top), math.min(VBB_curOB1.VBB_info.VBB_bottom, VBB_curOB2.VBB_info.VBB_bottom), VBB_curOB1.VBB_info.VBB_obVolume + VBB_curOB2.VBB_info.VBB_obVolume, VBB_curOB1.VBB_info.VBB_obType))
                        VBB_newOB.VBB_info.VBB_startTime := math.min(VBB_curOB1.VBB_info.VBB_startTime, VBB_curOB2.VBB_info.VBB_startTime)
                        VBB_newOB.VBB_info.VBB_breakTime := math.max(nz(VBB_curOB1.VBB_info.VBB_breakTime), nz(VBB_curOB2.VBB_info.VBB_breakTime))
                        VBB_newOB.VBB_info.VBB_breakTime := VBB_newOB.VBB_info.VBB_breakTime == 0 ? na : VBB_newOB.VBB_info.VBB_breakTime
                        VBB_newOB.VBB_info.VBB_timeframeStr := VBB_curOB1.VBB_info.VBB_timeframeStr

                        VBB_newOB.VBB_info.VBB_obVolume := VBB_curOB1.VBB_info.VBB_obVolume + VBB_curOB2.VBB_info.VBB_obVolume
                        VBB_newOB.VBB_info.VBB_obLowVolume := VBB_curOB1.VBB_info.VBB_obLowVolume + VBB_curOB2.VBB_info.VBB_obLowVolume
                        VBB_newOB.VBB_info.VBB_obHighVolume := VBB_curOB1.VBB_info.VBB_obHighVolume + VBB_curOB2.VBB_info.VBB_obHighVolume
                        VBB_newOB.VBB_info.VBB_bbVolume := nz(VBB_curOB1.VBB_info.VBB_bbVolume, 0) + nz(VBB_curOB2.VBB_info.VBB_bbVolume, 0)
                        VBB_newOB.VBB_info.VBB_breaker := VBB_curOB1.VBB_info.VBB_breaker or VBB_curOB2.VBB_info.VBB_breaker
                        
                        VBB_newOB.VBB_info.VBB_combined := true
                        if timeframe.in_seconds(VBB_curOB1.VBB_info.VBB_timeframeStr) != timeframe.in_seconds(VBB_curOB2.VBB_info.VBB_timeframeStr)
                            VBB_newOB.VBB_info.VBB_combinedTimeframesStr := (na(VBB_curOB1.VBB_info.VBB_combinedTimeframesStr) ? VBB_formatTimeframeString(VBB_curOB1.VBB_info.VBB_timeframeStr) : VBB_curOB1.VBB_info.VBB_combinedTimeframesStr) + " & " + (na(VBB_curOB2.VBB_info.VBB_combinedTimeframesStr) ? VBB_formatTimeframeString(VBB_curOB2.VBB_info.VBB_timeframeStr) : VBB_curOB2.VBB_info.VBB_combinedTimeframesStr)
                        VBB_allOrderBlocksList.unshift(VBB_newOB)
                        VBB_lastCombinations += 1


VBB_reqSeq (VBB_timeframeStr) =>
    [VBB_bullishOrderBlocksListF, VBB_bearishOrderBlocksListF] = request.security(syminfo.tickerid, VBB_timeframeStr, [VBB_bullishOrderBlocksList, VBB_bearishOrderBlocksList])
    [VBB_bullishOrderBlocksListF, VBB_bearishOrderBlocksListF]

VBB_getTFData (VBB_timeframeInfo VBB_timeframeInfoF, VBB_timeframeStr) =>
    if not VBB_isTimeframeLower(VBB_timeframeInfoF.VBB_timeframeStr, timeframe.period) and VBB_timeframeInfoF.VBB_isEnabled
        [VBB_bullishOrderBlocksListF, VBB_bearishOrderBlocksListF] = VBB_reqSeq(VBB_timeframeStr)
        [VBB_bullishOrderBlocksListF, VBB_bearishOrderBlocksListF]
    else
        [na, na]

VBB_handleTimeframeInfo (VBB_timeframeInfo VBB_timeframeInfoF, VBB_bullishOrderBlocksListF, VBB_bearishOrderBlocksListF) =>
    if not VBB_isTimeframeLower(VBB_timeframeInfoF.VBB_timeframeStr, timeframe.period) and VBB_timeframeInfoF.VBB_isEnabled
        VBB_timeframeInfoF.VBB_bullishOrderBlocksList := VBB_bullishOrderBlocksListF
        VBB_timeframeInfoF.VBB_bearishOrderBlocksList := VBB_bearishOrderBlocksListF

VBB_arrHasOB (VBB_orderBlock[] VBB_arr, VBB_orderBlock VBB_obF) =>
    VBB_hasOB = false
    if VBB_arr.size() > 0
        for VBB_i = 0 to VBB_arr.size() - 1
            VBB_orderBlock VBB_ob1 = VBB_arr.get(VBB_i)
            if VBB_isOBValid(VBB_ob1.VBB_info) and VBB_isOBValid(VBB_obF.VBB_info) and (VBB_ob1.VBB_info.VBB_breaker == VBB_obF.VBB_info.VBB_breaker) and VBB_doOBsTouch(VBB_ob1.VBB_info, VBB_obF.VBB_info)
                VBB_hasOB := true
                break
    VBB_hasOB

bool VBB_newBullishBBTick = false
bool VBB_newBearishBBTick = false
string VBB_BBAlertTime = ""

VBB_handleOrderBlocksFinal () =>
    if VBB_DEBUG
        log.info("Bullish OB Count " + str.tostring(VBB_bullishOrderBlocksList.size()))
        log.info("Bearish OB Count " + str.tostring(VBB_bearishOrderBlocksList.size()))

    VBB_newBullishOBAlert = false
    VBB_newBearishOBAlert = false

    VBB_newBullishBBAlert = false
    VBB_newBearishBBAlert = false
    
    VBB_alertTimeOB = ""
    VBB_alertTimeBB = ""

    VBB_orderBlock[] VBB_orderBlocksToAdd = array.new<VBB_orderBlock>(0)

    for VBB_i = 0 to VBB_timeframeInfos.size() - 1
        VBB_curTimeframe = VBB_timeframeInfos.get(VBB_i)
        if not VBB_curTimeframe.VBB_isEnabled
            continue
        if not na(VBB_curTimeframe.VBB_bullishOrderBlocksList)
            if VBB_curTimeframe.VBB_bullishOrderBlocksList.size() > 0
                for VBB_j = 0 to math.min(VBB_curTimeframe.VBB_bullishOrderBlocksList.size() - 1, VBB_bullishOrderBlocks - 1)
                    VBB_orderBlockInfoF = VBB_curTimeframe.VBB_bullishOrderBlocksList.get(VBB_j)
                    VBB_orderBlockInfoF.VBB_timeframeStr := VBB_curTimeframe.VBB_timeframeStr
                    VBB_orderBlocksToAdd.unshift(VBB_createOrderBlock(VBB_orderBlockInfo.copy(VBB_orderBlockInfoF)))

        if not na(VBB_curTimeframe.VBB_bullishOrderBlocksList)
            if VBB_curTimeframe.VBB_bearishOrderBlocksList.size() > 0
                for VBB_j = 0 to math.min(VBB_curTimeframe.VBB_bearishOrderBlocksList.size() - 1, VBB_bearishOrderBlocks - 1)
                    VBB_orderBlockInfoF = VBB_curTimeframe.VBB_bearishOrderBlocksList.get(VBB_j)
                    VBB_orderBlockInfoF.VBB_timeframeStr := VBB_curTimeframe.VBB_timeframeStr
                    VBB_orderBlocksToAdd.unshift(VBB_createOrderBlock(VBB_orderBlockInfo.copy(VBB_orderBlockInfoF)))

    // Check New Order & Breaker Blocks
    if VBB_orderBlocksToAdd.size () > 0
        for VBB_i = 0 to VBB_orderBlocksToAdd.size() - 1
            VBB_obToTest = VBB_orderBlocksToAdd.get(VBB_i)
            if VBB_obToTest.VBB_info.VBB_breaker == false
                if not VBB_arrHasOB(VBB_allOrderBlocksList, VBB_obToTest)
                    VBB_alertTimeOB := VBB_obToTest.VBB_info.VBB_timeframeStr
                    if VBB_obToTest.VBB_info.VBB_obType == "Bull"
                        VBB_newBullishOBAlert := true
                    else
                        VBB_newBearishOBAlert := true
            else
                if not VBB_arrHasOB(VBB_allOrderBlocksList, VBB_obToTest)
                    VBB_alertTimeBB := VBB_obToTest.VBB_info.VBB_timeframeStr
                    if VBB_obToTest.VBB_info.VBB_obType == "Bull"
                        VBB_newBearishBBAlert := true
                    else
                        VBB_newBullishBBAlert := true
    
    // Delete Old Order Blocks
    if VBB_allOrderBlocksList.size () > 0
        for VBB_i = 0 to VBB_allOrderBlocksList.size() - 1
            VBB_safeDeleteOrderBlock(VBB_allOrderBlocksList.get(VBB_i))
    VBB_allOrderBlocksList.clear()

    // Add New Order Blocks
    if VBB_orderBlocksToAdd.size () > 0
        for VBB_i = 0 to VBB_orderBlocksToAdd.size() - 1
            VBB_allOrderBlocksList.unshift(VBB_orderBlocksToAdd.get(VBB_i))

    if VBB_combineOBs
        VBB_combineOBsFunc()

    if VBB_allOrderBlocksList.size() > 0
        for VBB_i = 0 to VBB_allOrderBlocksList.size() - 1
            VBB_curOB = VBB_allOrderBlocksList.get(VBB_i)
            if VBB_isOBValid(VBB_curOB.VBB_info)
                VBB_renderOrderBlock(VBB_curOB)
    
    [VBB_alertTimeOB, VBB_alertTimeBB, VBB_newBullishOBAlert, VBB_newBearishOBAlert, VBB_newBullishBBAlert, VBB_newBearishBBAlert]

VBB_findOrderBlocks()

[VBB_bullishOrderBlocksListTimeframe1, VBB_bearishOrderBlocksListTimeframe1] = VBB_getTFData(VBB_timeframeInfos.get(0), VBB_timeframe1)

if barstate.isconfirmed and (bar_index > last_bar_index - VBB_maxDistanceToLastBar)
    VBB_handleTimeframeInfo(VBB_timeframeInfos.get(0), VBB_bullishOrderBlocksListTimeframe1, VBB_bearishOrderBlocksListTimeframe1)
    [VBB_alertTimeOB, VBB_alertTimeBB, VBB_newBullishOBAlert, VBB_newBearishOBAlert, VBB_newBullishBBAlert, VBB_newBearishBBAlert] = VBB_handleOrderBlocksFinal()
    if VBB_newBullishBBAlert
        VBB_newBullishBBTick := true
    if VBB_newBearishBBAlert
        VBB_newBearishBBTick := true

alertcondition((VBB_newBullishBBTick or VBB_newBearishBBTick) and not VBB_initRun, "Breaker Block Formation", "A new Breaker Block has formed.")
alertcondition(VBB_newBullishBBTick and not VBB_initRun, "Bullish Breaker Block Formation", "A new Bullish Breaker Block has formed.")
alertcondition(VBB_newBearishBBTick and not VBB_initRun, "Bearish Breaker Block Formation", "A new Bearish Breaker Block has formed.")

if barstate.isconfirmed
    VBB_initRun := false
