// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// ©️ Dev Lucem

//@version=5
//@author=devlucem

// THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
// THE <PERSON><PERSON>ZAG SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
// I HOPE U LOVE IT

// Seek Menu
import DevLucem/ZigLib/1 as ZigZag
indicator('ZigZag++', 'Gold-ZigZag++ [LD]', true, format.price, max_labels_count=200, max_lines_count=50)

// Fetch Ingredients 
Depth = input.int(2, 'Depth', minval=1, step=1, group="ZigZag Config")
Deviation = input.int(5, 'Deviation', minval=1, step=1, group="ZigZag Config")
Backstep = input.int(2, 'Backstep', minval=2, step=1, group="ZigZag Config")
line_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group="Lines")
labels = input(0, "Labels Transparency", group="Labels")
upcolor = input(color.lime, 'Bull Color', group="Colors")
dncolor = input(color.red, 'Bear Color', group="Colors")
lines = input(0, "Lines Transparency", group="Lines")
background = input(80, "Background Transparency", group="Colors")
label_size = switch input.int(3, "Label Size", minval=1, maxval=5, group="Labels")
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
repaint = input(true, 'Repaint Levels')
extend = input(false, "Extend ZigZag", group="Lines")

// Bake it with a simple oven this time
[direction, z1, z2] = ZigZag.zigzag(low, high, Depth, Deviation, Backstep)
string nowPoint = ""
var float lastPoint = na
if bool(ta.change(direction))
    lastPoint := z1.price[1]

// Let it Cool And Serve
line zz = na
label point = na

if repaint
    zz := line.new(z1, z2, xloc.bar_time, extend ? extend.right : extend.none, color.new(direction > 0 ? upcolor : dncolor, lines), width=line_thick)
    nowPoint := direction < 0 ? (z2.price < lastPoint ? "LL" : na) : (z2.price > lastPoint ? "HH" : na)
    if (nowPoint == "LL" or nowPoint == "HH")
        point := label.new(z2, nowPoint, xloc.bar_time, yloc.price, 
         color.new(direction < 0 ? upcolor : dncolor, labels), direction > 0 ? label.style_label_down : label.style_label_up, color.new(direction > 0 ? upcolor : dncolor, labels), label_size)
    if direction == direction[1]
        line.delete(zz[1])
        label.delete(point[1])
    else
        line.set_extend(zz[1], extend.none)
else
    if direction != direction[1]
        zz := line.new(z1[1], z2[1], xloc.bar_time, extend.none, color.new(direction > 0 ? upcolor : dncolor, lines), width=line_thick)
        nowPoint := direction[1] < 0 ? (z2.price[1] < lastPoint[1] ? "LL" : na) : (z2.price[1] > lastPoint[1] ? "HH" : na)
        if (nowPoint == "LL" or nowPoint == "HH")
            point := label.new(z2[1], nowPoint, xloc.bar_time, yloc.price, 
             color.new(direction[1] < 0 ? upcolor : dncolor, labels), direction[1] > 0 ? label.style_label_down : label.style_label_up, color.new(direction[1] > 0 ? upcolor : dncolor, labels), label_size)
bgcolor(direction < 0 ? color.new(dncolor, background) : color.new(upcolor, background), title='Direction Background')
plotarrow(direction, "direction", display=display.status_line)

// Logic to draw trendline touching 3 HH after an LL
var line trendline_long = na
var int hh_count = 0
var float hh1_price = na
var float hh2_price = na
var float hh3_price = na
var int hh1_bar = na
var int hh2_bar = na
var int hh3_bar = na
if (nowPoint == "LL")
    hh_count := 0
    line.delete(trendline_long)
if (nowPoint == "HH")
    hh_count := hh_count + 1
    if (hh_count == 1)
        hh1_price := z2.price
        hh1_bar := bar_index
    if (hh_count == 2)
        hh2_price := z2.price
        hh2_bar := bar_index
    if (hh_count == 3)
        hh3_price := z2.price
        hh3_bar := bar_index
        trendline_long := line.new(x1=hh1_bar, y1=hh1_price, x2=hh3_bar, y2=hh3_price, color=color.white, width=2)
    if (hh_count > 3)
        line.set_xy2(trendline_long, x=bar_index, y=z2.price)

// Long Condition: LL followed by 3 HH
long_condition = (nowPoint == "LL" and hh_count == 3 and high > line.get_y2(trendline_long))
if (long_condition)
    alert("Long condition met", alert.freq_once_per_bar)

// Logic to draw trendline touching 3 LL after an HH
var line trendline_short = na
var int ll_count = 0
var float ll1_price = na
var float ll2_price = na
var float ll3_price = na
var int ll1_bar = na
var int ll2_bar = na
var int ll3_bar = na
if (nowPoint == "HH")
    ll_count := 0
    line.delete(trendline_short)
if (nowPoint == "LL")
    ll_count := ll_count + 1
    if (ll_count == 1)
        ll1_price := z2.price
        ll1_bar := bar_index
    if (ll_count == 2)
        ll2_price := z2.price
        ll2_bar := bar_index
    if (ll_count == 3)
        ll3_price := z2.price
        ll3_bar := bar_index
        trendline_short := line.new(x1=ll1_bar, y1=ll1_price, x2=ll3_bar, y2=ll3_price, color=color.white, width=2)
    if (ll_count > 3)
        line.set_xy2(trendline_short, x=bar_index, y=z2.price)

// Short Condition: HH followed by 3 LL
short_condition = (nowPoint == "HH" and ll_count == 3 and low < line.get_y2(trendline_short))
if (short_condition)
    alert("Short condition met", alert.freq_once_per_bar)