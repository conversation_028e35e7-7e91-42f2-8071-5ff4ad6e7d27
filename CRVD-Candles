//@version=5
indicator("Volume Suite - By Leviathan", overlay = false, format = format.volume)

g1 = 'General'
g2 = 'Large Change Thresholds'
g3 = 'Volume-Price Imbalances'
g5 = 'Additional Settings'
g4 = 'Display Settings'
g6 = 'Moving Averages'
g7 = 'Divergences'  // Add new group

// Divergence Settings
showDivs = input.bool(true, "Show Divergences", group=g7)
bullDivColor = input.color(#26a69a, "Bullish Divergence", group=g7) 
bearDivColor = input.color(#ef5350, "Bearish Divergence", group=g7)
divLength = input.int(5, "Divergence Lookback", minval=2, group=g7)

// General
upcol    = input.color(#d1d4dc, 'Primary Colors              ▲', inline = 'udcol')
downcol  = input.color(#9598a1f6, '▼', inline = 'udcol')

// Large Change Thresholds
show_mult1     = input.bool(true, '',  inline = 'mult1', group = g2)
mult           = input.float(1.5, '>', inline = 'mult1', group = g2, step = 0.1)
upcol_mult1    = input.color(#c8e6c9, '     ▲', inline = 'mult1', group = g2)
downcol_mult1  = input.color(#faa1a4, '▼', inline = 'mult1', group = g2)

show_mult2     = input.bool(true, '', inline = 'mult2', group = g2)
mult2          = input.float(2.5, '>', inline = 'mult2', group = g2, step = 0.1)
upcol_mult2    = input.color(#a5d6a7, '     ▲', inline = 'mult2', group = g2)
downcol_mult2  = input.color(#f77c80, '▼', inline = 'mult2', group = g2)

show_mult3     = input.bool(true, '', inline = 'mult3', group = g2)
mult3          = input.float(3.5, '>', inline = 'mult3', group = g2, step = 0.1)
upcol_mult3    = input.color(#66bb6a, '     ▲', inline = 'mult3', group = g2)
downcol_mult3  = input.color(#f7525f, '▼', inline = 'mult3', group = g2)

// Display
crv_disp = input.string('Candles', 'Cumulative Relative Volume', options = ['Candles', 'Line'], group = g4)

// Additional settings
color_candles = input.bool(false, 'Color Candles', group = g5)
threshtype    = input.string('RELATIVE', 'Threshold Calculation Type', options = ['SMA', 'RELATIVE', 'Z-SCORE'], group = g5)
rellen        = input.int(20, 'Relative Length', group = g5)

// Relative function
f_relative(src) =>
    src / ta.sma(src, rellen)

// Relative Volume calculations
rvol = f_relative(volume)
obv  = ta.cum(close > open ? rvol : -rvol)

// Coloring 
CRV_COL       = close > open ? upcol : downcol
color bar_col = color.rgb(0,0,0,100)

// Threshold coloring
if threshtype=='RELATIVE'
    if show_mult1 and f_relative(rvol)>mult
        CRV_COL := close>open ? upcol_mult1 : downcol_mult1
        bar_col := CRV_COL
    if show_mult2 and f_relative(rvol)>mult2
        CRV_COL := close>open ? upcol_mult2 : downcol_mult2
        bar_col := CRV_COL
    if show_mult3 and f_relative(rvol)>mult3
        CRV_COL := close>open ? upcol_mult3 : downcol_mult3
        bar_col := CRV_COL


// Divergence Detection
// Divergence Variables
lbR = input.int(2, "Pivot Lookback Right", minval=1, group=g7)
lbL = input.int(5, "Pivot Lookback Left", minval=1, group=g7)
rangeUpper = input.int(60, "Range Top", minval=1, group=g7)
rangeLower = input.int(5, "Range Bottom", minval=1, group=g7)

// Get OBV values for divergence
obvValue = obv
plFound = na(ta.pivotlow(obvValue, lbL, lbR)) ? false : true
phFound = na(ta.pivothigh(obvValue, lbL, lbR)) ? false : true

// Regular Bullish Divergence
bottomObv = ta.valuewhen(plFound, obvValue[lbR], 0)
bottomPrice = ta.valuewhen(plFound, low[lbR], 0)
bottomIndex = ta.valuewhen(plFound, bar_index[lbR], 0)
prevBottomObv = ta.valuewhen(plFound, obvValue[lbR], 1)
prevBottomPrice = ta.valuewhen(plFound, low[lbR], 1)
prevBottomIndex = ta.valuewhen(plFound, bar_index[lbR], 1)

// Regular Bearish Divergence
topObv = ta.valuewhen(phFound, obvValue[lbR], 0)
topPrice = ta.valuewhen(phFound, high[lbR], 0)
topIndex = ta.valuewhen(phFound, bar_index[lbR], 0)
prevTopObv = ta.valuewhen(phFound, obvValue[lbR], 1)
prevTopPrice = ta.valuewhen(phFound, high[lbR], 1)
prevTopIndex = ta.valuewhen(phFound, bar_index[lbR], 1)

// Divergence Conditions
bullCond = bottomPrice < prevBottomPrice and bottomObv > prevBottomObv and plFound
bearCond = topPrice > prevTopPrice and topObv < prevTopObv and phFound

// Checking for range and time constraints
rangeObv = ta.barssince(plFound) <= rangeUpper and ta.barssince(plFound) >= rangeLower
rangePrv = ta.barssince(phFound) <= rangeUpper and ta.barssince(phFound) >= rangeLower

// Plot Divergences
if showDivs and bullCond and rangeObv
    line.new(prevBottomIndex, prevBottomObv, bottomIndex, bottomObv, 
             color=bullDivColor, width=2)
    label.new(bottomIndex, bottomObv, "Bull", 
             color=bullDivColor, textcolor=color.white, 
             style=label.style_label_up, size=size.small)

if showDivs and bearCond and rangePrv
    line.new(prevTopIndex, prevTopObv, topIndex, topObv, 
             color=bearDivColor, width=2)
    label.new(topIndex, topObv, "Bear", 
             color=bearDivColor, textcolor=color.white, 
             style=label.style_label_down, size=size.small)

// Plotting CRV
o_ = obv[1]
h_ = obv
l_ = obv
c_ = obv

plotcandle(crv_disp=='Candles' ? o_ : na, 
          crv_disp=='Candles' ? h_ : na, 
          crv_disp=='Candles' ? l_ : na, 
          crv_disp=='Candles' ? c_ : na, 
          color = CRV_COL, 
          wickcolor = CRV_COL, 
          bordercolor = CRV_COL, 
          title = 'CRVOL')

plot(crv_disp=='Line' ? obv : na, 
     color = CRV_COL, 
     title = 'CRVOL')

// Coloring candles
barcolor(color_candles and bar_col != color.rgb(0,0,0,100) ? bar_col : na)