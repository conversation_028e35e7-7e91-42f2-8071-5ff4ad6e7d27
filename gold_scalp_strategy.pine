//@version=5
strategy("Gold Smart Money Scalping Strategy", 
     overlay=true, 
     max_labels_count=500, 
     max_boxes_count=500,
     initial_capital=10000,
     default_qty_type=strategy.percent_of_equity,
     default_qty_value=100,
     commission_type=strategy.commission.percent,
     commission_value=0.1)

// =================== Input Parameters ===================
var GRP1 = "Session Settings"
var GRP2 = "Smart Money Settings"
var GRP3 = "Order Flow Settings"
var GRP4 = "Risk Management"

// Session Settings
useSessionFilter = input.bool(true, "Use Session Filter", group=GRP1)
asianSession = input.session("2100-0200", "Asian Session", group=GRP1)
londonSession = input.session("0200-0500", "London Session", group=GRP1)
nySession = input.session("0830-1100", "NY Session", group=GRP1)

// Smart Money Settings
x = input.int(25, "Index Period", minval=1, group=GRP2)
rr = input.int(14, "Volume Flow Period", minval=1, group=GRP2)
peakslen = input.int(500, "Normalization Period", minval=1, group=GRP2)
thr = input.float(0.85, "High Interest Threshold", minval=0.01, maxval=0.99, group=GRP2)

// Order Flow Settings
orderFlowWindow = input.int(12, "Order Flow Period", minval=1, group=GRP3)
smoothingLength = input.int(10, "Order Flow Smoothing", minval=1, group=GRP3)
velocityCalcLength = input.int(21, "Velocity Length", minval=1, group=GRP3)

// Risk Management
riskPerTrade = input.float(1.0, "Risk Per Trade %", minval=0.1, maxval=5.0, group=GRP4)
takeProfitAtr = input.float(2.0, "Take Profit ATR", minval=0.5, group=GRP4)
stopLossAtr = input.float(1.0, "Stop Loss ATR", minval=0.5, group=GRP4)
maxDailyLoss = input.float(-3.0, "Max Daily Loss %", minval=-10.0, maxval=0.0, group=GRP4)

// =================== Smart Money Calculations ===================

// Smart Money Interest Index (from smart_money_intrest)
dumb = ta.pvi-ta.ema(ta.pvi,255)
smart = ta.nvi-ta.ema(ta.nvi,255)
drsi = ta.rsi(dumb, rr)
srsi = ta.rsi(smart, rr)
r = srsi/drsi
sums = math.sum(r, x)
peak = ta.highest(sums, peakslen)
smartMoneyIndex = sums/peak

// Order Flow (from orderflow)
orderFlow = math.sum(close > close[1] ? volume : (close < close[1] ? -volume : 0), orderFlowWindow)
orderFlow := ta.hma(orderFlow, smoothingLength)
stdDev = ta.stdev(orderFlow, 45)
normalizedOrderFlow = orderFlow/(stdDev + stdDev)

// Liquidity Sweep Detection (from liquidation_levels)
atr = ta.atr(14)
swingHigh = ta.highest(high, 10)
swingLow = ta.lowest(low, 10)
dailyHigh = request.security(syminfo.tickerid, "D", high[1], lookahead=barmerge.lookahead_off)
dailyLow = request.security(syminfo.tickerid, "D", low[1], lookahead=barmerge.lookahead_off)
bullSweep = low < dailyLow and close > dailyLow and close - low > atr * 0.5
bearSweep = high > dailyHigh and close < dailyHigh and high - close > atr * 0.5

// CVD Analysis (from cvd)
[buy_volume, sell_volume] = request.security_lower_tf(syminfo.tickerid, timeframe.period, [close>open ? volume : 0, close<open ? volume : 0])
delta_vol = array.sum(buy_volume) - array.sum(sell_volume)
cum_delta_vol = ta.cum(delta_vol)

// Session Time Check
inTradingSession = useSessionFilter ? (
    time(timeframe.period, asianSession) or
    time(timeframe.period, londonSession) or 
    time(timeframe.period, nySession)) : true

// =================== Strategy Logic ===================

// Position Management
var float lastTradeTime = 0
var float dailyProfit = 0.0

// Reset daily profit at market open
if dayofweek != dayofweek[1]
    dailyProfit := 0.0

// Update daily profit
dailyProfit := dailyProfit + strategy.netprofit - strategy.netprofit[1]

// Smart Money Buy Conditions
smartMoneyBuy = smartMoneyIndex > thr and 
                normalizedOrderFlow > 0.5 and
                cum_delta_vol > cum_delta_vol[1] and
                bullSweep

// Smart Money Sell Conditions
smartMoneySell = smartMoneyIndex > thr and 
                 normalizedOrderFlow < -0.5 and
                 cum_delta_vol < cum_delta_vol[1] and
                 bearSweep

// Trading Conditions
canTrade = inTradingSession and 
          dailyProfit > (strategy.initial_capital * maxDailyLoss / 100) and
          (time - lastTradeTime) > 1000 * 60 * 5  // 5-minute minimum between trades

// Long Entry
if (canTrade and smartMoneyBuy)
    stopPrice = close - (atr * stopLossAtr)
    targetPrice = close + (atr * takeProfitAtr)
    strategy.entry("Long", strategy.long)
    strategy.exit("Long TP/SL", "Long", stop=stopPrice, limit=targetPrice)
    lastTradeTime := time

// Short Entry
if (canTrade and smartMoneySell)
    stopPrice = close + (atr * stopLossAtr)
    targetPrice = close - (atr * takeProfitAtr)
    strategy.entry("Short", strategy.short)
    strategy.exit("Short TP/SL", "Short", stop=stopPrice, limit=targetPrice)
    lastTradeTime := time

// Close all positions if max daily loss is hit
if dailyProfit < (strategy.initial_capital * maxDailyLoss / 100)
    strategy.close_all("Max Daily Loss")

// =================== Plotting ===================
// Smart Money Index
plot(smartMoneyIndex, "Smart Money Index", color=color.blue, style=plot.style_line)

// Order Flow
plot(normalizedOrderFlow, "Order Flow", color=color.purple)

// Entry/Exit Signals
plotshape(smartMoneyBuy and canTrade, "Long Signal", shape.triangleup, location.belowbar, color.green, size=size.small)
plotshape(smartMoneySell and canTrade, "Short Signal", shape.triangledown, location.abovebar, color.red, size=size.small)

// Sweep Levels
plot(bullSweep ? low : na, "Bull Sweep", color.new(color.green, 50), style=plot.style_circles)
plot(bearSweep ? high : na, "Bear Sweep", color.new(color.red, 50), style=plot.style_circles)

// Alert Conditions
alertcondition(smartMoneyBuy and canTrade, "Long Entry Alert", "Smart Money Buy Signal")
alertcondition(smartMoneySell and canTrade, "Short Entry Alert", "Smart Money Sell Signal")
