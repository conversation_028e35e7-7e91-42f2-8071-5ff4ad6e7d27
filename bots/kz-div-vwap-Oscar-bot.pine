// This Pine Script® code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © rvdvjn
//@version=5
indicator("kz-div-vwap-Oscar-fvg[Ravi]", "", true, max_boxes_count = 500, max_lines_count = 500, max_labels_count = 500, max_bars_back = 5000)

// ---------------------------------------- Indicator Selection --------------------------------------------------
var g_INDICATOR_SELECTION = "Indicator Selection"
enable_killzones = input.bool(true, "Enable ICT Killzones & Pivots", group = g_INDICATOR_SELECTION, tooltip = "Show/hide ICT Killzones, pivots, and related elements")
enable_divergence = input.bool(true, "Enable Divergence Indicator", group = g_INDICATOR_SELECTION, tooltip = "Show/hide the divergence indicator")
enable_oscar = input.bool(true, "Enable OSCAR Indicator", group = g_INDICATOR_SELECTION, tooltip = "Show/hide the OSCAR indicator with candle arrows")
enable_vwap = input.bool(true, "Enable VWAP", group = g_INDICATOR_SELECTION, tooltip = "Show/hide VWAP lines for different timeframes")
enable_htf_boxes = input.bool(true, "Enable HTF Boxes", group = g_INDICATOR_SELECTION, tooltip = "Show/hide higher timeframe boxes")
enable_fvg = input.bool(true, "Enable FVG Indicator", group = g_INDICATOR_SELECTION, tooltip = "Show/hide the Fair Value Gap (FVG) indicator")

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © tradeforopp

//@version=5
// indicator("ICT Killzones & Pivots Light [Ravi]", "ICT Killzones & Pivots Light [TFO]", true, max_labels_count = 500, max_lines_count = 500, max_boxes_count = 500) 


// ---------------------------------------- Constant Functions --------------------------------------------------
get_line_type(_style) =>
    result = switch _style
        'Solid' => line.style_solid
        'Dotted' => line.style_dotted
        'Dashed' => line.style_dashed
    result

get_size(x) =>
    result = switch x
        'Auto' => size.auto
        'Tiny' => size.tiny
        'Small' => size.small
        'Normal' => size.normal
        'Large' => size.large
        'Huge' => size.huge
// ---------------------------------------- Constant Functions --------------------------------------------------

    
// ---------------------------------------- Inputs --------------------------------------------------
var g_SETTINGS      = "Settings"
max_days            = input.int(30, "Session Drawing Limit", 1, tooltip = "Only this many drawings will be kept on the chart, for each selected drawing type (killzone boxes, pivot lines, etc.)", group = g_SETTINGS)
tf_limit            = input.timeframe("30", "Timeframe Limit", tooltip = "Drawings will not appear on timeframes greater than or equal to this", group = g_SETTINGS)
gmt_tz              = input.string('America/New_York', "Timezone", options = ['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], tooltip = "Note GMT is not adjusted to reflect Daylight Saving Time changes", group = g_SETTINGS)
lbl_size            = get_size(input.string('Normal', "Label Size", options = ['Auto', 'Tiny', 'Small', 'Normal', 'Large', 'Huge'], tooltip = "The size of all labels", group = g_SETTINGS))
txt_color           = input.color(color.black, "Text Color", tooltip = "The color of all label and table text", group = g_SETTINGS)
use_cutoff          = input.bool(false, "Drawing Cutoff Time", inline = "CO", tooltip = "When enabled, all pivots and open price lines will stop extending at this time", group = g_SETTINGS)
cutoff              = input.session("1800-1801", "", inline = "CO", group = g_SETTINGS)


var g_KZ            = "Killzones"
show_kz             = input.bool(true, "Show Killzone Boxes", inline = "KZ", group = g_KZ)
show_kz_text        = input.bool(true, "Display Text", inline = "KZ", group = g_KZ)

use_asia            = input.bool(true, "", inline = "ASIA", group = g_KZ)
as_txt              = input.string("Asia", "", inline = "ASIA", group = g_KZ)
asia                = input.session("2000-0000", "", inline = "ASIA", group = g_KZ)
as_color            = input.color(color.blue, "", inline = "ASIA", group = g_KZ)

use_london          = input.bool(true, "", inline = "LONDON", group = g_KZ)
lo_txt              = input.string("London", "", inline = "LONDON", group = g_KZ)
london              = input.session("0200-0500", "", inline = "LONDON", group = g_KZ)
lo_color            = input.color(color.red, "", inline = "LONDON", group = g_KZ)

use_nyam            = input.bool(true, "", inline = "NYAM", group = g_KZ)
na_txt              = input.string("NY AM", "", inline = "NYAM", group = g_KZ)
nyam                = input.session("0930-1100", "", inline = "NYAM", group = g_KZ)
na_color            = input.color(#089981, "", inline = "NYAM", group = g_KZ)

use_nylu            = input.bool(true, "", inline = "NYLU", group = g_KZ)
nl_txt              = input.string("NY Lunch", "", inline = "NYLU", group = g_KZ)
nylu                = input.session("1200-1300", "", inline = "NYLU", group = g_KZ)
nl_color            = input.color(color.yellow, "", inline = "NYLU", group = g_KZ)

use_nypm            = input.bool(true, "", inline = "NYPM", group = g_KZ)
np_txt              = input.string("NY PM", "", inline = "NYPM", group = g_KZ)
nypm                = input.session("1330-1600", "", inline = "NYPM", group = g_KZ)
np_color            = input.color(color.purple, "", inline = "NYPM", group = g_KZ)

box_transparency    = input.int(70, "Box Transparency", 0, 100, group = g_KZ)
text_transparency   = input.int(50, "Text Transparency", 0, 100, group = g_KZ)


var g_LABELS        = "Killzone Pivots"
show_pivots         = input.bool(true, "Show Pivots", inline = "PV", group = g_LABELS)
use_alerts          = input.bool(true, "Alert Broken Pivots", inline = "PV", tooltip = "The desired killzones must be enabled at the time that an alert is created, along with the show pivots option, in order for alerts to work", group = g_LABELS)
show_midpoints      = input.bool(false, "Show Pivot Midpoints", inline = "mp", group = g_LABELS)
stop_midpoints      = input.bool(true, "Stop Once Mitigated", inline = "mp", group = g_LABELS) 
show_labels         = input.bool(true, "Show Pivot Labels", inline = "LB", tooltip = "Show labels denoting each killzone's high and low. Optionally choose to show the price of each level. Right side will show labels on the right-hand side of the chart until they are reached", group = g_LABELS)
label_price         = input.bool(false, "Display Price", inline = "LB", group = g_LABELS)
label_right         = input.bool(false, "Right Side", inline = "LB", group = g_LABELS)
ext_pivots          = input.string("Until Mitigated", "Extend Pivots...", options = ['Until Mitigated', 'Past Mitigation'], group = g_LABELS)
ext_which           = input.string("Most Recent", "...From Which Sessions", options = ['Most Recent', 'All'], group = g_LABELS)

ash_str             = input.string("AS.H", "Killzone 1 Labels", inline = "L_AS", group = g_LABELS)
asl_str             = input.string("AS.L", "", inline = "L_AS", group = g_LABELS)

loh_str             = input.string("LO.H", "Killzone 2 Labels", inline = "L_LO", group = g_LABELS)
lol_str             = input.string("LO.L", "", inline = "L_LO", group = g_LABELS)

nah_str             = input.string("NYAM.H", "Killzone 3 Labels", inline = "L_NA", group = g_LABELS)
nal_str             = input.string("NYAM.L", "", inline = "L_NA", group = g_LABELS)

nlh_str             = input.string("NYL.H", "Killzone 4 Labels", inline = "L_NL", group = g_LABELS)
nll_str             = input.string("NYL.L", "", inline = "L_NL", group = g_LABELS)

nph_str             = input.string("NYPM.H", "Killzone 5 Labels", inline = "L_NP", group = g_LABELS)
npl_str             = input.string("NYPM.L", "", inline = "L_NP", group = g_LABELS)

kzp_style           = get_line_type(input.string(defval = 'Solid', title = "Pivot Style", options = ['Solid', 'Dotted', 'Dashed'], inline = "KZP", group = g_LABELS))
kzp_width           = input.int(1, "", inline = "KZP", group = g_LABELS)
kzm_style           = get_line_type(input.string(defval = 'Dotted', title = "Midpoint Style", options = ['Solid', 'Dotted', 'Dashed'], inline = "KZM", group = g_LABELS))
kzm_width           = input.int(1, "", inline = "KZM", group = g_LABELS)
// ---------------------------------------- Inputs --------------------------------------------------


// ---------------------------------------- Variables & Constants --------------------------------------------------
type kz
    string _title

    box[] _box

    line[] _hi_line
    line[] _md_line
    line[] _lo_line

    label[] _hi_label
    label[] _lo_label

    bool[] _hi_valid
    bool[] _md_valid
    bool[] _lo_valid

var as_kz = kz.new(as_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool())
var lo_kz = kz.new(lo_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool())
var na_kz = kz.new(na_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool())
var nl_kz = kz.new(nl_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool())
var np_kz = kz.new(np_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool())

t_as = not na(time("", asia, gmt_tz))
t_lo = not na(time("", london, gmt_tz))
t_na = not na(time("", nyam, gmt_tz))
t_nl = not na(time("", nylu, gmt_tz))
t_np = not na(time("", nypm, gmt_tz))
t_co = not na(time("", cutoff, gmt_tz))

var transparent = #ffffff00
var ext_current = ext_which == 'Most Recent'
var ext_past = ext_pivots == 'Past Mitigation'
// ---------------------------------------- Variables & Constants --------------------------------------------------


// ---------------------------------------- Functions --------------------------------------------------
get_box_color(color c) =>
    result = color.new(c, box_transparency)
    
get_text_color(color c) =>
    result = color.new(c, text_transparency)
// ---------------------------------------- Functions --------------------------------------------------


// ---------------------------------------- Core Logic --------------------------------------------------
del_kz(kz k) =>
    if k._box.size() > max_days
        k._box.pop().delete()
    if k._hi_line.size() > max_days
        k._hi_line.pop().delete()
        k._lo_line.pop().delete()
        k._hi_valid.pop()
        k._lo_valid.pop()
        if show_midpoints
            k._md_line.pop().delete()
            k._md_valid.pop()
    if k._hi_label.size() > max_days
        k._hi_label.pop().delete()
        k._lo_label.pop().delete()

update_price_string(label L, float P) =>
    S = L.get_text()
    pre = str.substring(S, 0, str.pos(S, " ")) 
    str.trim(pre)
    L.set_text(str.format("{0} ({1})", pre, P))

adjust_in_kz(kz kz, bool t) =>
    if t
        kz._box.get(0).set_right(time)
        kz._box.get(0).set_top(math.max(kz._box.get(0).get_top(), high))
        kz._box.get(0).set_bottom(math.min(kz._box.get(0).get_bottom(), low))

        if show_pivots and kz._hi_line.size() > 0
            kz._hi_line.get(0).set_x2(time)
            if high > kz._hi_line.get(0).get_y1()
                kz._hi_line.get(0).set_xy1(time, high)
                kz._hi_line.get(0).set_xy2(time, high)

            kz._lo_line.get(0).set_x2(time)
            if low < kz._lo_line.get(0).get_y1()
                kz._lo_line.get(0).set_xy1(time, low)
                kz._lo_line.get(0).set_xy2(time, low)
                
            if show_midpoints
                kz._md_line.get(0).set_x2(time)
                kz._md_line.get(0).set_xy1(time, math.avg(kz._hi_line.get(0).get_y2(), kz._lo_line.get(0).get_y2()))
                kz._md_line.get(0).set_xy2(time, math.avg(kz._hi_line.get(0).get_y2(), kz._lo_line.get(0).get_y2()))

        if show_labels and kz._hi_label.size() > 0
            if label_right
                kz._hi_label.get(0).set_x(time)
                kz._lo_label.get(0).set_x(time)
            if high > kz._hi_label.get(0).get_y()
                kz._hi_label.get(0).set_xy(time, high)
                if label_price
                    update_price_string(kz._hi_label.get(0), high)
            if low < kz._lo_label.get(0).get_y()
                kz._lo_label.get(0).set_xy(time, low)
                if label_price
                    update_price_string(kz._lo_label.get(0), low)
    

adjust_out_kz(kz kz, bool t) =>
    if kz._box.size() > 0 and show_pivots
        for i = 0 to kz._box.size() - 1
            if not ext_current or (ext_current and i == 0)
                if ext_past ? true : (kz._hi_valid.get(i) == true)
                    kz._hi_line.get(i).set_x2(time)
                    if show_labels and label_right
                        kz._hi_label.get(i).set_x(time)
                if high > kz._hi_line.get(i).get_y1() and kz._hi_valid.get(i) == true
                    if use_alerts and i == 0
                        alert("Broke "+kz._title+" High", alert.freq_once_per_bar)
                    kz._hi_valid.set(i, false)
                    if show_labels and label_right
                        kz._hi_label.get(0).set_style(label.style_label_down)
                else if (use_cutoff ? t_co : false)
                    kz._hi_valid.set(i, false)
                    
                if ext_past ? true : (kz._lo_valid.get(i) == true)
                    kz._lo_line.get(i).set_x2(time)
                    if show_labels and label_right
                        kz._lo_label.get(i).set_x(time)
                if low < kz._lo_line.get(i).get_y1() and kz._lo_valid.get(i) == true
                    if use_alerts and i == 0
                        alert("Broke "+kz._title+" Low", alert.freq_once_per_bar)
                    kz._lo_valid.set(i, false)
                    if show_labels and label_right
                        kz._lo_label.get(0).set_style(label.style_label_up)
                else if (use_cutoff ? t_co : false)
                    kz._lo_valid.set(i, false)
                    
                if show_midpoints and not t
                    if stop_midpoints ? (kz._md_valid.get(i) == true) : true
                        kz._md_line.get(i).set_x2(time)
                        if kz._md_valid.get(i) == true and low <= kz._md_line.get(i).get_y1() and high >= kz._md_line.get(i).get_y1()
                            kz._md_valid.set(i, false)

            else
                break


manage_kz(kz kz, bool use, bool t, color c, string box_txt, string hi_txt, string lo_txt) => 
    if timeframe.in_seconds("") <= timeframe.in_seconds(tf_limit) and use
        if t and not t[1]
            _c = get_box_color(c)
            _t = get_text_color(c)
            kz._box.unshift(box.new(time, high, time, low, xloc = xloc.bar_time, border_color = show_kz ? _c : na, bgcolor = show_kz ? _c : na, text = (show_kz and show_kz_text) ? box_txt : na, text_color = _t))

            if show_pivots
                kz._hi_line.unshift(line.new(time, high, time, high, xloc = xloc.bar_time, style = kzp_style, color = c, width = kzp_width))
                kz._lo_line.unshift(line.new(time, low, time, low, xloc = xloc.bar_time, style = kzp_style, color = c, width = kzp_width))
                if show_midpoints
                    kz._md_line.unshift(line.new(time, math.avg(high, low), time, math.avg(high, low), xloc = xloc.bar_time, style = kzm_style, color = c, width = kzm_width))
                    array.unshift(kz._md_valid, true)
                
                array.unshift(kz._hi_valid, true)
                array.unshift(kz._lo_valid, true) 

                if show_labels
                    _hi_txt = label_price ? str.format("{0} ({1})", hi_txt, high) : hi_txt
                    _lo_txt = label_price ? str.format("{0} ({1})", lo_txt, low)  : lo_txt
                    if label_right
                        kz._hi_label.unshift(label.new(time, high, _hi_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_left, size = lbl_size))
                        kz._lo_label.unshift(label.new(time, low,  _lo_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_left, size = lbl_size))
                    else
                        kz._hi_label.unshift(label.new(time, high, _hi_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_down, size = lbl_size))
                        kz._lo_label.unshift(label.new(time, low,  _lo_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_up, size = lbl_size))

            del_kz(kz)
        adjust_in_kz(kz, t)
        adjust_out_kz(kz, t)


manage_kz(as_kz, use_asia, t_as, as_color, as_txt, ash_str, asl_str)
manage_kz(lo_kz, use_london, t_lo, lo_color, lo_txt, loh_str, lol_str)
manage_kz(na_kz, use_nyam, t_na, na_color, na_txt, nah_str, nal_str)
manage_kz(nl_kz, use_nylu, t_nl, nl_color, nl_txt, nlh_str, nll_str)
manage_kz(np_kz, use_nypm, t_np, np_color, np_txt, nph_str, npl_str)
// ---------------------------------------- Core Logic --------------------------------------------------








//@version=5
// indicator("Divergence for Many Indicators v5", overlay = true, max_bars_back = 1000, max_lines_count = 400, max_labels_count = 400)

// Divergence Indicator Settings
var g_DIVERGENCE = "Divergence Indicator Settings"
prd = input.int(defval = 5, title = "Pivot Period", minval = 1, maxval = 50, group = g_DIVERGENCE)
source = input.string(defval = "Close", title = "Source for Pivot Points", options = ["Close", "High/Low"], group = g_DIVERGENCE)
searchdiv = input.string(defval = "Regular", title = "Divergence Type", options = ["Regular", "Hidden", "Regular/Hidden"], group = g_DIVERGENCE)
showindis = input.string(defval = "Don't Show", title = "Show Indicator Names", options = ["Full", "First Letter", "Don't Show"], group = g_DIVERGENCE)
showlimit = input.int(1, title="Minimum Number of Divergence", minval = 1, maxval = 11, group = g_DIVERGENCE)
maxpp = input.int(defval = 10, title = "Maximum Pivot Points to Check", minval = 1, maxval = 20, group = g_DIVERGENCE)
maxbars = input.int(defval = 100, title = "Maximum Bars to Check", minval = 30, maxval = 200, group = g_DIVERGENCE)
shownum = input.bool(defval = true, title = "Show Divergence Number", group = g_DIVERGENCE)
showlast = input.bool(defval = false, title = "Show Only Last Divergence", group = g_DIVERGENCE)
dontconfirm = input.bool(defval = false, title = "Don't Wait for Confirmation", group = g_DIVERGENCE)
showlines = input.bool(defval = false, title = "Show Divergence Lines", group = g_DIVERGENCE)
showpivot = input.bool(defval = false, title = "Show Pivot Points", group = g_DIVERGENCE)

var g_DIV_INDICATORS = "Divergence Indicators"
calcmacd = input.bool(defval = true, title = "MACD", group = g_DIV_INDICATORS)
calcmacda = input.bool(defval = true, title = "MACD Histogram", group = g_DIV_INDICATORS)
calcrsi = input.bool(defval = true, title = "RSI", group = g_DIV_INDICATORS)
calcstoc = input.bool(defval = true, title = "Stochastic", group = g_DIV_INDICATORS)
calccci = input.bool(defval = true, title = "CCI", group = g_DIV_INDICATORS)
calcmom = input.bool(defval = true, title = "Momentum", group = g_DIV_INDICATORS)
calcobv = input.bool(defval = true, title = "OBV", group = g_DIV_INDICATORS)
calcvwmacd = input.bool(true, title = "VWmacd", group = g_DIV_INDICATORS)
calccmf = input.bool(true, title = "Chaikin Money Flow", group = g_DIV_INDICATORS)
calcmfi = input.bool(true, title = "Money Flow Index", group = g_DIV_INDICATORS)
calcext = input.bool(false, title = "Check External Indicator", group = g_DIV_INDICATORS)
externalindi = input.source(defval = close, title = "External Indicator", group = g_DIV_INDICATORS)

var g_DIV_APPEARANCE = "Divergence Appearance"
pos_reg_div_col = input.color(defval = color.yellow, title = "Positive Regular Divergence", group = g_DIV_APPEARANCE)
neg_reg_div_col = input.color(defval = color.navy, title = "Negative Regular Divergence", group = g_DIV_APPEARANCE)
pos_hid_div_col = input.color(defval = color.lime, title = "Positive Hidden Divergence", group = g_DIV_APPEARANCE)
neg_hid_div_col = input.color(defval = color.red, title = "Negative Hidden Divergence", group = g_DIV_APPEARANCE)
pos_div_text_col = input.color(defval = color.black, title = "Positive Divergence Text Color", group = g_DIV_APPEARANCE)
neg_div_text_col = input.color(defval = color.white, title = "Negative Divergence Text Color", group = g_DIV_APPEARANCE)
reg_div_l_style_ = input.string(defval = "Solid", title = "Regular Divergence Line Style", options = ["Solid", "Dashed", "Dotted"], group = g_DIV_APPEARANCE)
hid_div_l_style_ = input.string(defval = "Dashed", title = "Hidden Divergence Line Style", options = ["Solid", "Dashed", "Dotted"], group = g_DIV_APPEARANCE)
reg_div_l_width = input.int(defval = 2, title = "Regular Divergence Line Width", minval = 1, maxval = 5, group = g_DIV_APPEARANCE)
hid_div_l_width = input.int(defval = 1, title = "Hidden Divergence Line Width", minval = 1, maxval = 5, group = g_DIV_APPEARANCE)
showmas = input.bool(defval = false, title = "Show MAs 50 & 200", inline = "ma12", group = g_DIV_APPEARANCE)
cma1col = input.color(defval = color.lime, title = "", inline = "ma12", group = g_DIV_APPEARANCE)
cma2col = input.color(defval = color.red, title = "", inline = "ma12", group = g_DIV_APPEARANCE)

// Only plot MAs if divergence indicator is enabled
plot(enable_divergence and showmas ? ta.sma(close, 50) : na, color = showmas ? cma1col : na)
plot(enable_divergence and showmas ? ta.sma(close, 200) : na, color = showmas ? cma2col: na)

// set line styles
var reg_div_l_style = reg_div_l_style_ == "Solid" ? line.style_solid :
                       reg_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted
var hid_div_l_style = hid_div_l_style_ == "Solid" ? line.style_solid :
                       hid_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted


// get indicators
rsi = ta.rsi(close, 14) // RSI
[macd, signal, deltamacd] = ta.macd(close, 12, 26, 9) // MACD
moment = ta.mom(close, 10) // Momentum
cci = ta.cci(close, 10) // CCI
Obv = ta.obv // OBV
stk = ta.sma(ta.stoch(close, high, low, 14), 3) // Stoch
maFast = ta.vwma(close, 12), maSlow = ta.vwma(close, 26), vwmacd = maFast - maSlow // volume weighted macd
Cmfm = ((close-low) - (high-close)) / (high - low), Cmfv = Cmfm * volume, cmf = ta.sma(Cmfv, 21) / ta.sma(volume,21) // Chaikin money flow
Mfi = ta.mfi(close, 14) // Money Flow Index

// keep indicators names and colors in arrays
var indicators_name = array.new_string(11)
var div_colors = array.new_color(4)
if barstate.isfirst
    // names
    array.set(indicators_name, 0, showindis == "Full" ? "MACD" : "M")
    array.set(indicators_name, 1, showindis == "Full" ? "Hist" : "H")
    array.set(indicators_name, 2, showindis == "Full" ? "RSI" : "E")
    array.set(indicators_name, 3, showindis == "Full" ? "Stoch" : "S")
    array.set(indicators_name, 4, showindis == "Full" ? "CCI" : "C")
    array.set(indicators_name, 5, showindis == "Full" ? "MOM" : "M")
    array.set(indicators_name, 6, showindis == "Full" ? "OBV" : "O")
    array.set(indicators_name, 7, showindis == "Full" ? "VWMACD" : "V")
    array.set(indicators_name, 8, showindis == "Full" ? "CMF" : "C")
    array.set(indicators_name, 9, showindis == "Full" ? "MFI" : "M")
    array.set(indicators_name,10, showindis == "Full" ? "Extrn" : "X")
    //colors
    array.set(div_colors, 0, pos_reg_div_col)
    array.set(div_colors, 1, neg_reg_div_col)
    array.set(div_colors, 2, pos_hid_div_col)
    array.set(div_colors, 3, neg_hid_div_col)

// Check if we get new Pivot High Or Pivot Low
float ph = enable_divergence ? ta.pivothigh((source == "Close" ? close : high), prd, prd) : na
float pl = enable_divergence ? ta.pivotlow((source == "Close" ? close : low), prd, prd) : na
plotshape(enable_divergence and ph and showpivot, text = "H",  style = shape.labeldown, color = color.new(color.white, 100), textcolor = color.red, location = location.abovebar, offset = -prd)
plotshape(enable_divergence and pl and showpivot, text = "L",  style = shape.labelup, color = color.new(color.white, 100), textcolor = color.lime, location = location.belowbar, offset = -prd)

// keep values and positions of Pivot Highs/Lows in the arrays
var int maxarraysize = 20
var ph_positions = array.new_int(maxarraysize, 0)
var pl_positions = array.new_int(maxarraysize, 0)
var ph_vals = array.new_float(maxarraysize, 0.)
var pl_vals = array.new_float(maxarraysize, 0.)

// add PHs to the array
if ph
    array.unshift(ph_positions, bar_index)
    array.unshift(ph_vals, ph)
    if array.size(ph_positions) > maxarraysize
        array.pop(ph_positions)
        array.pop(ph_vals)

// add PLs to the array
if pl
    array.unshift(pl_positions, bar_index)
    array.unshift(pl_vals, pl)
    if array.size(pl_positions) > maxarraysize
        array.pop(pl_positions)
        array.pop(pl_vals)

// functions to check Regular Divergences and Hidden Divergences

// function to check positive regular or negative hidden divergence
// cond == 1 => positive_regular, cond == 2=> negative_hidden
positive_regular_positive_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : low
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src > src[1] or close > close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(pl_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(pl_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(pl_vals, x))) or
               (cond == 2 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(pl_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] < virtual_line1 or close[y] < virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// function to check negative regular or positive hidden divergence
// cond == 1 => negative_regular, cond == 2=> positive_hidden
negative_regular_negative_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : high
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src < src[1] or close < close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(ph_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(ph_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(ph_vals, x))) or
               (cond == 2 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(ph_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] > virtual_line1 or close[y] > virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// calculate 4 types of divergence if enabled in the options and return divergences in an array
calculate_divs(cond, indicator)=>
    divs = array.new_int(4, 0)
    array.set(divs, 0, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? positive_regular_positive_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 1, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? negative_regular_negative_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 2, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? positive_regular_positive_hidden_divergence(indicator, 2) : 0)
    array.set(divs, 3, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? negative_regular_negative_hidden_divergence(indicator, 2) : 0)
    divs

// array to keep all divergences
var all_divergences = array.new_int(44) // 11 indicators * 4 divergence = 44 elements
// set related array elements
array_set_divs(div_pointer, index)=>
    for x = 0 to 3
        array.set(all_divergences, index * 4 + x, array.get(div_pointer, x))

// set divergences array
array_set_divs(calculate_divs(calcmacd, macd), 0)
array_set_divs(calculate_divs(calcmacda, deltamacd), 1)
array_set_divs(calculate_divs(calcrsi, rsi), 2)
array_set_divs(calculate_divs(calcstoc, stk), 3)
array_set_divs(calculate_divs(calccci, cci), 4)
array_set_divs(calculate_divs(calcmom, moment), 5)
array_set_divs(calculate_divs(calcobv, Obv), 6)
array_set_divs(calculate_divs(calcvwmacd, vwmacd), 7)
array_set_divs(calculate_divs(calccmf, cmf), 8)
array_set_divs(calculate_divs(calcmfi, Mfi), 9)
array_set_divs(calculate_divs(calcext, externalindi), 10)

// check minimum number of divergence, if less than showlimit then delete all divergence
total_div = 0
for x = 0 to array.size(all_divergences) - 1
    total_div := total_div + math.round(math.sign(array.get(all_divergences, x)))

if total_div < showlimit
    array.fill(all_divergences, 0)

// keep line in an array
var pos_div_lines = array.new_line(0)
var neg_div_lines = array.new_line(0)
var pos_div_labels = array.new_label(0)
var neg_div_labels = array.new_label(0)

// remove old lines and labels if showlast option is enabled
delete_old_pos_div_lines()=>
    if array.size(pos_div_lines) > 0
        for j = 0 to array.size(pos_div_lines) - 1
            line.delete(array.get(pos_div_lines, j))
        array.clear(pos_div_lines)

delete_old_neg_div_lines()=>
    if array.size(neg_div_lines) > 0
        for j = 0 to array.size(neg_div_lines) - 1
            line.delete(array.get(neg_div_lines, j))
        array.clear(neg_div_lines)

delete_old_pos_div_labels()=>
    if array.size(pos_div_labels) > 0
        for j = 0 to array.size(pos_div_labels) - 1
            label.delete(array.get(pos_div_labels, j))
        array.clear(pos_div_labels)

delete_old_neg_div_labels()=>
    if array.size(neg_div_labels) > 0
        for j = 0 to array.size(neg_div_labels) - 1
            label.delete(array.get(neg_div_labels, j))
        array.clear(neg_div_labels)

// delete last creted lines and labels until we met new PH/PV
delete_last_pos_div_lines_label(n)=>
    if n > 0 and array.size(pos_div_lines) >= n
        asz = array.size(pos_div_lines)
        for j = 1 to n
            line.delete(array.get(pos_div_lines, asz - j))
            array.pop(pos_div_lines)
        if array.size(pos_div_labels) > 0
            label.delete(array.get(pos_div_labels, array.size(pos_div_labels) - 1))
            array.pop(pos_div_labels)

delete_last_neg_div_lines_label(n)=>
    if n > 0 and array.size(neg_div_lines) >= n
        asz = array.size(neg_div_lines)
        for j = 1 to n
            line.delete(array.get(neg_div_lines, asz - j))
            array.pop(neg_div_lines)
        if array.size(neg_div_labels) > 0
            label.delete(array.get(neg_div_labels, array.size(neg_div_labels) - 1))
            array.pop(neg_div_labels)

// variables for Alerts
pos_reg_div_detected = false
neg_reg_div_detected = false
pos_hid_div_detected = false
neg_hid_div_detected = false

// to remove lines/labels until we met new // PH/PL
var last_pos_div_lines = 0
var last_neg_div_lines = 0
var remove_last_pos_divs = false
var remove_last_neg_divs = false
if pl
    remove_last_pos_divs := false
    last_pos_div_lines := 0
if ph
    remove_last_neg_divs := false
    last_neg_div_lines := 0

// draw divergences lines and labels
divergence_text_top = ""
divergence_text_bottom = ""
distances = array.new_int(0)
dnumdiv_top = 0
dnumdiv_bottom = 0
top_label_col = color.white
bottom_label_col = color.white
old_pos_divs_can_be_removed = true
old_neg_divs_can_be_removed = true
startpoint = dontconfirm ? 0 : 1 // used for don't confirm option

for x = 0 to 10
    div_type = -1
    for y = 0 to 3
        if array.get(all_divergences, x * 4 + y) > 0 // any divergence?
            div_type := y
            if (y % 2) == 1
                dnumdiv_top := dnumdiv_top + 1
                top_label_col := array.get(div_colors, y)
            if (y % 2) == 0
                dnumdiv_bottom := dnumdiv_bottom + 1
                bottom_label_col := array.get(div_colors, y)
            if not array.includes(distances, array.get(all_divergences, x * 4 + y))  // line not exist ?
                array.push(distances, array.get(all_divergences, x * 4 + y))
                new_line = showlines ? line.new(x1 = bar_index - array.get(all_divergences, x * 4 + y),
                          y1 = (source == "Close" ? close[array.get(all_divergences, x * 4 + y)] :
                                           (y % 2) == 0 ? low[array.get(all_divergences, x * 4 + y)] :
                                                          high[array.get(all_divergences, x * 4 + y)]),
                          x2 = bar_index - startpoint,
                          y2 = (source == "Close" ? close[startpoint] :
                                           (y % 2) == 0 ? low[startpoint] :
                                                          high[startpoint]),
                          color = array.get(div_colors, y),
                          style = y < 2 ? reg_div_l_style : hid_div_l_style,
                          width = y < 2 ? reg_div_l_width : hid_div_l_width
                          )
                          : na
                if (y % 2) == 0
                    if old_pos_divs_can_be_removed
                        old_pos_divs_can_be_removed := false
                        if not showlast and remove_last_pos_divs
                            delete_last_pos_div_lines_label(last_pos_div_lines)
                            last_pos_div_lines := 0
                        if showlast
                            delete_old_pos_div_lines()
                    array.push(pos_div_lines, new_line)
                    last_pos_div_lines := last_pos_div_lines + 1
                    remove_last_pos_divs := true

                if (y % 2) == 1
                    if old_neg_divs_can_be_removed
                        old_neg_divs_can_be_removed := false
                        if not showlast and remove_last_neg_divs
                            delete_last_neg_div_lines_label(last_neg_div_lines)
                            last_neg_div_lines := 0
                        if showlast
                            delete_old_neg_div_lines()
                    array.push(neg_div_lines, new_line)
                    last_neg_div_lines := last_neg_div_lines + 1
                    remove_last_neg_divs := true

            // set variables for alerts
            if y == 0
                pos_reg_div_detected := true
            if y == 1
                neg_reg_div_detected := true
            if y == 2
                pos_hid_div_detected := true
            if y == 3
                neg_hid_div_detected := true
    // get text for labels
    if div_type >= 0
        divergence_text_top    := divergence_text_top    + ((div_type % 2) == 1 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")
        divergence_text_bottom := divergence_text_bottom + ((div_type % 2) == 0 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")


// draw labels
if showindis != "Don't Show" or shownum
    if shownum and dnumdiv_top > 0
        divergence_text_top := divergence_text_top + str.tostring(dnumdiv_top)
    if shownum and dnumdiv_bottom > 0
        divergence_text_bottom := divergence_text_bottom + str.tostring(dnumdiv_bottom)
    if divergence_text_top != ""
        if showlast
            delete_old_neg_div_labels()
        array.push(neg_div_labels,
                      label.new( x = bar_index,
                                 y = math.max(high, high[1]),
                                 text = divergence_text_top,
                                 color = top_label_col,
                                 textcolor = neg_div_text_col,
                                 style = label.style_label_down
                                 ))

    if divergence_text_bottom != ""
        if showlast
            delete_old_pos_div_labels()
        array.push(pos_div_labels,
                      label.new( x = bar_index,
                                 y = math.min(low, low[1]),
                                 text = divergence_text_bottom,
                                 color = bottom_label_col,
                                 textcolor = pos_div_text_col,
                                 style = label.style_label_up
                                 ))


alertcondition(enable_divergence and pos_reg_div_detected, title='Positive Regular Divergence Detected', message='Positive Regular Divergence Detected')
alertcondition(enable_divergence and neg_reg_div_detected, title='Negative Regular Divergence Detected', message='Negative Regular Divergence Detected')
alertcondition(enable_divergence and pos_hid_div_detected, title='Positive Hidden Divergence Detected', message='Positive Hidden Divergence Detected')
alertcondition(enable_divergence and neg_hid_div_detected, title='Negative Hidden Divergence Detected', message='Negative Hidden Divergence Detected')

alertcondition(enable_divergence and (pos_reg_div_detected or pos_hid_div_detected), title='Positive Divergence Detected', message='Positive Divergence Detected')
alertcondition(enable_divergence and (neg_reg_div_detected or neg_hid_div_detected), title='Negative Divergence Detected', message='Negative Divergence Detected')




//@version=5
// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GenZai 18-08-2021 v5
// Modified to plot arrows on candles

// indicator("OSCAR with Candle Arrows", overlay=true)

// OSCAR Indicator Settings
var g_OSCAR = "OSCAR Indicator Settings"
len = input.int(title="Oscar Candles", defval=8, group = g_OSCAR)
slowLen = input.int(title="Slow Oscar Candles", defval=16, group = g_OSCAR)
smoothing = input.string(title="Oscar Smoothing", defval="RMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"], group = g_OSCAR)
slowOscarSmoothing = input.string(title="Slow Oscar Smoothing", defval="WMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"], group = g_OSCAR)
crossSignalSensitivity = input.float(title="Cross Signal Sensitivity", defval=0.5, step=0.1, group = g_OSCAR)
OscarOfIndicator = input.source(title="Choose input type", defval=close, group = g_OSCAR)
bottomSignalLine = input.float(title="Bottom Signal Line", defval=35, step=5, group = g_OSCAR)
topSignalLine = input.float(title="Top Signal Line", defval=65, step=5, group = g_OSCAR)
showValues = input.bool(title="Show Indicator Values", defval=false, group = g_OSCAR)

// Moving average function
ma(smoothingType, OscarParam, length) =>
    if smoothingType == "RMA"
        ta.rma(OscarParam, length)
    else if smoothingType == "SMA"
        ta.sma(OscarParam, length)
    else if smoothingType == "EMA"
        ta.ema(OscarParam, length)
    else if smoothingType == "WMA"
        ta.wma(OscarParam, length)
    else if smoothingType == "LSMA"
        ta.linreg(OscarParam, length, 0)
    else if smoothingType == "HMA"
        ta.hma(OscarParam, length)
    else
        OscarParam

// Oscar calculation
A = ta.highest(OscarOfIndicator, len)
B = ta.lowest(OscarOfIndicator, len)
OscarRough = (OscarOfIndicator - B) / (A - B) * 100
Oscar1 = (OscarRough[1] / 3) * 2
OscarThird = OscarRough / 3
Oscar = Oscar1 + OscarThird
smoothedOscarRough = ma(smoothing, OscarRough, len)
smoothedOscar = ma(smoothing, Oscar, len)

// Slow Oscar calculation
slowOscarMa(slowOscarSmoothing, slowOscarParam, slowLen) =>
    if slowOscarSmoothing == "RMA"
        ta.rma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "SMA"
        ta.sma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "EMA"
        ta.ema(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "WMA"
        ta.wma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "LSMA"
        ta.linreg(slowOscarParam, slowLen, 0)
    else if slowOscarSmoothing == "HMA"
        ta.hma(slowOscarParam, slowLen)
    else
        slowOscarParam

slowA = ta.highest(OscarOfIndicator, slowLen)
slowB = ta.lowest(OscarOfIndicator, slowLen)
slowOscarRough = (OscarOfIndicator - slowB) / (slowA - slowB) * 100
slowOscar1 = (slowOscarRough[1] / 3) * 2
slowOscarThird = slowOscarRough / 3
slowOscar = slowOscar1 + slowOscarThird
smoothedSlowOscar = slowOscarMa(slowOscarSmoothing, slowOscar, slowLen)

// Cross sensitivity calculation
crossSensitivity = math.max(smoothedOscarRough, smoothedOscar) - math.min(smoothedOscarRough, smoothedOscar)

// Signal conditions
buySignal1 = ta.crossover(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough < bottomSignalLine
buySignal2 = ta.crossover(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > smoothedOscar and smoothedOscar < bottomSignalLine

sellSignal1 = ta.crossunder(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > topSignalLine
sellSignal2 = ta.crossunder(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscar > smoothedOscarRough and smoothedOscar > topSignalLine

// Plot arrows on candles only if OSCAR indicator is enabled
// Buy Signal 1 - Triangle Up
plotshape(series=enable_oscar and buySignal1,title="Buy Signal 1",style=shape.triangleup,location=location.belowbar,color=color.green,size=size.large)

// Buy Signal 2 - Arrow Up
plotshape(series=enable_oscar and buySignal2,title="Buy Signal 2",style=shape.arrowup,location=location.belowbar,color=color.rgb(239, 247, 239),size=size.large)

// Sell Signal 1 - Triangle Down
plotshape(series=enable_oscar and sellSignal1,title="Sell Signal 1",style=shape.triangledown,location=location.abovebar,color=color.red,size=size.large)

// Sell Signal 2 - Arrow Down
plotshape(series=enable_oscar and sellSignal2,title="Sell Signal 2",style=shape.arrowdown,location=location.abovebar,color=#f7eeee,size=size.large)

// Display indicator values as labels
var label valueLabel = na
if enable_oscar and showValues
    valueLabel := label.new(x=bar_index,y=high,text="Oscar: " + str.tostring(smoothedOscar, "#.##") +     "\nOscar Rough: " + str.tostring(smoothedOscarRough, "#.##") +     "\nSensitivity: " + str.tostring(crossSensitivity, "#.##"),style=label.style_label_down,color=color.rgb(0, 0, 0, 80),textcolor=color.white,yloc=yloc.price)
    label.delete(valueLabel[1])





//VWAP

// VWAP Settings Group
var g_VWAP_SETTINGS = "VWAP Settings"
hideonDWM = input(false, title="Hide VWAP on 1D or Above", group=g_VWAP_SETTINGS, display = display.data_window)
src = input(title = "Source", defval = hlc3, group=g_VWAP_SETTINGS, display = display.data_window)
offset = input.int(0, title="Offset", group=g_VWAP_SETTINGS, minval=0, display = display.data_window)

// Anchor Period Settings
var g_VWAP_ANCHORS = "VWAP Anchor Periods"
show_session = input(true, title="Session", group=g_VWAP_ANCHORS, inline="session")
session_color = input.color(color.rgb(155, 244, 137), title="", group=g_VWAP_ANCHORS, inline="session")

show_week = input(false, title="Week", group=g_VWAP_ANCHORS, inline="week")
week_color = input.color(color.rgb(244, 155, 137), title="", group=g_VWAP_ANCHORS, inline="week")

show_month = input(false, title="Month", group=g_VWAP_ANCHORS, inline="month")
month_color = input.color(color.rgb(137, 155, 244), title="", group=g_VWAP_ANCHORS, inline="month")

show_quarter = input(false, title="Quarter", group=g_VWAP_ANCHORS, inline="quarter")
quarter_color = input.color(color.rgb(244, 137, 155), title="", group=g_VWAP_ANCHORS, inline="quarter")

show_year = input(false, title="Year", group=g_VWAP_ANCHORS, inline="year")
year_color = input.color(color.rgb(137, 244, 155), title="", group=g_VWAP_ANCHORS, inline="year")

cumVolume = ta.cum(volume)
if barstate.islast and cumVolume == 0
    runtime.error("No volume is provided by the data vendor.")

// Define new period conditions for each anchor period
isNewSession = timeframe.change("D")
isNewWeek = timeframe.change("W")
isNewMonth = timeframe.change("M")
isNewQuarter = timeframe.change("3M")
isNewYear = timeframe.change("12M")

// Handle data gaps
if na(src[1])
    isNewSession := true
    isNewWeek := true
    isNewMonth := true
    isNewQuarter := true
    isNewYear := true

// Initialize VWAP variables
float sessionVwap = na
float weekVwap = na
float monthVwap = na
float quarterVwap = na
float yearVwap = na

// Calculate VWAPs for each selected anchor period if not on daily or higher timeframe when hidden and VWAP is enabled
if enable_vwap and not (hideonDWM and timeframe.isdwm)
    // Session VWAP
    if show_session
        var float sumSrc = 0.0
        var float sumVol = 0.0

        if isNewSession
            sumSrc := src * volume
            sumVol := volume
        else
            sumSrc += src * volume
            sumVol += volume

        sessionVwap := sumVol != 0 ? sumSrc / sumVol : na

    // Week VWAP
    if show_week
        var float sumSrcWeek = 0.0
        var float sumVolWeek = 0.0

        if isNewWeek
            sumSrcWeek := src * volume
            sumVolWeek := volume
        else
            sumSrcWeek += src * volume
            sumVolWeek += volume

        weekVwap := sumVolWeek != 0 ? sumSrcWeek / sumVolWeek : na

    // Month VWAP
    if show_month
        var float sumSrcMonth = 0.0
        var float sumVolMonth = 0.0

        if isNewMonth
            sumSrcMonth := src * volume
            sumVolMonth := volume
        else
            sumSrcMonth += src * volume
            sumVolMonth += volume

        monthVwap := sumVolMonth != 0 ? sumSrcMonth / sumVolMonth : na

    // Quarter VWAP
    if show_quarter
        var float sumSrcQuarter = 0.0
        var float sumVolQuarter = 0.0

        if isNewQuarter
            sumSrcQuarter := src * volume
            sumVolQuarter := volume
        else
            sumSrcQuarter += src * volume
            sumVolQuarter += volume

        quarterVwap := sumVolQuarter != 0 ? sumSrcQuarter / sumVolQuarter : na

    // Year VWAP
    if show_year
        var float sumSrcYear = 0.0
        var float sumVolYear = 0.0

        if isNewYear
            sumSrcYear := src * volume
            sumVolYear := volume
        else
            sumSrcYear += src * volume
            sumVolYear += volume

        yearVwap := sumVolYear != 0 ? sumSrcYear / sumVolYear : na

// Plot each VWAP line with its own color if VWAP is enabled
plot(enable_vwap and show_session ? sessionVwap : na, title = "Session VWAP", color = session_color, offset = offset, linewidth = 2)
plot(enable_vwap and show_week ? weekVwap : na, title = "Week VWAP", color = week_color, offset = offset, linewidth = 2)
plot(enable_vwap and show_month ? monthVwap : na, title = "Month VWAP", color = month_color, offset = offset, linewidth = 2)
plot(enable_vwap and show_quarter ? quarterVwap : na, title = "Quarter VWAP", color = quarter_color, offset = offset, linewidth = 2)
plot(enable_vwap and show_year ? yearVwap : na, title = "Year VWAP", color = year_color, offset = offset, linewidth = 2)


// ---------------------------------------------------------------------------------------------- //
// Inputs  -------------------------------------------------------------------------------------- //

// HTF Box Settings
var g_HTF_BOX = "HTF Box Settings"
htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP', group=g_HTF_BOX)
htfUser1  = input.string('4 Hours', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP', group=g_HTF_BOX)
htfCndl2  = input.bool(false, '2nd HTF Box', inline='TYP3', group=g_HTF_BOX)
htfUser2  = input.string('1 Hour', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP3', group=g_HTF_BOX)
bullC     = input.color(#26a69a, 'Box : Bull', inline='CNDL', group=g_HTF_BOX)
bearC     = input.color(#ef5350, 'Bear', inline='CNDL', group=g_HTF_BOX)
trans     = input.int(85, 'Transp', inline='CNDL', minval=65, maxval=95, group=g_HTF_BOX)
lineWidth = input.int(1, 'Line Width', inline='CNDL', minval=1, maxval=4, group=g_HTF_BOX)

// Optional 3rd HTF Box
htfCndl3  = input.bool(false, '3rd HTF Box', inline='TYP4', group=g_HTF_BOX)
htfUser3  = input.string('1 Month', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP4', group=g_HTF_BOX)


// ---------------------------------------------------------------------------------------------- //
// Functions  ----------------------------------------------------------------------------------- //

checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 :  _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)

    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0.,     htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c]

f_getTF(_htf) =>
    htf = _htf == '3 Mins' ? '3' : _htf == '5 Mins' ? '5' : _htf == '10 Mins' ? '10' : _htf == '15 Mins' ? '15' : _htf == '30 Mins' ? '30' : _htf == '45 Mins' ? '45' : _htf == '1 Hour' ? '60' : _htf == '2 Hours' ? '120' : _htf == '3 Hours' ? '180' : _htf == '4 Hours' ? '240' : _htf == '1 Day' ? 'D' : _htf == '1 Week' ? 'W' : _htf == '1 Month' ? 'M' : _htf == '3 Months' ? '3M' : _htf == '6 Months' ? '6M' : _htf == '1 Year' ? '12M' : na
    htf

f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width) =>
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0] = f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)
                true
            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)
                true

// ---------------------------------------------------------------------------------------------- //
// Main Logic ---------------------------------------------------------------------------------- //

htf1 = f_getTF(htfUser1)
htf2 = f_getTF(htfUser2)
htf3 = f_getTF(htfUser3)
supported1 = checkIf(timeframe.period, htf1)
supported2 = checkIf(timeframe.period, htf2)
supported3 = checkIf(timeframe.period, htf3)

// Process HTF Boxes if enabled
if enable_htf_boxes and chart.is_standard
    if supported1
        f_processCandles(htfCndl1, htf1, bullC, bearC, trans, lineWidth)
    if supported2
        f_processCandles(htfCndl2, htf2, bullC, bearC, trans, lineWidth)
    if supported3
        f_processCandles(htfCndl3, htf3, bullC, bearC, trans, lineWidth)





// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// Copyright 2022 PapaAlan
//
// v 1.19 02/02/2024
//  Mitigation bug fixes
//  New at bottom of settings, option to show mitigated fvg boxes for user number of candles before they are removed.
// v 1.20 02/05/2024
//  Add requested option to allow user ot enable mitigation of fvg's whereby instead of using the current charts timeframe as the source
//    price for mitigation of all enabled fvg timeframes, instead mitigation will use the associated time frame of each fvg (close) to
//    determine the mitigation of fvg's.  ie. if chart is on 5min timeframe and 1hr fvg timeframe enabled, an fvg only mitigated if price action from 1hr meets the criteria
//    instead of the price action from the 5min chart itself.

//@version=5
// indicator(title='MTF Fair Value Gap Indicator ULTRA w/Alerts', shorttitle = 'MTF FVG ULTRA',overlay=true, max_boxes_count = 500, max_labels_count = 500, max_lines_count = 500)
var DebugTable = table.new(position = position.bottom_center, columns = 2, rows = 5, bgcolor = color.white, border_width = 1)

// |                TIPS              | //
descTip1            =   "FVG's commonly known as Fair Value Gaps are mostly in use for forex trading.  You can google the concept but it's very useful in price action trading, even on regular large cap stocks."
descTip2            =   "Think of it as an imbalance area where it may actually be under/over valued due to many orders being injected in a short amount of time, ie . an impulse created by the speed of the movement."
descTip3            =   "In essence, the FVG can become a kind of magnet drawing the price back to that level to attempt to balance out the orders (when? we don't know)."
descTip4            =   "\n\nPlease do research to understand the concept of FVG's.  \n\nYou can look for an opportunity as price approaches the FVG for entry either long/short because after all, it is an 'Area of Interest'."
descTip5            =   "\n\nNo indicator works 100% of the time so take in context as just another indicator. It tends work on larger time frames best. "
alertTip1           =   "You can set an alert for when an FVG is created, (Bullish, Bearish or Both).  Another alert is provided for when the price moves inside the FVG (Bullish, Bearish or Both).  "
alertTip2           =   "\n\nIMPORTANT NOTE: Due to the TradingView architecture, If you set an alert and then at some point go to the indicator settings and change the FVG timeframe boxes, the alert will still report on the old settings.  "
alertTip3           =   "Before you set alerts up by right clicking on the chart and selecting etc., make sure you have all the settings you desire checkmarked in the indicator settings.  "
alertTip4           =   "\n\nIf you do go back and change indicator settings, you should remove the old alert and create a new one!  "
alertTip5           =   "\n\nWhy?  You ask?  When you create an alert, TV uploads the code for the indicator to their server and runs it there (not on your computer), so if you change the settings locally it doesn't get put up on their server for alerts until you create another alert."
mkthrsTip           =   "Uncheck to show all FVG's created, (ie. market, postmarket, premarket"
detection_typeTip        =   "(Candle 1 = latest candle) - Body Method fvg where you can see candle 2 body between candle 1 and candle 3 wicks, both up and down directions.\nWicks Method creates Bullish FVGs if candle 3 top wick is less than candle 1 bottom wick and vice versa for Bearish."
showlabelTip        =   "Displays Info On FVG Bands"
fvgcolorsTip        =   "FVG Color Columns\nBullish  (current, 5min, 10min, 15min, 1hr, 4hr, 8hr, daily)\nBearish (current, 5min, 10min, 15min, 1hr, 4hr, 8hr, daily)"
mitigationS1        =   "NORMAL: causes FVG to disappear when price has moved completely through the zone.\n"
mitigationS2        =   "DYNAMIC: means the FVG zone will shrink when price moves into it eating up imbalance.\n"
mitigationS3        =   "NONE: means FVG zones will not be deleted when filled.  Warning:  This option may show many FVG's, reduce the max number of enabled FVG's settings for less clutter.\n"
mitigationS4        =   "HALF: means FVG zones will be deleted when price incursion into zone reaches half way point."
mitiactionTip1      =   mitigationS1 + mitigationS2 + mitigationS3 + mitigationS4
mitiactionTip2      =   "Only for above NONE option.  This is the fill color of all mitigated FVG's that would have occurred."
mititypeTip         =   "Mitigation Action: 'Normal' is when price moves completely through FVG.  'Dynamic' shrinks FVG as prices moves into it until finally mitigating the " +
                          "FVG.  'None' never removes any FVG and will probably be too busy on the screen and max out.  Half mitigates when price moves through " +
                          "FVG exactly that.  'Touch' will mitigate an FVG when the price equals the top or bottom of the FVG depending on the direction of " +
                          "movement.  Mitigation Type: Body or Wicks, people use either mode.  Mitigation of FVG occurs adding a factor of using body or wick hi/low/open/close " +
                          "values regarding action type."
entrychgclrTip      =   "As price enters any FVG, change the band color to user set colors"
ndtTip              =   "Causes indicator to NOT remove bands for daily FVGs"
timezoneTip         =   "For labels with creation dates/times, Enter your time zone's offset (+ or −) from UTC, example: New York = -5, for those in the U.S. take into account daylight savings time etc."//, including a decimal fraction if needed."
incursionTip1       =   "Incursion means the price enters an FVG area (band).  This type of alert MUST have Mitigation Action set to NORMAL, TOUCH OR NONE.\n\n"
incursionTip2       =   "If using NORMAL or NONE, before setting any alert for this, make sure you select the amount (percentage) of penetration of the price into the FVG band that you desire.  " +
                          "If using TOUCH percentage input is ignored.\n\n"
incursionTip3       =   "Incursion alerts are unique so be sure you go to the chart settings and select to set Add an Alert and this indicator name in the Condition Box. Then select Any alert() function call and press the Create button. "
incursionTip        =   incursionTip1+incursionTip2+incursionTip3
boxesonrightTip     =   "Enable to show FVG boxes in abbreviated form on right side of chart"
grp_tfs             =   "ENABLE AND SET DESIRED TIMEFRAMES - SET MAX FVGS TO SHOW FOR EACH TIMEFRAME (NO MORE THAN 500 TOTAL FOR ALL ENABLED TIMEFRAMES) - SELECT BOX FILL COLORS (BULL -- BEAR)"
group_info          =   ''
last_num_fvgs_Tip   =   "Enable this to switch from a showing all current fvgs to showing only the last x number of created fvgs.  Applies to bull and bears."
grp_fvglines        =   "SELECT FVG BORDER COLORS, WIDTH, STYLE - ENABLE FVG MIDLINES AND SET COLORS (BULL/BEAR)"
confirm_tip         =   "Confirm Mitigation:  Enable to delay any type of mitigation until the end of the current bar.\n" +
                          "Use Timeframes: Enable causes mitigations only based upon the timeframes you enabled and set. ie. " +
                          "if 1hr fvg timeframe is enabled and you are on a 15 min chart, mitigation would not possibly occur until the latest " +
                          "1hr values of close/high/low etc are updated." +
                          "Otherwise mitigations will be based on the chart's timeframe (open/high/low/close) values intruding on each fvg " +
                          "for every timeframe you enabled and are using."
momentum_filter_tip =   "A simple ATR based momentum filters out smaller FVG sizes based on the factor input. ie. the higher the factor value the more momentum " +
                          "is required to display an FVG.  Momentum Length is in Bars."
mitigation_tip      =   "Option to show mitigated fvg's until x number of bars until they are deleted."

hover1              =   input.bool(true,"Hover over (!) for information about FVG's",group = group_info,tooltip = descTip1+descTip2+descTip3+descTip4+descTip5)
hover2              =   input.bool(true,"Hover over (!) for alert usage",group = group_info,tooltip = alertTip1+alertTip2+alertTip3+alertTip4+alertTip5)
hoursOffsetInput    = input.float(-5.0, "GMT Timezone offset (in hours)", minval = -12.0, maxval = 14.0, step = 0.5, group='Display Settings',tooltip = timezoneTip)
var msOffsetInput   = hoursOffsetInput * 1000 * 60 * 60
OnlyMktHrs          = input.bool(false,"Enable",group='Display Settings', inline = "mkt",tooltip = mkthrsTip)
session_range_string= input.session(defval="0930-1600",title="Market Hours Only",group='Display Settings',inline = "mkt")
en_tf1              = input.bool(defval=true, title="Timeframe 1",group = grp_tfs,inline="00")
tf1                 = input.timeframe(defval="60",title="",group = grp_tfs,inline="00")
tf1max              = input.int(30,"",group = grp_tfs,inline="00",minval = 0)
tf1_bucol           = input.color(defval=color.new(color.white,80),title="Fill Colors",group=grp_tfs,inline="00")
tf1_becol           = input.color(defval=color.new(color.white,80),title="",group=grp_tfs,inline="00")
en_tf2              = input.bool(defval=true, title="Timeframe 2",group = grp_tfs,inline="01")
tf2                 = input.timeframe(defval="240",title="",group = grp_tfs,inline="01")
tf2max              = input.int(30,"",group = grp_tfs,inline="01",minval = 0)
tf2_bucol           = input.color(defval=color.new(color.white,80),title="Fill Colors",group=grp_tfs,inline="01")
tf2_becol           = input.color(defval=color.new(color.white,80),title="",group=grp_tfs,inline="01")
en_tf3              = input.bool(defval=false, title="Timeframe 3",group = grp_tfs,inline="02")
tf3                 = input.timeframe(defval="",title="",group = grp_tfs,inline="02")
tf3max              = input.int(30,"",group = grp_tfs,inline="02",minval = 0)
tf3_bucol           = input.color(defval=color.new(color.white,80),title="Fill Colors",group=grp_tfs,inline="02")
tf3_becol           = input.color(defval=color.new(color.white,80),title="",group=grp_tfs,inline="02")
en_tf4              = input.bool(defval=false, title="Timeframe 4",group = grp_tfs,inline="03")
tf4                 = input.timeframe(defval="",title="",group = grp_tfs,inline="03")
tf4max              = input.int(30,"",group = grp_tfs,inline="03",minval = 0)
tf4_bucol           = input.color(defval=color.new(color.white,80),title="Fill Colors",group=grp_tfs,inline="03")
tf4_becol           = input.color(defval=color.new(color.white,80),title="",group=grp_tfs,inline="03")
en_tf5              = input.bool(defval=false, title="Timeframe 5",group = grp_tfs,inline="04")
tf5                 = input.timeframe(defval="",title="",group = grp_tfs,inline="04")
tf5max              = input.int(30,"",group = grp_tfs,inline="04",minval = 0)
tf5_bucol           = input.color(defval=color.new(color.white,80),title="Fill Colors",group=grp_tfs,inline="04")
tf5_becol           = input.color(defval=color.new(color.white,80),title="",group=grp_tfs,inline="04")
en_tf6              = input.bool(defval=false, title="Timeframe 6",group = grp_tfs,inline="05")
tf6                 = input.timeframe(defval="",title="",group = grp_tfs,inline="05")
tf6max              = input.int(30,"",group = grp_tfs,inline="05",minval = 0)
tf6_bucol           = input.color(defval=color.new(color.white,80),title="Fill Colors",group=grp_tfs,inline="05")
tf6_becol           = input.color(defval=color.new(color.white,80),title="",group=grp_tfs,inline="05")
show_fvg_type       = input.string(defval = "Both",title='Creation Types', options = ["Both","Bullish","Bearish"],group='FVG Settings',inline='fvgtypes')
show_fvg_labels     = input.bool(true,"Labels",group='FVG Settings',inline='typesnlabels',tooltip = showlabelTip)
tsizeOption         = input.string(defval="Normal",title="",options= ["Huge","Large","Normal","Small","Tiny","Auto"],group = "FVG Settings",inline='typesnlabels')
labeltextsize       = (tsizeOption == "Huge") ? size.huge :
     (tsizeOption == "Large") ? size.large :
     (tsizeOption == "Small") ? size.small :
     (tsizeOption == "Tiny") ? size.tiny :
     (tsizeOption == "Normal") ? size.normal :
     (tsizeOption == "Auto") ? size.auto :
         size.normal
ilabel_shift        = input.int(1,"Offset",group = "FVG Settings",inline='typesnlabels')
label_shift = (time - time[1]) * ilabel_shift
labelcolor          = input.color(color.white,"",group = "FVG Settings",inline='typesnlabels')
show_timeonlabels   = input.bool(false,'Time?',group = 'FVG Settings',inline='typesnlabels')
enable_last_x_fvgs  = input.bool(defval=false,title='View Only Last x Number of Fvgs: ',group = "FVG Settings",inline="06",tooltip = last_num_fvgs_Tip)
num_last_x_fvgs     = input.int(defval=3,title="",group = "FVG Settings",inline='06',minval = 0)
fvgs_on_right       = input.bool(false,title='Show FVGs on Right =>',group = "FVG Settings",inline="07",tooltip = boxesonrightTip)
on_right_width      = input.int(defval=20,title='Box Width',group = "FVG Settings",inline="07")

box_border_width        = input.int(1,"Width",minval = 0, maxval = 5,group=grp_fvglines,inline='0')
bbstyle_string          = input.string("Dotted","Style",options = ["Dotted","Dashed","Solid"],group=grp_fvglines,inline='0')
box_border_style        = bbstyle_string == 'Dotted' ? line.style_dotted : bbstyle_string == 'Dashed' ? line.style_dashed : line.style_solid
box_border_bull_color   = input.color(color.new(#33ff33,10),"Border",group=grp_fvglines,inline='0')
box_border_bear_color   = input.color(color.new(#cc0033,10),"Border",group=grp_fvglines,inline='0')
show_midlines           = input.bool(false,"Midlines",group=grp_fvglines,inline="lines")
midline_width           = input.int(1,"Thickness",minval = 1, maxval = 5,group=grp_fvglines,inline='lines')
mbstyle_string          = input.string("Dashed","Style",options = ["Dotted","Dashed","Solid"],group=grp_fvglines,inline='lines')
line_style              = mbstyle_string == 'Dotted' ? line.style_dotted : mbstyle_string == 'Dashed' ? line.style_dashed : line.style_solid
bull_midline_color      = input.color(color.new(color.white,0),"",group=grp_fvglines,inline='lines')
bear_midline_color      = input.color(color.new(color.white,0),"",group=grp_fvglines,inline='lines')
det_type_s              =   input.string("Body", "Detection Type", options = ["Body","Wick"],group='Detection',inline='DIM0', tooltip = detection_typeTip)
detection_type           =   det_type_s == "Body" ? true : false
en_mom              =   input.bool(defval=false,title='Momentum Filter ',group='Detection',inline='0')
mom_factor          =   input.float(defval=1.0,title="Factor",minval=0,maxval=50,step=.1,group='Detection',inline='0',tooltip = momentum_filter_tip)
mom_length          =   input.int(100,"Length",group='Detection',inline='0')
mom_value           =   (ta.atr(mom_length) / 5) * mom_factor
enable_pips         =   input.bool(defval=false,title='Enable Minimal Pips',group = 'Detection',inline='DIM',tooltip='Only create FVG if greater than pips input value')
pips_value          =   input.int(defval=4,title='',group = 'Detection',inline='DIM')
incursion_alerts    =   input.bool(true,'Enable Incursion Alerts',group = 'Incursions',inline='DIM1',tooltip=incursionTip + 'Percentage is amount of price intrusion into FVG before alert')
incursion_pct       =   input.int(defval=20,title='-  Percentage',group = 'Incursions',inline='DIM1')
intrusion_percentage=   incursion_pct / 100
entrychangecolor    =   input.bool(false,"Change FVG Color On Entry          ",group = 'Incursions',inline='DIM4',tooltip = entrychgclrTip)
entry_bull_color    =   input.color(color.new(color.yellow,90),"Bull",group = 'Incursions',inline='DIM4')
entry_bear_color    =   input.color(color.new(color.blue,90),"Bear",group = 'Incursions',inline='DIM4')
miti_use_confirm    =   input.bool(defval=false,title='Confirm Mitigation',group = 'Mitigations',inline='DIM15',tooltip = confirm_tip)
mitig_use_tfs       =   input.bool(defval=false,title='Use Timeframes',group = 'Mitigations',inline="DIM15")
show_onscreen_miti_table = input.bool(defval=true,title='Screen Flash',group = 'Mitigations',inline='DIM16')
miti_table_location =   input.string(defval=position.bottom_left,title='*  Location ->',options=[position.bottom_left,position.bottom_center,position.bottom_right,position.top_left,position.top_center,position.top_right],group = 'Mitigations',inline='DIM16')
var MitiTable       =   table.new(position = miti_table_location, columns = 1, rows = 2, bgcolor = na, border_width = 1)
mitiaction          =   input.string("Normal","Mitigation Action",options = ["Normal","Dynamic","None","Half","Touch"],group = 'Mitigations',inline='DIM2',tooltip = mitiactionTip1)
intrusion_percentage:=  mitiaction == "Touch" ? 0 : intrusion_percentage
mitig_type          =   input.string("Wicks","Mitigation Type",options = ["Wicks","Body"],group = 'Mitigations',inline='DIM2',tooltip = mititypeTip)
UseBodyForMitigation=   mitig_type == "Body" ? true : false
// mitig_use_tfs       =   input.bool(defval=true,title='Use Timeframes',group = 'Mitigations',inline="DIM3")
nomiticolor         =   input.color(defval=color.new(color.yellow,85),title="FVG Color For 'None' Option",group = 'Mitigations',inline="DIM3",tooltip = mitiactionTip2)
nmc_trans           =   color.t(nomiticolor)
show_mitigations    =   input.bool(defval=true,title='Show mitigations',group = 'Mitigations',inline="4",tooltip = mitigation_tip)
miti_box_extendright=   input.bool(defval=false,title='Extend boxes right',group = 'Mitigations',inline="4")
miti_bucol          =   input.color(defval=color.new(#3179f5,25),title="Fill colors",group = 'Mitigations',inline="4")
miti_becol          =   input.color(defval=color.new(#3179f5,25),title="",group = 'Mitigations',inline="4")
miti_box_rmv_nbars  =   input.int(defval=3,title='Remove mitigation boxes after x bars',minval=0,group = 'Mitigations',inline="5")

type master_record
    array<box>      bullfvgs
    array<box>      bearfvgs
    color           bucol
    color           becol
    array<label>    bulllabels
    array<label>    bearlabels
    array<line>     bullmids
    array<line>     bearmids
    float           chigh
    float           chighb2
    float           clow
    float           clowb2
    float           ccloseb1
    float           copenb1
    array<box>      miti_bulls
    array<box>      miti_bears
    float           chighb1
    float           clowb1
    float           cclose
var MR = master_record.new()

[tf1_O,tf1_H,tf1_L,tf1_C] = request.security(syminfo.tickerid,tf1,[open[1],high[1],low[1],close[1]])
[tf2_O,tf2_H,tf2_L,tf2_C] = request.security(syminfo.tickerid,tf2,[open[1],high[1],low[1],close[1]])
[tf3_O,tf3_H,tf3_L,tf3_C] = request.security(syminfo.tickerid,tf3,[open[1],high[1],low[1],close[1]])
[tf4_O,tf4_H,tf4_L,tf4_C] = request.security(syminfo.tickerid,tf4,[open[1],high[1],low[1],close[1]])
[tf5_O,tf5_H,tf5_L,tf5_C] = request.security(syminfo.tickerid,tf5,[open[1],high[1],low[1],close[1]])
[tf6_O,tf6_H,tf6_L,tf6_C] = request.security(syminfo.tickerid,tf6,[open[1],high[1],low[1],close[1]])

// added these two globally as can't use them in functions with consistency/reliability towards incursion alerts
lasthigh    =   high[1]
lastlow     =   low[1]

bool[] en_tfs_array = array.from(en_tf1,en_tf2,en_tf3,en_tf4,en_tf5,en_tf6)
string[] tf_array = array.from(tf1,tf2,tf3,tf4,tf5,tf6)
int[] tf_maxarray = array.from(tf1max,tf2max,tf3max,tf4max,tf5max,tf6max)

var tf1_Oa = array.new_float(4,0.0), var tf1_Ha = array.new_float(4,0.0), var tf1_La = array.new_float(4,0.0), var tf1_Ca = array.new_float(4,0.0)
var tf2_Oa = array.new_float(4,0.0), var tf2_Ha = array.new_float(4,0.0), var tf2_La = array.new_float(4,0.0), var tf2_Ca = array.new_float(4,0.0)
var tf3_Oa = array.new_float(4,0.0), var tf3_Ha = array.new_float(4,0.0), var tf3_La = array.new_float(4,0.0), var tf3_Ca = array.new_float(4,0.0)
var tf4_Oa = array.new_float(4,0.0), var tf4_Ha = array.new_float(4,0.0), var tf4_La = array.new_float(4,0.0), var tf4_Ca = array.new_float(4,0.0)
var tf5_Oa = array.new_float(4,0.0), var tf5_Ha = array.new_float(4,0.0), var tf5_La = array.new_float(4,0.0), var tf5_Ca = array.new_float(4,0.0)
var tf6_Oa = array.new_float(4,0.0), var tf6_Ha = array.new_float(4,0.0), var tf6_La = array.new_float(4,0.0), var tf6_Ca = array.new_float(4,0.0)

var tf1_bulls = array.new_box(), var tf1_bears = array.new_box(), var tf1_bulabels = array.new_label(), var tf1_belabels = array.new_label()
var det_tf1bull = false, var det_tf1bear = false, var tf1_bumid = array.new_line(), var tf1_bemid = array.new_line()
var tf2_bulls = array.new_box(), var tf2_bears = array.new_box(), var tf2_bulabels = array.new_label(), var tf2_belabels = array.new_label()
var det_tf2bull = false, var det_tf2bear = false, var tf2_bumid = array.new_line(), var tf2_bemid = array.new_line()
var tf3_bulls = array.new_box(), var tf3_bears = array.new_box(), var tf3_bulabels = array.new_label(), var tf3_belabels = array.new_label()
var det_tf3bull = false, var det_tf3bear = false, var tf3_bumid = array.new_line(), var tf3_bemid = array.new_line()
var tf4_bulls = array.new_box(), var tf4_bears = array.new_box(), var tf4_bulabels = array.new_label(), var tf4_belabels = array.new_label()
var det_tf4bull = false, var det_tf4bear = false, var tf4_bumid = array.new_line(), var tf4_bemid = array.new_line()
var tf5_bulls = array.new_box(), var tf5_bears = array.new_box(), var tf5_bulabels = array.new_label(), var tf5_belabels = array.new_label()
var det_tf5bull = false, var det_tf5bear = false, var tf5_bumid = array.new_line(), var tf5_bemid = array.new_line()
var tf6_bulls = array.new_box(), var tf6_bears = array.new_box(), var tf6_bulabels = array.new_label(), var tf6_belabels = array.new_label()
var det_tf6bull = false, var det_tf6bear = false, var tf6_bumid = array.new_line(), var tf6_bemid = array.new_line()

var tf1_miti_bulls = array.new_box(), var tf1_miti_bears = array.new_box(), var tf2_miti_bulls = array.new_box(), var tf2_miti_bears = array.new_box(),
var tf3_miti_bulls = array.new_box(), var tf3_miti_bears = array.new_box(), var tf4_miti_bulls = array.new_box(), var tf4_miti_bears = array.new_box(),
var tf5_miti_bulls = array.new_box(), var tf5_miti_bears = array.new_box(), var tf6_miti_bulls = array.new_box(), var tf6_miti_bears = array.new_box(),

_pushrequestvalues(_tfaopen,_tfahigh,_tfalow,_tfaclose,_o,_h,_l,_c) =>
    result = false
    _tfaopen.push(_o)
    _tfahigh.push(_h)
    _tfalow.push(_l)
    _tfaclose.push(_c)
    result

_ischange(_o,_h,_l,_c) =>
    result = _o != _o[1] or _h != _h[1] or _l != _l[1] or _c != _c[1] ? true : false
    result

tf1_change = _ischange(tf1_O,tf1_H,tf1_L,tf1_C)
tf2_change = _ischange(tf2_O,tf2_H,tf2_L,tf2_C)
tf3_change = _ischange(tf3_O,tf3_H,tf3_L,tf3_C)
tf4_change = _ischange(tf4_O,tf4_H,tf4_L,tf4_C)
tf5_change = _ischange(tf5_O,tf5_H,tf5_L,tf5_C)
tf6_change = _ischange(tf6_O,tf6_H,tf6_L,tf6_C)

for i = 0 to en_tfs_array.size() - 1
    tf_enabled = en_tfs_array.get(i)
    if tf_enabled
        switch i
            0 => tf1_change ? _pushrequestvalues(tf1_Oa,tf1_Ha,tf1_La,tf1_Ca,tf1_O,tf1_H,tf1_L,tf1_C) : na
            1 => tf2_change ? _pushrequestvalues(tf2_Oa,tf2_Ha,tf2_La,tf2_Ca,tf2_O,tf2_H,tf2_L,tf2_C) : na
            2 => tf3_change ? _pushrequestvalues(tf3_Oa,tf3_Ha,tf3_La,tf3_Ca,tf3_O,tf3_H,tf3_L,tf3_C) : na
            3 => tf4_change ? _pushrequestvalues(tf4_Oa,tf4_Ha,tf4_La,tf4_Ca,tf4_O,tf4_H,tf4_L,tf4_C) : na
            4 => tf5_change ? _pushrequestvalues(tf5_Oa,tf5_Ha,tf5_La,tf5_Ca,tf5_O,tf5_H,tf5_L,tf5_C) : na
            5 => tf6_change ? _pushrequestvalues(tf6_Oa,tf6_Ha,tf6_La,tf6_Ca,tf6_O,tf6_H,tf6_L,tf6_C) : na

_is_in_session(sessionstring) =>
    not na(time(timeframe.period, sessionstring))

_gettf_label_str(_tfstring1) =>
    temptf = _tfstring1 == "" ? timeframe.period : _tfstring1
    result = not na(str.pos(temptf,"D")) ? "D" : not na(str.pos(temptf,"W")) ? "W"
               : not na(str.pos(temptf,"1M")) ? "M" : not na(str.pos(temptf,"3M")) ? "3M" : not na(str.pos(temptf,"6M")) ? "6M"
               : not na(str.pos(temptf,"12M")) ? "Y"
               : na(str.pos(temptf,"S")) and na(str.pos(temptf,"W")) and na(str.pos(temptf,"M")) and str.tonumber(temptf) >= 60
               ? str.tostring(str.tonumber(temptf) / 60) + "H"
               : na(str.pos(temptf,"S")) and na(str.pos(temptf,"W")) and na(str.pos(temptf,"M")) ? temptf + "m" : str.lower(temptf)

_isfvgbull(_type,_low,_high2,_close1,_open1) =>
    bool retval = false
    if _type  // if body fvg detection
        retval := _low > _high2 and _low < _close1 and _high2 > _open1
    else   // else wick detection
        retval := _low > _high2
    if enable_pips and retval and not ((_low - _high2) > (pips_value / 10000))
        retval := false
    if en_mom and retval
        if not (math.abs(_open1 - _close1) > mom_value)
            retval := false
    retval

// ************* SEE IF NEW BEAR FVGs DETECTED
_isfvgbear(_type,_low2,_high,_close1,_open1) =>
    bool retval = false
    if _type
        retval := _low2 > _high and _high > _close1 and _low2 < _open1
    else
        retval := _low2 > _high
    if enable_pips and retval and not ((_low2 - _high) > (pips_value / 10000))
        retval := false
    if en_mom and retval
        if not (math.abs(_open1 - _close1) > mom_value)
            retval := false
    retval

// *************** GET A FVG BOX TOP AND BOTTOM VALUES
_getboxelements(_boxarray,_i) =>
    _box = _boxarray.get(_i)
    _left       = _box.get_left()
    _top        = _box.get_top()
    _btm        = _box.get_bottom()
    _right      = _box.get_right()
    [_box,_left,_top,_btm,_right]

_box_n_label_delete(_boxarray,_mitiboxarray,_i,_labelarray,_labelflag,_linearray,_lineflag,_mitiflag,_direction) =>
    var a = ""
    if _boxarray.size() > 0
        [bptr,left,top,bottom,right] = _getboxelements(_boxarray,_i)
        box.delete(bptr)
        array.remove(_boxarray, _i)
        if _labelflag
            laptr = array.get(_labelarray,_i)
            a := laptr.get_text() + " Mitigated"

        if _mitiflag and show_mitigations
            if _mitiboxarray.size() > 0
                mbox = _mitiboxarray.get(_mitiboxarray.size() - 1)
                mtop = mbox.get_top()
                mbtm = mbox.get_bottom()
                if mtop != top and mbtm != bottom  // prevent duplications of miti boxes
                    _mitiboxarray.push(box.new(left=left, bottom=bottom, right=left + (time-left) , top=top, bgcolor=_direction ? miti_bucol : miti_becol , text=a,text_size=labeltextsize,text_color=labelcolor,text_halign=text.align_right,text_valign=text.align_center,border_color=_direction ? miti_bucol : miti_becol, border_width = 1, border_style = line.style_dotted,extend=miti_box_extendright ? extend.right : extend.none,xloc=xloc.bar_time))
            else
                _mitiboxarray.push(box.new(left=left, bottom=bottom, right=left + (time-left), top=top, bgcolor=_direction ? miti_bucol :miti_becol , text=a,text_size=labeltextsize,text_color=labelcolor,text_halign=text.align_right,text_valign=text.align_center,border_color=_direction ? miti_bucol : miti_becol, border_width = 1, border_style = line.style_dotted,extend=miti_box_extendright ? extend.right : extend.none,xloc=xloc.bar_time))

        if _labelflag
            laptr = _labelarray.get(_i), laptr.delete(), _labelarray.remove(_i)
        if _lineflag
            lptr = _linearray.get(_i), lptr.delete(), _linearray.remove(_i)

// *************** ADD A LABEL TO AN FVG
_addlabel(_fvglabelsarray,_time,_high,_string,_style,y,_show_label) =>
    var string s = na
    if _show_label
        if show_timeonlabels
            s := _string + str.format("  {0,date,HH:mm MM/dd/yy}", time + msOffsetInput)
        else
            s := _string
        laptr = label.new(_time,_high,text=s,color=color.rgb(0,0,0,100),style = _style,xloc=xloc.bar_time)
        laptr.set_textcolor(labelcolor)
        laptr.set_xy(_time + label_shift,y)
        laptr.set_size(labeltextsize)
        _fvglabelsarray.push(laptr)

_addline(_fvglinearray,_x1,_y1,_x2,_y2,_color,_style,_width,_extend) =>
    _fvglinearray.push(line.new(x1=_x1,y1=_y1,x2=_x2,y2=_y2,xloc=xloc.bar_time, extend= _extend,color=_color,style=_style,width=_width))

// compare existing bull fvg box values to current price action
_getbullfvgaction(_mr,_top,_bottom,_intrusion_percentage) =>
    midpt = (_top + _bottom) / 2
    float tlow = 0.0, float tclose = 0.0, float tlowb1 = 0.0
    if mitig_use_tfs
        tlow := _mr.clow, tclose := _mr.cclose, tlowb1 := _mr.clowb1
    else
        tlow := low, tclose := close, tlowb1 := lastlow
    threshold = _top - (_intrusion_percentage * (_top - _bottom))
    _have_intrusion = mitiaction == "Touch" ? tlow <= _top and tlowb1 >= _top : tlow < threshold and tlowb1 > threshold
    //DebugTable.cell(0, 0, text="_top tlow tclose tlowb1: " + str.tostring(_top) + " " + str.tostring(tlow) + " " + str.tostring(tclose) + " " + str.tostring(tlowb1), bgcolor = color.new(color.green, 0), text_color = color.white,text_halign=text.align_center)
    _lowundertop    = mitiaction == "Touch" ? tlow <= _top and tlowb1 >= _top : tlow < _top
    _lowunderbtm    = tlow < _bottom
    _lowundermid    = tlow < midpt
    _closeundertop  = mitiaction == "Touch" ? tclose <= _top and tlowb1 >= _top : tclose < _top
    _closeunderbtm  = tclose < _bottom
    _closeundermid  = tlow < midpt
    [_lowundertop,_lowunderbtm,_closeundertop,_closeunderbtm,_lowundermid,_closeundermid,_have_intrusion]

// compare existing bear fvg box values to current price action
_getbearfvgaction(_mr,_top,_bottom,_intrusion_percentage) =>
    midpt = (_top + _bottom) / 2
    float thigh = 0.0, float tclose = 0.0, float thighb1 = 0.0
    if mitig_use_tfs
        thigh := _mr.chigh, tclose := _mr.cclose, thighb1 := _mr.chighb1
    else
        thigh := high, tclose := close, thighb1 := lasthigh
    threshold = _bottom + (_intrusion_percentage * (_top - _bottom))
    _have_intrusion = mitiaction == "Touch" ? thigh >= _bottom and thighb1 <= _bottom : thigh > threshold and thighb1 < threshold
    _highovertop    = thigh > _top
    _highoverbtm    = mitiaction == "Touch" ? thigh >= _bottom and thighb1 <= _bottom : thigh > _bottom
    _highovermid    = thigh > midpt
    _closeovertop   = tclose > _top
    _closeoverbtm   = mitiaction == "Touch" ? tclose >= _bottom and thighb1 <= _bottom : tclose > _bottom
    _closeovermid   = tclose > midpt
    [_highovertop,_highoverbtm,_closeovertop,_closeoverbtm,_highovermid,_closeovermid,_have_intrusion]

// used for adjusting fvg label position with each new bar
_setlabelxy(_fvglabels,_i,_time,_top,_bottom) =>
    laptr = _fvglabels.get(_i)
    laptr.set_xy(_time + label_shift,(_top + _bottom) / 2)

_adjust_midline_y(_fvglines,_i,_top,_bottom) =>
    lptr = _fvglines.get(_i)
    lptr.set_y1((_top + _bottom) / 2)
    lptr.set_y2((_top + _bottom) / 2)

_set_box_left_side(_boxarray,_linearray) =>
    if _boxarray.size() > 0
        for i = _boxarray.size() - 1 to 0 by 1
            [boxptr,left,top,bottom,right] = _getboxelements(_boxarray,i)
            boxptr.set_left(time + label_shift)
            boxptr.set_right(time + label_shift + (time - time[1]) * on_right_width)
            if show_midlines
                lptr = _linearray.get(i)
                lptr.set_x1(time + label_shift)
                lptr.set_x2(time + label_shift + (time - time[1]) * on_right_width)

_box_refresh(_boxarray,_bgcolor,_bordercolor,_labelsarray,_linesarray,_linescolor,num_last_x_fvgs) =>
    // re-display boxes and any labels first
    for i = _boxarray.size() - 1 to 0
        bptr = _boxarray.get(i)
        bptr.set_bgcolor(_bgcolor)
        bptr.set_border_color(_bordercolor)
        if _labelsarray.size() > 0
            laptr = _labelsarray.get(i)
            laptr.set_textcolor(color.new(labelcolor,0))
        if _linesarray.size() > 0
            lptr = _linesarray.get(i)
            lptr.set_color(color.new(_linescolor,0))

    // disappear all boxes, lines and possibly labels except user selected latest ones
    if _boxarray.size() > num_last_x_fvgs
        for i = _boxarray.size() - 1 - num_last_x_fvgs to 0 by 1
            tb = _boxarray.get(i)
            box.set_bgcolor(tb,na)
            box.set_border_color(tb,na)
            if _labelsarray.size() > 0
                laptr = _labelsarray.get(i)
                laptr.set_textcolor(color.new(labelcolor,100))
            if _linesarray.size() > 0
                lptr = _linesarray.get(i)
                lptr.set_color(color.new(_linescolor,100))

// modify existing bull fvg boxes for incursion, deletion etc.
_update_bull_fvgs(_mr,_timestring) =>
    _boxbullarray = _mr.bullfvgs, _mitibulls = _mr.miti_bulls, _bullfvgcolor = _mr.bucol, _labelsbull = _mr.bulllabels
    _bullmidlines = _mr.bullmids
    do_loop = miti_use_confirm ? barstate.isconfirmed : true
    if do_loop
        for i = _boxbullarray.size() - 1 to 0 by 1
            [boxelem,left,top,bottom,right] = _getboxelements(_boxbullarray,i)
            [lowundertop,lowunderbtm,closeundertop,closeunderbtm,lowundermid,closeundermid,intrusion] = _getbullfvgaction(_mr,top,bottom,intrusion_percentage)
            // INCURSION ALERT DONE HERE: if mitigation action is normal,none or touch then do alert
            if (mitiaction == "Normal" or mitiaction == "None" or mitiaction == "Touch") and intrusion and incursion_alerts
                if show_onscreen_miti_table
                    MitiTable.cell(0, 0, text="Mitigated Bull Mitigated Bull " + str.tostring(top), bgcolor = color.new(color.green, 0), text_color = color.white,text_halign=text.align_center)
                alert("Bull FVG Incursion " + str.tostring(_timestring), alert.freq_once_per_bar)
            else
                MitiTable.clear(0,0,0,0)
            if entrychangecolor
                if lowundertop  // low < top
                    boxelem.set_bgcolor(entry_bull_color)

            if show_fvg_labels
                _setlabelxy(_labelsbull,i,time,top,bottom)
            // *** if mitigation is set to dynamic and body is referenced with price and price is within fvg
            if mitiaction == "Dynamic" and UseBodyForMitigation and closeundertop
                boxelem.set_top(close)
                if show_fvg_labels
                    _setlabelxy(_labelsbull,i,time,close,bottom)
                if show_midlines
                    _adjust_midline_y(_bullmidlines,i,close,bottom)
            else
            // else if mitigation set to dynamic and body mitigation NOT enabled and btm wick goes below top of fvg band then move band top lower
                if mitiaction == "Dynamic" and lowundertop
                    boxelem.set_top(low)
                    if show_fvg_labels
                        _setlabelxy(_labelsbull,i,time,low,bottom)
                    if show_midlines
                        _adjust_midline_y(_bullmidlines,i,low,bottom)
            // section to delete boxes when fvg filled
            if mitiaction == "None" // if 'none' selected for mitigation
                if UseBodyForMitigation
                    if closeunderbtm
                        boxelem.set_bgcolor(color.new(nomiticolor,nmc_trans))
                else
                    if lowunderbtm
                        boxelem.set_bgcolor(color.new(nomiticolor,nmc_trans))
                if (UseBodyForMitigation and closeunderbtm) or (not UseBodyForMitigation and lowunderbtm)
                    if show_fvg_labels
                        laptr = _labelsbull.get(i)
                        curr_text = laptr.get_text()
                        found = str.contains(curr_text,"Mitigated")
                        if not found
                            laptr.set_text(curr_text + " Mitigated")
            if mitiaction == "Normal" or mitiaction == "Dynamic"
                if UseBodyForMitigation
                    if closeunderbtm
                        _box_n_label_delete(_boxbullarray, _mitibulls,i,_labelsbull, show_fvg_labels,_bullmidlines,show_midlines,true,true)
                else
                    if lowunderbtm
                        _box_n_label_delete(_boxbullarray, _mitibulls,i,_labelsbull, show_fvg_labels,_bullmidlines,show_midlines,true,true)
            if mitiaction == "Half" // if half mitigation
                if UseBodyForMitigation and closeundermid
                    _box_n_label_delete(_boxbullarray, _mitibulls,i,_labelsbull, show_fvg_labels,_bullmidlines,show_midlines,true,true)
                else
                    if lowundermid
                        _box_n_label_delete(_boxbullarray, _mitibulls,i,_labelsbull, show_fvg_labels,_bullmidlines,show_midlines,true,true)
            if mitiaction == "Touch" // if half mitigation
                if UseBodyForMitigation and closeundertop
                    _box_n_label_delete(_boxbullarray, _mitibulls,i,_labelsbull, show_fvg_labels,_bullmidlines,show_midlines,true,true)
                else
                    if lowundertop
                        _box_n_label_delete(_boxbullarray, _mitibulls,i,_labelsbull, show_fvg_labels,_bullmidlines,show_midlines,true,true)
        if fvgs_on_right
            _set_box_left_side(_boxbullarray,_bullmidlines)
        if enable_last_x_fvgs and array.size(_boxbullarray) > 0
            _box_refresh(_boxbullarray,_bullfvgcolor,box_border_bull_color,_labelsbull,_bullmidlines,bull_midline_color,num_last_x_fvgs)

// modify existing bear fvg boxes for incursion, deletion etc.
_update_bear_fvgs(_mr,_timestring) => //_boxbeararray,_mitibears,_bearfvgcolor,_labelsbear,_bearmidlines,_timestring) =>
    _boxbeararray = _mr.bearfvgs, _mitibears = _mr.miti_bears, _bearfvgcolor = _mr.becol, _labelsbear = _mr.bearlabels
    _bearmidlines = _mr.bearmids
    do_loop = miti_use_confirm ? barstate.isconfirmed : true
    if do_loop
        for i = _boxbeararray.size() - 1 to 0 by 1
            [boxelem,left,top,bottom,right] = _getboxelements(_boxbeararray,i)
            [highovertop,highoverbtm,closeovertop,closeoverbtm,highovermid,closeovermid,intrusion] = _getbearfvgaction(_mr,top,bottom,intrusion_percentage)
            // INCURSION ALERT DONE HERE: if mitigation action is normal or none then do alert
            if (mitiaction == "Normal" or mitiaction == "None" or mitiaction == "Touch") and intrusion and incursion_alerts
                if show_onscreen_miti_table
                    MitiTable.cell(0, 0, text="Mitigated Bear Mitigated Bear " + str.tostring(bottom), bgcolor = color.new(color.red, 0), text_color = color.white,text_halign=text.align_center)
                alert("Bear FVG Incursion " + str.tostring(_timestring), alert.freq_once_per_bar)
            else
                MitiTable.clear(0,0,0,0)
            if entrychangecolor
                if highoverbtm
                    boxelem.set_bgcolor(entry_bear_color)
            if show_fvg_labels
                _setlabelxy(_labelsbear,i,time,top,bottom)
            // *** if mitigation is set to dynamic and body is referenced with price and price is within fvg
            if mitiaction == "Dynamic" and UseBodyForMitigation and closeoverbtm
                boxelem.set_bottom(close)
                if show_fvg_labels
                    _setlabelxy(_labelsbear,i,time,close,bottom)
                if show_midlines
                    _adjust_midline_y(_bearmidlines,i,close,bottom)
            else
            // else if mitigation set to dynamic and body mitigation NOT enabled and top wick goes above bottom of fvg band then move band bottom up
                if mitiaction == "Dynamic" and highoverbtm
                    boxelem.set_bottom(high)
                    if show_fvg_labels
                        _setlabelxy(_labelsbear,i,time,top,high)
                    if show_midlines
                        _adjust_midline_y(_bearmidlines,i,top,high)
            if mitiaction == "None" // if 'none' selected for mitigation
                if UseBodyForMitigation
                    if closeovertop
                        boxelem.set_bgcolor(color.new(nomiticolor,nmc_trans))
                else
                    if highovertop
                        boxelem.set_bgcolor(color.new(nomiticolor,nmc_trans))
                if (UseBodyForMitigation and closeovertop) or (not UseBodyForMitigation and highovertop)
                    if show_fvg_labels
                        laptr = _labelsbear.get(i)
                        curr_text = laptr.get_text()
                        found = str.contains(curr_text,"Mitigated")
                        if not found
                            laptr.set_text(curr_text + " Mitigated")
            if mitiaction == "Normal" or mitiaction == "Dynamic"
                if UseBodyForMitigation
                    if closeovertop
                        _box_n_label_delete(_boxbeararray, _mitibears,i,_labelsbear, show_fvg_labels,_bearmidlines,show_midlines,true,false)
                else
                    if highovertop
                        _box_n_label_delete(_boxbeararray, _mitibears,i,_labelsbear, show_fvg_labels,_bearmidlines,show_midlines,true,false)
            if mitiaction == "Half" // if half mitigation
                if UseBodyForMitigation
                    if closeovermid
                        _box_n_label_delete(_boxbeararray, _mitibears,i,_labelsbear, show_fvg_labels,_bearmidlines,show_midlines,true,false)
                else
                    if highovermid
                        _box_n_label_delete(_boxbeararray, _mitibears,i,_labelsbear, show_fvg_labels,_bearmidlines,show_midlines,true,false)
            if mitiaction == "Touch" // if half mitigation
                if UseBodyForMitigation
                    if closeoverbtm
                        _box_n_label_delete(_boxbeararray, _mitibears,i,_labelsbear, show_fvg_labels,_bearmidlines,show_midlines,true,false)
                else
                    if highoverbtm
                        _box_n_label_delete(_boxbeararray, _mitibears,i,_labelsbear, show_fvg_labels,_bearmidlines,show_midlines,true,false)
        if fvgs_on_right
            _set_box_left_side(_boxbeararray,_bearmidlines)
        if enable_last_x_fvgs and array.size(_boxbeararray) > 0
            _box_refresh(_boxbeararray,_bearfvgcolor,box_border_bear_color,_labelsbear,_bearmidlines,bear_midline_color,num_last_x_fvgs)

// check for duplicate boxes required for eliminating duplicate higher timeframe fvg boxes
_duplicate_box(_boxarray,_top,_bottom) =>
    found = false
    if _boxarray.size() > 0 //array.size(_boxarray) > 0
        for i = _boxarray.size() - 1 to 0 by 1
            [_barray,left,top,bottom,right] = _getboxelements(_boxarray,i)
            if _top == top or _bottom == bottom
                found := true
                break
    found

_update_mitigation_boxes(_struct) =>
    result = false
    if _struct.size() > 0
        for i = _struct.size() - 1 to 0
            mbox = _struct.get(i)
            boxright = mbox.get_right()
            if time > boxright + ((time - time[1]) * (miti_box_rmv_nbars - 1))
                mbox.delete()
                _struct.remove(i)
    result

inSession = _is_in_session(session_range_string)

_handle_all(_labelstring, _struct,_maxarraysize) =>
    var bool _newBull = false
    var bool _newBear = false
    if OnlyMktHrs and inSession or not OnlyMktHrs
        _newBull := show_fvg_type == "Both" or show_fvg_type == "Bullish" ? _isfvgbull(detection_type,_struct.clow,_struct.chighb2,_struct.ccloseb1,_struct.copenb1) : false
        _newBear := show_fvg_type == "Both" or show_fvg_type == "Bearish" ? _isfvgbear(detection_type,_struct.clowb2,_struct.chigh,_struct.ccloseb1,_struct.copenb1) : false
    if _newBull
        if _struct.bullfvgs.size() > _maxarraysize + 1
            _box_n_label_delete(_struct.bullfvgs, _struct.miti_bulls,0,_struct.bulllabels, show_fvg_labels,_struct.bullmids,show_midlines,false,true)
        if not _duplicate_box(_struct.bullfvgs,_struct.clow,_struct.chighb2)
            right = fvgs_on_right ? time + ((time - time[1]) * on_right_width) : time + ((time - time[1]) * 10)//bar_index+on_right_width : bar_index + 2
            extend = fvgs_on_right ? extend.none : extend.right
            _struct.bullfvgs.push(box.new(left=time[2], bottom=_struct.chighb2, right=right, top=_struct.clow, bgcolor=_struct.bucol , border_color=box_border_bull_color, border_width = box_border_width, border_style = box_border_style,extend=extend,xloc=xloc.bar_time))
            if show_fvg_labels
                _addlabel(_struct.bulllabels,time[2],_struct.chigh,_labelstring + " Bull",label.style_label_left,(_struct.chighb2 + _struct.clow) / 2, show_fvg_labels)
            if show_midlines
                mid = (_struct.clow + _struct.chighb2) / 2
                _addline(_struct.bullmids,time[2],mid,right,mid,bull_midline_color,line_style,midline_width,extend)
    if _newBear
        if _struct.bearfvgs.size() > _maxarraysize + 1
            _box_n_label_delete(_struct.bearfvgs, _struct.miti_bears,0,_struct.bearlabels, show_fvg_labels,_struct.bearmids,show_midlines,false,false)
        if not _duplicate_box(_struct.bearfvgs,_struct.clowb2,_struct.chigh)  // inputs top, bottom of box
            right = fvgs_on_right ? time + ((time - time[1]) * on_right_width) : time + ((time - time[1]) * 10)//right = fvgs_on_right ? bar_index+on_right_width : bar_index + 2 //[2]
            extend = fvgs_on_right ? extend.none : extend.right
            _struct.bearfvgs.push(box.new(left=time[2], top=_struct.clowb2, right=right, bottom=_struct.chigh, bgcolor=_struct.becol, border_color=box_border_bear_color, border_width = box_border_width, border_style = box_border_style,extend=extend,xloc=xloc.bar_time))
            if show_fvg_labels
                _addlabel(_struct.bearlabels,time[2],_struct.clowb2,_labelstring + " Bear",label.style_label_left,(_struct.clowb2 + _struct.chigh) / 2, show_fvg_labels)
            if show_midlines
                mid = (_struct.clowb2 + _struct.chigh) / 2
                _addline(_struct.bearmids,time[2],mid,right,mid,bear_midline_color,line_style,midline_width,extend)

    if _struct.bullfvgs.size() > 0  // this code is to handle incursion alerts before FVG's possibly being mitigated
        _update_bull_fvgs(_struct,_labelstring)
    if _struct.miti_bulls.size() > 0
        _update_mitigation_boxes(_struct.miti_bulls)
    if _struct.bearfvgs.size() > 0  // this code is to handle incursion alerts before FVG's possibly being mitigated
        _update_bear_fvgs(_struct,_labelstring)
    if _struct.miti_bears.size() > 0
        _update_mitigation_boxes(_struct.miti_bears)
    [_newBull,_newBear]

_set_enabled_tf_info(_index) =>
    result = switch _index
        0 => MR.bullfvgs := tf1_bulls, MR.bearfvgs := tf1_bears, MR.bucol := tf1_bucol, MR.becol := tf1_becol, MR.bulllabels := tf1_bulabels, MR.bearlabels := tf1_belabels, MR.bullmids := tf1_bumid,
                 MR.bearmids := tf1_bemid, MR.chigh := tf1_Ha.last(), MR.chighb2 := tf1_Ha.get(tf1_Ha.size() - 3), MR.clow := tf1_La.last(), MR.clowb2 := tf1_La.get(tf1_La.size() - 3), MR.ccloseb1 := tf1_Ca.get(tf1_Ca.size() - 2), MR.copenb1 := tf1_Oa.get(tf1_Oa.size() - 2),
                 MR.miti_bulls := tf1_miti_bulls, MR.miti_bears := tf1_miti_bears, MR.chighb1 := tf1_Ha.get(tf1_Ha.size() - 2), MR.clowb1 := tf1_La.get(tf1_La.size() - 2), MR.cclose := tf1_Ca.last()
        1 => MR.bullfvgs := tf2_bulls, MR.bearfvgs := tf2_bears, MR.bucol := tf2_bucol, MR.becol := tf2_becol, MR.bulllabels := tf2_bulabels, MR.bearlabels := tf2_belabels, MR.bullmids := tf2_bumid,
                 MR.bearmids := tf2_bemid, MR.chigh := tf2_Ha.last(), MR.chighb2 := tf2_Ha.get(tf2_Ha.size() - 3), MR.clow := tf2_La.last(), MR.clowb2 := tf2_La.get(tf2_La.size() - 3), MR.ccloseb1 := tf2_Ca.get(tf2_Ca.size() - 2), MR.copenb1 := tf2_Oa.get(tf2_Oa.size() - 2),
                 MR.miti_bulls := tf2_miti_bulls, MR.miti_bears := tf2_miti_bears, MR.chighb1 := tf2_Ha.get(tf2_Ha.size() - 2), MR.clowb1 := tf2_La.get(tf2_La.size() - 2), MR.cclose := tf2_Ca.last()
        2 => MR.bullfvgs := tf3_bulls, MR.bearfvgs := tf3_bears, MR.bucol := tf3_bucol, MR.becol := tf3_becol, MR.bulllabels := tf3_bulabels, MR.bearlabels := tf3_belabels, MR.bullmids := tf3_bumid,
                 MR.bearmids := tf3_bemid, MR.chigh := tf3_Ha.last(), MR.chighb2 := tf3_Ha.get(tf3_Ha.size() - 3), MR.clow := tf3_La.last(), MR.clowb2 := tf3_La.get(tf3_La.size() - 3), MR.ccloseb1 := tf3_Ca.get(tf3_Ca.size() - 2), MR.copenb1 := tf3_Oa.get(tf3_Oa.size() - 2),
                 MR.miti_bulls := tf3_miti_bulls, MR.miti_bears := tf3_miti_bears, MR.chighb1 := tf3_Ha.get(tf3_Ha.size() - 2), MR.clowb1 := tf3_La.get(tf3_La.size() - 2), MR.cclose := tf3_Ca.last()
        3 => MR.bullfvgs := tf4_bulls, MR.bearfvgs := tf4_bears, MR.bucol := tf4_bucol, MR.becol := tf4_becol, MR.bulllabels := tf4_bulabels, MR.bearlabels := tf4_belabels, MR.bullmids := tf4_bumid,
                 MR.bearmids := tf4_bemid, MR.chigh := tf4_Ha.last(), MR.chighb2 := tf4_Ha.get(tf4_Ha.size() - 3), MR.clow := tf4_La.last(), MR.clowb2 := tf4_La.get(tf4_La.size() - 3), MR.ccloseb1 := tf4_Ca.get(tf4_Ca.size() - 2), MR.copenb1 := tf4_Oa.get(tf4_Oa.size() - 2),
                 MR.miti_bulls := tf4_miti_bulls, MR.miti_bears := tf4_miti_bears, MR.chighb1 := tf4_Ha.get(tf4_Ha.size() - 2), MR.clowb1 := tf4_La.get(tf4_La.size() - 2), MR.cclose := tf4_Ca.last()
        4 => MR.bullfvgs := tf5_bulls, MR.bearfvgs := tf5_bears, MR.bucol := tf5_bucol, MR.becol := tf5_becol, MR.bulllabels := tf5_bulabels, MR.bearlabels := tf5_belabels, MR.bullmids := tf5_bumid,
                 MR.bearmids := tf5_bemid, MR.chigh := tf5_Ha.last(), MR.chighb2 := tf5_Ha.get(tf5_Ha.size() - 3), MR.clow := tf5_La.last(), MR.clowb2 := tf5_La.get(tf5_La.size() - 3), MR.ccloseb1 := tf5_Ca.get(tf5_Ca.size() - 2), MR.copenb1 := tf5_Oa.get(tf5_Oa.size() - 2),
                 MR.miti_bulls := tf5_miti_bulls, MR.miti_bears := tf5_miti_bears, MR.chighb1 := tf5_Ha.get(tf5_Ha.size() - 2), MR.clowb1 := tf5_La.get(tf5_La.size() - 2), MR.cclose := tf5_Ca.last()
        5 => MR.bullfvgs := tf6_bulls, MR.bearfvgs := tf6_bears, MR.bucol := tf6_bucol, MR.becol := tf6_becol, MR.bulllabels := tf6_bulabels, MR.bearlabels := tf6_belabels, MR.bullmids := tf6_bumid,
                 MR.bearmids := tf6_bemid, MR.chigh := tf6_Ha.last(), MR.chighb2 := tf6_Ha.get(tf6_Ha.size() - 3), MR.clow := tf6_La.last(), MR.clowb2 := tf6_La.get(tf6_La.size() - 3), MR.ccloseb1 := tf6_Ca.get(tf6_Ca.size() - 2), MR.copenb1 := tf6_Oa.get(tf6_Oa.size() - 2),
                 MR.miti_bulls := tf6_miti_bulls, MR.miti_bears := tf6_miti_bears, MR.chighb1 := tf6_Ha.get(tf6_Ha.size() - 2), MR.clowb1 := tf6_La.get(tf6_La.size() - 2), MR.cclose := tf6_Ca.last()

// ------- MAIN LOOP -------//
bool newbull = false, bool newbear = false
// Only process FVG indicator if enabled
if enable_fvg
    for i = 0 to en_tfs_array.size() - 1
        tf_enabled = en_tfs_array.get(i)
        if tf_enabled
            _set_enabled_tf_info(i)
            int tf_max_fvgs = tf_maxarray.get(i)
            t_tf = str.tostring(tf_array.get(i))

            [tnewboxBull,tnewboxBear] = _handle_all(_gettf_label_str(t_tf),MR,tf_max_fvgs)
            newbull := tnewboxBull ? true : newbull
            newbear := tnewboxBear ? true : newbear

alertcondition(enable_fvg and newbull,title='Bull FVG Creation',message = 'Bull FVG Creation' )
alertcondition(enable_fvg and newbear,title='Bear FVG Creation',message = 'Bear FVG Creation' )
alertcondition(enable_fvg and (newbull or newbear),title='Bull or Bear FVG Creation',message = 'Bull or Bear FVG Creation' )