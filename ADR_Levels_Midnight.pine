
//@version=5
// AR Levels [Pro+] - Enhanced Anchor Points
// This version uses configurable anchor points for all Average Range calculations
// - Supports New York midnight (00:00 NY time), UTC Time, Monday/Tuesday Open, and Monthly anchors
// - Draws AR levels with proper anchor points for daily, weekly, and monthly timeframes
// - Efficiently manages memory by limiting historical levels
// - Allows customization of colors, display options, and anchor points
indicator("AR Levels [Pro+]", overlay=true, max_lines_count=500, max_labels_count=500, max_bars_back=1000, dynamic_requests=true)

// Input settings
show_daily = input.bool(true, "Show Daily ADR", group="Timeframes")
show_weekly = input.bool(true, "Show Weekly AWR", group="Timeframes")
show_monthly = input.bool(true, "Show Monthly AMR", group="Timeframes")

// ADR Settings
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group="ADR Settings")
show_full_adr = input.bool(true, "Show Full ADR", group="ADR Settings")
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group="ADR Settings")
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group="ADR Settings")
show_half_adr = input.bool(false, "Show 1/2 ADR", group="ADR Settings")
show_historical = input.bool(true, "Show Historical ADR Levels", group="ADR Settings")

// Anchor Time Settings
var g_ANCHOR = "Anchor Time Settings"
days_to_keep = input.int(3, "Days of AR Levels to Keep", minval=1, maxval=10, group=g_ANCHOR, tooltip="Number of days of AR levels to display. Older levels will be deleted.")

// Daily Anchor Settings
var g_DAILY_ANCHOR = "Daily Anchor Settings"
daily_anchor_type = input.string("New York Midnight", "Daily Anchor Type", options=["New York Midnight", "UTC Time", "Session Open"], group=g_DAILY_ANCHOR)
daily_anchor_hour = input.int(0, "Daily Anchor Hour (0-23)", minval=0, maxval=23, group=g_DAILY_ANCHOR, tooltip="For UTC Time anchor type. Default is 0 for NY midnight")
daily_anchor_minute = input.int(0, "Daily Anchor Minute (0-59)", minval=0, maxval=59, group=g_DAILY_ANCHOR)

// Weekly Anchor Settings
var g_WEEKLY_ANCHOR = "Weekly Anchor Settings"
weekly_anchor_type = input.string("Tuesday Open", "Weekly Anchor Type", options=["Monday Open", "Tuesday Open", "UTC Time"], group=g_WEEKLY_ANCHOR, tooltip="Tuesday Open is the traditional 'True Week Open' in ICT methodology")
weekly_anchor_hour = input.int(0, "Weekly Anchor Hour (0-23)", minval=0, maxval=23, group=g_WEEKLY_ANCHOR, tooltip="For UTC Time anchor type")
weekly_anchor_minute = input.int(0, "Weekly Anchor Minute (0-59)", minval=0, maxval=59, group=g_WEEKLY_ANCHOR)
weekly_anchor_day = input.string("Monday", "Weekly Anchor Day", options=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], group=g_WEEKLY_ANCHOR)

// Monthly Anchor Settings
var g_MONTHLY_ANCHOR = "Monthly Anchor Settings"
monthly_anchor_type = input.string("First Day", "Monthly Anchor Type", options=["First Day", "Second Week", "UTC Time"], group=g_MONTHLY_ANCHOR)
monthly_anchor_hour = input.int(0, "Monthly Anchor Hour (0-23)", minval=0, maxval=23, group=g_MONTHLY_ANCHOR, tooltip="For UTC Time anchor type")
monthly_anchor_minute = input.int(0, "Monthly Anchor Minute (0-59)", minval=0, maxval=59, group=g_MONTHLY_ANCHOR)

// True Day Open Separator Settings
show_true_day_separator = input.bool(true, "Show True Day Open Separator", group="Separator Settings")
true_day_separator_color = input.color(color.new(color.green, 50), "Separator Color", group="Separator Settings")

// Line Settings
line_width = input.int(1, "Line Width", minval=1, maxval=4, group="Line Settings")
line_style = input.string("Solid", "Line Style", options=["Solid", "Dashed", "Dotted"], group="Line Settings")
extend_right = input.int(50, "Extend Right", minval=0, maxval=250, group="Line Settings", tooltip="Maximum allowed is 250 bars to avoid Pine Script's 500-bar future limit")
extend_left = input.int(200, "Extend Left", minval=0, maxval=500, group="Line Settings", tooltip="How many bars to the left the AR levels should extend")
offset_markers = input.bool(false, "Use Offset Markers", group="Line Settings", tooltip="Plot AR levels as offset markers on the side for a cleaner look")

// Text Settings
var g_TEXT = "Text Settings"
show_text = input.bool(true, "Show Text", group=g_TEXT)
text_size = input.string("Small", "Text Size", options=["Tiny", "Small", "Normal", "Large"], group=g_TEXT)
show_labels_for = input.string("Most Recent Only", "Show Labels For", options=["Most Recent Only", "All Levels", "None"], group=g_TEXT)

// Color Settings
daily_full_color = input.color(#ffffff, "Daily Full Range", group="Daily Colors")
daily_one_third_color = input.color(#ffffff, "Daily 1/3 Range", group="Daily Colors")
daily_two_third_color = input.color(#ffffff, "Daily 2/3 Range", group="Daily Colors")
daily_half_color = input.color(#ffffff, "Daily 1/2 Range", group="Daily Colors")

weekly_full_color = input.color(#ffffff, "Weekly Full Range", group="Weekly Colors")
weekly_one_third_color = input.color(#ffffff, "Weekly 1/3 Range", group="Weekly Colors")
weekly_two_third_color = input.color(#ffffff, "Weekly 2/3 Range", group="Weekly Colors")
weekly_half_color = input.color(#ffffff, "Weekly 1/2 Range", group="Weekly Colors")

monthly_full_color = input.color(#ffffff, "Monthly Full Range", group="Monthly Colors")
monthly_one_third_color = input.color(#ffffff, "Monthly 1/3 Range", group="Monthly Colors")
monthly_two_third_color = input.color(#ffffff, "Monthly 2/3 Range", group="Monthly Colors")
monthly_half_color = input.color(#ffffff, "Monthly 1/2 Range", group="Monthly Colors")

// Convert line style input to Pine style
var lineStyleValue = line.style_solid
if line_style == "Dashed"
    lineStyleValue := line.style_dashed
else if line_style == "Dotted"
    lineStyleValue := line.style_dotted

// Function to calculate the Average Range for a given timeframe
calculateAverageRange(tf, lookback_period) =>
    // Get high and low values for the lookback period
    high_array = request.security(syminfo.tickerid, tf, high, lookahead=barmerge.lookahead_on)
    low_array = request.security(syminfo.tickerid, tf, low, lookahead=barmerge.lookahead_on)

    // Calculate the sum of ranges
    float range_sum = 0.0
    for i = 1 to lookback_period
        if not na(high_array[i]) and not na(low_array[i])
            range_sum += (high_array[i] - low_array[i])

    // Return the average
    range_sum / lookback_period

// Store the true day open bar index for drawing lines from the anchor time
var int true_day_bar_index = na

clearOldLines(line_array) =>
    if array.size(line_array) > days_to_keep * 2  // Only delete when we have significantly more lines than needed
        // Keep the most recent lines (twice the days_to_keep to avoid frequent deletions)
        int keep_count = days_to_keep * 2
        int delete_count = array.size(line_array) - keep_count

        // Delete oldest lines
        for i = 0 to delete_count - 1
            if array.size(line_array) > 0
                line.delete(array.get(line_array, 0))
                array.remove(line_array, 0)

// Function to detect daily anchor time
isDailyAnchor() =>
    int current_hour = hour(time)
    int current_minute = minute(time)

    if daily_anchor_type == "New York Midnight"
        // New York is UTC-4 or UTC-5 depending on DST
        // We'll use a simplified approach here
        int ny_hour = current_hour >= 4 ? current_hour - 4 : current_hour + 20  // Approximate NY time
        ny_hour == 0 and current_minute == 0
    else if daily_anchor_type == "UTC Time"
        current_hour == daily_anchor_hour and current_minute == daily_anchor_minute
    else if daily_anchor_type == "Session Open"
        // Session open is typically at the start of the trading day
        current_hour == 9 and current_minute == 30  // Default to market open at 9:30 AM

// Function to detect weekly anchor time
isWeeklyAnchor() =>
    int current_hour = hour(time)
    int current_minute = minute(time)

    if weekly_anchor_type == "Monday Open"
        dayofweek == 1 and current_hour == 0 and current_minute == 0
    else if weekly_anchor_type == "Tuesday Open"
        dayofweek == 2 and current_hour == 0 and current_minute == 0
    else if weekly_anchor_type == "UTC Time"
        // Get day of week as integer (1-7 for Monday-Sunday)
        int dow = 0
        if weekly_anchor_day == "Monday"
            dow := 1
        else if weekly_anchor_day == "Tuesday"
            dow := 2
        else if weekly_anchor_day == "Wednesday"
            dow := 3
        else if weekly_anchor_day == "Thursday"
            dow := 4
        else if weekly_anchor_day == "Friday"
            dow := 5
        else if weekly_anchor_day == "Saturday"
            dow := 6
        else if weekly_anchor_day == "Sunday"
            dow := 7

        dayofweek == dow and current_hour == weekly_anchor_hour and current_minute == weekly_anchor_minute

// Function to detect monthly anchor time
isMonthlyAnchor() =>
    int current_hour = hour(time)
    int current_minute = minute(time)

    if monthly_anchor_type == "First Day"
        dayofmonth(time) == 1 and current_hour == 0 and current_minute == 0
    else if monthly_anchor_type == "Second Week"
        // Second Monday of the month
        dayofweek == 1 and dayofmonth(time) > 7 and dayofmonth(time) <= 14 and current_hour == 0 and current_minute == 0
    else if monthly_anchor_type == "UTC Time"
        dayofmonth(time) == 1 and current_hour == monthly_anchor_hour and current_minute == monthly_anchor_minute
// Initialize true_day_bar_index on the first bar

if barstate.isfirst
    true_day_bar_index := 0  // Start at the first bar

// Global variables to store true day open values
var float daily_true_day_open = na
var float weekly_true_day_open = na
var float monthly_true_day_open = na

// Global variables to store range values
var float daily_range = na
var float weekly_range = na
var float monthly_range = na

// Store ADR levels
var float daily_full_up = na
var float daily_full_down = na
var float daily_one_third_up = na
var float daily_one_third_down = na
var float daily_two_third_up = na
var float daily_two_third_down = na
var float daily_half_up = na
var float daily_half_down = na

var float weekly_full_up = na
var float weekly_full_down = na
var float weekly_one_third_up = na
var float weekly_one_third_down = na
var float weekly_two_third_up = na
var float weekly_two_third_down = na
var float weekly_half_up = na
var float weekly_half_down = na

var float monthly_full_up = na
var float monthly_full_down = na
var float monthly_one_third_up = na
var float monthly_one_third_down = na
var float monthly_two_third_up = na
var float monthly_two_third_down = na
var float monthly_half_up = na
var float monthly_half_down = na

// Arrays to store historical lines
var line[] daily_full_up_lines = array.new_line()
var line[] daily_full_down_lines = array.new_line()
var line[] daily_one_third_up_lines = array.new_line()
var line[] daily_one_third_down_lines = array.new_line()
var line[] daily_two_third_up_lines = array.new_line()
var line[] daily_two_third_down_lines = array.new_line()
var line[] daily_half_up_lines = array.new_line()
var line[] daily_half_down_lines = array.new_line()

var line[] weekly_full_up_lines = array.new_line()
var line[] weekly_full_down_lines = array.new_line()
var line[] weekly_one_third_up_lines = array.new_line()
var line[] weekly_one_third_down_lines = array.new_line()
var line[] weekly_two_third_up_lines = array.new_line()
var line[] weekly_two_third_down_lines = array.new_line()
var line[] weekly_half_up_lines = array.new_line()
var line[] weekly_half_down_lines = array.new_line()

var line[] monthly_full_up_lines = array.new_line()
var line[] monthly_full_down_lines = array.new_line()
var line[] monthly_one_third_up_lines = array.new_line()
var line[] monthly_one_third_down_lines = array.new_line()
var line[] monthly_two_third_up_lines = array.new_line()
var line[] monthly_two_third_down_lines = array.new_line()
var line[] monthly_half_up_lines = array.new_line()
var line[] monthly_half_down_lines = array.new_line()

// Arrays to store timestamps of when ADR levels were calculated
var int[] daily_timestamps = array.new_int()
var int[] weekly_timestamps = array.new_int()
var int[] monthly_timestamps = array.new_int()

// Calculate initial range values
if barstate.isfirst
    daily_range := calculateAverageRange("D", adr_days)
    weekly_range := calculateAverageRange("W", adr_days)
    monthly_range := calculateAverageRange("M", adr_days)

// Check if we're at the daily anchor time
if isDailyAnchor()
    // Update true day open bar index
    true_day_bar_index := bar_index
    daily_true_day_open := open

    // Calculate Daily ADR levels
    daily_full_up := daily_true_day_open + daily_range
    daily_full_down := daily_true_day_open - daily_range
    daily_one_third_up := daily_true_day_open + (daily_range / 3)
    daily_one_third_down := daily_true_day_open - (daily_range / 3)
    daily_two_third_up := daily_true_day_open + (daily_range * 2 / 3)
    daily_two_third_down := daily_true_day_open - (daily_range * 2 / 3)
    daily_half_up := daily_true_day_open + (daily_range / 2)
    daily_half_down := daily_true_day_open - (daily_range / 2)

    // Clear old lines before drawing new ones
    if show_daily
        clearOldLines(daily_full_up_lines)
        clearOldLines(daily_full_down_lines)
        clearOldLines(daily_one_third_up_lines)
        clearOldLines(daily_one_third_down_lines)
        clearOldLines(daily_two_third_up_lines)
        clearOldLines(daily_two_third_down_lines)
        clearOldLines(daily_half_up_lines)
        clearOldLines(daily_half_down_lines)

        // Draw new lines
        if show_full_adr
            line daily_full_up_line = line.new(x1=bar_index, y1=daily_full_up, x2=bar_index+extend_right, y2=daily_full_up, color=daily_full_color, style=lineStyleValue, width=line_width)
            line daily_full_down_line = line.new(x1=bar_index, y1=daily_full_down, x2=bar_index+extend_right, y2=daily_full_down, color=daily_full_color, style=lineStyleValue, width=line_width)
            array.push(daily_full_up_lines, daily_full_up_line)
            array.push(daily_full_down_lines, daily_full_down_line)

        if show_one_third_adr
            line daily_one_third_up_line = line.new(x1=bar_index, y1=daily_one_third_up, x2=bar_index+extend_right, y2=daily_one_third_up, color=daily_one_third_color, style=lineStyleValue, width=line_width)
            line daily_one_third_down_line = line.new(x1=bar_index, y1=daily_one_third_down, x2=bar_index+extend_right, y2=daily_one_third_down, color=daily_one_third_color, style=lineStyleValue, width=line_width)
            array.push(daily_one_third_up_lines, daily_one_third_up_line)
            array.push(daily_one_third_down_lines, daily_one_third_down_line)

        if show_two_third_adr
            line daily_two_third_up_line = line.new(x1=bar_index, y1=daily_two_third_up, x2=bar_index+extend_right, y2=daily_two_third_up, color=daily_two_third_color, style=lineStyleValue, width=line_width)
            line daily_two_third_down_line = line.new(x1=bar_index, y1=daily_two_third_down, x2=bar_index+extend_right, y2=daily_two_third_down, color=daily_two_third_color, style=lineStyleValue, width=line_width)
            array.push(daily_two_third_up_lines, daily_two_third_up_line)
            array.push(daily_two_third_down_lines, daily_two_third_down_line)

        if show_half_adr
            line daily_half_up_line = line.new(x1=bar_index, y1=daily_half_up, x2=bar_index+extend_right, y2=daily_half_up, color=daily_half_color, style=lineStyleValue, width=line_width)
            line daily_half_down_line = line.new(x1=bar_index, y1=daily_half_down, x2=bar_index+extend_right, y2=daily_half_down, color=daily_half_color, style=lineStyleValue, width=line_width)
            array.push(daily_half_up_lines, daily_half_up_line)
            array.push(daily_half_down_lines, daily_half_down_line)

    // Store the current timestamp for this day's ADR levels
    array.unshift(daily_timestamps, int(time))
    // Keep only the timestamps for the days we want to display
    while array.size(daily_timestamps) > days_to_keep
        array.pop(daily_timestamps)

    // Draw separator line if enabled
    if show_true_day_separator
        line.new(x1=bar_index,y1=high * 1.05, x2=bar_index,y2=low * 0.95,color=true_day_separator_color,style=line.style_dashed,width=1,extend=extend.both)

// Check if we're at the weekly anchor time
if isWeeklyAnchor()
    weekly_true_day_open := open

    // Calculate Weekly AWR levels
    weekly_full_up := weekly_true_day_open + weekly_range
    weekly_full_down := weekly_true_day_open - weekly_range
    weekly_one_third_up := weekly_true_day_open + (weekly_range / 3)
    weekly_one_third_down := weekly_true_day_open - (weekly_range / 3)
    weekly_two_third_up := weekly_true_day_open + (weekly_range * 2 / 3)
    weekly_two_third_down := weekly_true_day_open - (weekly_range * 2 / 3)
    weekly_half_up := weekly_true_day_open + (weekly_range / 2)
    weekly_half_down := weekly_true_day_open - (weekly_range / 2)

    // Clear old lines before drawing new ones
    if show_weekly
        clearOldLines(weekly_full_up_lines)
        clearOldLines(weekly_full_down_lines)
        clearOldLines(weekly_one_third_up_lines)
        clearOldLines(weekly_one_third_down_lines)
        clearOldLines(weekly_two_third_up_lines)
        clearOldLines(weekly_two_third_down_lines)
        clearOldLines(weekly_half_up_lines)
        clearOldLines(weekly_half_down_lines)

        // Draw new lines
        if show_full_adr
            line weekly_full_up_line = line.new(x1=bar_index, y1=weekly_full_up, x2=bar_index+extend_right, y2=weekly_full_up, color=weekly_full_color, style=lineStyleValue, width=line_width)
            line weekly_full_down_line = line.new(x1=bar_index, y1=weekly_full_down, x2=bar_index+extend_right, y2=weekly_full_down, color=weekly_full_color, style=lineStyleValue, width=line_width)
            array.push(weekly_full_up_lines, weekly_full_up_line)
            array.push(weekly_full_down_lines, weekly_full_down_line)

        if show_one_third_adr
            line weekly_one_third_up_line = line.new(x1=bar_index, y1=weekly_one_third_up, x2=bar_index+extend_right, y2=weekly_one_third_up, color=weekly_one_third_color, style=lineStyleValue, width=line_width)
            line weekly_one_third_down_line = line.new(x1=bar_index, y1=weekly_one_third_down, x2=bar_index+extend_right, y2=weekly_one_third_down, color=weekly_one_third_color, style=lineStyleValue, width=line_width)
            array.push(weekly_one_third_up_lines, weekly_one_third_up_line)
            array.push(weekly_one_third_down_lines, weekly_one_third_down_line)

        if show_two_third_adr
            line weekly_two_third_up_line = line.new(x1=bar_index, y1=weekly_two_third_up, x2=bar_index+extend_right, y2=weekly_two_third_up, color=weekly_two_third_color, style=lineStyleValue, width=line_width)
            line weekly_two_third_down_line = line.new(x1=bar_index, y1=weekly_two_third_down, x2=bar_index+extend_right, y2=weekly_two_third_down, color=weekly_two_third_color, style=lineStyleValue, width=line_width)
            array.push(weekly_two_third_up_lines, weekly_two_third_up_line)
            array.push(weekly_two_third_down_lines, weekly_two_third_down_line)

        if show_half_adr
            line weekly_half_up_line = line.new(x1=bar_index, y1=weekly_half_up, x2=bar_index+extend_right, y2=weekly_half_up, color=weekly_half_color, style=lineStyleValue, width=line_width)
            line weekly_half_down_line = line.new(x1=bar_index, y1=weekly_half_down, x2=bar_index+extend_right, y2=weekly_half_down, color=weekly_half_color, style=lineStyleValue, width=line_width)
            array.push(weekly_half_up_lines, weekly_half_up_line)
            array.push(weekly_half_down_lines, weekly_half_down_line)

    // Store the current timestamp for this week's AWR levels
    array.unshift(weekly_timestamps, int(time))
    // Keep only the timestamps for the weeks we want to display
    while array.size(weekly_timestamps) > days_to_keep
        array.pop(weekly_timestamps)

    // Draw separator line if enabled
    if show_true_day_separator
        line.new(x1=bar_index,y1=high * 1.05, x2=bar_index,y2=low * 0.95,color=color.new(color.blue, 50),style=line.style_dashed,width=1,extend=extend.both)

// Check if we're at the monthly anchor time
if isMonthlyAnchor()
    monthly_true_day_open := open

    // Calculate Monthly AMR levels
    monthly_full_up := monthly_true_day_open + monthly_range
    monthly_full_down := monthly_true_day_open - monthly_range
    monthly_one_third_up := monthly_true_day_open + (monthly_range / 3)
    monthly_one_third_down := monthly_true_day_open - (monthly_range / 3)
    monthly_two_third_up := monthly_true_day_open + (monthly_range * 2 / 3)
    monthly_two_third_down := monthly_true_day_open - (monthly_range * 2 / 3)
    monthly_half_up := monthly_true_day_open + (monthly_range / 2)
    monthly_half_down := monthly_true_day_open - (monthly_range / 2)

    // Clear old lines before drawing new ones
    if show_monthly
        clearOldLines(monthly_full_up_lines)
        clearOldLines(monthly_full_down_lines)
        clearOldLines(monthly_one_third_up_lines)
        clearOldLines(monthly_one_third_down_lines)
        clearOldLines(monthly_two_third_up_lines)
        clearOldLines(monthly_two_third_down_lines)
        clearOldLines(monthly_half_up_lines)
        clearOldLines(monthly_half_down_lines)

        // Draw new lines
        if show_full_adr
            line monthly_full_up_line = line.new(x1=bar_index, y1=monthly_full_up, x2=bar_index+extend_right, y2=monthly_full_up, color=monthly_full_color, style=lineStyleValue, width=line_width)
            line monthly_full_down_line = line.new(x1=bar_index, y1=monthly_full_down, x2=bar_index+extend_right, y2=monthly_full_down, color=monthly_full_color, style=lineStyleValue, width=line_width)
            array.push(monthly_full_up_lines, monthly_full_up_line)
            array.push(monthly_full_down_lines, monthly_full_down_line)

        if show_one_third_adr
            line monthly_one_third_up_line = line.new(x1=bar_index, y1=monthly_one_third_up, x2=bar_index+extend_right, y2=monthly_one_third_up, color=monthly_one_third_color, style=lineStyleValue, width=line_width)
            line monthly_one_third_down_line = line.new(x1=bar_index, y1=monthly_one_third_down, x2=bar_index+extend_right, y2=monthly_one_third_down, color=monthly_one_third_color, style=lineStyleValue, width=line_width)
            array.push(monthly_one_third_up_lines, monthly_one_third_up_line)
            array.push(monthly_one_third_down_lines, monthly_one_third_down_line)

        if show_two_third_adr
            line monthly_two_third_up_line = line.new(x1=bar_index, y1=monthly_two_third_up, x2=bar_index+extend_right, y2=monthly_two_third_up, color=monthly_two_third_color, style=lineStyleValue, width=line_width)
            line monthly_two_third_down_line = line.new(x1=bar_index, y1=monthly_two_third_down, x2=bar_index+extend_right, y2=monthly_two_third_down, color=monthly_two_third_color, style=lineStyleValue, width=line_width)
            array.push(monthly_two_third_up_lines, monthly_two_third_up_line)
            array.push(monthly_two_third_down_lines, monthly_two_third_down_line)

        if show_half_adr
            line monthly_half_up_line = line.new(x1=bar_index, y1=monthly_half_up, x2=bar_index+extend_right, y2=monthly_half_up, color=monthly_half_color, style=lineStyleValue, width=line_width)
            line monthly_half_down_line = line.new(x1=bar_index, y1=monthly_half_down, x2=bar_index+extend_right, y2=monthly_half_down, color=monthly_half_color, style=lineStyleValue, width=line_width)
            array.push(monthly_half_up_lines, monthly_half_up_line)
            array.push(monthly_half_down_lines, monthly_half_down_line)

    // Store the current timestamp for this month's AMR levels
    array.unshift(monthly_timestamps, int(time))
    // Keep only the timestamps for the months we want to display
    while array.size(monthly_timestamps) > days_to_keep
        array.pop(monthly_timestamps)

    // Draw separator line if enabled
    if show_true_day_separator
        line.new(x1=bar_index,y1=high * 1.05, x2=bar_index,y2=low * 0.95,color=color.new(color.red, 50),style=line.style_dashed,width=1,extend=extend.both)

// Calculate range values only at the beginning and when anchor points are found
if barstate.isfirst or isDailyAnchor() or isWeeklyAnchor() or isMonthlyAnchor()
    daily_range := calculateAverageRange("D", adr_days)
    weekly_range := calculateAverageRange("W", adr_days)
    monthly_range := calculateAverageRange("M", adr_days)



// Function to clear old lines - only delete lines when we have more than days_to_keep
// Function to draw ADR level and return the line
drawADRLevel(price_level, color_value, label_text, timestamp, is_most_recent) =>
    if na(price_level) or na(timestamp)
        line.new(na, na, na, na)
    else
        // Find the bar index for this timestamp
        int bar_offset = 0
        for i = 0 to 500
            if i <= bar_index and int(time[i]) == timestamp
                bar_offset := i
                break

        // Calculate start and end bar positions
        int startBar = 0
        int endBar = 0

        if offset_markers
            // For offset markers, we just draw a short line at the right edge of the chart
            startBar := bar_index - 5
            endBar := bar_index + 5
        else
            // For normal lines, start from the anchor point (bar_index - bar_offset)
            startBar := bar_index - bar_offset  // Start at the true day open bar
            endBar := bar_index + extend_right  // Extend to the right

        // Ensure we never exceed the 500-bar future limit
        if endBar > bar_index + 499
            endBar := bar_index + 499

        // Create the line directly with x,y coordinates
        line result = line.new(
             x1=startBar,  // Start from the anchor point
             y1=price_level,
             x2=endBar,
             y2=price_level,
             color=color_value,
             style=lineStyleValue,
             width=line_width,
             xloc=xloc.bar_index)  // Explicitly use bar_index mode

        // Only create labels if show_text is enabled and based on the show_labels_for setting
        if show_text and show_labels_for != "None" and (show_labels_for == "All Levels" or (show_labels_for == "Most Recent Only" and is_most_recent))
            // Determine label size based on text_size setting
            var label_size = size.small
            if text_size == "Tiny"
                label_size := size.tiny
            else if text_size == "Small"
                label_size := size.small
            else if text_size == "Normal"
                label_size := size.normal
            else if text_size == "Large"
                label_size := size.large

            // Place labels at the right side of the chart (end of the line)
            // Create label at the appropriate position
            if endBar <= bar_index + 499  // Ensure we don't exceed Pine Script's future bar limit
                label.new(x=endBar, y=price_level, text=label_text, style=label.style_label_left, color=color.rgb(0, 0, 0, 0), textcolor=color_value, size=label_size, xloc=xloc.bar_index)
        result

// Function to update existing AR level lines
updateARLevelLines() =>
    // Update existing lines to extend to the current bar
    if array.size(daily_full_up_lines) > 0
        for i = 0 to array.size(daily_full_up_lines) - 1
            if i < array.size(daily_full_up_lines)
                line.set_x2(array.get(daily_full_up_lines, i), bar_index + extend_right)
            if i < array.size(daily_full_down_lines)
                line.set_x2(array.get(daily_full_down_lines, i), bar_index + extend_right)

            if show_one_third_adr
                if i < array.size(daily_one_third_up_lines)
                    line.set_x2(array.get(daily_one_third_up_lines, i), bar_index + extend_right)
                if i < array.size(daily_one_third_down_lines)
                    line.set_x2(array.get(daily_one_third_down_lines, i), bar_index + extend_right)

            if show_two_third_adr
                if i < array.size(daily_two_third_up_lines)
                    line.set_x2(array.get(daily_two_third_up_lines, i), bar_index + extend_right)
                if i < array.size(daily_two_third_down_lines)
                    line.set_x2(array.get(daily_two_third_down_lines, i), bar_index + extend_right)

            if show_half_adr
                if i < array.size(daily_half_up_lines)
                    line.set_x2(array.get(daily_half_up_lines, i), bar_index + extend_right)
                if i < array.size(daily_half_down_lines)
                    line.set_x2(array.get(daily_half_down_lines, i), bar_index + extend_right)

    if array.size(weekly_full_up_lines) > 0
        for i = 0 to array.size(weekly_full_up_lines) - 1
            if i < array.size(weekly_full_up_lines)
                line.set_x2(array.get(weekly_full_up_lines, i), bar_index + extend_right)
            if i < array.size(weekly_full_down_lines)
                line.set_x2(array.get(weekly_full_down_lines, i), bar_index + extend_right)

            if show_one_third_adr
                if i < array.size(weekly_one_third_up_lines)
                    line.set_x2(array.get(weekly_one_third_up_lines, i), bar_index + extend_right)
                if i < array.size(weekly_one_third_down_lines)
                    line.set_x2(array.get(weekly_one_third_down_lines, i), bar_index + extend_right)

            if show_two_third_adr
                if i < array.size(weekly_two_third_up_lines)
                    line.set_x2(array.get(weekly_two_third_up_lines, i), bar_index + extend_right)
                if i < array.size(weekly_two_third_down_lines)
                    line.set_x2(array.get(weekly_two_third_down_lines, i), bar_index + extend_right)

            if show_half_adr
                if i < array.size(weekly_half_up_lines)
                    line.set_x2(array.get(weekly_half_up_lines, i), bar_index + extend_right)
                if i < array.size(weekly_half_down_lines)
                    line.set_x2(array.get(weekly_half_down_lines, i), bar_index + extend_right)

    if array.size(monthly_full_up_lines) > 0
        for i = 0 to array.size(monthly_full_up_lines) - 1
            if i < array.size(monthly_full_up_lines)
                line.set_x2(array.get(monthly_full_up_lines, i), bar_index + extend_right)
            if i < array.size(monthly_full_down_lines)
                line.set_x2(array.get(monthly_full_down_lines, i), bar_index + extend_right)

            if show_one_third_adr
                if i < array.size(monthly_one_third_up_lines)
                    line.set_x2(array.get(monthly_one_third_up_lines, i), bar_index + extend_right)
                if i < array.size(monthly_one_third_down_lines)
                    line.set_x2(array.get(monthly_one_third_down_lines, i), bar_index + extend_right)

            if show_two_third_adr
                if i < array.size(monthly_two_third_up_lines)
                    line.set_x2(array.get(monthly_two_third_up_lines, i), bar_index + extend_right)
                if i < array.size(monthly_two_third_down_lines)
                    line.set_x2(array.get(monthly_two_third_down_lines, i), bar_index + extend_right)

            if show_half_adr
                if i < array.size(monthly_half_up_lines)
                    line.set_x2(array.get(monthly_half_up_lines, i), bar_index + extend_right)
                if i < array.size(monthly_half_down_lines)
                    line.set_x2(array.get(monthly_half_down_lines, i), bar_index + extend_right)

// Update AR level lines on each bar
updateARLevelLines()

// Only draw ADR levels on the last bar if we don't have any levels yet
if barstate.islast and (array.size(daily_full_up_lines) == 0 or array.size(weekly_full_up_lines) == 0 or array.size(monthly_full_up_lines) == 0)
    // Clear all existing lines first
    if show_daily
        clearOldLines(daily_full_up_lines)
        clearOldLines(daily_full_down_lines)
        clearOldLines(daily_one_third_up_lines)
        clearOldLines(daily_one_third_down_lines)
        clearOldLines(daily_two_third_up_lines)
        clearOldLines(daily_two_third_down_lines)
        clearOldLines(daily_half_up_lines)
        clearOldLines(daily_half_down_lines)

    if show_weekly
        clearOldLines(weekly_full_up_lines)
        clearOldLines(weekly_full_down_lines)
        clearOldLines(weekly_one_third_up_lines)
        clearOldLines(weekly_one_third_down_lines)
        clearOldLines(weekly_two_third_up_lines)
        clearOldLines(weekly_two_third_down_lines)
        clearOldLines(weekly_half_up_lines)
        clearOldLines(weekly_half_down_lines)

    if show_monthly
        clearOldLines(monthly_full_up_lines)
        clearOldLines(monthly_full_down_lines)
        clearOldLines(monthly_one_third_up_lines)
        clearOldLines(monthly_one_third_down_lines)
        clearOldLines(monthly_two_third_up_lines)
        clearOldLines(monthly_two_third_down_lines)
        clearOldLines(monthly_half_up_lines)
        clearOldLines(monthly_half_down_lines)
    // Draw Daily ADR levels
    if show_daily and array.size(daily_timestamps) > 0
        // Loop through each day's timestamp and draw its ADR levels
        for t = 0 to math.min(array.size(daily_timestamps) - 1, days_to_keep - 1)
            timestamp = array.get(daily_timestamps, t)

            // Calculate ADR levels for this day
            float day_full_up = na
            float day_full_down = na
            float day_one_third_up = na
            float day_one_third_down = na
            float day_two_third_up = na
            float day_two_third_down = na
            float day_half_up = na
            float day_half_down = na

            if not na(daily_true_day_open) and not na(daily_range)
                day_full_up := daily_true_day_open + daily_range
                day_full_down := daily_true_day_open - daily_range
                day_one_third_up := daily_true_day_open + (daily_range / 3)
                day_one_third_down := daily_true_day_open - (daily_range / 3)
                day_two_third_up := daily_true_day_open + (daily_range * 2 / 3)
                day_two_third_down := daily_true_day_open - (daily_range * 2 / 3)
                day_half_up := daily_true_day_open + (daily_range / 2)
                day_half_down := daily_true_day_open - (daily_range / 2)

            // Check if this is the most recent timestamp (t == 0)
            bool is_most_recent = (t == 0)

            // Draw Full ADR levels
            if show_full_adr
                // Find the bar index for this timestamp to use as the anchor point
                int anchor_bar = bar_index
                for i = 0 to 500
                    if i <= bar_index and int(time[i]) == timestamp
                        anchor_bar := bar_index - i
                        break

                line daily_full_up_line = drawADRLevel(day_full_up, daily_full_color, "ADR+", timestamp, is_most_recent)
                line daily_full_down_line = drawADRLevel(day_full_down, daily_full_color, "ADR-", timestamp, is_most_recent)
                if not na(daily_full_up_line)
                    array.push(daily_full_up_lines, daily_full_up_line)
                if not na(daily_full_down_line)
                    array.push(daily_full_down_lines, daily_full_down_line)

            // Draw 1/3 ADR levels
            if show_one_third_adr
                line daily_one_third_up_line = drawADRLevel(day_one_third_up, daily_one_third_color, "1/3 ADR+", timestamp, is_most_recent)
                line daily_one_third_down_line = drawADRLevel(day_one_third_down, daily_one_third_color, "1/3 ADR-", timestamp, is_most_recent)
                if not na(daily_one_third_up_line)
                    array.push(daily_one_third_up_lines, daily_one_third_up_line)
                if not na(daily_one_third_down_line)
                    array.push(daily_one_third_down_lines, daily_one_third_down_line)

            // Draw 2/3 ADR levels
            if show_two_third_adr
                line daily_two_third_up_line = drawADRLevel(day_two_third_up, daily_two_third_color, "2/3 ADR+", timestamp, is_most_recent)
                line daily_two_third_down_line = drawADRLevel(day_two_third_down, daily_two_third_color, "2/3 ADR-", timestamp, is_most_recent)
                if not na(daily_two_third_up_line)
                    array.push(daily_two_third_up_lines, daily_two_third_up_line)
                if not na(daily_two_third_down_line)
                    array.push(daily_two_third_down_lines, daily_two_third_down_line)

            // Draw 1/2 ADR levels
            if show_half_adr
                line daily_half_up_line = drawADRLevel(day_half_up, daily_half_color, "1/2 ADR+", timestamp, is_most_recent)
                line daily_half_down_line = drawADRLevel(day_half_down, daily_half_color, "1/2 ADR-", timestamp, is_most_recent)
                if not na(daily_half_up_line)
                    array.push(daily_half_up_lines, daily_half_up_line)
                if not na(daily_half_down_line)
                    array.push(daily_half_down_lines, daily_half_down_line)

    // Draw Weekly AWR levels
    if show_weekly and array.size(weekly_timestamps) > 0
        // Loop through each week's timestamp and draw its AWR levels
        for t = 0 to math.min(array.size(weekly_timestamps) - 1, days_to_keep - 1)
            timestamp = array.get(weekly_timestamps, t)

            // Calculate AWR levels for this week
            float week_full_up = na
            float week_full_down = na
            float week_one_third_up = na
            float week_one_third_down = na
            float week_two_third_up = na
            float week_two_third_down = na
            float week_half_up = na
            float week_half_down = na

            if not na(weekly_true_day_open) and not na(weekly_range)
                week_full_up := weekly_true_day_open + weekly_range
                week_full_down := weekly_true_day_open - weekly_range
                week_one_third_up := weekly_true_day_open + (weekly_range / 3)
                week_one_third_down := weekly_true_day_open - (weekly_range / 3)
                week_two_third_up := weekly_true_day_open + (weekly_range * 2 / 3)
                week_two_third_down := weekly_true_day_open - (weekly_range * 2 / 3)
                week_half_up := weekly_true_day_open + (weekly_range / 2)
                week_half_down := weekly_true_day_open - (weekly_range / 2)

            // Check if this is the most recent timestamp (t == 0)
            bool is_most_recent = (t == 0)

            // Draw Full AWR levels
            if show_full_adr
                // Find the bar index for this timestamp to use as the anchor point
                int anchor_bar = bar_index
                for i = 0 to 500
                    if i <= bar_index and int(time[i]) == timestamp
                        anchor_bar := bar_index - i
                        break

                line weekly_full_up_line = drawADRLevel(week_full_up, weekly_full_color, "AWR+", timestamp, is_most_recent)
                line weekly_full_down_line = drawADRLevel(week_full_down, weekly_full_color, "AWR-", timestamp, is_most_recent)
                if not na(weekly_full_up_line)
                    array.push(weekly_full_up_lines, weekly_full_up_line)
                if not na(weekly_full_down_line)
                    array.push(weekly_full_down_lines, weekly_full_down_line)

            // Draw 1/3 AWR levels
            if show_one_third_adr
                line weekly_one_third_up_line = drawADRLevel(week_one_third_up, weekly_one_third_color, "1/3 AWR+", timestamp, is_most_recent)
                line weekly_one_third_down_line = drawADRLevel(week_one_third_down, weekly_one_third_color, "1/3 AWR-", timestamp, is_most_recent)
                if not na(weekly_one_third_up_line)
                    array.push(weekly_one_third_up_lines, weekly_one_third_up_line)
                if not na(weekly_one_third_down_line)
                    array.push(weekly_one_third_down_lines, weekly_one_third_down_line)

            // Draw 2/3 AWR levels
            if show_two_third_adr
                line weekly_two_third_up_line = drawADRLevel(week_two_third_up, weekly_two_third_color, "2/3 AWR+", timestamp, is_most_recent)
                line weekly_two_third_down_line = drawADRLevel(week_two_third_down, weekly_two_third_color, "2/3 AWR-", timestamp, is_most_recent)
                if not na(weekly_two_third_up_line)
                    array.push(weekly_two_third_up_lines, weekly_two_third_up_line)
                if not na(weekly_two_third_down_line)
                    array.push(weekly_two_third_down_lines, weekly_two_third_down_line)

            // Draw 1/2 AWR levels
            if show_half_adr
                line weekly_half_up_line = drawADRLevel(week_half_up, weekly_half_color, "1/2 AWR+", timestamp, is_most_recent)
                line weekly_half_down_line = drawADRLevel(week_half_down, weekly_half_color, "1/2 AWR-", timestamp, is_most_recent)
                if not na(weekly_half_up_line)
                    array.push(weekly_half_up_lines, weekly_half_up_line)
                if not na(weekly_half_down_line)
                    array.push(weekly_half_down_lines, weekly_half_down_line)

    // Draw Monthly AMR levels
    if show_monthly and array.size(monthly_timestamps) > 0
        // Loop through each month's timestamp and draw its AMR levels
        for t = 0 to math.min(array.size(monthly_timestamps) - 1, days_to_keep - 1)
            timestamp = array.get(monthly_timestamps, t)

            // Calculate AMR levels for this month
            float month_full_up = na
            float month_full_down = na
            float month_one_third_up = na
            float month_one_third_down = na
            float month_two_third_up = na
            float month_two_third_down = na
            float month_half_up = na
            float month_half_down = na

            if not na(monthly_true_day_open) and not na(monthly_range)
                month_full_up := monthly_true_day_open + monthly_range
                month_full_down := monthly_true_day_open - monthly_range
                month_one_third_up := monthly_true_day_open + (monthly_range / 3)
                month_one_third_down := monthly_true_day_open - (monthly_range / 3)
                month_two_third_up := monthly_true_day_open + (monthly_range * 2 / 3)
                month_two_third_down := monthly_true_day_open - (monthly_range * 2 / 3)
                month_half_up := monthly_true_day_open + (monthly_range / 2)
                month_half_down := monthly_true_day_open - (monthly_range / 2)

            // Check if this is the most recent timestamp (t == 0)
            bool is_most_recent = (t == 0)

            // Draw Full AMR levels
            if show_full_adr
                // Find the bar index for this timestamp to use as the anchor point
                int anchor_bar = bar_index
                for i = 0 to 500
                    if i <= bar_index and int(time[i]) == timestamp
                        anchor_bar := bar_index - i
                        break

                line monthly_full_up_line = drawADRLevel(month_full_up, monthly_full_color, "AMR+", timestamp, is_most_recent)
                line monthly_full_down_line = drawADRLevel(month_full_down, monthly_full_color, "AMR-", timestamp, is_most_recent)
                if not na(monthly_full_up_line)
                    array.push(monthly_full_up_lines, monthly_full_up_line)
                if not na(monthly_full_down_line)
                    array.push(monthly_full_down_lines, monthly_full_down_line)

            // Draw 1/3 AMR levels
            if show_one_third_adr
                line monthly_one_third_up_line = drawADRLevel(month_one_third_up, monthly_one_third_color, "1/3 AMR+", timestamp, is_most_recent)
                line monthly_one_third_down_line = drawADRLevel(month_one_third_down, monthly_one_third_color, "1/3 AMR-", timestamp, is_most_recent)
                if not na(monthly_one_third_up_line)
                    array.push(monthly_one_third_up_lines, monthly_one_third_up_line)
                if not na(monthly_one_third_down_line)
                    array.push(monthly_one_third_down_lines, monthly_one_third_down_line)

            // Draw 2/3 AMR levels
            if show_two_third_adr
                line monthly_two_third_up_line = drawADRLevel(month_two_third_up, monthly_two_third_color, "2/3 AMR+", timestamp, is_most_recent)
                line monthly_two_third_down_line = drawADRLevel(month_two_third_down, monthly_two_third_color, "2/3 AMR-", timestamp, is_most_recent)
                if not na(monthly_two_third_up_line)
                    array.push(monthly_two_third_up_lines, monthly_two_third_up_line)
                if not na(monthly_two_third_down_line)
                    array.push(monthly_two_third_down_lines, monthly_two_third_down_line)

            // Draw 1/2 AMR levels
            if show_half_adr
                line monthly_half_up_line = drawADRLevel(month_half_up, monthly_half_color, "1/2 AMR+", timestamp, is_most_recent)
                line monthly_half_down_line = drawADRLevel(month_half_down, monthly_half_color, "1/2 AMR-", timestamp, is_most_recent)
                if not na(monthly_half_up_line)
                    array.push(monthly_half_up_lines, monthly_half_up_line)
                if not na(monthly_half_down_line)
                    array.push(monthly_half_down_lines, monthly_half_down_line)

// No plots to avoid dots when price moves

// Alert conditions for price touching ADR levels
var string daily_full_up_alert = "Price touched Daily Full ADR+ level"
var string daily_full_down_alert = "Price touched Daily Full ADR- level"
var string daily_one_third_up_alert = "Price touched Daily 1/3 ADR+ level"
var string daily_one_third_down_alert = "Price touched Daily 1/3 ADR- level"

alertcondition(show_daily and (ta.crossover(high, daily_full_up) or ta.crossunder(low, daily_full_up)), "Price touched Daily Full ADR+", daily_full_up_alert)
alertcondition(show_daily and (ta.crossover(high, daily_full_down) or ta.crossunder(low, daily_full_down)), "Price touched Daily Full ADR-", daily_full_down_alert)
alertcondition(show_daily and (ta.crossover(high, daily_one_third_up) or ta.crossunder(low, daily_one_third_up)), "Price touched Daily 1/3 ADR+", daily_one_third_up_alert)
alertcondition(show_daily and (ta.crossover(high, daily_one_third_down) or ta.crossunder(low, daily_one_third_down)), "Price touched Daily 1/3 ADR-", daily_one_third_down_alert)
