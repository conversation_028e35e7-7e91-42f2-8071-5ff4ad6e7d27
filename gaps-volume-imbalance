// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Vulnerable_human_x

//@version=5
indicator("ICT - GAPS, Volume & Price Imbalances", shorttitle = "ICT - Imbalances [VHX]", overlay=true, max_boxes_count = 500, max_lines_count = 500)


//Volume Imbalance
bool classicvi = na
bool advancedvi = na
vitype = input.string("Classic", "Volume Imbalance Type",options = ["None","Classic","Advanced"], group="Volume Imbalance", tooltip = "Classic mode includes only a single type of body gap, while advanced mode covers all body gap variants.")

if vitype == "Classic"
    classicvi := true
    advancedvi := false
else if vitype == "Advanced"
    classicvi := false
    advancedvi := true
else if vitype == "None"
    classicvi := false
    advancedvi := false

bullimbalance = input.color(color.new(#00e640,90), "Bullish VI Color", group="Volume Imbalance", inline="1")
bearimbalance = input.color(color.new(#e30000,90), "Bearish VI Color", group="Volume Imbalance", inline="1")
viMaxBoxSet = input.int(defval=8, title='Maximum Box Displayed', minval=1, maxval=100, group="Volume Imbalance", tooltip='Minimum = 1, Maximum = 100')
vimitigationtype = input.string(defval="Engulf", title="VI Mitigation Type", options = ["Engulf","Mitigate"], group="Volume Imbalance", tooltip = "Body close below Bullish VI and above Bearish VI required for Engulf Mitigation Type")
extendvibox = input.bool(true, title="Extend Unmitigated VI Boxes", group="Volume Imbalance")
extendallvis = input.bool(false,title="Extend all VI Boxes", group="Volume Imbalance")

//Gaps
gaptype = input.string("True GAP", "GAPs Type",options = ["None","True GAP","GAP + Inefficiency"], group="GAP", tooltip = "True GAPs are price zones with no buyside or sellside delivery at all. GAP + Inefficiency, however, cover both regions where there's an absence of buyside and sellside activity, and areas with one-sided price action (the gap between the close of the previous candle and the open of the current one).")

bool truegap = na
bool gapwithimb = na

if gaptype == "True GAP"
    truegap := true
    gapwithimb := false
else if gaptype == "GAP + Inefficiency"
    truegap := false
    gapwithimb := true
else if gaptype == "None"
    truegap := false
    gapwithimb := false

gapcolor = input.color(color.new(#4ec1f7, 90), "GAP Color", group="GAP")
gapsMaxBoxSet = input.int(defval=7, title='Maximum Box Displayed', minval=1, maxval=100, group="GAP", tooltip='Minimum = 1, Maximum = 100')
cegap= input.bool(true,title="GAP Consequent Encroachment (C.E.)", group="GAP")
cegaplinetype = input.string(line.style_dashed, "C.E. Line Style", options = [line.style_dashed,line.style_dotted,line.style_solid], group="GAP")
gapmitigationtype = input.string(defval="Engulf", title="GAP Mitigation Type", options = ["Engulf","Rebalance"], group="GAP", tooltip = "Body close or open above/below a GAP is required for Engulf Mitigation Type. Rebalance : Price ranges where buyside or sellside delivery is offered are erased as the candle forms.")
extendgapbox = input.bool(true, title="Extend Unmitigated GAP Boxes", group="GAP")

//FVG
bool plotFVG = na
bool liquidityvoidmode = na

fvgtype = input.string("Normal", "FVG Type",options = ["None","Normal","Liquidity Void"],inline="18", group="FVG", tooltip = "Consecutively formed FVGs are collectively referred to as a Liquidity Void.")
showifvg = input.bool(false,"IMPLIED FVG", inline="19", group="FVG")
Inversefvgmode = input.bool(false, "INVERSE FVG", group = "FVG",inline="19", tooltip = "FVG Mitigation type should be set to 'ENGULF' for Inverse FVG to work")

if fvgtype == "Normal"
    plotFVG := true
    liquidityvoidmode := false
else if fvgtype == "Liquidity Void"
    plotFVG := false
    liquidityvoidmode := true
else if fvgtype == "None"
    plotFVG := false
    liquidityvoidmode := false

fvgBullColor = input.color(defval=color.new(color.green, 90), title='Bullish FVG',inline="1", group="FVG")
fvgBearColor = input.color(defval=color.new(color.red, 90), title='Bearish FVG',inline="1", group="FVG")

ifvgBullColor = input.color(defval=color.new(color.green, 90), title='Bullish IMP.FVG',inline="3", group="FVG")
ifvgBearColor = input.color(defval=color.new(color.red, 90), title='Bearish IMP.FVG',inline="3", group="FVG")

buinvfvgcolor = input.color(defval=color.new(#fbc02d, 94), title='Bullish INV.FVG',inline="4", group="FVG")
beinvfvgcolor = input.color(defval=color.new(#fbc02d, 94), title='Bearish INV.FVG',inline="4", group="FVG")

fvgMaxBoxSet = input.int(defval=15, title='FVG/IMP.FVG Box Limit', minval=1, maxval=100, group="FVG", tooltip='Minimum = 1, Maximum = 100')
invfvgMaxBoxSet = input.int(defval=4, title='Inverse FVG Box Limit', minval=1, maxval=100, group='FVG', tooltip='Minimum = 1, Maximum = 100')
fvgmitigationtype = input.string(defval="Engulf", title="FVG/LV Mitigation", options = ["Engulf","Mitigate","Rebalance"], group="FVG", tooltip = "ENGULF: Body close below Bullish FVG and above Bearish FVG required for Engulf Mitigation Type")
ifvgmitigationtype = input.string(defval="Engulf", title="IMPLIED FVG Mitigation", options = ["Engulf","Mitigate"], group="FVG", tooltip = "ENGULF: Body close below Bullish FVG and above Bearish FVG required for Engulf Mitigation Type")
cefvg = input.bool(true,title="FVG Consequent Encroachment (C.E.)", group="FVG")
celinetype = input.string(line.style_dashed, "C.E. Line Style", options = [line.style_dashed,line.style_dotted,line.style_solid], group="FVG")
cetransparency = input.int(50,title="C.E. Transparency", group="FVG")
extendfvgbox = input.bool(true, title="Extend All Unmitigated Boxes", group="FVG")


//Box Styling
BoxBorder = input.string(defval=line.style_solid, title='Box Border Style', options=[line.style_dashed, line.style_dotted, line.style_solid], group="Box Style", tooltip='To disable border, set Border Width below to 0')
BorderTransparency = input.int(defval=85, title='Border Box Transparency', minval=0, maxval=100, group="Box Style")
Highlightboxtransparancy = input.int(80, "Highlight Box BG", group="Box Style", tooltip = "Highlight box background color when price is inside the box")
Highlightboxbordertransparancy = input.int(67, "Highlight Box Border", group="Box Style", tooltip = "Highlight box Border color when price is inside the box")
plotBoxLabel = input.bool(defval=true, title='Plot Label', group="Box Style")
BoxLabelSize = input.string(defval=size.tiny, title="Label Size", options=[size.huge, size.large, size.small, size.tiny, size.auto, size.normal], group="Box Style")
BoxLabelColor = input.color(defval=color.rgb(161, 163, 171), title='Label Color', group="Box Style")
labelhalign = input.string(text.align_center, "Label Horizontal Align", options = [text.align_center, text.align_right, text.align_left])
labelvalign = input.string(text.align_center, "Label Vertical Align", options = [text.align_top, text.align_center, text.align_bottom])


filterMitBOX = input.bool(defval=true, title='Mitigated Box Color', group="Mitigated Box Style")
mitBOXColor = input.color(defval=color.new(color.gray, 92), title='Mitigated Box Color', group="Mitigated Box Style", tooltip='Set Transparency to 0 to make mitigated Boxes disappear')
MitBoxLabelColor = input.color(defval=color.rgb(161, 163, 171, 88), title='Mitigated Box Label Color', group="Mitigated Box Style")

var bool buimpfvgtouch = false
var bool beimpfvgtouch = false
var bool bufvgtouch = false
var bool befvgtouch = false
var bool buvitouch = false
var bool bevitouch = false
var bool bugaptouch = false
var bool begaptouch = false
var bool buinvfvgtouch = false
var bool beinvfvgtouch = false

bool isbuinversefvg = false
bool isbeinversefvg = false

var int _fvg = 2
var int _vi = 3


var box[] _bearBoxesFVG = array.new_box()
var box[] _bullBoxesFVG = array.new_box()
var box[] _gapsboxesbu = array.new_box()
var box[] _gapsboxesbe = array.new_box()
var box[] _bullishvi = array.new_box()
var box[] _bearishvi = array.new_box()
var box[] _bullishifvg = array.new_box()
var box[] _bearishifvg = array.new_box()
var line[] _bufvgce = array.new_line()
var line[] _befvgce = array.new_line()
var box[] bearInvFVG = array.new_box()
var box[] bullInvFVG = array.new_box()
var line[] buinvfvgce = array.new_line()
var line[] beinvfvgce = array.new_line()
var line[] bugapce = array.new_line()
var line[] begapce = array.new_line()


//------function to extend all VIs------------
_controlBox2(_boxes, _high, _low) =>
    if array.size(_boxes) > 0
        for i = array.size(_boxes) - 1 to 0 by 1
            _box = array.get(_boxes, i)
            _boxLow = box.get_bottom(_box)
            _boxHigh = box.get_top(_box)
            _boxRight = box.get_right(_box)
            if extendallvis
                box.set_right(_box, bar_index + 1)
//------------         


//Volume Imbalance
isBuVItype1(index) =>
    (open[index] > close[index+1] and low[index] <= high[index+1] and close[index] > open[index] and close[index+1] > open[index+1])

isBeVItype1(index) =>
    (open[index] < close[index+1] and high[index] >= low[index+1] and close[index] < open[index] and close[index+1] < open[index+1])

isBuVItype2(index) =>
    (open[index] < close[index+1] and close[index] > open[index] and close[index+1] > open[index+1] and high[index] >= low[index+1]) //close[index] > close[index+1]

isBeVItype2(index) =>
    (open[index] > close[index+1]  and close[index] < open[index] and close[index+1] < open[index+1] and low[index] <= high[index+1]) //close[index] < close[index+1]

isBuVItype3(index) =>
    ((open[index] > close[index+1] or open[index] < close[index+1]) and close[index] > open[index] and close[index+1] < open[index+1] and high[index] >= low[index+1] and low[index] <= high[index+1])

isBeVItype3(index) =>
    ((open[index] < close[index+1] or open[index] >  close[index+1]) and close[index] < open[index] and close[index+1] > open[index+1] and low[index] <= high[index+1] and high[index] >= low[index+1])




//Bullish Volume Imbalance
if isBuVItype1(0) and (classicvi or advancedvi)
    box _bullboxVI = na
    _bullboxVI := box.new(left=bar_index-1, top=open, right=bar_index+1, bottom=close[1], bgcolor=bullimbalance,border_style=BoxBorder, border_color=color.new(bullimbalance,BorderTransparency), text=plotBoxLabel?"VI+":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bullishvi) > viMaxBoxSet
        box.delete(array.shift(_bullishvi))
    array.push(_bullishvi, _bullboxVI)
    

//Bearish Volume Imbalance
if isBeVItype1(0) and (classicvi or advancedvi)
    box _bearboxVI = na
    _bearboxVI := box.new(left=bar_index-1, top=close[1], right=bar_index+1, bottom=open, bgcolor=bearimbalance,border_style=BoxBorder, border_color=color.new(bearimbalance,BorderTransparency),text=plotBoxLabel?"VI-":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bearishvi) > viMaxBoxSet
        box.delete(array.shift(_bearishvi))
    array.push(_bearishvi, _bearboxVI)



//Bullish Volume Imbalance Type 2
if isBuVItype2(0) and advancedvi
    box _bullboxVI = na
    _bullboxVI := box.new(left=bar_index-1, top=close[1], right=bar_index+1, bottom=open, bgcolor=bullimbalance,border_style=BoxBorder, border_color=color.new(bullimbalance,BorderTransparency), text=plotBoxLabel?"VI+":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bullishvi) > viMaxBoxSet
        box.delete(array.shift(_bullishvi))
    array.push(_bullishvi, _bullboxVI)



//Bearish Volume Imbalance Type 2
if isBeVItype2(0) and advancedvi
    box _bearboxVI = na
    _bearboxVI := box.new(left=bar_index-1, top=open, right=bar_index+1, bottom=close[1], bgcolor=bearimbalance,border_style=BoxBorder, border_color=color.new(bearimbalance,BorderTransparency),text=plotBoxLabel?"VI-":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bearishvi) > viMaxBoxSet
        box.delete(array.shift(_bearishvi))
    array.push(_bearishvi, _bearboxVI)


//Bullish Volume Imbalance Type 3
if isBuVItype3(0) and advancedvi
    box _bullboxVI = na
    _bullboxVI := box.new(left=bar_index-1, top=math.max(open,close[1]), right=bar_index+1, bottom=math.min(open,close[1]), bgcolor=bullimbalance,border_style=BoxBorder, border_color=color.new(bullimbalance,BorderTransparency), text=plotBoxLabel?"VI+":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bullishvi) > viMaxBoxSet
        box.delete(array.shift(_bullishvi))
    array.push(_bullishvi, _bullboxVI)


//Bearish Volume Imbalance Type 3
if isBeVItype3(0) and advancedvi
    box _bearboxVI = na
    _bearboxVI := box.new(left=bar_index-1, top=math.min(open,close[1]), right=bar_index+1, bottom=math.max(open,close[1]), bgcolor=bearimbalance,border_style=BoxBorder, border_color=color.new(bearimbalance,BorderTransparency),text=plotBoxLabel?"VI-":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bearishvi) > viMaxBoxSet
        box.delete(array.shift(_bearishvi))
    array.push(_bearishvi, _bearboxVI)



_controlBox2(_bullishvi,high,low)
_controlBox2(_bearishvi,high,low)






//-------------GAP------------------
if ((high[1] < low) or ( low[1] > high)) and truegap
    box _gapsbu = na
    box _gapsbe = na
    line bugapceline = na
    line begapceline = na
    if high[1] < low
        _gapsbu := box.new(left=bar_index-1, top=low, right=bar_index+1, bottom=high[1], bgcolor=gapcolor, border_color=color.new(gapcolor,BorderTransparency), text=plotBoxLabel?"GAP+":na,border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        bugapceline := cegap?line.new(bar_index-1,math.avg(low,high[1]),bar_index,math.avg(low,high[1]), style = celinetype, color=color.new(gapcolor,cetransparency)):na
    else if low[1] > high
        _gapsbe := box.new(left=bar_index-1, top=low[1], right=bar_index+1, bottom=high, bgcolor=gapcolor, border_color=color.new(gapcolor,BorderTransparency), text=plotBoxLabel?"GAP-":na,border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        begapceline := cegap?line.new(bar_index-1,math.avg(low[1], high),bar_index,math.avg(low[1],high), style = celinetype, color=color.new(gapcolor,cetransparency)):na
    if array.size(_gapsboxesbu) > gapsMaxBoxSet and array.size(bugapce) > gapsMaxBoxSet
        box.delete(array.shift(_gapsboxesbu))
        line.delete(array.shift(bugapce))
    array.push(_gapsboxesbu, _gapsbu)
    array.push(bugapce, bugapceline)
    if array.size(_gapsboxesbe) > gapsMaxBoxSet and array.size(begapce) > gapsMaxBoxSet
        box.delete(array.shift(_gapsboxesbe))
        line.delete(array.shift(begapce))
    array.push(_gapsboxesbe, _gapsbe)
    array.push(begapce, begapceline)
else if ((high[1] < low) or ( low[1] > high)) and gapwithimb
    box _gapsbu = na
    box _gapsbe = na
    line bugapceline = na
    line begapceline = na
    if high[1] < low
        _gapsbu := box.new(left=bar_index-1, top=open, right=bar_index+1, bottom=close[1], bgcolor=gapcolor, border_color=color.new(gapcolor,BorderTransparency), text=plotBoxLabel?"GAP+":na,border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        bugapceline := cegap?line.new(bar_index-1,math.avg(open,close[1]),bar_index,math.avg(open,close[1]), style = celinetype, color=color.new(gapcolor,cetransparency)):na
    else if low[1] > high
        _gapsbe := box.new(left=bar_index-1, top=close[1], right=bar_index+1, bottom=open, bgcolor=gapcolor, border_color=color.new(gapcolor,BorderTransparency), text=plotBoxLabel?"GAP-":na,border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        begapceline := cegap?line.new(bar_index-1,math.avg(close[1],open),bar_index,math.avg(close[1],open), style = celinetype, color=color.new(gapcolor,cetransparency)):na
    if array.size(_gapsboxesbu) > gapsMaxBoxSet and array.size(bugapce) > gapsMaxBoxSet
        box.delete(array.shift(_gapsboxesbu))
        line.delete(array.shift(bugapce))
    array.push(_gapsboxesbu, _gapsbu)
    array.push(bugapce, bugapceline)
    if array.size(_gapsboxesbe) > gapsMaxBoxSet and array.size(begapce) > gapsMaxBoxSet
        box.delete(array.shift(_gapsboxesbe))
        line.delete(array.shift(begapce))
    array.push(_gapsboxesbe, _gapsbe)
    array.push(begapce, begapceline)
//-------------------------------------------



//---------------fvg-------------------
isFvgUp(index) =>
    (low[index] > high[index + 2] and low[index + 1] <= high[index + 2] and high[index + 1] >= low[index])

isFvgDown(index) =>
    (high[index] < low[index + 2] and high[index] >= low[index + 1] and high[index +1] >= low[index +2])
//------------------------------------


//----------FVG----------------
//Bullish FVG Box Plotting
if isFvgUp(0) and not(liquidityvoidmode)
    box _bullboxFVG = na
    line _buline = na
    if plotFVG or Inversefvgmode
        _bullboxFVG := box.new(left=bar_index-2, top=low[0], right=bar_index, bottom=high[2], bgcolor=fvgBullColor, border_color=color.new(fvgBullColor,BorderTransparency), border_style=BoxBorder, border_width=1,
         text=plotBoxLabel ? "FVG+" : na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        _buline := cefvg?line.new(bar_index-2,math.avg(low,high[2]),bar_index,math.avg(low,high[2]), style = celinetype, color=color.new(fvgBullColor,cetransparency)):na
    if array.size(_bullBoxesFVG) > fvgMaxBoxSet and array.size(_bufvgce) > fvgMaxBoxSet
        box.delete(array.shift(_bullBoxesFVG))
        line.delete(array.shift(_bufvgce))
    array.push(_bullBoxesFVG, _bullboxFVG)
    array.push(_bufvgce,_buline)


//Bearish FVG Box Plotting  
if isFvgDown(0) and not(liquidityvoidmode)
    box _bearboxFVG = na
    line _beline = na
    if plotFVG or Inversefvgmode
        _bearboxFVG := box.new(left=bar_index-2, top=low[2], right=bar_index, bottom=high[0], bgcolor=fvgBearColor, border_color=color.new(fvgBearColor,BorderTransparency), border_style=BoxBorder, border_width=1,
         text=plotBoxLabel ? "FVG-" : na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)    
        _beline := cefvg?line.new(bar_index-2,math.avg(low[2],high),bar_index,math.avg(low[2],high), style = celinetype, color=color.new(fvgBearColor,cetransparency)):na 
    if array.size(_bearBoxesFVG) > fvgMaxBoxSet and array.size(_befvgce) > fvgMaxBoxSet
        box.delete(array.shift(_bearBoxesFVG))
        line.delete(array.shift(_befvgce))
    array.push(_bearBoxesFVG, _bearboxFVG)
    array.push(_befvgce,_beline)
//------------------------------




//------------Liquidity Void Mode----------------

// Variables for generating alert
bool bulv = na
bool belv = na


for i=0 to 10 by 1
    if isFvgUp(i) and isFvgUp(i+1) and (liquidityvoidmode or Inversefvgmode) and array.size(_bullBoxesFVG) > 0 and not(plotFVG)
        bulv := true
        box _bullboxFVG = na
        line _buline = na
        x = array.size(_bullBoxesFVG) - 1
        _box = array.get(_bullBoxesFVG, x)
        tempboxlow = box.get_bottom(_box)
        tempboxleft = box.get_left(_box)
        box.delete(array.pop(_bullBoxesFVG))
        line.delete(array.pop(_bufvgce))
        _bullboxFVG := box.new(left=tempboxleft, top=low[0], right=bar_index, bottom=tempboxlow, bgcolor=fvgBullColor, border_color=color.new(fvgBullColor,BorderTransparency), border_width=1,text="LV+", border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        _buline := cefvg?line.new(tempboxleft,math.avg(low,tempboxlow),bar_index,math.avg(low,tempboxlow), style = celinetype, color=color.new(fvgBullColor,cetransparency)):na
        if array.size(_bullBoxesFVG) > fvgMaxBoxSet and array.size(_bufvgce) > fvgMaxBoxSet
            box.delete(array.shift(_bullBoxesFVG))
            line.delete(array.shift(_bufvgce))
        array.push(_bullBoxesFVG, _bullboxFVG)
        array.push(_bufvgce,_buline)
    if isFvgUp(i) and not(isFvgUp(i+1)) and (liquidityvoidmode or Inversefvgmode) and not(plotFVG)
        box _bullboxFVG = na
        line _buline = na
        _bullboxFVG := box.new(left=bar_index-2, top=low[0], right=bar_index, bottom=high[2], bgcolor=fvgBullColor, border_color=color.new(fvgBullColor,BorderTransparency), border_width=1,text="FVG+", border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        _buline := cefvg?line.new(bar_index-2,math.avg(low,high[2]),bar_index,math.avg(low,high[2]), style = celinetype, color=color.new(fvgBullColor,cetransparency)):na
        if array.size(_bullBoxesFVG) > fvgMaxBoxSet and array.size(_bufvgce) > fvgMaxBoxSet
            box.delete(array.shift(_bullBoxesFVG))
            line.delete(array.shift(_bufvgce))
        array.push(_bullBoxesFVG, _bullboxFVG)
        array.push(_bufvgce,_buline)
    break



//---Bearish Liquidity Void
for i=0 to 10 by 1
    if isFvgDown(i) and isFvgDown(i+1)  and (liquidityvoidmode or Inversefvgmode) and array.size(_bearBoxesFVG) > 0 and not(plotFVG)
        belv := true
        box _bearboxFVG = na
        line _beline = na
        x = array.size(_bearBoxesFVG) - 1
        _box = array.get(_bearBoxesFVG, x)
        tempboxhigh = box.get_top(_box)
        tempboxleft = box.get_left(_box)
        box.delete(array.pop(_bearBoxesFVG))
        line.delete(array.pop(_befvgce))
        _bearboxFVG := box.new(left=tempboxleft, top=tempboxhigh, right=bar_index, bottom=high[0], bgcolor=fvgBearColor, border_color=color.new(fvgBearColor,BorderTransparency), border_width=1,text= "LV-", border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        _beline := cefvg?line.new(tempboxleft,math.avg(tempboxhigh,high),bar_index,math.avg(tempboxhigh,high), style = celinetype, color=color.new(fvgBearColor,cetransparency)):na 
        if array.size(_bearBoxesFVG) > fvgMaxBoxSet and array.size(_befvgce) > fvgMaxBoxSet
            box.delete(array.shift(_bearBoxesFVG))
            line.delete(array.shift(_befvgce))
        array.push(_bearBoxesFVG, _bearboxFVG)
        array.push(_befvgce,_beline)
    if isFvgDown(i) and not(isFvgDown(i+1))  and (liquidityvoidmode or Inversefvgmode) and not(plotFVG)
        box _bearboxFVG = na
        line _beline = na
        _bearboxFVG := box.new(left=bar_index-2, top=low[2], right=bar_index, bottom=high[0], bgcolor=fvgBearColor, border_color=color.new(fvgBearColor,BorderTransparency), border_width=1,text= "FVG-", border_style=BoxBorder, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
        _beline := cefvg?line.new(bar_index-2,math.avg(low[2],high),bar_index,math.avg(low[2],high), style = celinetype, color=color.new(fvgBearColor,cetransparency)):na 
        if array.size(_bearBoxesFVG) > fvgMaxBoxSet and array.size(_befvgce) > fvgMaxBoxSet
            box.delete(array.shift(_bearBoxesFVG))
            line.delete(array.shift(_befvgce))
        array.push(_bearBoxesFVG, _bearboxFVG)
        array.push(_befvgce,_beline)
    break
//-----------------------------------






//-----BULLISH IMPLIED FAIR VALUE GAP---------------//
isBuIFvg(index) =>
    (high[index] > high[index+2] and low[index+2] < low[index] and low[index] <= high[index+2] and high[index+2] - math.max(open[index+2],close[index+2]) > (math.max(open[index+2],close[index+2]) - math.min(open[index+2],close[index+2]))/2 and math.min(open[index],close[index]) - low[index] > (math.max(open[index],close[index]) - math.min(open[index],close[index]))/2 and low[index] > low[index+1] and (high[index+2] + math.max(open[index+2],close[index+2]))/2 < (math.min(open[index],close[index]) + low[index])/2) and high[index] > high[index+1] and close[index+1] > open[index+1]

//-----BEARISH IMPLIED FAIR VALUE GAP---------------//
isBeIFvg(index) =>
    (low[index] < low[index+2] and high[index+2] > high[index] and high[index] >= low[index+2] and math.min(open[index+2],close[index+2]) - low[index+2] > (math.max(open[index+2],close[index+2]) - math.min(open[index+2],close[index+2]))/2 and high[index] - math.max(open[index],close[index]) > (math.max(open[index],close[index]) - math.min(open[index],close[index]))/2 and high[index] < high[index+1] and (math.min(open[index+2],close[index+2]) + low[index+2])/2 > (high[index] + math.max(open[index],close[index]))/2) and low[index] < low[index+1] and close[index+1] < open[index+1]


if isBuIFvg(0) and showifvg
    box _bullboxifvg = na
    _bullboxifvg := box.new(left=bar_index-2, top=(math.min(open,close) + low)/2, right=bar_index+1, bottom=(high[2] + math.max(open[2],close[2]))/2, bgcolor=ifvgBullColor,border_style=BoxBorder, border_color=color.new(ifvgBullColor,BorderTransparency),text=plotBoxLabel?"Imp.FVG+":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bullishifvg) > fvgMaxBoxSet
        box.delete(array.shift(_bullishifvg))
    array.push(_bullishifvg, _bullboxifvg)


if isBeIFvg(0) and showifvg
    box _bearboxifvg = na
    _bearboxifvg := box.new(left=bar_index-2, top=(math.min(open[2],close[2]) + low[2])/2, right=bar_index+1, bottom=(math.max(open,close) + high)/2, bgcolor=ifvgBearColor,border_style=BoxBorder, border_color=color.new(ifvgBearColor,BorderTransparency),text=plotBoxLabel?"Imp.FVG-":na, text_halign=labelhalign, text_valign=labelvalign, text_size=BoxLabelSize, text_color=BoxLabelColor)
    if array.size(_bearishifvg) > fvgMaxBoxSet
        box.delete(array.shift(_bearishifvg))
    array.push(_bearishifvg, _bearboxifvg)







//-------IMPLIED FVG EXTEND  -  MITIGATION TYPE = ENGULF -----------
// Implied fvg boxes extend (bullish)
if array.size(_bullishifvg) > 0 and extendfvgbox and barstate.isconfirmed and ifvgmitigationtype=="Engulf"
    for i = array.size(_bullishifvg) - 1 to 0 by 1
        _box = array.get(_bullishifvg, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and open >= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else if (close < _boxLow or open < _boxLow) and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
                

// Implied fvg boxes extend (bearish)
if array.size(_bearishifvg) > 0 and extendfvgbox and barstate.isconfirmed and ifvgmitigationtype=="Engulf"
    for i = array.size(_bearishifvg) - 1 to 0 by 1
        _box = array.get(_bearishifvg, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and open <= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else if (close > _boxHigh or open > _boxHigh) and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)



// Implied fvg box border animation (bullish)
if array.size(_bullishifvg) > 0 and extendfvgbox and ifvgmitigationtype=="Engulf"
    for i = array.size(_bullishifvg) - 1 to 0 by 1
        _box = array.get(_bullishifvg, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and bar_index == _boxRight and low < _boxHigh
            box.set_bgcolor(_box,color.new(ifvgBullColor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(ifvgBullColor,Highlightboxbordertransparancy))
            buimpfvgtouch := true

// Implied fvg box border animation (bearish)
if array.size(_bearishifvg) > 0 and extendfvgbox and ifvgmitigationtype=="Engulf"
    for i = array.size(_bearishifvg) - 1 to 0 by 1
        _box = array.get(_bearishifvg, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and bar_index == _boxRight and high > _boxLow
            box.set_bgcolor(_box,color.new(ifvgBearColor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(ifvgBearColor,Highlightboxbordertransparancy))
            beimpfvgtouch := true




//-------IMPLIED FVG EXTEND  -  MITIGATION TYPE = MITIGATE -----------
// Implied fvg boxes extend (bullish)
if array.size(_bullishifvg) > 0 and extendfvgbox and barstate.isconfirmed and ifvgmitigationtype=="Mitigate"
    for i = array.size(_bullishifvg) - 1 to 0 by 1
        _box = array.get(_bullishifvg, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if low >= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else if low < _boxHigh and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)



// Implied fvg boxes extend (bearish)
if array.size(_bearishifvg) > 0 and extendfvgbox and barstate.isconfirmed and ifvgmitigationtype=="Mitigate"
    for i = array.size(_bearishifvg) - 1 to 0 by 1
        _box = array.get(_bearishifvg, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if high <= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else if high > _boxLow and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
//---------------




//-------FVG EXTEND  -  MITIGATION TYPE = ENGULF -----------
//Bullish FVG Extend
if array.size(_bullBoxesFVG) > 0 and array.size(_bufvgce) > 0 and extendfvgbox and barstate.isconfirmed and fvgmitigationtype=="Engulf"
    for i = array.size(_bullBoxesFVG) - 1 to 0 by 1
        box beifvgbox = na
        line beifvgceline = na
        _line = array.get(_bufvgce, i)
        _box = array.get(_bullBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else if close < _boxLow and bar_index == _boxRight and Inversefvgmode
            beifvgbox := box.copy(_box)
            array.push(bearInvFVG, beifvgbox)
            beifvgceline := line.copy(_line)
            array.push(beinvfvgce, beifvgceline)
            box.delete(_box)
            line.delete(_line)
            isbeinversefvg := true
        else if close < _boxLow and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)


//Bearish FVG Extend
if array.size(_bearBoxesFVG) > 0 and array.size(_befvgce) > 0 and extendfvgbox and barstate.isconfirmed and fvgmitigationtype=="Engulf"
    for i = array.size(_bearBoxesFVG) - 1 to 0 by 1
        box buifvgbox = na
        line buifvgceline = na
        _line = array.get(_befvgce, i)
        _box = array.get(_bearBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else if close > _boxHigh and bar_index == _boxRight and Inversefvgmode
            buifvgbox := box.copy(_box)
            array.push(bullInvFVG, buifvgbox)
            buifvgceline := line.copy(_line)
            array.push(buinvfvgce, buifvgceline)
            box.delete(_box)
            line.delete(_line)
            isbuinversefvg := true
        else if close > _boxHigh and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)


//box border animation - FVG+
if array.size(_bullBoxesFVG) > 0 and extendfvgbox and fvgmitigationtype=="Engulf" and plotFVG
    for i = array.size(_bullBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bullBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and bar_index == _boxRight and low < _boxHigh
            box.set_bgcolor(_box,color.new(fvgBullColor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(fvgBullColor,Highlightboxbordertransparancy))
            bufvgtouch := true


//box border animation - FVG-
if array.size(_bearBoxesFVG) > 0 and extendfvgbox and fvgmitigationtype=="Engulf" and plotFVG
    for i = array.size(_bearBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bearBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and bar_index == _boxRight and high > _boxLow
            box.set_bgcolor(_box,color.new(fvgBearColor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(fvgBearColor,Highlightboxbordertransparancy))
            befvgtouch := true


//Bullish inverse FVG Extend
if array.size(bullInvFVG) > 0 and barstate.isconfirmed and fvgmitigationtype=="Engulf" and Inversefvgmode
    for i = array.size(bullInvFVG) - 1 to 0 by 1
        _box = array.get(bullInvFVG, i)
        _line = array.get(buinvfvgce, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        box.set_bgcolor(_box, buinvfvgcolor)
        if plotBoxLabel
            box.set_text(_box, "I.FVG+")
            box.set_text_halign(_box, labelhalign)
            box.set_text_valign(_box, labelvalign)
            box.set_text_size(_box, BoxLabelSize)
            box.set_text_color(_box, BoxLabelColor)
        box.set_border_color(_box, color.new(buinvfvgcolor,BorderTransparency))
        line.set_color(_line,color.new(buinvfvgcolor,cetransparency))
        line.set_style(_line, celinetype)
        if close >= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else if close < _boxLow //and bar_index == _boxRight
            box.delete(_box)
            line.delete(_line)
            //box.set_bgcolor(_box, mitBOXColor)
            //box.set_border_color(_box, mitBOXColor)
            //box.set_text_color(_box,MitBoxLabelColor)



//Bearish inverse FVG Extend
if array.size(bearInvFVG) > 0 and barstate.isconfirmed and fvgmitigationtype=="Engulf" and Inversefvgmode
    for i = array.size(bearInvFVG) - 1 to 0 by 1
        _box = array.get(bearInvFVG, i)
        _line = array.get(beinvfvgce, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        box.set_bgcolor(_box, beinvfvgcolor)
        if plotBoxLabel
            box.set_text(_box, "I.FVG-")
            box.set_text_halign(_box, labelhalign)
            box.set_text_valign(_box, labelvalign)
            box.set_text_size(_box, BoxLabelSize)
            box.set_text_color(_box, BoxLabelColor)
        box.set_border_color(_box, color.new(beinvfvgcolor,BorderTransparency))
        line.set_color(_line, color.new(beinvfvgcolor,cetransparency))
        line.set_style(_line, celinetype)
        if close <= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else if close > _boxHigh //and bar_index == _boxRight
            box.delete(_box)
            line.delete(_line)
            //box.set_bgcolor(_box, mitBOXColor)
            //box.set_border_color(_box, mitBOXColor)
            //box.set_text_color(_box,MitBoxLabelColor)





//box border animation - Inverse FVG+
if array.size(bullInvFVG) > 0 and extendfvgbox and fvgmitigationtype=="Engulf" and Inversefvgmode
    for i = array.size(bullInvFVG) - 1 to 0 by 1
        _box = array.get(bullInvFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and bar_index == _boxRight and low < _boxHigh
            box.set_bgcolor(_box,color.new(buinvfvgcolor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(buinvfvgcolor,Highlightboxbordertransparancy))
            buinvfvgtouch := true


//box border animation - Inverse FVG-
if array.size(bearInvFVG) > 0 and extendfvgbox and fvgmitigationtype=="Engulf" and Inversefvgmode
    for i = array.size(bearInvFVG) - 1 to 0 by 1
        _box = array.get(bearInvFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and bar_index == _boxRight and high > _boxLow
            box.set_bgcolor(_box,color.new(beinvfvgcolor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(beinvfvgcolor,Highlightboxbordertransparancy))
            beinvfvgtouch := true





//----------Inverse FVG only mode-------------------
if array.size(_bearBoxesFVG) > 0 and array.size(_befvgce) > 0 and not(plotFVG) and not(liquidityvoidmode) and Inversefvgmode
    for i = array.size(_bearBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bearBoxesFVG, i)
        _line = array.get(_befvgce, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        box.set_bgcolor(_box, color.new(fvgBearColor,100))
        box.set_text_color(_box, color.new(BoxLabelColor,100))
        box.set_border_color(_box,color.new(fvgBearColor,100))
        line.set_color(_line,color.new(fvgBearColor,100))


if array.size(_bullBoxesFVG) > 0 and array.size(_bufvgce) > 0 and not(plotFVG) and not(liquidityvoidmode) and Inversefvgmode
    for i = array.size(_bullBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bullBoxesFVG, i)
        _line = array.get(_bufvgce, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        box.set_bgcolor(_box, color.new(fvgBullColor,100))
        box.set_text_color(_box, color.new(BoxLabelColor,100))
        box.set_border_color(_box,color.new(fvgBullColor,100))
        line.set_color(_line,color.new(fvgBullColor,100))

//-------------------------------------------------





//-------FVG EXTEND  -  MITIGATION TYPE = MITIGATE -----------
//bullish FVG
if array.size(_bullBoxesFVG) > 0  and array.size(_bufvgce) > 0 and extendfvgbox and barstate.isconfirmed and fvgmitigationtype=="Mitigate"
    for i = array.size(_bullBoxesFVG) - 1 to 0 by 1
        _line = array.get(_bufvgce, i)
        _box = array.get(_bullBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if low >= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else 
            if low < _boxHigh and bar_index == _boxRight and filterMitBOX
                box.set_bgcolor(_box, mitBOXColor)
                box.set_border_color(_box, mitBOXColor)
                box.set_text_color(_box,MitBoxLabelColor)
                line.set_color(_line,mitBOXColor)



//Bearish FVG
if array.size(_bearBoxesFVG) > 0 and array.size(_befvgce) > 0 and extendfvgbox and barstate.isconfirmed and fvgmitigationtype=="Mitigate"
    for i = array.size(_bearBoxesFVG) - 1 to 0 by 1
        _line = array.get(_befvgce, i)
        _box = array.get(_bearBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if high <= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else 
            if high > _boxLow and bar_index == _boxRight and filterMitBOX
                box.set_bgcolor(_box, mitBOXColor)
                box.set_border_color(_box, mitBOXColor)
                box.set_text_color(_box,MitBoxLabelColor)
                line.set_color(_line,mitBOXColor)





//-------FVG EXTEND  -  MITIGATION TYPE = Rebalance -----------
//bullish FVG - Extend - Rebalance
if array.size(_bullBoxesFVG) > 0 and array.size(_bufvgce) > 0 and extendfvgbox and fvgmitigationtype=="Rebalance" and barstate.isconfirmed
    for i = array.size(_bullBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bullBoxesFVG, i)
        _line = array.get(_bufvgce, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if low >= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else if low < _boxHigh and low > _boxLow and bar_index == _boxRight
            box.set_top(_box,low)
            box.set_right(_box, bar_index + 1)
            line.set_y1(_line,(low + _boxLow)/2)
            line.set_y2(_line,(low + _boxLow)/2)
            line.set_x2(_line, bar_index + 1)
        else if low <= _boxLow and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)




//Bearish FVG - Extend - Rebalance
if array.size(_bearBoxesFVG) > 0 and array.size(_befvgce) > 0 and extendfvgbox and fvgmitigationtype=="Rebalance" and barstate.isconfirmed
    for i = array.size(_bearBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bearBoxesFVG, i)
        _line = array.get(_befvgce, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if high <= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line, bar_index + 1)
        else if high > _boxLow and high < _boxHigh and bar_index == _boxRight
            box.set_bottom(_box,high)
            box.set_right(_box, bar_index + 1)
            line.set_y1(_line,(high+_boxHigh)/2)
            line.set_y2(_line,(high+_boxHigh)/2)
            line.set_x2(_line, bar_index + 1)
        else if high >= _boxLow and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)



//box border animation - FVG+
if array.size(_bullBoxesFVG) > 0 and extendfvgbox and fvgmitigationtype=="Rebalance"
    for i = array.size(_bullBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bullBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if low < _boxHigh and bar_index == _boxRight and low > _boxLow
            box.set_bgcolor(_box,color.new(fvgBullColor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(fvgBullColor,Highlightboxbordertransparancy))
            bufvgtouch := true


//box border animation - FVG-
if array.size(_bearBoxesFVG) > 0 and extendfvgbox and fvgmitigationtype=="Rebalance"
    for i = array.size(_bearBoxesFVG) - 1 to 0 by 1
        _box = array.get(_bearBoxesFVG, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if high > _boxLow and bar_index == _boxRight and high < _boxHigh
            box.set_bgcolor(_box,color.new(fvgBearColor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(fvgBearColor,Highlightboxbordertransparancy))
            befvgtouch := true




if array.size(bullInvFVG) > invfvgMaxBoxSet
    box.delete(array.shift(bullInvFVG))
if array.size(bearInvFVG) > invfvgMaxBoxSet
    box.delete(array.shift(bearInvFVG))
if array.size(buinvfvgce) > invfvgMaxBoxSet
    line.delete(array.shift(buinvfvgce))
if array.size(beinvfvgce) > invfvgMaxBoxSet
    line.delete(array.shift(beinvfvgce))


//-------VI EXTEND  -  MITIGATION TYPE = ENGULF -----------
//bullish VI extend
if array.size(_bullishvi) > 0 and extendvibox and barstate.isconfirmed and vimitigationtype=="Engulf"
    for i = array.size(_bullishvi) - 1 to 0 by 1
        _box = array.get(_bullishvi, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else 
            if close < _boxLow and bar_index == _boxRight and filterMitBOX
                box.set_bgcolor(_box, mitBOXColor)
                box.set_border_color(_box, mitBOXColor)
                box.set_text_color(_box,MitBoxLabelColor)

//Bearish VI Extend
if array.size(_bearishvi) > 0 and extendvibox and barstate.isconfirmed and vimitigationtype=="Engulf"
    for i = array.size(_bearishvi) - 1 to 0 by 1
        _box = array.get(_bearishvi, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else 
            if close > _boxHigh and bar_index == _boxRight and filterMitBOX
                box.set_bgcolor(_box, mitBOXColor)
                box.set_border_color(_box, mitBOXColor)
                box.set_text_color(_box,MitBoxLabelColor)


//Vi Box Border Animation (bullish)
if array.size(_bullishvi) > 0 and extendvibox and vimitigationtype=="Engulf"
    for i = array.size(_bullishvi) - 1 to 0 by 1
        _box = array.get(_bullishvi, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and bar_index == _boxRight and low < _boxHigh
            box.set_bgcolor(_box,color.new(bullimbalance,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(bullimbalance,Highlightboxbordertransparancy))
            buvitouch := true

//Vi Box Border Animation (bearish)
if array.size(_bearishvi) > 0 and extendvibox and vimitigationtype=="Engulf"
    for i = array.size(_bearishvi) - 1 to 0 by 1
        _box = array.get(_bearishvi, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and bar_index == _boxRight and high > _boxLow
            box.set_bgcolor(_box,color.new(bearimbalance,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(bearimbalance,Highlightboxbordertransparancy))
            bevitouch := true




//-------VI EXTEND  -  MITIGATION TYPE = MITIGATE -----------
//bullish VI extend MITIGATION TYPE - MITIGATE
if array.size(_bullishvi) > 0 and extendvibox and barstate.isconfirmed and vimitigationtype=="Mitigate"
    for i = array.size(_bullishvi) - 1 to 0 by 1
        _box = array.get(_bullishvi, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if low > _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else 
            if low <= _boxHigh and bar_index == _boxRight and filterMitBOX
                box.set_bgcolor(_box, mitBOXColor)
                box.set_border_color(_box, mitBOXColor)
                box.set_text_color(_box,MitBoxLabelColor)
                buvitouch := true

//Bearish VI Extend MITIGATION TYPE MITIGATE
if array.size(_bearishvi) > 0 and extendvibox and barstate.isconfirmed and vimitigationtype=="Mitigate"
    for i = array.size(_bearishvi) - 1 to 0 by 1
        _box = array.get(_bearishvi, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if high < _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
        else 
            if high >= _boxHigh and bar_index == _boxRight and filterMitBOX
                box.set_bgcolor(_box, mitBOXColor)
                box.set_border_color(_box, mitBOXColor)
                box.set_text_color(_box,MitBoxLabelColor)
                bevitouch := true





//-------GAP EXTEND  -  MITIGATION TYPE = ENGULF -----------
//gap boxes extend (bullish)
if array.size(_gapsboxesbu) > 0 and array.size(bugapce) > 0 and extendgapbox and barstate.isconfirmed and gapmitigationtype=="Engulf"
    for i = array.size(_gapsboxesbu) - 1 to 0 by 1
        _box = array.get(_gapsboxesbu, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        _line = array.get(bugapce,i)
        if close >= _boxLow and open >= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line,bar_index + 1)
        else if (close < _boxLow or open < _boxLow) and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)


//gap boxes extend (bearish)
if array.size(_gapsboxesbe) > 0 and array.size(begapce) > 0  and extendgapbox and barstate.isconfirmed and gapmitigationtype=="Engulf"
    for i = array.size(_gapsboxesbe) - 1 to 0 by 1
        _box = array.get(_gapsboxesbe, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        _line = array.get(begapce,i)
        if close <= _boxHigh and open <= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line,bar_index + 1)
        else if (close > _boxHigh or open > _boxHigh) and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)


// gap box border animation(bullish)
if array.size(_gapsboxesbu) > 0 and extendgapbox and gapmitigationtype=="Engulf"
    for i = array.size(_gapsboxesbu) - 1 to 0 by 1
        _box = array.get(_gapsboxesbu, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close >= _boxLow and open >= _boxLow and bar_index == _boxRight and low < _boxHigh
            box.set_bgcolor(_box,color.new(gapcolor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(gapcolor,Highlightboxbordertransparancy))
            bugaptouch := true


// gap box border animation (bearish)
if array.size(_gapsboxesbe) > 0 and extendgapbox  and gapmitigationtype=="Engulf"
    for i = array.size(_gapsboxesbe) - 1 to 0 by 1
        _box = array.get(_gapsboxesbe, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if close <= _boxHigh and open <= _boxHigh and bar_index == _boxRight and high > _boxLow
            box.set_bgcolor(_box,color.new(gapcolor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(gapcolor,Highlightboxbordertransparancy))
            begaptouch := true
//------------------------




//-------GAP EXTEND  -  MITIGATION TYPE = REBALANCE -----------
//gap boxes extend (bullish)
if array.size(_gapsboxesbu) > 0 and array.size(bugapce) > 0 and extendgapbox and barstate.isconfirmed and gapmitigationtype== "Rebalance"
    for i = array.size(_gapsboxesbu) - 1 to 0 by 1
        _box = array.get(_gapsboxesbu, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        _line = array.get(bugapce,i)
        if low >= _boxHigh and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line,bar_index + 1)
        else if low < _boxHigh and low > _boxLow and bar_index == _boxRight
            box.set_top(_box, low)
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line,bar_index + 1)
            line.set_y1(_line, (low+_boxLow)/2)
            line.set_y2(_line, (low+_boxLow)/2)
        else if low <= _boxLow and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)

//gap boxes extend (bearish)
if array.size(_gapsboxesbe) > 0 and array.size(begapce) > 0  and extendgapbox and barstate.isconfirmed and gapmitigationtype== "Rebalance"
    for i = array.size(_gapsboxesbe) - 1 to 0 by 1
        _box = array.get(_gapsboxesbe, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        _line = array.get(begapce,i)
        if high <= _boxLow and bar_index == _boxRight
            box.set_right(_box, bar_index + 1)
            line.set_x2(_line,bar_index + 1)
        else if high > _boxLow and high < _boxHigh and bar_index == _boxRight
            box.set_bottom(_box, high)
            box.set_right(_box, bar_index + 1)
            line.set_y1(_line, (high+_boxHigh)/2)
            line.set_y2(_line, (high+_boxHigh)/2)
            line.set_x2(_line,bar_index + 1)
        else if high >= _boxHigh and bar_index == _boxRight and filterMitBOX
            box.set_bgcolor(_box, mitBOXColor)
            box.set_border_color(_box, mitBOXColor)
            box.set_text_color(_box,MitBoxLabelColor)
            line.set_color(_line,mitBOXColor)


// gap box border animation(bullish)
if array.size(_gapsboxesbu) > 0 and extendgapbox and gapmitigationtype=="Rebalance"
    for i = array.size(_gapsboxesbu) - 1 to 0 by 1
        _box = array.get(_gapsboxesbu, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if low > _boxLow and bar_index == _boxRight and low < _boxHigh
            box.set_bgcolor(_box,color.new(gapcolor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(gapcolor,Highlightboxbordertransparancy))
            bugaptouch := true


// gap box border animation (bearish)
if array.size(_gapsboxesbe) > 0 and extendgapbox  and gapmitigationtype=="Rebalance"
    for i = array.size(_gapsboxesbe) - 1 to 0 by 1
        _box = array.get(_gapsboxesbe, i)
        _boxLow = box.get_bottom(_box)
        _boxHigh = box.get_top(_box)
        _boxRight = box.get_right(_box)
        if high < _boxHigh and bar_index == _boxRight and high > _boxLow
            box.set_bgcolor(_box,color.new(gapcolor,Highlightboxtransparancy))
            box.set_border_color(_box,color.new(gapcolor,Highlightboxbordertransparancy))
            begaptouch := true
//--------------------




//---------ALERTS----------------
alertcondition(barstate.isconfirmed and isFvgUp(0) , title="FVG+", message="FVG+ : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and isFvgDown(0) , title="FVG-", message="FVG- : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and isBuIFvg(0) , title="Implied.FVG+", message="I.FVG+ : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and isBeIFvg(0) , title="Implied.FVG-", message="I.FVG- : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and (((isBuVItype1(0) or isBuVItype2(0) or isBuVItype3(0)) and advancedvi) or (isBuVItype1(0) and classicvi)),title="VI+",message="VI+ : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and (((isBeVItype1(0) or isBeVItype2(0) or isBeVItype3(0)) and advancedvi) or (isBuVItype1(0) and classicvi)),title="VI-",message="VI- : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and high[1] < low,title="GAP+", message="GAP+ : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and low[1] > high,title="GAP-", message="GAP- : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and isbuinversefvg, title="Inverse.FVG+", message="Inverse.FVG+ : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and isbeinversefvg, title="Inverse.FVG-", message="Inverse.FVG- : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and bulv, title="LV+", message="Bullish Liquidity Void : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(barstate.isconfirmed and belv, title="LV-", message="Bearish Liquidity Void : {{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")

alertcondition(bugaptouch, title="GAP(+) Mitigation", message="GAP(+) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(begaptouch, title="GAP(-) Mitigation", message="GAP(-) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(buimpfvgtouch, title="Implied.FVG(+) Mitigation", message="Implied.FVG(+) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(beimpfvgtouch, title="Implied.FVG(-) Mitigation", message="Implied.FVG(-) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(bufvgtouch, title="FVG(+) Mitigation", message="FVG(+) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(befvgtouch, title="FVG(-) Mitigation", message="FVG(-) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(buvitouch, title="VI(+) Mitigation", message="VI(+) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(bevitouch, title="VI(-) Mitigation", message="VI(-) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(buinvfvgtouch, title="Inverse.FVG(+) Mitigation", message="Inverse.FVG(+) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
alertcondition(beinvfvgtouch, title="Inverse.FVG(-) Mitigation", message="Inverse.FVG(-) Mitigated :{{exchange}}:{{ticker}} TIMEFRAME:{{interval}}")
//end of script







