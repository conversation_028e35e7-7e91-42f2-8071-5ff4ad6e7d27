//@version=5
indicator("Silver Bullet - Combined FVG & Signals", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// === Inputs ===
selectedTF         = input.string("1", "Selected FVG Timeframe", options=["1", "3", "5", "15"], group="Session")
showSB             = input.bool(true,     "Show 10–11 AM Session",        group="Session")
showHistoricalFVGs = input.bool(true,     "Show Historical FVGs",         group="Session")
extendLine         = input.bool(true,     "Extend Vertical Separators",   group="Session")
extendHLine        = input.bool(true,     "Extend Historical Lines",      group="Session & FVG")
boxShift           = input.int(1,         "Shift FVG Box Left", minval=0, group="Session")
historyDays        = input.int(5,         "Look-back (Days)", minval=1, maxval=500, group="Session & FVG")
labelSizeOpt       = input.string("Tiny", "Session Label Size", options=["Auto","Tiny","Small","Normal","Large","Huge"], group="Session")
labelColor         = input.color(color.white, "Session Label Color",       group="Session")
labelXOffset       = input.int(20,        "Session Label X Offset", minval=0,   group="Session")
labelYOffset       = input.float(0.0,     "Session Label Y Offset",           group="Session")
flipSource         = input.string("Wick", "Flip Source", options=["Wick","Close"], group="FVG Style")

// === Trading Signal Inputs ===
tradeLabelSize     = input.string("Normal", "Trade Label Size", options=["Tiny","Small","Normal","Large","Huge"], group="Trading Signals")
enableSBSignals    = input.bool(true,     "Enable SB Signals (Original)",     group="Trading Signals")
enableSMISignals   = input.bool(true,     "Enable SMI Signals",               group="Trading Signals")
smiLookback        = input.int(100,       "SMI Lookback Candles", minval=1, maxval=200, group="Trading Signals")
debugLabels        = input.bool(false,    "Show Debug Labels",                 group="Debug")
debugNumbering     = input.bool(false,    "Show Numbering Debug",              group="Debug")
showEngulfingDebug = input.bool(false,    "Show Engulfing Debug",              group="Debug")
showSMIDebug       = input.bool(false,    "Show SMI Debug",                    group="Debug")
showDetailedDebug  = input.bool(false,    "Show Detailed Debug",               group="Debug")

// === FVG Styles ===
currentFVGCol = input.color(#e8ed5e89,    "Current FVG Color",   group="Colors")
histFVGCol    = input.color(color.rgb(94, 134, 237, 80), "Historical FVG Color", group="Colors")
midlineColor  = input.color(#787b86,      "Midline Color", group="Colors")
invisBorder   = color.new(color.black, 100)

// === Label size mapping ===
labelSizeMap(x) =>
    switch x
        'Auto'   => size.auto
        'Tiny'   => size.tiny
        'Small'  => size.small
        'Normal' => size.normal
        'Large'  => size.large
        'Huge'   => size.huge

// === Session logic ===
timeSess(tf, s) => time(tf, s, "America/New_York")
SB_AM   = timeSess(timeframe.period, "1000-1100")
strAM   = SB_AM and not SB_AM[1]
n       = bar_index
minT    = syminfo.mintick
inAM    = timeSess(selectedTF, "1000-1100")
after10 = inAM and not strAM

// === Pull wick/close from selectedTF ===
[lf_low, lf_high, lf_close] = request.security(syminfo.tickerid, selectedTF, [low, high, close])

// === Session separators & storage ===
var line    vLine        = na
var line[]  sessHLines   = array.new_line()
var int[]   sessHTimes   = array.new_int()
var float[] sessHPrices  = array.new_float()
var label[] sessHLabels  = array.new_label()

// === Current session-line vars ===
var line   hLine   = na
var label  hLabel  = na
var float  hPrice  = na
var int    hStart  = na

// ── On new 10:00 AM session ──
if strAM and showSB
    vLine := line.new(n, close, n, close + minT, color=color.white, extend=extendLine ? extend.both : extend.none)
    if not na(hLine)
        line.set_extend(hLine, extend.none)
        line.set_x2(hLine, n)
        line.set_y2(hLine, hPrice)
        array.push(sessHLines, hLine)
        array.push(sessHTimes, time)
        array.push(sessHPrices, hPrice)
        array.push(sessHLabels, hLabel)
    hStart := n
    hPrice := open
    hLine  := line.new(hStart, hPrice, n, hPrice, color=color.white, style=line.style_dotted, extend=extend.none)
    d = dayofmonth < 10 ? "0"+str.tostring(dayofmonth) : str.tostring(dayofmonth)
    m = month      < 10 ? "0"+str.tostring(month)      : str.tostring(month)
    y = (year%100) < 10  ? "0"+str.tostring(year%100)   : str.tostring(year%100)
    dateStr = d + "/" + m + "/" + y
    hLabel := label.new(x=n+labelXOffset, y=hPrice+labelYOffset, text=dateStr, xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_right, size=labelSizeMap(labelSizeOpt), textcolor=labelColor, color=color.new(color.white,100))

// ── Update current session horizontal each bar ──
if not na(hLine)
    line.set_extend(hLine, extend.none)
    line.set_x2(hLine, n)
    line.set_y2(hLine, hPrice)
    label.set_x(hLabel, n+labelXOffset)
    label.set_y(hLabel, hPrice+labelYOffset)

// === FVG detection on 1-min ===
fvgDetect() =>
    bull = low > high[2] and close[1] > high[2]
    bear = high < low[2]  and close[1] < low[2]
    top  = bull ? low     : bear ? low[2]     : na
    bot  = bull ? high[2] : bear ? high        : na
    [bull, bear, top, bot]
[bull, bear, top, bot] = request.security(syminfo.tickerid, selectedTF, fvgDetect())

// === Historical FVG storage ===
var box[]   fvgBoxes = array.new_box()
var line[]  fvgMids  = array.new_line()
var bool[]  fvgBull  = array.new_bool()
var int[]   fvgTimes = array.new_int()

// === Single-session FVG state ===
var box    currBox  = na
var line   currMid  = na
var bool   drawn    = false
var bool   flipped  = false
var float  fvgTop   = na
var float  fvgBot   = na
var bool   isBull   = na

// === Trading Signal State Variables ===
var bool   swingConfirmed = false
var bool   tapped       = false
var int    tapBar       = na
var bool   waitEngulfing = false
var int    candlesSinceTap = 0
var float  lastUpLow     = na  // low of last up‑close candle
var float  lastDownHigh  = na  // high of last down‑close candle

// === SMI Signal State Variables (Historical FVG Only) ===
var int    smiBar        = na
var bool   waitSMIEngulfing = false
var float  smiLastUpLow     = na
var float  smiLastDownHigh  = na
var bool   smiHistoricalDetected = false

// ── At 10:00 AM stash last FVG & reset ──
if strAM
    if not na(currBox)
        array.push(fvgBoxes, currBox)
        array.push(fvgMids,   currMid)
        array.push(fvgBull,   isBull)
        array.push(fvgTimes,  time)
    drawn   := false
    flipped := false
    currBox := na
    currMid := na
    fvgTop  := na
    fvgBot  := na
    isBull  := na
    // Reset trading signal variables
    swingConfirmed := false
    tapped         := false
    waitEngulfing  := false
    candlesSinceTap := 0
    lastUpLow      := na
    lastDownHigh   := na
    // Reset SMI signal variables
    smiBar         := na
    waitSMIEngulfing := false
    smiLastUpLow   := na
    smiLastDownHigh := na
    smiHistoricalDetected := false

// ── Draw first FVG after 10:00 AM ──
if after10 and not drawn and (bull or bear)
    isBull  := bull
    fvgTop  := top
    fvgBot  := bot
    currBox := box.new(n-boxShift, fvgTop, n, fvgBot, border_color=invisBorder, bgcolor=currentFVGCol)
    currMid := line.new(n-boxShift, (fvgTop+fvgBot)/2, n, (fvgTop+fvgBot)/2, color=midlineColor, style=line.style_dashed)
    drawn   := true
    flipped := false
    if debugLabels
        label.new(bar_index,
                  (fvgTop+fvgBot)/2,
                  isBull ? "Bull FVG" : "Bear FVG",
                  xloc.bar_index,
                  yloc.price,
                  style     = label.style_label_center,
                  textcolor = color.white,
                  color     = isBull ? color.blue : color.orange,
                  size      = labelSizeMap("Small"))

// ── Extend & flip FVG mid-line ──
if drawn and not na(currBox)
    box.set_right(currBox, n)
    line.set_x2(currMid, n)
    if not flipped
        srcLow  = flipSource == "Wick" ? lf_low   : lf_close
        srcHigh = flipSource == "Wick" ? lf_high  : lf_close
        if isBull and srcLow < fvgBot
            box.set_bgcolor(currBox, currentFVGCol)
            box.set_border_color(currBox, invisBorder)
            flipped := true
        else if not isBull and srcHigh > fvgTop
            box.set_bgcolor(currBox, currentFVGCol)
            box.set_border_color(currBox, invisBorder)
            flipped := true

// === SMI (Smart Money Index) Calculation ===
x = input.int(25, "SMI Index Period", minval = 1, group="SMI Settings")
rr = input.int(14, "SMI Volume Flow Period", minval = 1, group="SMI Settings")
peakslen = input.int(500, "SMI Normalization Period", minval = 1, group="SMI Settings")
thr = input.float(0.9, "SMI High Interest Threshold", minval = 0.01, maxval = 0.99, group="SMI Settings")

dumb = ta.pvi-ta.ema(ta.pvi,255)
smart = ta.nvi-ta.ema(ta.nvi,255)

drsi = ta.rsi(dumb, rr)
srsi = ta.rsi(smart, rr)

r = srsi/drsi //ratio shows if smart money is buying from dumb money selling and vice versa

sums = math.sum(r, x)
peak = ta.highest(sums, peakslen)

index = sums/peak

smiCondition = index > thr

// SMI Arrow Display
plotshape(series= smiCondition ? 1 : na, title="High Smart Money Interest", color=color.rgb(233, 239, 233), style=shape.arrowup, size=size.normal, location=location.belowbar, force_overlay=true)

// === ZigZag Calculation (moved here to be accessible in SMI logic) ===
import DevLucem/ZigLib/1 as ZigZag
Depth = input.int(12, 'ZigZag Depth', minval=1, step=1, group="ZigZag Config")
Deviation = input.int(5, 'ZigZag Deviation', minval=1, step=1, group="ZigZag Config")
Backstep = input.int(2, 'ZigZag Backstep', minval=2, step=1, group="ZigZag Config")

// ZigZag Display Controls
showZigZagLines = input.bool(true, "Show ZigZag Lines", group="ZigZag Display")
showZigZagLabels = input.bool(true, "Show ZigZag Labels", group="ZigZag Display")
line_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group="ZigZag Display")
labels = input(0, "Labels Transparency", group="ZigZag Display")
upcolor = input(color.lime, 'Bull Color', group="ZigZag Display")
dncolor = input(color.red, 'Bear Color', group="ZigZag Display")
lines = input(0, "Lines Transparency", group="ZigZag Display")
background = input(80, "Background Transparency", group="ZigZag Display")
label_size = switch input.int(3, "Label Size", minval=1, maxval=5, group="ZigZag Display")
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
repaint = input(true, 'Repaint Levels', group="ZigZag Display")
extend = input(false, "Extend ZigZag", group="ZigZag Display")

[direction, z1, z2] = ZigZag.zigzag(low, high, Depth, Deviation, Backstep)
string nowPoint = ""
var float lastPoint = z1.price[1]
if bool(ta.change(direction))
    lastPoint := z1.price[1]

// Calculate current ZigZag point
nowPoint := direction<0? (z2.price<lastPoint? "LL": "HL"): (z2.price>lastPoint? "HH": "LH")

// === ZigZag Display Logic ===
if showZigZagLines or showZigZagLabels
    line zz = na
    label point = na

    if repaint
        if showZigZagLines
            zz := line.new(z1, z2, xloc.bar_time, extend? extend.right: extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
        if showZigZagLabels
            point := label.new(z2, nowPoint, xloc.bar_time, yloc.price,
             color.new(direction<0? upcolor: dncolor, labels), direction>0? label.style_label_down: label.style_label_up, color.new(direction>0? upcolor: dncolor, labels), label_size)
        if direction == direction[1]
            if showZigZagLines
                line.delete(zz[1])
            if showZigZagLabels
                label.delete(point[1])
        else
            if showZigZagLines
                line.set_extend(zz[1], extend.none)
    else
        if direction != direction[1]
            if showZigZagLines
                zz := line.new(z1[1], z2[1], xloc.bar_time, extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
            if showZigZagLabels
                point := label.new(z2[1], nowPoint, xloc.bar_time, yloc.price,
                 color.new(direction[1]<0? upcolor: dncolor, labels), direction[1]>0? label.style_label_down: label.style_label_up, color.new(direction[1]>0? upcolor: dncolor, labels), label_size)

// === Function to check if price is touching any historical FVG ===
checkHistoricalFVGTouch() =>
    touchingHistFVG = false
    histFVGType = false  // false = bearish, true = bullish
    if array.size(fvgBoxes) > 0 and showHistoricalFVGs
        cutoffTime = time - historyDays * 86400000
        for i = 0 to array.size(fvgBoxes) - 1
            if array.get(fvgTimes, i) >= cutoffTime
                isBullHist = array.get(fvgBull, i)
                b = array.get(fvgBoxes, i)
                fvgTopHist = box.get_top(b)
                fvgBotHist = box.get_bottom(b)

                // Check if current price is touching this historical FVG
                if (isBullHist and low <= fvgTopHist and high >= fvgBotHist) or
                   (not isBullHist and high >= fvgBotHist and low <= fvgTopHist)
                    touchingHistFVG := true
                    histFVGType := isBullHist
                    break
    [touchingHistFVG, histFVGType]

[touchingHistFVG, histFVGType] = checkHistoricalFVGTouch()

// ── Detect the FVG "tap" for trading signals ──
if drawn and not tapped and after10
    // first confirm swing‑away
    if not swingConfirmed and ((isBull and close > fvgTop) or (not isBull and close < fvgBot))
        swingConfirmed := true
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Swing Confirmed",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.black,
                      color     = color.yellow,
                      size      = labelSizeMap("Small"))
    // then the actual tap
    else if swingConfirmed and ((isBull and low <= fvgTop) or (not isBull and high >= fvgBot))
        tapBar           := bar_index
        waitEngulfing    := true
        candlesSinceTap  := 0
        tapped           := true
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Tapped FVG",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.blue,
                      color     = color.new(color.white,90),
                      size      = labelSizeMap("Small"))
        if debugNumbering
            label.new(bar_index,
                      high + 20 * minT,
                      "TAP: Starting numbering from bar " + str.tostring(bar_index),
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.white,
                      color     = color.purple,
                      size      = labelSizeMap("Small"))

// ── SMI Signal Detection Logic (Historical FVG Focus) ──
if enableSMISignals and not waitSMIEngulfing  // No time filter - can happen anytime
    // Debug: Show all conditions
    if showDetailedDebug
        label.new(bar_index, high + 5*minT,
                  "SMI:" + str.tostring(enableSMISignals) + " Wait:" + str.tostring(waitSMIEngulfing) + " Touch:" + str.tostring(touchingHistFVG),
                  xloc.bar_index, yloc.price,
                  style = label.style_label_center,
                  textcolor = color.white,
                  color = color.blue,
                  size = labelSizeMap("Tiny"))

    // ONLY look for Historical FVG touches (more robust approach)
    if touchingHistFVG and not smiHistoricalDetected
        // Check if SMI was detected in the last 100 candles
        smiFoundInHistory = false
        smiCountInHistory = 0
        for i = 0 to smiLookback - 1
            if smiCondition[i]
                smiFoundInHistory := true
                smiCountInHistory := smiCountInHistory + 1

        if showDetailedDebug
            label.new(bar_index, low - 5*minT,
                      "HistFVG Touch! SMI found:" + str.tostring(smiFoundInHistory) + " Count:" + str.tostring(smiCountInHistory) + " FVGType:" + str.tostring(histFVGType),
                      xloc.bar_index, yloc.price,
                      style = label.style_label_center,
                      textcolor = color.white,
                      color = color.yellow,
                      size = labelSizeMap("Tiny"))

        if smiFoundInHistory
            smiHistoricalDetected := true
            smiBar := bar_index
            waitSMIEngulfing := true
            smiLastUpLow := na
            smiLastDownHigh := na
            if showSMIDebug
                label.new(bar_index,
                          close,
                          "SMI Found + Historical FVG Touch - Waiting for Engulfing",
                          xloc.bar_index,
                          yloc.price,
                          style     = label.style_label_center,
                          textcolor = color.white,
                          color     = color.orange,
                          size      = labelSizeMap("Small"))
        else if showDetailedDebug
            label.new(bar_index,
                      close,
                      "Historical FVG Touch - No SMI in last " + str.tostring(smiLookback) + " candles",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.white,
                      color     = color.gray,
                      size      = labelSizeMap("Tiny"))

// ── SMI + FVG ENGULFING SIGNAL LOGIC ──
if waitSMIEngulfing and enableSMISignals
    barsSinceSMI = bar_index - smiBar

    // Debug: Show waiting status
    if showDetailedDebug
        label.new(bar_index, close + 10*minT,
                  "Waiting SMI Engulf - Bars:" + str.tostring(barsSinceSMI) + " TouchHist:" + str.tostring(touchingHistFVG) + " HistDet:" + str.tostring(smiHistoricalDetected),
                  xloc.bar_index, yloc.price,
                  style = label.style_label_center,
                  textcolor = color.white,
                  color = color.purple,
                  size = labelSizeMap("Tiny"))

    // update last opposite‑close extremes for SMI signals
    if close > open
        smiLastUpLow := low
    else if close < open
        smiLastDownHigh := high

    if barsSinceSMI <= 10
        // SMI signals now ONLY work with historical FVGs
        if smiHistoricalDetected and touchingHistFVG
            // Smart direction detection for SMI reversal signals
            // Multiple methods to determine if price is approaching from above or below

            // Method 1: ZigZag direction (current trend)
            zzBearish = direction < 0  // ZigZag showing bearish trend (approaching from above)
            zzBullish = direction > 0  // ZigZag showing bullish trend (approaching from below)

            // Method 2: Recent price action (last 5 bars)
            recentHigh = ta.highest(high, 5)
            recentLow = ta.lowest(low, 5)
            fvgMidpoint = (fvgTop + fvgBot) / 2

            // If recent high is significantly above FVG and current price near FVG = approaching from above
            approachingFromAbove = (recentHigh > fvgTop + (fvgTop - fvgBot) * 0.5) and (close <= fvgTop)
            // If recent low is significantly below FVG and current price near FVG = approaching from below
            approachingFromBelow = (recentLow < fvgBot - (fvgTop - fvgBot) * 0.5) and (close >= fvgBot)

            // Method 3: Price momentum (EMA direction)
            ema20 = ta.ema(close, 20)
            ema50 = ta.ema(close, 50)
            momentumBearish = ema20 < ema20[1] and close < ema20  // Bearish momentum
            momentumBullish = ema20 > ema20[1] and close > ema20  // Bullish momentum

            // Combined direction assessment (majority vote)
            bearishSignals = (zzBearish ? 1 : 0) + (approachingFromAbove ? 1 : 0) + (momentumBearish ? 1 : 0)
            bullishSignals = (zzBullish ? 1 : 0) + (approachingFromBelow ? 1 : 0) + (momentumBullish ? 1 : 0)

            // Determine expected reversal direction
            expectBullishReversal = bearishSignals >= 2  // If bearish approach, expect bullish reversal
            expectBearishReversal = bullishSignals >= 2  // If bullish approach, expect bearish reversal

            if showDetailedDebug
                label.new(bar_index, close - 10*minT,
                          "SMI Direction - ZZ:" + str.tostring(direction) + " Above:" + str.tostring(approachingFromAbove) + " Below:" + str.tostring(approachingFromBelow) + " BearSig:" + str.tostring(bearishSignals) + " BullSig:" + str.tostring(bullishSignals) + " ExpBuy:" + str.tostring(expectBullishReversal) + " ExpSell:" + str.tostring(expectBearishReversal),
                          xloc.bar_index, yloc.price,
                          style = label.style_label_center,
                          textcolor = color.white,
                          color = color.aqua,
                          size = labelSizeMap("Tiny"))

            // SMI BUY: Expecting bullish reversal + bullish engulfing
            if expectBullishReversal and close > open and not na(smiLastDownHigh) and close >= smiLastDownHigh
                label.new(bar_index, low - 15*minT,
                          "SMI-Buy",
                          style=label.style_label_up,
                          textcolor=color.white,
                          color=color.lime,
                          size=labelSizeMap(tradeLabelSize))
                waitSMIEngulfing := false
                smiLastUpLow := na
                smiLastDownHigh := na
                smiHistoricalDetected := false
                if showSMIDebug
                    label.new(bar_index, close, "SMI Buy: Bearish Approach + Bullish Reversal (Signals:" + str.tostring(bearishSignals) + ")",
                             xloc.bar_index, yloc.price,
                             style     = label.style_label_center,
                             textcolor = color.white,
                             color     = color.lime,
                             size      = labelSizeMap("Small"))

            // SMI SELL: Expecting bearish reversal + bearish engulfing
            else if expectBearishReversal and close < open and not na(smiLastUpLow) and close <= smiLastUpLow
                label.new(bar_index, high + 15*minT,
                          "SMI-Sell",
                          style=label.style_label_down,
                          textcolor=color.white,
                          color=color.maroon,
                          size=labelSizeMap(tradeLabelSize))
                waitSMIEngulfing := false
                smiLastUpLow := na
                smiLastDownHigh := na
                smiHistoricalDetected := false
                if showSMIDebug
                    label.new(bar_index, close, "SMI Sell: Bullish Approach + Bearish Reversal (Signals:" + str.tostring(bullishSignals) + ")",
                             xloc.bar_index, yloc.price,
                             style     = label.style_label_center,
                             textcolor = color.white,
                             color     = color.maroon,
                             size      = labelSizeMap("Small"))

            // Debug: Show detailed signal check with smart direction detection
            else if showDetailedDebug
                buyCheck = expectBullishReversal and close > open and not na(smiLastDownHigh) and close >= smiLastDownHigh
                sellCheck = expectBearishReversal and close < open and not na(smiLastUpLow) and close <= smiLastUpLow

                debugText = "SMI Smart Check - Buy:" + str.tostring(buyCheck) + " Sell:" + str.tostring(sellCheck)
                debugText := debugText + " ExpBullRev:" + str.tostring(expectBullishReversal) + " ExpBearRev:" + str.tostring(expectBearishReversal)
                debugText := debugText + " BearSig:" + str.tostring(bearishSignals) + " BullSig:" + str.tostring(bullishSignals)
                debugText := debugText + " C>O:" + str.tostring(close > open) + " C<O:" + str.tostring(close < open)
                debugText := debugText + " DownHigh:" + str.tostring(smiLastDownHigh) + " UpLow:" + str.tostring(smiLastUpLow)

                label.new(bar_index, close,
                         debugText,
                         xloc.bar_index, yloc.price,
                         style = label.style_label_center,
                         textcolor = color.white,
                         color = color.red,
                         size = labelSizeMap("Tiny"))





    else
        // no SMI engulf within 10 bars → give up
        waitSMIEngulfing := false
        smiLastUpLow := na
        smiLastDownHigh := na
        smiHistoricalDetected := false
        if showSMIDebug
            label.new(bar_index, close, "SMI: No Engulfing Found - Stopped after 10 candles",
                     xloc.bar_index, yloc.price,
                     style     = label.style_label_center,
                     textcolor = color.white,
                     color     = color.gray,
                     size      = labelSizeMap("Small"))

// ── ORIGINAL SB SIGNAL LOGIC (Completely Independent from SMI) ──
if enableSBSignals and waitEngulfing  // Only depends on SB signals being enabled
    barsSince = bar_index - tapBar

    // update last opposite‑close extremes for SB signals
    if close > open
        lastUpLow := low
    else if close < open
        lastDownHigh := high

    if barsSince <= 10
        // SB BUY: bullish engulf = green close above last down-candle's high
        if isBull and close > open and not na(lastDownHigh) and close >= lastDownHigh
            label.new(bar_index, low - 10*minT,
                      "SB-Buy",
                      style=label.style_label_up,
                      textcolor=color.white,
                      color=color.green,
                      size=labelSizeMap(tradeLabelSize))
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "SB Bullish Engulfing: Green close above last red high",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.green,
                         size      = labelSizeMap("Small"))

        // SB SELL: bearish engulf = red close below last up-candle's low
        else if not isBull and close < open and not na(lastUpLow) and close <= lastUpLow
            label.new(bar_index, high + 10*minT,
                      "SB-Sell",
                      style=label.style_label_down,
                      textcolor=color.white,
                      color=color.red,
                      size=labelSizeMap(tradeLabelSize))
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "SB Bearish Engulfing: Red close below last green low",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.red,
                         size      = labelSizeMap("Small"))

    else
        // no SB engulf within 10 bars → give up
        waitEngulfing := false
        lastUpLow := na
        lastDownHigh := na
        if showEngulfingDebug
            label.new(bar_index, (fvgTop+fvgBot)/2, "SB: No Engulfing Found - Stopped after 10 candles",
                     xloc.bar_index, yloc.price,
                     style     = label.style_label_center,
                     textcolor = color.white,
                     color     = color.gray,
                     size      = labelSizeMap("Small"))

// ── Render last X days of historical FVGs ──
if array.size(fvgBoxes) > 0 and showHistoricalFVGs
    cutoffTime = time - historyDays * 86400000
    for i = 0 to array.size(fvgBoxes) - 1
        if array.get(fvgTimes, i) >= cutoffTime
            b = array.get(fvgBoxes, i)
            m = array.get(fvgMids,  i)
            box.set_right(b, n)
            box.set_bgcolor(b, histFVGCol)
            line.set_x2(m, n)

// ── Extend & label last X days of session horizontals ──
if array.size(sessHLines) > 0 and extendHLine
    cutoffSess = time - historyDays * 86400000
    for i = 0 to array.size(sessHLines) - 1
        if array.get(sessHTimes, i) >= cutoffSess
            ln    = array.get(sessHLines,  i)
            price = array.get(sessHPrices, i)
            lb    = array.get(sessHLabels, i)
            line.set_extend(ln, extend.none)
            line.set_x2(ln, n)
            line.set_y2(ln, price)
            label.set_x(lb, n + labelXOffset)
            label.set_y(lb, price + labelYOffset)

// ── Timeframe warning ──
var table tab = table.new(position=position.top_right, columns=1, rows=1)
if barstate.islast and timeframe.in_seconds(timeframe.period) > 15 * 60
    table.cell(tab, 0, 0, "Use timeframe ≤ 15 min", text_color=color.red)

// Alert condition for SMI
alertcondition(condition=smiCondition, title="High Smart Money Interest Alert", message="High Smart Money Interest detected!")








// zig-zag

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Dev Lucem

//@version=5
//@author=devlucem

//
//       THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
//       THE ZIGZAG SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
//       I HOPE U LOVE IT
//






// Optional: Show direction arrow
plotarrow(direction, "direction", display=display.status_line)


// ////////
// // Declare Meal Was Sweet By Force
alertcondition(nowPoint == "HH" and z2.price != z2.price[1], "New Higher High", 'Zigzag on {{ticker}} higher higher high detected at {{time}}')
alertcondition(nowPoint == "LH" and z2.price != z2.price[1], "New Lower High", 'Zigzag on {{ticker}} higher lower high detected at {{time}}')
alertcondition(nowPoint == "HL" and z2.price != z2.price[1], "New Higher Low", 'Zigzag on {{ticker}} higher lower low detected at {{time}}')
alertcondition(nowPoint == "LL" and z2.price != z2.price[1], "New Lower Low", 'Zigzag on {{ticker}} lower low detected at {{time}}')
alertcondition(direction != direction[1], 'Direction Changed', 'Zigzag on {{ticker}} direction changed at {{time}}')
alertcondition(direction != direction[1] and direction>0, 'Bullish Direction', 'Zigzag on {{ticker}} bullish direction at {{time}}')
alertcondition(direction != direction[1] and direction<0, 'Bearish Direction', 'Zigzag on {{ticker}} bearish direction at {{time}}')

if direction != direction[1]
    alert((direction<0? "Bearish": "Bullish") + " Direction Final ", alert.freq_once_per_bar_close)