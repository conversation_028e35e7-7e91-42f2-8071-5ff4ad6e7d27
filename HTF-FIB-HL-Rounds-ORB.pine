//@version=5
indicator("HTF-HL-Rounds-ORB-AXR [Ravi]", "",true,format.price, max_labels_count=200, max_lines_count=50)

//@version=5
// indicator("Enhanced Fibonacci Levels with Color Customization", overlay=true)

//@version=5
// indicator(title='Higher Time Frame-High & Low [Ravi]',
//           shorttitle='High Time Frame H/L',
//           overlay=true,
//           max_lines_count = 500,
//           max_labels_count = 500
//           )

// Arrays to store lines and labels
var dailyLines = array.new_line()
var weeklyLines = array.new_line()
var monthlyLines = array.new_line()
var dailyLabels = array.new_label()
var weeklyLabels = array.new_label()
var monthlyLabels = array.new_label()

// Function to clear old lines
clearLines(linesArray) =>
    if array.size(linesArray) > 0
        for i = 0 to array.size(linesArray) - 1
            line.delete(array.get(linesArray, i))
        array.clear(linesArray)

// Function to clear old labels
clearLabels(labelsArray) =>
    if array.size(labelsArray) > 0
        for i = 0 to array.size(labelsArray) - 1
            label.delete(array.get(labelsArray, i))
        array.clear(labelsArray)

// ---------------------------------------------------------------------------------------------------------------------}
// Inputs for each timeframe
// ---------------------------------------------------------------------------------------------------------------------{


show_daily = input.bool(true, "Show Daily Levels", group="Timeframe Settings")
show_weekly = input.bool(true, "Show Weekly Levels", group="Timeframe Settings")
show_monthly = input.bool(true, "Show Monthly Levels", group="Timeframe Settings")

extension = input.int(50, "Extension to the right", group="General Settings")
show_break = input.bool(false, "Show Breakout Labels", group="General Settings")
show_trend = input.bool(false, "Show Trend Lines", group="General Settings")


// Color settings
daily_high_color = input.color(#ffffff9c, "Daily High", group="Colors")
daily_low_color = input.color(#ffffff9c, "Daily Low", group="Colors")
weekly_high_color = input.color(#ffffff9c, "Weekly High", group="Colors")
weekly_low_color = input.color(#ffffff9c, "Weekly Low", group="Colors")
monthly_high_color = input.color(#ffffff9c, "Monthly High", group="Colors")
monthly_low_color = input.color(#ffffff9c, "Monthly Low", group="Colors")

// ---------------------------------------------------------------------------------------------------------------------}
// Functions
// ---------------------------------------------------------------------------------------------------------------------{
getLevels(timeframe) =>
    indexHighTF = barstate.isrealtime ? 1 : 0
    indexCurrTF = barstate.isrealtime ? 0 : 1
    high_tf = request.security(syminfo.tickerid, timeframe, high[indexHighTF])[indexCurrTF]
    low_tf = request.security(syminfo.tickerid, timeframe, low[indexHighTF])[indexCurrTF]
    [high_tf, low_tf]

drawLevels(show, tf_high, tf_low, high_color, low_color, tf_name, linesArray, labelsArray) =>
    if show and barstate.islast
        // Clear old lines and labels
        clearLines(linesArray)
        clearLabels(labelsArray)

        // Draw lines
        high_line = line.new(x1=bar_index, y1=tf_high, x2=bar_index + extension, y2=tf_high, color=high_color, width=2)
        array.push(linesArray, high_line)

        // Add label for high line
        high_label = label.new(x=bar_index + extension, y=tf_high, text=tf_name + " High", style=label.style_label_left, color=color.new(color.black, 100), textcolor=chart.fg_color, size=size.normal)
        array.push(labelsArray, high_label)

        low_line = line.new(x1=bar_index, y1=tf_low, x2=bar_index + extension, y2=tf_low, color=low_color, width=2)
        array.push(linesArray, low_line)

        // Add label for low line
        low_label = label.new(x=bar_index + extension, y=tf_low, text=tf_name + " Low", style=label.style_label_left, color=color.new(color.black, 100), textcolor=chart.fg_color, size=size.normal)
        array.push(labelsArray, low_label)

        if show_trend
            avg = math.avg(tf_high, tf_low)
            mid_line = line.new(x1=bar_index, y1=avg, x2=bar_index + extension, y2=avg, color=chart.fg_color, width=1, style=line.style_solid)
            array.push(linesArray, mid_line)

            // Add label for mid line
            mid_label = label.new(x=bar_index + extension, y=avg, text=tf_name + " Mid", style=label.style_label_left, color=color.new(color.black, 100), textcolor=chart.fg_color, size=size.normal)
            array.push(labelsArray, mid_label)

// ---------------------------------------------------------------------------------------------------------------------}
// Get levels for each timeframe
// ---------------------------------------------------------------------------------------------------------------------{
[daily_high, daily_low] = getLevels("D")
[weekly_high, weekly_low] = getLevels("W")
[monthly_high, monthly_low] = getLevels("M")

// Calculate change conditions
daily_changed = not ta.change(daily_high) or not ta.change(daily_low)
weekly_changed = not ta.change(weekly_high) or not ta.change(weekly_low)
monthly_changed = not ta.change(monthly_high) or not ta.change(monthly_low)

// Plot levels in global scope
plot(show_daily and not show_trend and daily_changed ? daily_high : na, "Daily High", style=plot.style_linebr, linewidth=1, color=daily_high_color)
plot(show_daily and not show_trend and daily_changed ? daily_low : na, "Daily Low", style=plot.style_linebr, linewidth=1, color=daily_low_color)

plot(show_weekly and not show_trend and weekly_changed ? weekly_high : na, "Weekly High", style=plot.style_linebr, linewidth=2, color=weekly_high_color)
plot(show_weekly and not show_trend and weekly_changed ? weekly_low : na, "Weekly Low", style=plot.style_linebr, linewidth=2, color=weekly_low_color)

plot(show_monthly and not show_trend and monthly_changed ? monthly_high : na, "Monthly High", style=plot.style_linebr, linewidth=3, color=monthly_high_color)
plot(show_monthly and not show_trend and monthly_changed ? monthly_low : na, "Monthly Low", style=plot.style_linebr, linewidth=3, color=monthly_low_color)

// Draw extended lines and labels
drawLevels(show_daily, daily_high, daily_low, daily_high_color, daily_low_color, "Daily", dailyLines, dailyLabels)
drawLevels(show_weekly, weekly_high, weekly_low, weekly_high_color, weekly_low_color, "Weekly", weeklyLines, weeklyLabels)
drawLevels(show_monthly, monthly_high, monthly_low, monthly_high_color, monthly_low_color, "Monthly", monthlyLines, monthlyLabels)


// Alert condition for daily breakout
alertcondition(ta.crossover(high, daily_high), title="Daily High Breakout", message="Price broke above the Daily High")
alertcondition(ta.crossunder(high, daily_low), title="Daily Low Breakout", message="Price broke below the Daily Low")

// Alert condition for weekly breakout
alertcondition(ta.crossover(high, weekly_high), title="Weekly High Breakout", message="Price broke above the Weekly High")
alertcondition(ta.crossunder(high, weekly_low), title="Weekly Low Breakout", message="Price broke below the Weekly Low")

// Alert condition for monthly breakout
alertcondition(ta.crossover(high, monthly_high), title="Monthly High Breakout", message="Price broke above the Monthly High")
alertcondition(ta.crossunder(high, monthly_low), title="Monthly Low Breakout", message="Price broke below the Monthly Low")





//vwap

hideonDWM = input(false, title="Hide VWAP on 1D or Above", group="VWAP Settings", display = display.data_window)
var anchor = input.string(defval = "Session", title="Anchor Period",
 options=["Session", "Week", "Month", "Quarter", "Year", "Decade", "Century", "Earnings", "Dividends", "Splits"], group="VWAP Settings")
src = input(title = "Source", defval = hlc3, group="VWAP Settings", display = display.data_window)
offset = input.int(0, title="Offset", group="VWAP Settings", minval=0, display = display.data_window)

BANDS_GROUP = "Bands Settings"
CALC_MODE_TOOLTIP = "Determines the units used to calculate the distance of the bands. When 'Percentage' is selected, a multiplier of 1 means 1%."
calcModeInput = input.string("Standard Deviation", "Bands Calculation Mode", options = ["Standard Deviation", "Percentage"], group = BANDS_GROUP, tooltip = CALC_MODE_TOOLTIP, display = display.data_window)
showBand_1 = input(true, title = "", group = BANDS_GROUP, inline = "band_1", display = display.data_window)
bandMult_1 = input.float(1.0, title = "Bands Multiplier #1", group = BANDS_GROUP, inline = "band_1", step = 0.5, minval=0, display = display.data_window)
showBand_2 = input(false, title = "", group = BANDS_GROUP, inline = "band_2", display = display.data_window)
bandMult_2 = input.float(2.0, title = "Bands Multiplier #2", group = BANDS_GROUP, inline = "band_2", step = 0.5, minval=0, display = display.data_window)
showBand_3 = input(false, title = "", group = BANDS_GROUP, inline = "band_3", display = display.data_window)
bandMult_3 = input.float(3.0, title = "Bands Multiplier #3", group = BANDS_GROUP, inline = "band_3", step = 0.5, minval=0, display = display.data_window)

cumVolume = ta.cum(volume)
if barstate.islast and cumVolume == 0
    runtime.error("No volume is provided by the data vendor.")

new_earnings = request.earnings(syminfo.tickerid, earnings.actual, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_dividends = request.dividends(syminfo.tickerid, dividends.gross, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_split = request.splits(syminfo.tickerid, splits.denominator, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)

isNewPeriod = switch anchor
	"Earnings"  => not na(new_earnings)
	"Dividends" => not na(new_dividends)
	"Splits"    => not na(new_split)
	"Session"   => timeframe.change("D")
	"Week"      => timeframe.change("W")
	"Month"     => timeframe.change("M")
	"Quarter"   => timeframe.change("3M")
	"Year"      => timeframe.change("12M")
	"Decade"    => timeframe.change("12M") and year % 10 == 0
	"Century"   => timeframe.change("12M") and year % 100 == 0
	=> false

isEsdAnchor = anchor == "Earnings" or anchor == "Dividends" or anchor == "Splits"
if na(src[1]) and not isEsdAnchor
	isNewPeriod := true

float vwapValue = na
float upperBandValue1 = na
float lowerBandValue1 = na
float upperBandValue2 = na
float lowerBandValue2 = na
float upperBandValue3 = na
float lowerBandValue3 = na

if not (hideonDWM and timeframe.isdwm)
    [_vwap, _stdevUpper, _] = ta.vwap(src, isNewPeriod, 1)
	vwapValue := _vwap
    stdevAbs = _stdevUpper - _vwap
	bandBasis = calcModeInput == "Standard Deviation" ? stdevAbs : _vwap * 0.01
	upperBandValue1 := _vwap + bandBasis * bandMult_1
	lowerBandValue1 := _vwap - bandBasis * bandMult_1
	upperBandValue2 := _vwap + bandBasis * bandMult_2
	lowerBandValue2 := _vwap - bandBasis * bandMult_2
	upperBandValue3 := _vwap + bandBasis * bandMult_3
	lowerBandValue3 := _vwap - bandBasis * bandMult_3

plot(vwapValue, title = "VWAP", color = color.rgb(155, 244, 137), offset = offset,linewidth = 3)


// Round Numbers Settings Group
showRoundNumbers = input.bool(false, "Show Round Numbers", group="Round Numbers", inline="rn_toggle")
roundNumbersExpanded = input.bool(false, "⚙", group="Round Numbers", inline="rn_toggle")

// Only show these inputs if roundNumbersExpanded is true
numLevels = input.int(20, "Number of Levels (Above/Below)", minval=1, maxval=50, 
         group="Round Numbers Settings", inline="rn_levels")
priceInterval = input.int(5, "Price Interval", minval=1, 
         tooltip="Draw lines at every X price points", 
         group="Round Numbers Settings", inline="rn_levels")

// Line Settings (only visible when roundNumbersExpanded is true)
extendLeft = input.int(500, "Extend Left", minval=0, 
         group="Round Numbers Settings")
extendRight = input.int(500, "Extend Right", minval=0, 
         group="Round Numbers Settings")
lineColor = input.color(#eadf10dc, "Line Color", 
         group="Round Numbers Settings")
lineStyle = input.string("Solid", "Line Style", 
         options=["Solid", "Dashed", "Dotted"], 
         group="Round Numbers Settings")
lineWidth = input.int(2, "Line Width", minval=1, maxval=4, 
         group="Round Numbers Settings")
showLabels = input.bool(true, "Show Labels", 
         group="Round Numbers Settings")

// Convert line style input to Pine style
var lineStyleDict = line.style_solid
if lineStyle == "Dashed"
    lineStyleDict := line.style_dashed
else if lineStyle == "Dotted"
    lineStyleDict := line.style_dotted

// Function to determine if the number is a big round number
isBigRoundNumber(num) =>
    num % priceInterval == 0

// Store lines in array
var line[] linesArray = array.new_line()

// Clear previous lines and draw new ones on the last bar
if barstate.islast and showRoundNumbers
    // Get current price and round it to nearest interval
    currentPrice = close
    basePrice = math.round(currentPrice / priceInterval) * priceInterval
    
    // Calculate start and end prices based on current price
    startPrice = basePrice - (numLevels * priceInterval)
    endPrice = basePrice + (numLevels * priceInterval)
    
    // Clear previous lines
    if array.size(linesArray) > 0
        for i = array.size(linesArray) - 1 to 0
            line.delete(array.get(linesArray, i))
        array.clear(linesArray)
    
    // Plot new lines
    for i = startPrice to endPrice by priceInterval
        if isBigRoundNumber(i)
            newLine = line.new(
                 x1=bar_index - extendLeft, 
                 y1=i, 
                 x2=bar_index + extendRight, 
                 y2=i, 
                 color=lineColor,
                 style=lineStyleDict,
                 width=lineWidth)
            array.push(linesArray, newLine)
            
            // Add labels on both ends if enabled
            if showLabels
                // label.new(
                //      bar_index - extendLeft, 
                //      i, 
                //      str.tostring(i),
                //      style=label.style_label_right,
                //      color=color.new(lineColor, 100),
                //      textcolor=color.new(lineColor, 0))
                label.new(
                     bar_index + extendRight, 
                     i, 
                     str.tostring(i),
                     style=label.style_label_left,
                     color=color.new(lineColor, 100),
                     textcolor=color.new(lineColor, 0))




// Opening Range Breakout (ORB)
inputMax = input.int(5, title="ORB total time (minutes)")
sess = input.session("0915-0920", title="Session Time")
t = time(timeframe.period, sess + ":1234567")
hide = timeframe.isintraday and timeframe.multiplier <= inputMax

is_newbar(res) =>
    ta.change(time(res)) != 0

in_session = not na(t)
is_first = in_session and not in_session[1]

var float orb_high = na
var float orb_low = na

if is_first
    orb_high := high
    orb_low := low
else
    orb_high := orb_high
    orb_low := orb_low

if high > orb_high and in_session
    orb_high := high
if low < orb_low and in_session
    orb_low := low

plot(hide ? orb_high : na, style=plot.style_line, color=orb_high[1] != orb_high ? na : color.green, title="ORB High", linewidth=2)
plot(hide ? orb_low : na, style=plot.style_line, color=orb_low[1] != orb_low ? na : color.red, title="ORB Low", linewidth=2)




//@version=5
// indicator("ICT ADR/AWR/AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
show_only_intraday = input.bool(false, "Show Only on Intraday Timeframes", group=group_settings)
timezone = input.string("America/New_York", "Time Zone", group=group_settings)
max_periods = input.int(3, "Periods to Display", minval=1, maxval=30, tooltip="Number of days/weeks/months to display", group=group_settings)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, group=group_time)
week_start_day = input.string("Monday", "Week Start Day", options=["Sunday", "Monday"], group=group_time)
month_start_day = input.int(1, "Month Start Day", minval=1, maxval=31, tooltip="Day of month to start monthly calculations", group=group_time)

// Daily ADR Settings
group_adr = "Daily ADR Settings"
show_daily_levels = input.bool(true, "Show Daily Levels", group=group_adr)
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group=group_adr)
show_full_adr = input.bool(true, "Show Full ADR", group=group_adr)
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group=group_adr)
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group=group_adr)
show_half_adr = input.bool(false, "Show 1/2 ADR", group=group_adr)

// Weekly AWR Settings
group_awr = "Weekly AWR Settings"
show_weekly_levels = input.bool(true, "Show Weekly Levels", group=group_awr)
awr_weeks = input.int(4, "Weeks to Average", minval=1, maxval=52, group=group_awr)
show_full_awr = input.bool(true, "Show Full AWR", group=group_awr)
show_one_third_awr = input.bool(true, "Show 1/3 AWR", group=group_awr)
show_two_third_awr = input.bool(false, "Show 2/3 AWR", group=group_awr)
show_half_awr = input.bool(false, "Show 1/2 AWR", group=group_awr)

// Monthly AMR Settings
group_amr = "Monthly AMR Settings"
show_monthly_levels = input.bool(true, "Show Monthly Levels", group=group_amr)
amr_months = input.int(3, "Months to Average", minval=1, maxval=24, group=group_amr)
show_full_amr = input.bool(true, "Show Full AMR", group=group_amr)
show_one_third_amr = input.bool(true, "Show 1/3 AMR", group=group_amr)
show_two_third_amr = input.bool(false, "Show 2/3 AMR", group=group_amr)
show_half_amr = input.bool(false, "Show 1/2 AMR", group=group_amr)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Day Open", group=group_line)

// Color Settings
group_color_daily = "Daily Color Settings"
true_day_open_color = input.color(color.orange, "True Day Open Line", group=group_color_daily)
full_adr_color = input.color(color.white, "Full ADR", group=group_color_daily)
one_third_adr_color = input.color(color.aqua, "1/3 ADR", group=group_color_daily)
two_third_adr_color = input.color(color.yellow, "2/3 ADR", group=group_color_daily)
half_adr_color = input.color(color.purple, "1/2 ADR", group=group_color_daily)

// Weekly Color Settings
group_color_weekly = "Weekly Color Settings"
true_week_open_color = input.color(color.lime, "True Week Open Line", group=group_color_weekly)
full_awr_color = input.color(color.white, "Full AWR", group=group_color_weekly)
one_third_awr_color = input.color(color.aqua, "1/3 AWR", group=group_color_weekly)
two_third_awr_color = input.color(color.yellow, "2/3 AWR", group=group_color_weekly)
half_awr_color = input.color(color.purple, "1/2 AWR", group=group_color_weekly)

// Monthly Color Settings
group_color_monthly = "Monthly Color Settings"
true_month_open_color = input.color(color.red, "True Month Open Line", group=group_color_monthly)
full_amr_color = input.color(color.white, "Full AMR", group=group_color_monthly)
one_third_amr_color = input.color(color.aqua, "1/3 AMR", group=group_color_monthly)
two_third_amr_color = input.color(color.yellow, "2/3 AMR", group=group_color_monthly)
half_amr_color = input.color(color.purple, "1/2 AMR", group=group_color_monthly)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === TIME LOGIC ===
// Daily time logic
start_of_day = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
end_of_day = timestamp(timezone, year, month, dayofmonth, 23, 59)
is_new_day = (time == start_of_day) or (barstate.isfirst and show_daily_levels)
in_current_day = time >= start_of_day and time <= end_of_day

// Weekly time logic
is_sunday = dayofweek == 1
is_monday = dayofweek == 2
week_start_condition = week_start_day == "Sunday" ? is_sunday : is_monday
week_timestamp = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
start_of_week = week_timestamp and week_start_condition
is_new_week = (time == week_timestamp and week_start_condition) or (barstate.isfirst and show_weekly_levels)
in_current_week = true  // We'll always be in the current week, just need to track when a new week starts

// Monthly time logic
is_first_day_of_month = dayofmonth == month_start_day
month_timestamp = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
start_of_month = month_timestamp and is_first_day_of_month
is_new_month = (time == month_timestamp and is_first_day_of_month) or (barstate.isfirst and show_monthly_levels)
in_current_month = true  // We'll always be in the current month, just need to track when a new month starts

// Function to calculate the Average Daily Range
calculate_adr(lookback_period) =>
    // Request daily high-low range data
    dh = request.security(syminfo.tickerid, "D", high, barmerge.gaps_off, barmerge.lookahead_off)
    dl = request.security(syminfo.tickerid, "D", low, barmerge.gaps_off, barmerge.lookahead_off)
    drange = dh - dl

    // Calculate the average of the daily ranges
    ta.sma(drange, lookback_period)

// Function to calculate the Average Weekly Range
calculate_awr(lookback_period) =>
    // Request weekly high-low range data
    wh = request.security(syminfo.tickerid, "W", high, barmerge.gaps_off, barmerge.lookahead_off)
    wl = request.security(syminfo.tickerid, "W", low, barmerge.gaps_off, barmerge.lookahead_off)
    wrange = wh - wl

    // Calculate the average of the weekly ranges
    ta.sma(wrange, lookback_period)

// Function to calculate the Average Monthly Range
calculate_amr(lookback_period) =>
    // Request monthly high-low range data
    mh = request.security(syminfo.tickerid, "M", high, barmerge.gaps_off, barmerge.lookahead_off)
    ml = request.security(syminfo.tickerid, "M", low, barmerge.gaps_off, barmerge.lookahead_off)
    mrange = mh - ml

    // Calculate the average of the monthly ranges
    ta.sma(mrange, lookback_period)

// === STATE VARIABLES ===
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// === DAILY VARIABLES ===
// True Day Open variables
var line true_day_open_line = na
var label true_day_open_label = na
var line true_day_vertical = na
var float true_day_open_price = na
var bool true_day_line_active = false

// ADR level lines
var line full_adr_up_line = na
var line full_adr_down_line = na
var line one_third_adr_up_line = na
var line one_third_adr_down_line = na
var line two_third_adr_up_line = na
var line two_third_adr_down_line = na
var line half_adr_up_line = na
var line half_adr_down_line = na

// ADR level labels
var label full_adr_up_label = na
var label full_adr_down_label = na
var label one_third_adr_up_label = na
var label one_third_adr_down_label = na
var label two_third_adr_up_label = na
var label two_third_adr_down_label = na
var label half_adr_up_label = na
var label half_adr_down_label = na

// ADR level values
var float daily_range_val = na
var float full_adr_up = na
var float full_adr_down = na
var float one_third_adr_up = na
var float one_third_adr_down = na
var float two_third_adr_up = na
var float two_third_adr_down = na
var float half_adr_up = na
var float half_adr_down = na

// === WEEKLY VARIABLES ===
// True Week Open variables
var line true_week_open_line = na
var label true_week_open_label = na
var line true_week_vertical = na
var float true_week_open_price = na
var bool true_week_line_active = false

// AWR level lines
var line full_awr_up_line = na
var line full_awr_down_line = na
var line one_third_awr_up_line = na
var line one_third_awr_down_line = na
var line two_third_awr_up_line = na
var line two_third_awr_down_line = na
var line half_awr_up_line = na
var line half_awr_down_line = na

// AWR level labels
var label full_awr_up_label = na
var label full_awr_down_label = na
var label one_third_awr_up_label = na
var label one_third_awr_down_label = na
var label two_third_awr_up_label = na
var label two_third_awr_down_label = na
var label half_awr_up_label = na
var label half_awr_down_label = na

// AWR level values
var float weekly_range_val = na
var float full_awr_up = na
var float full_awr_down = na
var float one_third_awr_up = na
var float one_third_awr_down = na
var float two_third_awr_up = na
var float two_third_awr_down = na
var float half_awr_up = na
var float half_awr_down = na

// === MONTHLY VARIABLES ===
// True Month Open variables
var line true_month_open_line = na
var label true_month_open_label = na
var line true_month_vertical = na
var float true_month_open_price = na
var bool true_month_line_active = false

// AMR level lines
var line full_amr_up_line = na
var line full_amr_down_line = na
var line one_third_amr_up_line = na
var line one_third_amr_down_line = na
var line two_third_amr_up_line = na
var line two_third_amr_down_line = na
var line half_amr_up_line = na
var line half_amr_down_line = na

// AMR level labels
var label full_amr_up_label = na
var label full_amr_down_label = na
var label one_third_amr_up_label = na
var label one_third_amr_down_label = na
var label two_third_amr_up_label = na
var label two_third_amr_down_label = na
var label half_amr_up_label = na
var label half_amr_down_label = na

// AMR level values
var float monthly_range_val = na
var float full_amr_up = na
var float full_amr_down = na
var float one_third_amr_up = na
var float one_third_amr_down = na
var float two_third_amr_up = na
var float two_third_amr_down = na
var float half_amr_up = na
var float half_amr_down = na

// === HISTORY ARRAYS ===
// Arrays to store historical range values
var float[] daily_range_history = array.new_float()
var float[] weekly_range_history = array.new_float()
var float[] monthly_range_history = array.new_float()

// === DAILY ARRAYS ===
// Arrays to store historical daily lines by type
var line[] tdo_lines = array.new_line()
var line[] full_adr_up_lines = array.new_line()
var line[] full_adr_down_lines = array.new_line()
var line[] one_third_adr_up_lines = array.new_line()
var line[] one_third_adr_down_lines = array.new_line()
var line[] two_third_adr_up_lines = array.new_line()
var line[] two_third_adr_down_lines = array.new_line()
var line[] half_adr_up_lines = array.new_line()
var line[] half_adr_down_lines = array.new_line()
var line[] daily_vertical_lines = array.new_line()

// Arrays to store historical daily labels by type
var label[] tdo_labels = array.new_label()
var label[] full_adr_up_labels = array.new_label()
var label[] full_adr_down_labels = array.new_label()
var label[] one_third_adr_up_labels = array.new_label()
var label[] one_third_adr_down_labels = array.new_label()
var label[] two_third_adr_up_labels = array.new_label()
var label[] two_third_adr_down_labels = array.new_label()
var label[] half_adr_up_labels = array.new_label()
var label[] half_adr_down_labels = array.new_label()

// === WEEKLY ARRAYS ===
// Arrays to store historical weekly lines by type
var line[] two_lines = array.new_line()
var line[] full_awr_up_lines = array.new_line()
var line[] full_awr_down_lines = array.new_line()
var line[] one_third_awr_up_lines = array.new_line()
var line[] one_third_awr_down_lines = array.new_line()
var line[] two_third_awr_up_lines = array.new_line()
var line[] two_third_awr_down_lines = array.new_line()
var line[] half_awr_up_lines = array.new_line()
var line[] half_awr_down_lines = array.new_line()
var line[] weekly_vertical_lines = array.new_line()

// Arrays to store historical weekly labels by type
var label[] two_labels = array.new_label()
var label[] full_awr_up_labels = array.new_label()
var label[] full_awr_down_labels = array.new_label()
var label[] one_third_awr_up_labels = array.new_label()
var label[] one_third_awr_down_labels = array.new_label()
var label[] two_third_awr_up_labels = array.new_label()
var label[] two_third_awr_down_labels = array.new_label()
var label[] half_awr_up_labels = array.new_label()
var label[] half_awr_down_labels = array.new_label()

// === MONTHLY ARRAYS ===
// Arrays to store historical monthly lines by type
var line[] tmo_lines = array.new_line()
var line[] full_amr_up_lines = array.new_line()
var line[] full_amr_down_lines = array.new_line()
var line[] one_third_amr_up_lines = array.new_line()
var line[] one_third_amr_down_lines = array.new_line()
var line[] two_third_amr_up_lines = array.new_line()
var line[] two_third_amr_down_lines = array.new_line()
var line[] half_amr_up_lines = array.new_line()
var line[] half_amr_down_lines = array.new_line()
var line[] monthly_vertical_lines = array.new_line()

// Arrays to store historical monthly labels by type
var label[] tmo_labels = array.new_label()
var label[] full_amr_up_labels = array.new_label()
var label[] full_amr_down_labels = array.new_label()
var label[] one_third_amr_up_labels = array.new_label()
var label[] one_third_amr_down_labels = array.new_label()
var label[] two_third_amr_up_labels = array.new_label()
var label[] two_third_amr_down_labels = array.new_label()
var label[] half_amr_up_labels = array.new_label()
var label[] half_amr_down_labels = array.new_label()

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)



// === MAIN LOGIC ===
// Calculate range values
float current_adr = calculate_adr(adr_days)
float current_awr = calculate_awr(awr_weeks)
float current_amr = calculate_amr(amr_months)

// Special initialization for monthly levels on the first bar
if barstate.isfirst and show_monthly_levels and not true_month_line_active
    monthly_range_val := current_amr
    true_month_open_price := close
    true_month_line_active := true

    // Calculate AMR levels
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Create true month open line
    true_month_open_line := line.new(bar_index, true_month_open_price, bar_index, true_month_open_price, color=true_month_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tmo_lines, true_month_open_line)

    // Create true month open label
    if show_labels
        true_month_open_label := label.new(bar_index + label_x_offset_bars, true_month_open_price + label_y_offset, "TMO" + (show_price_in_label ? str.format(" ({0})", true_month_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_month_open_color, color=color.new(color.black, 100))
        array.push(tmo_labels, true_month_open_label)

    // Create AMR level lines
    if show_full_amr
        // Full AMR up line
        full_amr_up_line := line.new(bar_index, full_amr_up, bar_index, full_amr_up, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_up_lines, full_amr_up_line)

        // Full AMR up label
        if show_labels
            full_amr_up_label := label.new(bar_index + label_x_offset_bars, full_amr_up + label_y_offset, "AMR+" + (show_price_in_label ? str.format(" ({0})", full_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_up_labels, full_amr_up_label)

        // Full AMR down line
        full_amr_down_line := line.new(bar_index, full_amr_down, bar_index, full_amr_down, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_down_lines, full_amr_down_line)

        // Full AMR down label
        if show_labels
            full_amr_down_label := label.new(bar_index + label_x_offset_bars, full_amr_down + label_y_offset, "AMR-" + (show_price_in_label ? str.format(" ({0})", full_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_down_labels, full_amr_down_label)

    if show_one_third_amr
        // 1/3 AMR up line
        one_third_amr_up_line := line.new(bar_index, one_third_amr_up, bar_index, one_third_amr_up, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_up_lines, one_third_amr_up_line)

        // 1/3 AMR up label
        if show_labels
            one_third_amr_up_label := label.new(bar_index + label_x_offset_bars, one_third_amr_up + label_y_offset, "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", one_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_up_labels, one_third_amr_up_label)

        // 1/3 AMR down line
        one_third_amr_down_line := line.new(bar_index, one_third_amr_down, bar_index, one_third_amr_down, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_down_lines, one_third_amr_down_line)

        // 1/3 AMR down label
        if show_labels
            one_third_amr_down_label := label.new(bar_index + label_x_offset_bars, one_third_amr_down + label_y_offset, "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", one_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_down_labels, one_third_amr_down_label)

    if show_two_third_amr
        // 2/3 AMR up line
        two_third_amr_up_line := line.new(bar_index, two_third_amr_up, bar_index, two_third_amr_up, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_up_lines, two_third_amr_up_line)

        // 2/3 AMR up label
        if show_labels
            two_third_amr_up_label := label.new(bar_index + label_x_offset_bars, two_third_amr_up + label_y_offset, "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", two_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_up_labels, two_third_amr_up_label)

        // 2/3 AMR down line
        two_third_amr_down_line := line.new(bar_index, two_third_amr_down, bar_index, two_third_amr_down, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_down_lines, two_third_amr_down_line)

        // 2/3 AMR down label
        if show_labels
            two_third_amr_down_label := label.new(bar_index + label_x_offset_bars, two_third_amr_down + label_y_offset, "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", two_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_down_labels, two_third_amr_down_label)

    if show_half_amr
        // 1/2 AMR up line
        half_amr_up_line := line.new(bar_index, half_amr_up, bar_index, half_amr_up, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_up_lines, half_amr_up_line)

        // 1/2 AMR up label
        if show_labels
            half_amr_up_label := label.new(bar_index + label_x_offset_bars, half_amr_up + label_y_offset, "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", half_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_up_labels, half_amr_up_label)

        // 1/2 AMR down line
        half_amr_down_line := line.new(bar_index, half_amr_down, bar_index, half_amr_down, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_down_lines, half_amr_down_line)

        // 1/2 AMR down label
        if show_labels
            half_amr_down_label := label.new(bar_index + label_x_offset_bars, half_amr_down + label_y_offset, "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", half_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_down_labels, half_amr_down_label)

// Check if we're at the start of a new day
if is_new_day and show_daily_levels
    // Store the current ADR value in history and for today's use
    array.unshift(daily_range_history, current_adr)
    if array.size(daily_range_history) > adr_days
        array.pop(daily_range_history)

    // Update the daily range value for today
    daily_range_val := current_adr

    // Update true day open price
    true_day_open_price := barstate.isfirst ? close : open
    true_day_line_active := true

    // Calculate ADR levels
    full_adr_up := true_day_open_price + daily_range_val
    full_adr_down := true_day_open_price - daily_range_val
    one_third_adr_up := true_day_open_price + (daily_range_val / 3)
    one_third_adr_down := true_day_open_price - (daily_range_val / 3)
    two_third_adr_up := true_day_open_price + (daily_range_val * 2 / 3)
    two_third_adr_down := true_day_open_price - (daily_range_val * 2 / 3)
    half_adr_up := true_day_open_price + (daily_range_val / 2)
    half_adr_down := true_day_open_price - (daily_range_val / 2)

    // Create true day open line
    true_day_open_line := line.new(bar_index, true_day_open_price, bar_index, true_day_open_price, color=true_day_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tdo_lines, true_day_open_line)
    manage_line_history(tdo_lines)

    // Delete old labels when a new TDO appears
    if show_labels
        delete_all_labels(tdo_labels)
        delete_all_labels(full_adr_up_labels)
        delete_all_labels(full_adr_down_labels)
        delete_all_labels(one_third_adr_up_labels)
        delete_all_labels(one_third_adr_down_labels)
        delete_all_labels(two_third_adr_up_labels)
        delete_all_labels(two_third_adr_down_labels)
        delete_all_labels(half_adr_up_labels)
        delete_all_labels(half_adr_down_labels)

        // Create true day open label
        true_day_open_label := label.new(bar_index + label_x_offset_bars, true_day_open_price + label_y_offset, "TDO" + (show_price_in_label ? str.format(" ({0})", true_day_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_day_open_color, color=color.new(color.black, 100))
        array.push(tdo_labels, true_day_open_label)

    // Create vertical line at day open
    if show_vertical
        true_day_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_day_open_color, style=line.style_dashed)
        array.unshift(daily_vertical_lines, true_day_vertical)
        manage_line_history(daily_vertical_lines)

    // Create ADR level lines
    if show_full_adr
        // Full ADR up line
        full_adr_up_line := line.new(bar_index, full_adr_up, bar_index, full_adr_up, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_up_lines, full_adr_up_line)
        manage_line_history(full_adr_up_lines)

        // Full ADR up label
        if show_labels
            full_adr_up_label := label.new(bar_index + label_x_offset_bars, full_adr_up + label_y_offset, "ADR+" + (show_price_in_label ? str.format(" ({0})", full_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_up_labels, full_adr_up_label)

        // Full ADR down line
        full_adr_down_line := line.new(bar_index, full_adr_down, bar_index, full_adr_down, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_down_lines, full_adr_down_line)
        manage_line_history(full_adr_down_lines)

        // Full ADR down label
        if show_labels
            full_adr_down_label := label.new(bar_index + label_x_offset_bars, full_adr_down + label_y_offset, "ADR-" + (show_price_in_label ? str.format(" ({0})", full_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_down_labels, full_adr_down_label)

    // Create 1/3 ADR level lines
    if show_one_third_adr
        // 1/3 ADR up line
        one_third_adr_up_line := line.new(bar_index, one_third_adr_up, bar_index, one_third_adr_up, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_up_lines, one_third_adr_up_line)
        manage_line_history(one_third_adr_up_lines)

        // 1/3 ADR up label
        if show_labels
            one_third_adr_up_label := label.new(bar_index + label_x_offset_bars, one_third_adr_up + label_y_offset, "1/3 ADR+" + (show_price_in_label ? str.format(" ({0})", one_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_up_labels, one_third_adr_up_label)

        // 1/3 ADR down line
        one_third_adr_down_line := line.new(bar_index, one_third_adr_down, bar_index, one_third_adr_down, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_down_lines, one_third_adr_down_line)
        manage_line_history(one_third_adr_down_lines)

        // 1/3 ADR down label
        if show_labels
            one_third_adr_down_label := label.new(bar_index + label_x_offset_bars, one_third_adr_down + label_y_offset, "1/3 ADR-" + (show_price_in_label ? str.format(" ({0})", one_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_down_labels, one_third_adr_down_label)

    // Create 2/3 ADR level lines
    if show_two_third_adr
        // 2/3 ADR up line
        two_third_adr_up_line := line.new(bar_index, two_third_adr_up, bar_index, two_third_adr_up, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_up_lines, two_third_adr_up_line)
        manage_line_history(two_third_adr_up_lines)

        // 2/3 ADR up label
        if show_labels
            two_third_adr_up_label := label.new(bar_index + label_x_offset_bars, two_third_adr_up + label_y_offset, "2/3 ADR+" + (show_price_in_label ? str.format(" ({0})", two_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_up_labels, two_third_adr_up_label)

        // 2/3 ADR down line
        two_third_adr_down_line := line.new(bar_index, two_third_adr_down, bar_index, two_third_adr_down, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_down_lines, two_third_adr_down_line)
        manage_line_history(two_third_adr_down_lines)

        // 2/3 ADR down label
        if show_labels
            two_third_adr_down_label := label.new(bar_index + label_x_offset_bars, two_third_adr_down + label_y_offset, "2/3 ADR-" + (show_price_in_label ? str.format(" ({0})", two_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_down_labels, two_third_adr_down_label)

    // Create 1/2 ADR level lines
    if show_half_adr
        // 1/2 ADR up line
        half_adr_up_line := line.new(bar_index, half_adr_up, bar_index, half_adr_up, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_up_lines, half_adr_up_line)
        manage_line_history(half_adr_up_lines)

        // 1/2 ADR up label
        if show_labels
            half_adr_up_label := label.new(bar_index + label_x_offset_bars, half_adr_up + label_y_offset, "1/2 ADR+" + (show_price_in_label ? str.format(" ({0})", half_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_up_labels, half_adr_up_label)

        // 1/2 ADR down line
        half_adr_down_line := line.new(bar_index, half_adr_down, bar_index, half_adr_down, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_down_lines, half_adr_down_line)
        manage_line_history(half_adr_down_lines)

        // 1/2 ADR down label
        if show_labels
            half_adr_down_label := label.new(bar_index + label_x_offset_bars, half_adr_down + label_y_offset, "1/2 ADR-" + (show_price_in_label ? str.format(" ({0})", half_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_down_labels, half_adr_down_label)

    // All lines are now managed by their respective arrays

// Check if we're at the start of a new week
if is_new_week and show_weekly_levels
    // Store the current AWR value in history and for this week's use
    array.unshift(weekly_range_history, current_awr)
    if array.size(weekly_range_history) > awr_weeks
        array.pop(weekly_range_history)

    // Update the weekly range value for this week
    weekly_range_val := current_awr

    // Update true week open price
    true_week_open_price := barstate.isfirst ? close : open
    true_week_line_active := true

    // Calculate AWR levels
    full_awr_up := true_week_open_price + weekly_range_val
    full_awr_down := true_week_open_price - weekly_range_val
    one_third_awr_up := true_week_open_price + (weekly_range_val / 3)
    one_third_awr_down := true_week_open_price - (weekly_range_val / 3)
    two_third_awr_up := true_week_open_price + (weekly_range_val * 2 / 3)
    two_third_awr_down := true_week_open_price - (weekly_range_val * 2 / 3)
    half_awr_up := true_week_open_price + (weekly_range_val / 2)
    half_awr_down := true_week_open_price - (weekly_range_val / 2)

    // Create true week open line
    true_week_open_line := line.new(bar_index, true_week_open_price, bar_index, true_week_open_price, color=true_week_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(two_lines, true_week_open_line)
    manage_line_history(two_lines)

    // Delete old labels when a new TWO appears
    if show_labels
        delete_all_labels(two_labels)
        delete_all_labels(full_awr_up_labels)
        delete_all_labels(full_awr_down_labels)
        delete_all_labels(one_third_awr_up_labels)
        delete_all_labels(one_third_awr_down_labels)
        delete_all_labels(two_third_awr_up_labels)
        delete_all_labels(two_third_awr_down_labels)
        delete_all_labels(half_awr_up_labels)
        delete_all_labels(half_awr_down_labels)

        // Create true week open label
        true_week_open_label := label.new(bar_index + label_x_offset_bars, true_week_open_price + label_y_offset, "TWO" + (show_price_in_label ? str.format(" ({0})", true_week_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_week_open_color, color=color.new(color.black, 100))
        array.push(two_labels, true_week_open_label)

    // Create vertical line at week open
    if show_vertical
        true_week_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_week_open_color, style=line.style_dashed)
        array.unshift(weekly_vertical_lines, true_week_vertical)
        manage_line_history(weekly_vertical_lines)

    // Create AWR level lines
    if show_full_awr
        // Full AWR up line
        full_awr_up_line := line.new(bar_index, full_awr_up, bar_index, full_awr_up, color=full_awr_color, width=line_width, style=line_style_value)
        array.unshift(full_awr_up_lines, full_awr_up_line)
        manage_line_history(full_awr_up_lines)

        // Full AWR up label
        if show_labels
            full_awr_up_label := label.new(bar_index + label_x_offset_bars, full_awr_up + label_y_offset, "AWR+" + (show_price_in_label ? str.format(" ({0})", full_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_awr_color, color=color.new(color.black, 100))
            array.push(full_awr_up_labels, full_awr_up_label)

        // Full AWR down line
        full_awr_down_line := line.new(bar_index, full_awr_down, bar_index, full_awr_down, color=full_awr_color, width=line_width, style=line_style_value)
        array.unshift(full_awr_down_lines, full_awr_down_line)
        manage_line_history(full_awr_down_lines)

        // Full AWR down label
        if show_labels
            full_awr_down_label := label.new(bar_index + label_x_offset_bars, full_awr_down + label_y_offset, "AWR-" + (show_price_in_label ? str.format(" ({0})", full_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_awr_color, color=color.new(color.black, 100))
            array.push(full_awr_down_labels, full_awr_down_label)

    // Create 1/3 AWR level lines
    if show_one_third_awr
        // 1/3 AWR up line
        one_third_awr_up_line := line.new(bar_index, one_third_awr_up, bar_index, one_third_awr_up, color=one_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_awr_up_lines, one_third_awr_up_line)
        manage_line_history(one_third_awr_up_lines)

        // 1/3 AWR up label
        if show_labels
            one_third_awr_up_label := label.new(bar_index + label_x_offset_bars, one_third_awr_up + label_y_offset, "1/3 AWR+" + (show_price_in_label ? str.format(" ({0})", one_third_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_awr_color, color=color.new(color.black, 100))
            array.push(one_third_awr_up_labels, one_third_awr_up_label)

        // 1/3 AWR down line
        one_third_awr_down_line := line.new(bar_index, one_third_awr_down, bar_index, one_third_awr_down, color=one_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_awr_down_lines, one_third_awr_down_line)
        manage_line_history(one_third_awr_down_lines)

        // 1/3 AWR down label
        if show_labels
            one_third_awr_down_label := label.new(bar_index + label_x_offset_bars, one_third_awr_down + label_y_offset, "1/3 AWR-" + (show_price_in_label ? str.format(" ({0})", one_third_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_awr_color, color=color.new(color.black, 100))
            array.push(one_third_awr_down_labels, one_third_awr_down_label)

    // Create 2/3 AWR level lines
    if show_two_third_awr
        // 2/3 AWR up line
        two_third_awr_up_line := line.new(bar_index, two_third_awr_up, bar_index, two_third_awr_up, color=two_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_awr_up_lines, two_third_awr_up_line)
        manage_line_history(two_third_awr_up_lines)

        // 2/3 AWR up label
        if show_labels
            two_third_awr_up_label := label.new(bar_index + label_x_offset_bars, two_third_awr_up + label_y_offset, "2/3 AWR+" + (show_price_in_label ? str.format(" ({0})", two_third_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_awr_color, color=color.new(color.black, 100))
            array.push(two_third_awr_up_labels, two_third_awr_up_label)

        // 2/3 AWR down line
        two_third_awr_down_line := line.new(bar_index, two_third_awr_down, bar_index, two_third_awr_down, color=two_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_awr_down_lines, two_third_awr_down_line)
        manage_line_history(two_third_awr_down_lines)

        // 2/3 AWR down label
        if show_labels
            two_third_awr_down_label := label.new(bar_index + label_x_offset_bars, two_third_awr_down + label_y_offset, "2/3 AWR-" + (show_price_in_label ? str.format(" ({0})", two_third_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_awr_color, color=color.new(color.black, 100))
            array.push(two_third_awr_down_labels, two_third_awr_down_label)

    // Create 1/2 AWR level lines
    if show_half_awr
        // 1/2 AWR up line
        half_awr_up_line := line.new(bar_index, half_awr_up, bar_index, half_awr_up, color=half_awr_color, width=line_width, style=line_style_value)
        array.unshift(half_awr_up_lines, half_awr_up_line)
        manage_line_history(half_awr_up_lines)

        // 1/2 AWR up label
        if show_labels
            half_awr_up_label := label.new(bar_index + label_x_offset_bars, half_awr_up + label_y_offset, "1/2 AWR+" + (show_price_in_label ? str.format(" ({0})", half_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_awr_color, color=color.new(color.black, 100))
            array.push(half_awr_up_labels, half_awr_up_label)

        // 1/2 AWR down line
        half_awr_down_line := line.new(bar_index, half_awr_down, bar_index, half_awr_down, color=half_awr_color, width=line_width, style=line_style_value)
        array.unshift(half_awr_down_lines, half_awr_down_line)
        manage_line_history(half_awr_down_lines)

        // 1/2 AWR down label
        if show_labels
            half_awr_down_label := label.new(bar_index + label_x_offset_bars, half_awr_down + label_y_offset, "1/2 AWR-" + (show_price_in_label ? str.format(" ({0})", half_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_awr_color, color=color.new(color.black, 100))
            array.push(half_awr_down_labels, half_awr_down_label)

// Check if we're at the start of a new month
if is_new_month and show_monthly_levels
    // Store the current AMR value in history and for this month's use
    array.unshift(monthly_range_history, current_amr)
    if array.size(monthly_range_history) > amr_months
        array.pop(monthly_range_history)

    // Update the monthly range value for this month
    monthly_range_val := current_amr

    // Update true month open price
    true_month_open_price := barstate.isfirst ? close : open
    true_month_line_active := true

    // Calculate AMR levels
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Create true month open line
    true_month_open_line := line.new(bar_index, true_month_open_price, bar_index, true_month_open_price, color=true_month_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tmo_lines, true_month_open_line)
    manage_line_history(tmo_lines)

    // Delete old labels when a new TMO appears
    if show_labels
        delete_all_labels(tmo_labels)
        delete_all_labels(full_amr_up_labels)
        delete_all_labels(full_amr_down_labels)
        delete_all_labels(one_third_amr_up_labels)
        delete_all_labels(one_third_amr_down_labels)
        delete_all_labels(two_third_amr_up_labels)
        delete_all_labels(two_third_amr_down_labels)
        delete_all_labels(half_amr_up_labels)
        delete_all_labels(half_amr_down_labels)

        // Create true month open label
        true_month_open_label := label.new(bar_index + label_x_offset_bars, true_month_open_price + label_y_offset, "TMO" + (show_price_in_label ? str.format(" ({0})", true_month_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_month_open_color, color=color.new(color.black, 100))
        array.push(tmo_labels, true_month_open_label)

    // Create vertical line at month open
    if show_vertical
        true_month_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_month_open_color, style=line.style_dashed)
        array.unshift(monthly_vertical_lines, true_month_vertical)
        manage_line_history(monthly_vertical_lines)

    // Create AMR level lines
    if show_full_amr
        // Full AMR up line
        full_amr_up_line := line.new(bar_index, full_amr_up, bar_index, full_amr_up, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_up_lines, full_amr_up_line)
        manage_line_history(full_amr_up_lines)

        // Full AMR up label
        if show_labels
            full_amr_up_label := label.new(bar_index + label_x_offset_bars, full_amr_up + label_y_offset, "AMR+" + (show_price_in_label ? str.format(" ({0})", full_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_up_labels, full_amr_up_label)

        // Full AMR down line
        full_amr_down_line := line.new(bar_index, full_amr_down, bar_index, full_amr_down, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_down_lines, full_amr_down_line)
        manage_line_history(full_amr_down_lines)

        // Full AMR down label
        if show_labels
            full_amr_down_label := label.new(bar_index + label_x_offset_bars, full_amr_down + label_y_offset, "AMR-" + (show_price_in_label ? str.format(" ({0})", full_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_down_labels, full_amr_down_label)

    // Create 1/3 AMR level lines
    if show_one_third_amr
        // 1/3 AMR up line
        one_third_amr_up_line := line.new(bar_index, one_third_amr_up, bar_index, one_third_amr_up, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_up_lines, one_third_amr_up_line)
        manage_line_history(one_third_amr_up_lines)

        // 1/3 AMR up label
        if show_labels
            one_third_amr_up_label := label.new(bar_index + label_x_offset_bars, one_third_amr_up + label_y_offset, "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", one_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_up_labels, one_third_amr_up_label)

        // 1/3 AMR down line
        one_third_amr_down_line := line.new(bar_index, one_third_amr_down, bar_index, one_third_amr_down, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_down_lines, one_third_amr_down_line)
        manage_line_history(one_third_amr_down_lines)

        // 1/3 AMR down label
        if show_labels
            one_third_amr_down_label := label.new(bar_index + label_x_offset_bars, one_third_amr_down + label_y_offset, "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", one_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_down_labels, one_third_amr_down_label)

    // Create 2/3 AMR level lines
    if show_two_third_amr
        // 2/3 AMR up line
        two_third_amr_up_line := line.new(bar_index, two_third_amr_up, bar_index, two_third_amr_up, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_up_lines, two_third_amr_up_line)
        manage_line_history(two_third_amr_up_lines)

        // 2/3 AMR up label
        if show_labels
            two_third_amr_up_label := label.new(bar_index + label_x_offset_bars, two_third_amr_up + label_y_offset, "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", two_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_up_labels, two_third_amr_up_label)

        // 2/3 AMR down line
        two_third_amr_down_line := line.new(bar_index, two_third_amr_down, bar_index, two_third_amr_down, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_down_lines, two_third_amr_down_line)
        manage_line_history(two_third_amr_down_lines)

        // 2/3 AMR down label
        if show_labels
            two_third_amr_down_label := label.new(bar_index + label_x_offset_bars, two_third_amr_down + label_y_offset, "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", two_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_down_labels, two_third_amr_down_label)

    // Create 1/2 AMR level lines
    if show_half_amr
        // 1/2 AMR up line
        half_amr_up_line := line.new(bar_index, half_amr_up, bar_index, half_amr_up, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_up_lines, half_amr_up_line)
        manage_line_history(half_amr_up_lines)

        // 1/2 AMR up label
        if show_labels
            half_amr_up_label := label.new(bar_index + label_x_offset_bars, half_amr_up + label_y_offset, "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", half_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_up_labels, half_amr_up_label)

        // 1/2 AMR down line
        half_amr_down_line := line.new(bar_index, half_amr_down, bar_index, half_amr_down, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_down_lines, half_amr_down_line)
        manage_line_history(half_amr_down_lines)

        // 1/2 AMR down label
        if show_labels
            half_amr_down_label := label.new(bar_index + label_x_offset_bars, half_amr_down + label_y_offset, "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", half_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_down_labels, half_amr_down_label)

// Update lines during the current day
if in_current_day and true_day_line_active
    // Update true day open line
    line.set_x2(true_day_open_line, bar_index)
    line.set_y2(true_day_open_line, true_day_open_price)

    // Update true day open label
    if show_labels
        label.set_x(true_day_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_day_open_label, true_day_open_price + label_y_offset)

    // Update ADR level lines
    if show_full_adr
        line.set_x2(full_adr_up_line, bar_index)
        line.set_y2(full_adr_up_line, full_adr_up)
        line.set_x2(full_adr_down_line, bar_index)
        line.set_y2(full_adr_down_line, full_adr_down)

        if show_labels
            label.set_x(full_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_up_label, full_adr_up + label_y_offset)
            label.set_x(full_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_down_label, full_adr_down + label_y_offset)

    if show_one_third_adr
        line.set_x2(one_third_adr_up_line, bar_index)
        line.set_y2(one_third_adr_up_line, one_third_adr_up)
        line.set_x2(one_third_adr_down_line, bar_index)
        line.set_y2(one_third_adr_down_line, one_third_adr_down)

        if show_labels
            label.set_x(one_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_up_label, one_third_adr_up + label_y_offset)
            label.set_x(one_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_down_label, one_third_adr_down + label_y_offset)

    if show_two_third_adr
        line.set_x2(two_third_adr_up_line, bar_index)
        line.set_y2(two_third_adr_up_line, two_third_adr_up)
        line.set_x2(two_third_adr_down_line, bar_index)
        line.set_y2(two_third_adr_down_line, two_third_adr_down)

        if show_labels
            label.set_x(two_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_up_label, two_third_adr_up + label_y_offset)
            label.set_x(two_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_down_label, two_third_adr_down + label_y_offset)

    if show_half_adr
        line.set_x2(half_adr_up_line, bar_index)
        line.set_y2(half_adr_up_line, half_adr_up)
        line.set_x2(half_adr_down_line, bar_index)
        line.set_y2(half_adr_down_line, half_adr_down)

        if show_labels
            label.set_x(half_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_up_label, half_adr_up + label_y_offset)
            label.set_x(half_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_down_label, half_adr_down + label_y_offset)

// Update lines during the current week
if in_current_week and true_week_line_active and show_weekly_levels
    // Update true week open line
    line.set_x2(true_week_open_line, bar_index)
    line.set_y2(true_week_open_line, true_week_open_price)

    // Update true week open label
    if show_labels
        label.set_x(true_week_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_week_open_label, true_week_open_price + label_y_offset)

    // Update AWR level lines
    if show_full_awr
        line.set_x2(full_awr_up_line, bar_index)
        line.set_y2(full_awr_up_line, full_awr_up)
        line.set_x2(full_awr_down_line, bar_index)
        line.set_y2(full_awr_down_line, full_awr_down)

        if show_labels
            label.set_x(full_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_awr_up_label, full_awr_up + label_y_offset)
            label.set_x(full_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_awr_down_label, full_awr_down + label_y_offset)

    if show_one_third_awr
        line.set_x2(one_third_awr_up_line, bar_index)
        line.set_y2(one_third_awr_up_line, one_third_awr_up)
        line.set_x2(one_third_awr_down_line, bar_index)
        line.set_y2(one_third_awr_down_line, one_third_awr_down)

        if show_labels
            label.set_x(one_third_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_awr_up_label, one_third_awr_up + label_y_offset)
            label.set_x(one_third_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_awr_down_label, one_third_awr_down + label_y_offset)

    if show_two_third_awr
        line.set_x2(two_third_awr_up_line, bar_index)
        line.set_y2(two_third_awr_up_line, two_third_awr_up)
        line.set_x2(two_third_awr_down_line, bar_index)
        line.set_y2(two_third_awr_down_line, two_third_awr_down)

        if show_labels
            label.set_x(two_third_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_awr_up_label, two_third_awr_up + label_y_offset)
            label.set_x(two_third_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_awr_down_label, two_third_awr_down + label_y_offset)

    if show_half_awr
        line.set_x2(half_awr_up_line, bar_index)
        line.set_y2(half_awr_up_line, half_awr_up)
        line.set_x2(half_awr_down_line, bar_index)
        line.set_y2(half_awr_down_line, half_awr_down)

        if show_labels
            label.set_x(half_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_awr_up_label, half_awr_up + label_y_offset)
            label.set_x(half_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_awr_down_label, half_awr_down + label_y_offset)

// Update lines during the current month
if in_current_month and true_month_line_active and show_monthly_levels
    // Update true month open line
    line.set_x2(true_month_open_line, bar_index)
    line.set_y2(true_month_open_line, true_month_open_price)

    // Update true month open label
    if show_labels
        label.set_x(true_month_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_month_open_label, true_month_open_price + label_y_offset)

    // Update AMR level lines
    if show_full_amr
        line.set_x2(full_amr_up_line, bar_index)
        line.set_y2(full_amr_up_line, full_amr_up)
        line.set_x2(full_amr_down_line, bar_index)
        line.set_y2(full_amr_down_line, full_amr_down)

        if show_labels
            label.set_x(full_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_amr_up_label, full_amr_up + label_y_offset)
            label.set_x(full_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_amr_down_label, full_amr_down + label_y_offset)

    if show_one_third_amr
        line.set_x2(one_third_amr_up_line, bar_index)
        line.set_y2(one_third_amr_up_line, one_third_amr_up)
        line.set_x2(one_third_amr_down_line, bar_index)
        line.set_y2(one_third_amr_down_line, one_third_amr_down)

        if show_labels
            label.set_x(one_third_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_amr_up_label, one_third_amr_up + label_y_offset)
            label.set_x(one_third_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_amr_down_label, one_third_amr_down + label_y_offset)

    if show_two_third_amr
        line.set_x2(two_third_amr_up_line, bar_index)
        line.set_y2(two_third_amr_up_line, two_third_amr_up)
        line.set_x2(two_third_amr_down_line, bar_index)
        line.set_y2(two_third_amr_down_line, two_third_amr_down)

        if show_labels
            label.set_x(two_third_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_amr_up_label, two_third_amr_up + label_y_offset)
            label.set_x(two_third_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_amr_down_label, two_third_amr_down + label_y_offset)

    if show_half_amr
        line.set_x2(half_amr_up_line, bar_index)
        line.set_y2(half_amr_up_line, half_amr_up)
        line.set_x2(half_amr_down_line, bar_index)
        line.set_y2(half_amr_down_line, half_amr_down)

        if show_labels
            label.set_x(half_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_amr_up_label, half_amr_up + label_y_offset)
            label.set_x(half_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_amr_down_label, half_amr_down + label_y_offset)

// === TIMEFRAME RESTRICTION ===
// Only apply timeframe restriction if show_only_intraday is true and we're on a daily or higher timeframe
if show_only_intraday and timeframe.isdaily
    // Daily levels
    line.delete(true_day_open_line)
    label.delete(true_day_open_label)
    line.delete(true_day_vertical)

    if show_full_adr
        line.delete(full_adr_up_line)
        line.delete(full_adr_down_line)
        label.delete(full_adr_up_label)
        label.delete(full_adr_down_label)

    if show_one_third_adr
        line.delete(one_third_adr_up_line)
        line.delete(one_third_adr_down_line)
        label.delete(one_third_adr_up_label)
        label.delete(one_third_adr_down_label)

    if show_two_third_adr
        line.delete(two_third_adr_up_line)
        line.delete(two_third_adr_down_line)
        label.delete(two_third_adr_up_label)
        label.delete(two_third_adr_down_label)

    if show_half_adr
        line.delete(half_adr_up_line)
        line.delete(half_adr_down_line)
        label.delete(half_adr_up_label)
        label.delete(half_adr_down_label)

    // Delete all daily labels in arrays
    delete_all_labels(tdo_labels)
    delete_all_labels(full_adr_up_labels)
    delete_all_labels(full_adr_down_labels)
    delete_all_labels(one_third_adr_up_labels)
    delete_all_labels(one_third_adr_down_labels)
    delete_all_labels(two_third_adr_up_labels)
    delete_all_labels(two_third_adr_down_labels)
    delete_all_labels(half_adr_up_labels)
    delete_all_labels(half_adr_down_labels)

    true_day_line_active := false

    // Weekly levels
    line.delete(true_week_open_line)
    label.delete(true_week_open_label)
    line.delete(true_week_vertical)

    if show_full_awr
        line.delete(full_awr_up_line)
        line.delete(full_awr_down_line)
        label.delete(full_awr_up_label)
        label.delete(full_awr_down_label)

    if show_one_third_awr
        line.delete(one_third_awr_up_line)
        line.delete(one_third_awr_down_line)
        label.delete(one_third_awr_up_label)
        label.delete(one_third_awr_down_label)

    if show_two_third_awr
        line.delete(two_third_awr_up_line)
        line.delete(two_third_awr_down_line)
        label.delete(two_third_awr_up_label)
        label.delete(two_third_awr_down_label)

    if show_half_awr
        line.delete(half_awr_up_line)
        line.delete(half_awr_down_line)
        label.delete(half_awr_up_label)
        label.delete(half_awr_down_label)

    // Delete all weekly labels in arrays
    delete_all_labels(two_labels)
    delete_all_labels(full_awr_up_labels)
    delete_all_labels(full_awr_down_labels)
    delete_all_labels(one_third_awr_up_labels)
    delete_all_labels(one_third_awr_down_labels)
    delete_all_labels(two_third_awr_up_labels)
    delete_all_labels(two_third_awr_down_labels)
    delete_all_labels(half_awr_up_labels)
    delete_all_labels(half_awr_down_labels)

    true_week_line_active := false

    // Monthly levels
    line.delete(true_month_open_line)
    label.delete(true_month_open_label)
    line.delete(true_month_vertical)

    if show_full_amr
        line.delete(full_amr_up_line)
        line.delete(full_amr_down_line)
        label.delete(full_amr_up_label)
        label.delete(full_amr_down_label)

    if show_one_third_amr
        line.delete(one_third_amr_up_line)
        line.delete(one_third_amr_down_line)
        label.delete(one_third_amr_up_label)
        label.delete(one_third_amr_down_label)

    if show_two_third_amr
        line.delete(two_third_amr_up_line)
        line.delete(two_third_amr_down_line)
        label.delete(two_third_amr_up_label)
        label.delete(two_third_amr_down_label)

    if show_half_amr
        line.delete(half_amr_up_line)
        line.delete(half_amr_down_line)
        label.delete(half_amr_up_label)
        label.delete(half_amr_down_label)

    // Delete all monthly labels in arrays
    delete_all_labels(tmo_labels)
    delete_all_labels(full_amr_up_labels)
    delete_all_labels(full_amr_down_labels)
    delete_all_labels(one_third_amr_up_labels)
    delete_all_labels(one_third_amr_down_labels)
    delete_all_labels(two_third_amr_up_labels)
    delete_all_labels(two_third_amr_down_labels)
    delete_all_labels(half_amr_up_labels)
    delete_all_labels(half_amr_down_labels)

    true_month_line_active := false

// === ALERT CONDITIONS ===
// Alert conditions for price touching ADR levels
alertcondition(ta.crossover(high, full_adr_up) or ta.crossunder(low, full_adr_up), "Price touched Full ADR+", "Price touched Full ADR+ level")
alertcondition(ta.crossover(high, full_adr_down) or ta.crossunder(low, full_adr_down), "Price touched Full ADR-", "Price touched Full ADR- level")
alertcondition(ta.crossover(high, one_third_adr_up) or ta.crossunder(low, one_third_adr_up), "Price touched 1/3 ADR+", "Price touched 1/3 ADR+ level")
alertcondition(ta.crossover(high, one_third_adr_down) or ta.crossunder(low, one_third_adr_down), "Price touched 1/3 ADR-", "Price touched 1/3 ADR- level")
alertcondition(ta.crossover(high, two_third_adr_up) or ta.crossunder(low, two_third_adr_up), "Price touched 2/3 ADR+", "Price touched 2/3 ADR+ level")
alertcondition(ta.crossover(high, two_third_adr_down) or ta.crossunder(low, two_third_adr_down), "Price touched 2/3 ADR-", "Price touched 2/3 ADR- level")
alertcondition(ta.crossover(high, half_adr_up) or ta.crossunder(low, half_adr_up), "Price touched 1/2 ADR+", "Price touched 1/2 ADR+ level")
alertcondition(ta.crossover(high, half_adr_down) or ta.crossunder(low, half_adr_down), "Price touched 1/2 ADR-", "Price touched 1/2 ADR- level")

// Alert conditions for price touching AWR levels
alertcondition(ta.crossover(high, full_awr_up) or ta.crossunder(low, full_awr_up), "Price touched Full AWR+", "Price touched Full AWR+ level")
alertcondition(ta.crossover(high, full_awr_down) or ta.crossunder(low, full_awr_down), "Price touched Full AWR-", "Price touched Full AWR- level")
alertcondition(ta.crossover(high, one_third_awr_up) or ta.crossunder(low, one_third_awr_up), "Price touched 1/3 AWR+", "Price touched 1/3 AWR+ level")
alertcondition(ta.crossover(high, one_third_awr_down) or ta.crossunder(low, one_third_awr_down), "Price touched 1/3 AWR-", "Price touched 1/3 AWR- level")
alertcondition(ta.crossover(high, two_third_awr_up) or ta.crossunder(low, two_third_awr_up), "Price touched 2/3 AWR+", "Price touched 2/3 AWR+ level")
alertcondition(ta.crossover(high, two_third_awr_down) or ta.crossunder(low, two_third_awr_down), "Price touched 2/3 AWR-", "Price touched 2/3 AWR- level")
alertcondition(ta.crossover(high, half_awr_up) or ta.crossunder(low, half_awr_up), "Price touched 1/2 AWR+", "Price touched 1/2 AWR+ level")
alertcondition(ta.crossover(high, half_awr_down) or ta.crossunder(low, half_awr_down), "Price touched 1/2 AWR-", "Price touched 1/2 AWR- level")

// Alert conditions for price touching AMR levels
alertcondition(ta.crossover(high, full_amr_up) or ta.crossunder(low, full_amr_up), "Price touched Full AMR+", "Price touched Full AMR+ level")
alertcondition(ta.crossover(high, full_amr_down) or ta.crossunder(low, full_amr_down), "Price touched Full AMR-", "Price touched Full AMR- level")
alertcondition(ta.crossover(high, one_third_amr_up) or ta.crossunder(low, one_third_amr_up), "Price touched 1/3 AMR+", "Price touched 1/3 AMR+ level")
alertcondition(ta.crossover(high, one_third_amr_down) or ta.crossunder(low, one_third_amr_down), "Price touched 1/3 AMR-", "Price touched 1/3 AMR- level")
alertcondition(ta.crossover(high, two_third_amr_up) or ta.crossunder(low, two_third_amr_up), "Price touched 2/3 AMR+", "Price touched 2/3 AMR+ level")
alertcondition(ta.crossover(high, two_third_amr_down) or ta.crossunder(low, two_third_amr_down), "Price touched 2/3 AMR-", "Price touched 2/3 AMR- level")
alertcondition(ta.crossover(high, half_amr_up) or ta.crossunder(low, half_amr_up), "Price touched 1/2 AMR+", "Price touched 1/2 AMR+ level")
alertcondition(ta.crossover(high, half_amr_down) or ta.crossunder(low, half_amr_down), "Price touched 1/2 AMR-", "Price touched 1/2 AMR- level")