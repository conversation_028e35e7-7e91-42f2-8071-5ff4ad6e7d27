// ============================================================================
// MULTIPLE ROUND NUMBERS CONFIGURATION
// ============================================================================

// Master toggle for all round numbers
showRoundNumbers = show_round_numbers
roundNumbersExpanded = input.bool(false, "⚙", group="Round Numbers", inline="rn_toggle")

// ----------------------------------------
// ROUND NUMBERS SET 1 - Major Levels
// ----------------------------------------
showRN1 = input.bool(true, "Enable Set 1 (Major)", group="Round Numbers Set 1", inline="rn1_toggle")
rn1Expanded = input.bool(false, "⚙", group="Round Numbers Set 1", inline="rn1_toggle")

numLevels1 = input.int(20, "Levels (Above/Below)", minval=1, maxval=50,
         group="Round Numbers Set 1", inline="rn1_levels")
priceInterval1 = input.int(10, "Interval", minval=1,
         tooltip="Major round numbers (10, 20, 30, etc.)",
         group="Round Numbers Set 1", inline="rn1_levels")

// Set 1 Line Settings
extendLeft1 = input.int(500, "Extend Left", minval=0, group="Round Numbers Set 1")
extendRight1 = input.int(500, "Extend Right", minval=0, group="Round Numbers Set 1")
lineColor1 = input.color(#ff6b35, "Line Color", group="Round Numbers Set 1")
lineStyle1 = input.string("Solid", "Line Style", options=["Solid", "Dashed", "Dotted"], group="Round Numbers Set 1")
lineWidth1 = input.int(2, "Line Width", minval=1, maxval=4, group="Round Numbers Set 1")
showLabels1 = input.bool(true, "Show Labels", group="Round Numbers Set 1")

// ----------------------------------------
// ROUND NUMBERS SET 2 - Minor Levels
// ----------------------------------------
showRN2 = input.bool(true, "Enable Set 2 (Minor)", group="Round Numbers Set 2", inline="rn2_toggle")
rn2Expanded = input.bool(false, "⚙", group="Round Numbers Set 2", inline="rn2_toggle")

numLevels2 = input.int(20, "Levels (Above/Below)", minval=1, maxval=50,
         group="Round Numbers Set 2", inline="rn2_levels")
priceInterval2 = input.int(5, "Interval", minval=1,
         tooltip="Minor round numbers (5, 15, 25, etc.)",
         group="Round Numbers Set 2", inline="rn2_levels")

// Set 2 Line Settings
extendLeft2 = input.int(500, "Extend Left", minval=0, group="Round Numbers Set 2")
extendRight2 = input.int(500, "Extend Right", minval=0, group="Round Numbers Set 2")
lineColor2 = input.color(#4ecdc4, "Line Color", group="Round Numbers Set 2")
lineStyle2 = input.string("Dashed", "Line Style", options=["Solid", "Dashed", "Dotted"], group="Round Numbers Set 2")
lineWidth2 = input.int(1, "Line Width", minval=1, maxval=4, group="Round Numbers Set 2")
showLabels2 = input.bool(false, "Show Labels", group="Round Numbers Set 2")

// ----------------------------------------
// ROUND NUMBERS SET 3 - Micro Levels
// ----------------------------------------
showRN3 = input.bool(false, "Enable Set 3 (Micro)", group="Round Numbers Set 3", inline="rn3_toggle")
rn3Expanded = input.bool(false, "⚙", group="Round Numbers Set 3", inline="rn3_toggle")

numLevels3 = input.int(15, "Levels (Above/Below)", minval=1, maxval=50,
         group="Round Numbers Set 3", inline="rn3_levels")
priceInterval3 = input.int(1, "Interval", minval=1,
         tooltip="Micro round numbers (1, 2, 3, etc.)",
         group="Round Numbers Set 3", inline="rn3_levels")

// Set 3 Line Settings
extendLeft3 = input.int(300, "Extend Left", minval=0, group="Round Numbers Set 3")
extendRight3 = input.int(300, "Extend Right", minval=0, group="Round Numbers Set 3")
lineColor3 = input.color(#95e1d3, "Line Color", group="Round Numbers Set 3")
lineStyle3 = input.string("Dotted", "Line Style", options=["Solid", "Dashed", "Dotted"], group="Round Numbers Set 3")
lineWidth3 = input.int(1, "Line Width", minval=1, maxval=4, group="Round Numbers Set 3")
showLabels3 = input.bool(false, "Show Labels", group="Round Numbers Set 3")

// ----------------------------------------
// HELPER FUNCTIONS
// ----------------------------------------

// Function to convert line style string to Pine style
getLineStyle(style) =>
    switch style
        "Solid" => line.style_solid
        "Dashed" => line.style_dashed
        "Dotted" => line.style_dotted
        => line.style_solid

// Function to determine if the number is a round number for given interval
isRoundNumber(num, interval) =>
    num % interval == 0

// Store lines in separate arrays for each set
var line[] linesArray1 = array.new_line()
var line[] linesArray2 = array.new_line()
var line[] linesArray3 = array.new_line()

// ----------------------------------------
// DRAWING LOGIC FOR MULTIPLE ROUND NUMBER SETS
// ----------------------------------------

// Function to clear and draw round number lines for a specific set
drawRoundNumberSet(linesArray, numLevels, priceInterval, extendLeft, extendRight, lineColor, lineStyle, lineWidth, showLabels, setName) =>
    if array.size(linesArray) > 0
        for i = array.size(linesArray) - 1 to 0
            line.delete(array.get(linesArray, i))
        array.clear(linesArray)

    // Get current price and calculate base price for this interval
    currentPrice = close
    basePrice = math.round(currentPrice / priceInterval) * priceInterval

    // Calculate start and end prices
    startPrice = basePrice - (numLevels * priceInterval)
    endPrice = basePrice + (numLevels * priceInterval)

    // Draw lines for this set
    for i = startPrice to endPrice by priceInterval
        if isRoundNumber(i, priceInterval)
            newLine = line.new(
                 x1=bar_index - extendLeft,
                 y1=i,
                 x2=bar_index + extendRight,
                 y2=i,
                 color=lineColor,
                 style=getLineStyle(lineStyle),
                 width=lineWidth)
            array.push(linesArray, newLine)

            // Add labels if enabled
            if showLabels
                label.new(
                     bar_index + extendRight,
                     i,
                     setName + ":" + str.tostring(i),
                     style=label.style_label_left,
                     color=color.new(lineColor, 100),
                     textcolor=color.new(lineColor, 0),
                     size=size.small)

// Main drawing logic - execute on last bar when round numbers are enabled
if barstate.islast and showRoundNumbers

    // Draw Round Numbers Set 1 (Major Levels)
    if showRN1
        drawRoundNumberSet(linesArray1, numLevels1, priceInterval1, extendLeft1, extendRight1,
                          lineColor1, lineStyle1, lineWidth1, showLabels1, "RN1")

    // Draw Round Numbers Set 2 (Minor Levels)
    if showRN2
        drawRoundNumberSet(linesArray2, numLevels2, priceInterval2, extendLeft2, extendRight2,
                          lineColor2, lineStyle2, lineWidth2, showLabels2, "RN2")

    // Draw Round Numbers Set 3 (Micro Levels)
    if showRN3
        drawRoundNumberSet(linesArray3, numLevels3, priceInterval3, extendLeft3, extendRight3,
                          lineColor3, lineStyle3, lineWidth3, showLabels3, "RN3")