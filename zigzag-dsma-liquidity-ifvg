// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// ©️ Dev Lucem

//@version=5
//@author=devlucem

// THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
// THE <PERSON><PERSON>ZAG SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
// I HOPE U LOVE IT

// Seek Menu

// indicator(title = 'Volumized Order Blocks | Ravi', overlay = true, max_boxes_count = vobMaxBoxesCount, max_labels_count = vobMaxBoxesCount, max_lines_count = vobMaxBoxesCount, max_bars_back = 5000)

const bool vobDEBUG = false
const int vobMaxBoxesCount = 500
const float vobOverlapThresholdPercentage = 0
const int vobMaxDistanceToLastBar = 1750 // Affects Running Time
const int vobMaxOrderBlocks = 30


import DevLucem/ZigLib/1 as ZigZag
indicator(title = 'zigzag-dsma-ifvg-liquidity', overlay = true, max_boxes_count = vobMaxBoxesCount, max_labels_count = vobMaxBoxesCount, max_lines_count = vobMaxBoxesCount, max_bars_back = 5000)


//@version=5
// indicator('Liquidity Heatmap (Ravi)', overlay=true, max_bars_back=500, max_lines_count=500, max_boxes_count=500, max_labels_count=500)

// --------------- INPUTS ---------------
var GRP1 = "•••••••••• INTRADAY TIMEFRAMES ••••••••••"
// 1
ltimeframe1Show = input.bool(true, title='', inline='1', group=GRP1)
ltimeframe1 = input.timeframe('15', title='', inline='1', group=GRP1)
lleftBars1 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP1, inline='1')
lrightBars1 = input.int(defval=7, title='Right', minval=2, maxval=20, group=GRP1, inline='1', tooltip="Highest/lowest point in x right and left bars.")

// 2
ltimeframe2Show = input.bool(true, title='', inline='2', group=GRP1)
ltimeframe2 = input.timeframe('30', title='', inline='2', group=GRP1)
lleftBars2 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP1, inline='2')
lrightBars2 = input.int(defval=7, title='Right', minval=2, maxval=20, group=GRP1, inline='2', tooltip="Highest/lowest point in x right and left bars.")

// 3
ltimeframe3Show = input.bool(true, title='', inline='3', group=GRP1)
ltimeframe3 = input.timeframe('60', title='', inline='3', group=GRP1)
lleftBars3 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP1, inline='3')
lrightBars3 = input.int(defval=6, title='Right', minval=2, maxval=20, group=GRP1, inline='3', tooltip="Highest/lowest point in x right and left bars.")

// 4
ltimeframe4Show = input.bool(true, title='', inline='4', group=GRP1)
ltimeframe4 = input.timeframe('120', title='', inline='4', group=GRP1)
lleftBars4 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP1, inline='4')
lrightBars4 = input.int(defval=6, title='Right', minval=2, maxval=20, group=GRP1, inline='4', tooltip="Highest/lowest point in x right and left bars.")

// 5
ltimeframe5Show = input.bool(true, title='', inline='5', group=GRP1)
ltimeframe5 = input.timeframe('240', title='', inline='5', group=GRP1)
lleftBars5 = input.int(defval=6, title='Left', minval=2, maxval=20, group=GRP1, inline='5')
lrightBars5 = input.int(defval=6, title='Right', minval=2, maxval=20, group=GRP1, inline='5', tooltip="Highest/lowest point in x right and left bars.")

// 6
ltimeframe6Show = input.bool(true, title='', inline='6', group=GRP1)
ltimeframe6 = input.timeframe('D', title='', inline='6', group=GRP1)
lleftBars6 = input.int(defval=5, title='Left', minval=2, maxval=20, group=GRP1, inline='6')
lrightBars6 = input.int(defval=5, title='Right', minval=2, maxval=20, group=GRP1, inline='6', tooltip="Highest/lowest point in x right and left bars.")


var GRP2 = "•••••••••• HIGHER TIMEFRAMES (> 4HR) ••••••••••"
// 1
htimeframe1Show = input.bool(true, title='', inline='1', group=GRP2)
htimeframe1 = input.timeframe('480', title='', inline='1', group=GRP2)
hleftBars1 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP2, inline='1')
hrightBars1 = input.int(defval=7, title='Right', minval=2, maxval=20, group=GRP2, inline='1', tooltip="Highest/lowest point in x right and left bars.")

// 2
htimeframe2Show = input.bool(true, title='', inline='2', group=GRP2)
htimeframe2 = input.timeframe('D', title='', inline='2', group=GRP2)
hleftBars2 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP2, inline='2')
hrightBars2 = input.int(defval=7, title='Right', minval=2, maxval=20, group=GRP2, inline='2', tooltip="Highest/lowest point in x right and left bars.")

// 3
htimeframe3Show = input.bool(true, title='', inline='3', group=GRP2)
htimeframe3 = input.timeframe('3D', title='', inline='3', group=GRP2)
hleftBars3 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP2, inline='3')
hrightBars3 = input.int(defval=6, title='Right', minval=2, maxval=20, group=GRP2, inline='3', tooltip="Highest/lowest point in x right and left bars.")

// 4
htimeframe4Show = input.bool(true, title='', inline='4', group=GRP2)
htimeframe4 = input.timeframe('W', title='', inline='4', group=GRP2)
hleftBars4 = input.int(defval=7, title='Left', minval=2, maxval=20, group=GRP2, inline='4')
hrightBars4 = input.int(defval=6, title='Right', minval=2, maxval=20, group=GRP2, inline='4', tooltip="Highest/lowest point in x right and left bars.")

// 5
htimeframe5Show = input.bool(true, title='', inline='5', group=GRP2)
htimeframe5 = input.timeframe('M', title='', inline='5', group=GRP2)
hleftBars5 = input.int(defval=6, title='Left', minval=2, maxval=20, group=GRP2, inline='5')
hrightBars5 = input.int(defval=6, title='Right', minval=2, maxval=20, group=GRP2, inline='5', tooltip="Highest/lowest point in x right and left bars.")

// 6
htimeframe6Show = input.bool(false, title='', inline='6', group=GRP2)
htimeframe6 = input.timeframe('2M', title='', inline='6', group=GRP2)
hleftBars6 = input.int(defval=5, title='Left', minval=2, maxval=20, group=GRP2, inline='6')
hrightBars6 = input.int(defval=5, title='Right', minval=2, maxval=20, group=GRP2, inline='6', tooltip="Highest/lowest point in x right and left bars.")

var GRP3 = "•••••••••• Other Settings ••••••••••"
hideLTF = input.bool(true, "Hide lines lower than enabled timeframes?", group = GRP3)
// --------------- INPUTS ---------------


// --------------- COLORS AND LENGTH ---------------
topColor1 = color.new(color.white, 70)
bottomColor1 = color.new(color.white, 70)
lineLength1 = 6

topColor2 = color.new(color.white, 60)
bottomColor2 = color.new(color.white, 60)
lineLength2 = 10

topColor3 = color.new(color.white, 50)
bottomColor3 = color.new(color.white, 50)
lineLength3 = 10

topColor4 = color.new(color.white, 40)
bottomColor4 = color.new(color.white, 40)
lineLength4 = 10

topColor5 = color.new(color.white, 30)
bottomColor5 = color.new(color.white, 30)
lineLength5 = 15

topColor6 = color.new(color.white, 20)
bottomColor6 = color.new(color.white, 20)
lineLength6 = 15
// --------------- COLORS AND LENGTH ---------------


// --------------- FUNCTIONS ---------------
getPivotData(lb, rb) =>
    ph = ta.pivothigh(lb, rb)
    phtimestart = ph ? time[rb-1] : na
    
    pl = ta.pivotlow(lb, rb)
    pltimestart = pl ? time[rb-1] : na
    
    [ph, phtimestart, pl, pltimestart]

getLineStyle(_style) =>
    _linestyle = _style == "Solid" ? line.style_solid : _style == "Dashed" ? line.style_dashed : line.style_dotted
    _linestyle

resolutionInMinutes(tf = "") =>
    chartTf = timeframe.multiplier * (timeframe.isseconds ? 1. / 60 : timeframe.isminutes ? 1. : timeframe.isdaily ? 60. * 24 : timeframe.isweekly ? 60. * 24 * 7 : timeframe.ismonthly ? 60. * 24 * 30.4375 : na)
    float result = tf == "" ? chartTf : request.security(syminfo.tickerid, tf, chartTf)

f_timeFrom(length, _units) =>
    int _timeFrom = na
    _unit = str.replace_all(_units, 's', '')
    _timeFrom := int(time + resolutionInMinutes() * 60 * 1000 * length)
    _timeFrom

notLowerTimeframe(tf) =>
    _cond = hideLTF ? resolutionInMinutes() < resolutionInMinutes(tf) : true
    _cond


//  ▓ ▒ ░ ░ 

generateText(_n = 5, _large = false) =>
    _symbol = "░"  
    _text = ""
    for i = _n to 0
        _text := _text + " "
    for i = _n to 0
        _text := _text + _symbol
    if _large
        _text := _text + "\n" + _text

    _text


generateArrow(_n = 5, _symbol, _large = false) =>  
    _text = ""
    for i = _n to 0
        _text := _text + " "
    for i = _n to 0
        _text := _text + _symbol
    if _large
        _text := _text + "\n" + _text

    _text
// --------------- FUNCTIONS ---------------

isLtf = resolutionInMinutes() < resolutionInMinutes("240")

// --------------- Calculate Pivots ---------------
[phchart, phtimestartchart, plchart, pltimestartchart] = request.security(syminfo.tickerid, "5", getPivotData(6, 6), lookahead = barmerge.lookahead_on)

[lph1, lphtimestart1, lpl1, lpltimestart1] = request.security(syminfo.tickerid, ltimeframe1, getPivotData(lleftBars1, lrightBars1), lookahead = barmerge.lookahead_on)
[lph2, lphtimestart2, lpl2, lpltimestart2] = request.security(syminfo.tickerid, ltimeframe2, getPivotData(lleftBars2, lrightBars2), lookahead = barmerge.lookahead_on)
[lph3, lphtimestart3, lpl3, lpltimestart3] = request.security(syminfo.tickerid, ltimeframe3, getPivotData(lleftBars3, lrightBars3), lookahead = barmerge.lookahead_on)
[lph4, lphtimestart4, lpl4, lpltimestart4] = request.security(syminfo.tickerid, ltimeframe4, getPivotData(lleftBars4, lrightBars4), lookahead = barmerge.lookahead_on)
[lph5, lphtimestart5, lpl5, lpltimestart5] = request.security(syminfo.tickerid, ltimeframe5, getPivotData(lleftBars5, lrightBars5), lookahead = barmerge.lookahead_on)
[lph6, lphtimestart6, lpl6, lpltimestart6] = request.security(syminfo.tickerid, ltimeframe6, getPivotData(lleftBars6, lrightBars6), lookahead = barmerge.lookahead_on)

[hph1, hphtimestart1, hpl1, hpltimestart1] = request.security(syminfo.tickerid, htimeframe1, getPivotData(hleftBars1, hrightBars1), lookahead = barmerge.lookahead_on)
[hph2, hphtimestart2, hpl2, hpltimestart2] = request.security(syminfo.tickerid, htimeframe2, getPivotData(hleftBars2, hrightBars2), lookahead = barmerge.lookahead_on)
[hph3, hphtimestart3, hpl3, hpltimestart3] = request.security(syminfo.tickerid, htimeframe3, getPivotData(hleftBars3, hrightBars3), lookahead = barmerge.lookahead_on)
[hph4, hphtimestart4, hpl4, hpltimestart4] = request.security(syminfo.tickerid, htimeframe4, getPivotData(hleftBars4, hrightBars4), lookahead = barmerge.lookahead_on)
[hph5, hphtimestart5, hpl5, hpltimestart5] = request.security(syminfo.tickerid, htimeframe5, getPivotData(hleftBars5, hrightBars5), lookahead = barmerge.lookahead_on)
[hph6, hphtimestart6, hpl6, hpltimestart6] = request.security(syminfo.tickerid, htimeframe6, getPivotData(hleftBars6, hrightBars6), lookahead = barmerge.lookahead_on)

ph1 = isLtf ? lph1 : hph1
phtimestart1 = isLtf ? lphtimestart1 : hphtimestart1
pl1 = isLtf ? lpl1 : hpl1
pltimestart1 = isLtf ? lpltimestart1 : hpltimestart1

ph2 = isLtf ? lph2 : hph2
phtimestart2 = isLtf ? lphtimestart2 : hphtimestart2
pl2 = isLtf ? lpl2 : hpl2
pltimestart2 = isLtf ? lpltimestart2 : hpltimestart2

ph3 = isLtf ? lph3 : hph3
phtimestart3 = isLtf ? lphtimestart3 : hphtimestart3
pl3 = isLtf ? lpl3 : hpl3
pltimestart3 = isLtf ? lpltimestart3 : hpltimestart3

ph4 = isLtf ? lph4 : hph4
phtimestart4 = isLtf ? lphtimestart4 : hphtimestart4
pl4 = isLtf ? lpl4 : hpl4
pltimestart4 = isLtf ? lpltimestart4 : hpltimestart4

ph5 = isLtf ? lph5 : hph5
phtimestart5 = isLtf ? lphtimestart5 : hphtimestart5
pl5 = isLtf ? lpl5 : hpl5
pltimestart5 = isLtf ? lpltimestart5 : hpltimestart5

ph6 = isLtf ? lph6 : hph6
phtimestart6 = isLtf ? lphtimestart6 : hphtimestart6
pl6 = isLtf ? lpl6 : hpl6
pltimestart6 = isLtf ? lpltimestart6 : hpltimestart6

pivothighchart = na(phchart[1]) and phchart ? phchart : na
pivotlowchart  = na(plchart[1]) and plchart ? plchart : na

pivothigh1 = na(ph1[1]) and ph1 ? ph1 : na
pivotlow1  = na(pl1[1]) and pl1 ? pl1 : na

pivothigh2 = na(ph2[1]) and ph2 ? ph2 : na
pivotlow2  = na(pl2[1]) and pl2 ? pl2 : na

pivothigh3 = na(ph3[1]) and ph3 ? ph3 : na
pivotlow3  = na(pl3[1]) and pl3 ? pl3 : na

pivothigh4 = na(ph4[1]) and ph4 ? ph4 : na
pivotlow4  = na(pl4[1]) and pl4 ? pl4 : na

pivothigh5 = na(ph5[1]) and ph5 ? ph5 : na
pivotlow5  = na(pl5[1]) and pl5 ? pl5 : na

pivothigh6 = na(ph6[1]) and ph6 ? ph6 : na
pivotlow6  = na(pl6[1]) and pl6 ? pl6 : na
// --------------- Calculate Pivots ---------------

//  --------------- Add to array ---------------
var float[] pivothighs1 = array.new_float(0)
var float[] pivotlows1 = array.new_float(0)

var float[] pivothighs2 = array.new_float(0)
var float[] pivotlows2 = array.new_float(0)

var float[] pivothighs3 = array.new_float(0)
var float[] pivotlows3 = array.new_float(0)

var float[] pivothighs4 = array.new_float(0)
var float[] pivotlows4 = array.new_float(0)

var float[] pivothighs5 = array.new_float(0)
var float[] pivotlows5 = array.new_float(0)

var float[] pivothighs6 = array.new_float(0)
var float[] pivotlows6 = array.new_float(0)
//  --------------- Add to array ---------------


// --------------- Plot pivot points ---------------
// if barstate.islast
//     label.new(bar_index, high, str.tostring(resolutionInMinutes()) +"\n"+ str.tostring(resolutionInMinutes("3")))
offset_block = phchart * 0.0002
offset_arrow = phchart * 0.0005
// ONLY LOW TIMEFRAME > 3
showTimeframe1 = isLtf ? ltimeframe1Show : htimeframe1Show
validTimeframe1 = isLtf ? notLowerTimeframe(ltimeframe1) : notLowerTimeframe(htimeframe1)

if showTimeframe1 and pivothighchart and resolutionInMinutes() <= resolutionInMinutes("3")
    label.new(phtimestartchart, y=phchart + offset_block, xloc=xloc.bar_time, text=generateText(12), style=label.style_none, textcolor=topColor1)
if showTimeframe1 and pivotlowchart and resolutionInMinutes() <= resolutionInMinutes("3")
    label.new(pltimestartchart, y=phchart + offset_block, xloc=xloc.bar_time, text=generateText(12), style=label.style_none, textcolor=bottomColor1)
    

// Timeframe 1
if showTimeframe1 and pivothigh1 and validTimeframe1
    label.new(phtimestart1, y=ph1 + offset_block, xloc=xloc.bar_time, text=generateText(lineLength1), style=label.style_none, textcolor=topColor1)
if showTimeframe1 and pivotlow1 and validTimeframe1
    label.new(pltimestart1, pl1, xloc=xloc.bar_time, text=generateText(lineLength1), style=label.style_none, textcolor=bottomColor1)

// Timeframe 2
showTimeframe2 = isLtf ? ltimeframe2Show : htimeframe2Show
validTimeframe2 = isLtf ? notLowerTimeframe(ltimeframe2) : notLowerTimeframe(htimeframe2)
if showTimeframe2 and pivothigh2 and validTimeframe2
    label.new(phtimestart2, ph2, xloc=xloc.bar_time, text=generateText(lineLength2), style=label.style_none, textcolor=topColor2)
if showTimeframe2 and pivotlow2 and validTimeframe2
    label.new(pltimestart2, pl2, xloc=xloc.bar_time, text=generateText(lineLength2), style=label.style_none, textcolor=bottomColor2)

// Timeframe 3
showTimeframe3 = isLtf ? ltimeframe3Show : htimeframe3Show
validTimeframe3 = isLtf ? notLowerTimeframe(ltimeframe3) : notLowerTimeframe(htimeframe3)
if showTimeframe3 and pivothigh3 and validTimeframe3
    label.new(phtimestart3, ph3, xloc=xloc.bar_time, text=generateText(lineLength3), style=label.style_none, textcolor=topColor3)
if showTimeframe3 and pivotlow3 and validTimeframe3
    label.new(pltimestart3, pl3, xloc=xloc.bar_time, text=generateText(lineLength3), style=label.style_none, textcolor=bottomColor3)

// Timeframe 4
showTimeframe4 = isLtf ? ltimeframe4Show : htimeframe4Show
validTimeframe4 = isLtf ? notLowerTimeframe(ltimeframe4) : notLowerTimeframe(htimeframe4)
if showTimeframe4 and pivothigh4 and validTimeframe4
    label.new(phtimestart4, ph4, xloc=xloc.bar_time, text=generateText(lineLength4), style=label.style_none, textcolor=topColor4)
if showTimeframe4 and pivotlow4 and validTimeframe4
    label.new(pltimestart4, pl4, xloc=xloc.bar_time, text=generateText(lineLength4), style=label.style_none, textcolor=bottomColor4)

// Timeframe 5
showTimeframe5 = isLtf ? ltimeframe5Show : htimeframe5Show
validTimeframe5 = isLtf ? notLowerTimeframe(ltimeframe5) : notLowerTimeframe(htimeframe5)
if showTimeframe5 and pivothigh5 and validTimeframe5
    label.new(phtimestart5, ph5, xloc=xloc.bar_time, text=generateText(lineLength5, true), style=label.style_none, textcolor=topColor5)
if showTimeframe5 and pivotlow5 and validTimeframe5
    label.new(pltimestart5, pl5, xloc=xloc.bar_time, text=generateText(lineLength5, true), style=label.style_none, textcolor=bottomColor5)

// Timeframe 6
showTimeframe6 = isLtf ? ltimeframe6Show : htimeframe6Show
validTimeframe6 = isLtf ? notLowerTimeframe(ltimeframe6) : notLowerTimeframe(htimeframe6)
if showTimeframe6 and pivothigh6 and validTimeframe6
    label.new(phtimestart6, ph6, xloc=xloc.bar_time, text=generateText(lineLength6, true), style=label.style_none, textcolor=topColor6)
if showTimeframe6 and pivotlow6 and validTimeframe6
    label.new(pltimestart6, pl6, xloc=xloc.bar_time, text=generateText(lineLength6, true), style=label.style_none, textcolor=bottomColor6)
// --------------- Plot pivot points ---------------


// --------------- Equal highs ---------------
// --------------- FUNCTIONS ---------------



// indicator('zigzag-dsma-vob', 'Gold-ZigZag++ [LD]', true, format.price, max_labels_count=200, max_lines_count=50)
bool show_zigzag = input.bool(false, title="Show ZigZag", group="ZigZag Config")

// Fetch Ingredients 
zigzagDepth = input.int(2, 'Depth', minval=1, step=1, group="ZigZag Config")
zigzagDeviation = input.int(5, 'Deviation', minval=1, step=1, group="ZigZag Config")
zigzagBackstep = input.int(2, 'Backstep', minval=2, step=1, group="ZigZag Config")
zigzagLine_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group="Lines")
zigzagLabels = input(0, "Labels Transparency", group="Labels")
zigzagUpcolor = input(color.lime, 'Bull Color', group="Colors")
zigzagDncolor = input(color.red, 'Bear Color', group="Colors")
zigzagLines = input(0, "Lines Transparency", group="Lines")
// zigzagBackground = input(90, "Background Transparency", group="Colors")
zigzagLabel_size = switch input.int(3, "Label Size", minval=1, maxval=5, group="Labels")
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
zigzagRepaint = input(true, 'Repaint Levels')
zigzagExtend = input(false, "Extend ZigZag", group="Lines")

// Bake it with a simple oven this time
[zigzagDirection, zigzagZ1, zigzagZ2] = ZigZag.zigzag(low, high, zigzagDepth, zigzagDeviation, zigzagBackstep)
zigzagNowPoint = ""
var float zigzagLastPoint = na
var bool zigzagWaiting_for_ll = false  // Flag to indicate we're waiting for a LL after seeing a HH
var bool zigzagWaiting_for_hh = false  // Flag to indicate we're waiting for a HH after seeing a LL

if bool(ta.change(zigzagDirection))
    zigzagLastPoint := zigzagZ1.price[1]

// Let it Cool And Serve

line zigzagZZ = na
label zigzagPoint = na
label zigzagArrow_ll = na  // Arrow for LL
label zigzagArrow_hh = na  // Arrow for HH
label zigzagBuy_signal = na  // Label for buy signal
label zigzagSell_signal = na  // Label for sell signal

if show_zigzag and zigzagRepaint
    zigzagZZ := line.new(zigzagZ1, zigzagZ2, xloc.bar_time, zigzagExtend ? extend.right : extend.none, color.new(zigzagDirection > 0 ? zigzagUpcolor : zigzagDncolor, zigzagLines), width=zigzagLine_thick)
    zigzagNowPoint := zigzagDirection < 0 ? (zigzagZ2.price < zigzagLastPoint ? "LL" : "HL") : (zigzagZ2.price > zigzagLastPoint ? "HH" : "LH")
    
    // Track HH and LL sequence
    if (zigzagNowPoint == "HH")
        if zigzagWaiting_for_hh
            // Detected HH after LL, so plot sell signal and reset flag
            zigzagSell_signal := label.new(zigzagZ2, text="SELL", xloc=xloc.bar_time, yloc=yloc.abovebar, color=color.new(color.red, 0), size=size.large, style=label.style_label_down)
            zigzagWaiting_for_hh := false
        zigzagWaiting_for_ll := true  // Start waiting for LL after HH
    else if (zigzagNowPoint == "LL")
        if zigzagWaiting_for_ll
            // Detected LL after HH, so plot buy signal and reset flag
            zigzagBuy_signal := label.new(zigzagZ2, text="BUY", xloc=xloc.bar_time, yloc=yloc.belowbar, color=color.new(color.green, 0), size=size.large, style=label.style_label_up)
            zigzagWaiting_for_ll := false
        zigzagWaiting_for_hh := true  // Start waiting for HH after LL
    else
        // For HL and LH, reset flags
        zigzagWaiting_for_ll := false
        zigzagWaiting_for_hh := false
        
    // if (zigzagNowPoint == "LL" or zigzagNowPoint == "HH" or zigzagNowPoint == "LH" or zigzagNowPoint == "HL")  // Include LH and HL in labels
    if (zigzagNowPoint == "LL" or zigzagNowPoint == "HH")  // Include LH and HL in labels
        zigzagPoint := label.new(zigzagZ2, zigzagNowPoint, xloc.bar_time, yloc.price, 
         color.new(zigzagDirection < 0 ? zigzagUpcolor : zigzagDncolor, zigzagLabels), zigzagDirection > 0 ? label.style_label_down : label.style_label_up, color.new(zigzagDirection > 0 ? zigzagUpcolor : zigzagDncolor, zigzagLabels), zigzagLabel_size)

        // Draw arrow for LL and HH
        if (zigzagNowPoint == "LL")
            zigzagArrow_ll := label.new(zigzagZ2, text="↑", xloc=xloc.bar_time, yloc=yloc.belowbar, color=color.new(zigzagUpcolor, 0), size=size.large, style=label.style_label_up)
        if (zigzagNowPoint == "HH")
            zigzagArrow_hh := label.new(zigzagZ2, text="↓", xloc=xloc.bar_time, yloc=yloc.abovebar, color=color.new(zigzagDncolor, 0), size=size.large, style=label.style_label_down)

    if zigzagDirection == zigzagDirection[1]
        line.delete(zigzagZZ[1])
        label.delete(zigzagPoint[1])
        label.delete(zigzagArrow_ll[1])  // Delete previous arrow if repaint happens
        label.delete(zigzagArrow_hh[1])  // Delete previous arrow if repaint happens
        label.delete(zigzagBuy_signal[1])  // Delete previous buy signal if repaint happens
        label.delete(zigzagSell_signal[1])  // Delete previous sell signal if repaint happens
    else
        line.set_extend(zigzagZZ[1], extend.none)
else
    if show_zigzag and zigzagDirection != zigzagDirection[1]
        zigzagZZ := line.new(zigzagZ1[1], zigzagZ2[1], xloc.bar_time, extend.none, color.new(zigzagDirection[1] > 0 ? zigzagUpcolor : zigzagDncolor, zigzagLines), width=zigzagLine_thick)
        zigzagNowPoint := zigzagDirection[1] < 0 ? (zigzagZ2.price[1] < zigzagLastPoint[1] ? "LL" : "HL") : (zigzagZ2.price[1] > zigzagLastPoint[1] ? "HH" : "LH")
        
        // Track HH and LL sequence
        if (zigzagNowPoint == "HH")
            if zigzagWaiting_for_hh
                // Detected HH after LL, so plot sell signal and reset flag
                zigzagSell_signal := label.new(zigzagZ2[1], text="SELL", xloc=xloc.bar_time, yloc=yloc.abovebar, color=color.new(color.red, 0), size=size.large, style=label.style_label_down)
                zigzagWaiting_for_hh := false
            zigzagWaiting_for_ll := true  // Start waiting for LL after HH
        else if (zigzagNowPoint == "LL")
            if zigzagWaiting_for_ll
                // Detected LL after HH, so plot buy signal and reset flag
                zigzagBuy_signal := label.new(zigzagZ2[1], text="BUY", xloc=xloc.bar_time, yloc=yloc.belowbar, color=color.new(color.green, 0), size=size.large, style=label.style_label_up)
                zigzagWaiting_for_ll := false
            zigzagWaiting_for_hh := true  // Start waiting for HH after LL
        else
            // For HL and LH, reset flags
            zigzagWaiting_for_ll := false
            zigzagWaiting_for_hh := false

        if (zigzagNowPoint == "LL" or zigzagNowPoint == "HH" or zigzagNowPoint == "LH" or zigzagNowPoint == "HL")  // Include LH and HL in labels
            zigzagPoint := label.new(zigzagZ2[1], zigzagNowPoint, xloc.bar_time, yloc.price, 
             color.new(zigzagDirection[1] < 0 ? zigzagUpcolor : zigzagDncolor, zigzagLabels), zigzagDirection[1] > 0 ? label.style_label_down : label.style_label_up, color.new(zigzagDirection[1] > 0 ? zigzagUpcolor : zigzagDncolor, zigzagLabels), zigzagLabel_size)

            // Draw arrow for LL and HH
            if (zigzagNowPoint == "LL")
                zigzagArrow_ll := label.new(zigzagZ2[1], text="↑", xloc=xloc.bar_time, yloc=yloc.belowbar, color=color.new(zigzagUpcolor, 0), size=size.large, style=label.style_label_up)
            if (zigzagNowPoint == "HH")
                zigzagArrow_hh := label.new(zigzagZ2[1], text="↓", xloc=xloc.bar_time, yloc=yloc.abovebar, color=color.new(zigzagDncolor, 0), size=size.large, style=label.style_label_down)

// bgcolor(zigzagDirection < 0 ? color.new(zigzagDncolor, zigzagBackground) : color.new(zigzagUpcolor, zigzagBackground), title='Direction Background')
plotarrow(zigzagDirection, "direction", display=display.status_line)

// Create alert conditions
zigzagBuy_condition = (zigzagNowPoint == "LL" and zigzagWaiting_for_ll)
zigzagSell_condition = (zigzagNowPoint == "HH" and zigzagWaiting_for_hh)

alertcondition(zigzagBuy_condition, "Zigzag Buy Signal", 'Buy Signal Detected: LL after HH')
alertcondition(zigzagSell_condition, "Zigzag Sell Signal", 'Sell Signal Detected: HH after LL')

// Existing alert conditions
alertcondition(zigzagNowPoint == "HH" and zigzagZ2.price != zigzagZ2.price[1], "New Higher High", 'Zigzag on {{ticker}} new higher high detected at {{time}}')
alertcondition(zigzagNowPoint == "LL" and zigzagZ2.price != zigzagZ2.price[1], "New Lower Low", 'Zigzag on {{ticker}} new lower low detected at {{time}}')
alertcondition(zigzagNowPoint == "LH" and zigzagZ2.price != zigzagZ2.price[1], "New Lower High", 'Zigzag on {{ticker}} new lower high detected at {{time}}')
alertcondition(zigzagNowPoint == "HL" and zigzagZ2.price != zigzagZ2.price[1], "New Higher Low", 'Zigzag on {{ticker}} new higher low detected at {{time}}')
alertcondition(zigzagDirection != zigzagDirection[1], 'Direction Changed', 'Zigzag on {{ticker}} direction changed at {{time}}')
alertcondition(zigzagDirection != zigzagDirection[1] and zigzagDirection > 0, 'Bullish Direction', 'Zigzag on {{ticker}} bullish direction at {{time}}')
alertcondition(zigzagDirection != zigzagDirection[1] and zigzagDirection < 0, 'Bearish Direction', 'Zigzag on {{ticker}} bearish direction at {{time}}')






//dsma


//@version=5
// indicator("Multi Deviation Scaled Moving Average [Ravi] - Extended", "Multi DSMA - [Ravi] - Ext", overlay=true)

// ---------------------------------------------------------------------------------------------------------------------
// USER INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

int dsma_period = input.int(30, title="Period")
int dsma_step = 100 - input.int(60, "Sensitivity", minval = 0, maxval = 100, tooltip = "The Lower input Lower sensitivity")
series float dsma_src = hlc3

// Timeframe Inputs for DSMA
bool dsma_show_tf1 = input.bool(true, title="Show 3-Minute DSMA")
dsma_tf1 = input.timeframe('3', "3-Minute Timeframe")

bool dsma_show_tf2 = input.bool(true, title="Show 5-Minute DSMA")
dsma_tf2 = input.timeframe('5', "5-Minute Timeframe")

bool dsma_show_tf3 = input.bool(true, title="Show 15-Minute DSMA")
dsma_tf3 = input.timeframe('15', "15-Minute Timeframe")

bool dsma_show_tf4 = input.bool(true, title="Show 1-Hour DSMA")
dsma_tf4 = input.timeframe('60', "1-Hour Timeframe")

bool dsma_show_tf5 = input.bool(true, title="Show 4-Hour DSMA")
dsma_tf5 = input.timeframe('240', "4-Hour Timeframe")

bool dsma_show_tf6 = input.bool(true, title="Show 1-Day DSMA")
dsma_tf6 = input.timeframe('D', "1-Day Timeframe")

// Configurable Colors for upper and lower for each timeframe
color dsma_upper_color1 = input.color(#41a1ce, "3-Minute Upper Color")
color dsma_down_color1 = input.color(#ce8541, "3-Minute Down Color")
color dsma_upper_color2 = input.color(#41a1ce, "5-Minute Upper Color")
color dsma_down_color2 = input.color(#ce8541, "5-Minute Down Color")
color dsma_upper_color3 = input.color(#41a1ce, "15-Minute Upper Color")
color dsma_down_color3 = input.color(#ce8541, "15-Minute Down Color")
color dsma_upper_color4 = input.color(#41a1ce, "1-Hour Upper Color")
color dsma_down_color4 = input.color(#ce8541, "1-Hour Down Color")
color dsma_upper_color5 = input.color(#41a1ce, "4-Hour Upper Color")
color dsma_down_color5 = input.color(#ce8541, "4-Hour Down Color")
color dsma_upper_color6 = input.color(#41a1ce, "1-Day Upper Color")
color dsma_down_color6 = input.color(#ce8541, "1-Day Down Color")

// Colors for each timeframe's DSMA
color dsma_dsma1_color = color.new(#1f77b4, 0) // 3-Minute DSMA Color
color dsma_dsma2_color = color.new(#ff7f0e, 0) // 5-Minute DSMA Color
color dsma_dsma3_color = color.new(#2ca02c, 0) // 15-Minute DSMA Color
color dsma_dsma4_color = color.new(#d62728, 0) // 1-Hour DSMA Color
color dsma_dsma5_color = color.new(#9467bd, 0) // 4-Hour DSMA Color
color dsma_dsma6_color = color.new(#8c564b, 0) // 1-Day DSMA Color

// ---------------------------------------------------------------------------------------------------------------------
// INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

// Function to calculate the Deviation Scaled Moving Average (DSMA)
dsma(src, int dsma_period)=>
    var float dsma_a1 = 0.0
    var float dsma_b1 = 0.0
    var float dsma_c1 = 0.0
    var float dsma_c2 = 0.0
    var float dsma_c3 = 0.0
    var float dsma_filt = 0.0
    var float dsma_dsma = 0.0
    var float dsma_s = 0.0

    if barstate.isfirst
        dsma_pi = 3.1415926535897932
        dsma_g = math.sqrt(2)
        dsma_s := 2 * dsma_pi / dsma_period
        dsma_a1 := math.exp(-dsma_g * dsma_pi / (0.5 * dsma_period))
        dsma_b1 := 2 * dsma_a1 * math.cos(dsma_g * dsma_s / (0.5 * dsma_period))
        dsma_c2 := dsma_b1
        dsma_c3 := -dsma_a1 * dsma_a1
        dsma_c1 := 1 - dsma_c2 - dsma_c3

    dsma_zeros = (close - close[2])
    dsma_filt := dsma_c1 * (dsma_zeros + dsma_zeros[1]) / 2 + dsma_c2 * nz(dsma_filt[1]) + dsma_c3 * nz(dsma_filt[2])

    dsma_rms = math.sqrt(ta.ema(math.pow(dsma_filt, 2), dsma_period))
    dsma_scaled_filt = dsma_rms != 0 ? dsma_filt / dsma_rms : 0
    dsma_alpha1 = math.abs(dsma_scaled_filt) * 5 / dsma_period
    dsma_dsma := dsma_alpha1 * close + (1 - dsma_alpha1) * nz(dsma_dsma[1])

    dsma_dsma

// Function to calculate trend percentage, color, and average DSMA
dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color, dsma_down_color)=>
    dsma_dsma_arr = array.new<float>()

    dsma_length = dsma_period
    dsma1 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma2 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma3 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma4 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma5 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma6 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma7 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma8 = dsma(dsma_src, dsma_length)

    array.push(dsma_dsma_arr, dsma1)
    array.push(dsma_dsma_arr, dsma2)
    array.push(dsma_dsma_arr, dsma3)
    array.push(dsma_dsma_arr, dsma4)
    array.push(dsma_dsma_arr, dsma5)
    array.push(dsma_dsma_arr, dsma6)
    array.push(dsma_dsma_arr, dsma7)
    array.push(dsma_dsma_arr, dsma8)

    dsma_val = 0.14285714285714285714285714285714

    dsma_score = 0.
    for dsma_i = 0 to array.size(dsma_dsma_arr) - 1
        dsma_dsma = array.get(dsma_dsma_arr, dsma_i)
        if dsma_dsma > array.get(dsma_dsma_arr, 7)
            dsma_score += dsma_val

    dsma_color =  dsma_score > 0.5 
                 ? color.from_gradient(dsma_score, 0.5, 1, na, dsma_upper_color) 
                 : color.from_gradient(dsma_score, 0, 0.5, dsma_down_color, na)

    [dsma_score, array.avg(dsma_dsma_arr), dsma_color]

// Apply the multi-timeframe logic using `request.security` for all timeframes
[dsma_score1, dsma_ma1, dsma_color1] = request.security(syminfo.tickerid, dsma_tf1, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color1, dsma_down_color1))
[dsma_score2, dsma_ma2, dsma_color2] = request.security(syminfo.tickerid, dsma_tf2, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color2, dsma_down_color2))
[dsma_score3, dsma_ma3, dsma_color3] = request.security(syminfo.tickerid, dsma_tf3, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color3, dsma_down_color3))
[dsma_score4, dsma_ma4, dsma_color4] = request.security(syminfo.tickerid, dsma_tf4, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color4, dsma_down_color4))
[dsma_score5, dsma_ma5, dsma_color5] = request.security(syminfo.tickerid, dsma_tf5, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color5, dsma_down_color5))
[dsma_score6, dsma_ma6, dsma_color6] = request.security(syminfo.tickerid, dsma_tf6, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color6, dsma_down_color6))

// Detect crossovers for signal generation
dsma_cross_up1 = ta.crossover(dsma_score1, 0.3)
dsma_cross_dn1 = ta.crossunder(dsma_score1, 0.7)

dsma_cross_up2 = ta.crossover(dsma_score2, 0.3)
dsma_cross_dn2 = ta.crossunder(dsma_score2, 0.7)

dsma_cross_up3 = ta.crossover(dsma_score3, 0.3)
dsma_cross_dn3 = ta.crossunder(dsma_score3, 0.7)

dsma_cross_up4 = ta.crossover(dsma_score4, 0.3)
dsma_cross_dn4 = ta.crossunder(dsma_score4, 0.7)

dsma_cross_up5 = ta.crossover(dsma_score5, 0.3)
dsma_cross_dn5 = ta.crossunder(dsma_score5, 0.7)

dsma_cross_up6 = ta.crossover(dsma_score6, 0.3)
dsma_cross_dn6 = ta.crossunder(dsma_score6, 0.7)

// ---------------------------------------------------------------------------------------------------------------------
// VISUALIZATION
// ---------------------------------------------------------------------------------------------------------------------

// Detect crossovers for signal generation






// 3-Minute DSMA
plot(dsma_show_tf1 ? dsma_ma1 : na, color = dsma_color1, linewidth = 2)
plotshape(dsma_show_tf1 and dsma_cross_up1 ? dsma_ma1 : na, title="3-Min Up", location=location.absolute, color=color.new(dsma_upper_color1, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf1 and dsma_cross_dn1 ? dsma_ma1 : na, title="3-Min Down", location=location.absolute, color=color.new(dsma_down_color1, 50), size=size.small, style=shape.diamond)


// //3-Minute Plot buy and sell signals
// plotshape(dsma_show_tf1 and dsma_cross_up1, title="3m-buy signal", location=location.belowbar, color=#c3eac4, size=size.small, style=shape.labelup, text="3-ma-↑")
// plotshape(dsma_show_tf1 and dsma_cross_dn1, title="3m-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="3-ma-↓")

//3-Minute alerts
alertcondition(dsma_show_tf1 and dsma_cross_up1, "3m-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf1 and dsma_cross_dn1, "3m-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 5-Minute DSMA
plot(dsma_show_tf2 ? dsma_ma2 : na, color = dsma_color2, linewidth = 2)
plotshape(dsma_show_tf2 and dsma_cross_up2 ? dsma_ma2 : na, title="5-Min Up", location=location.absolute, color=color.new(dsma_upper_color2, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf2 and dsma_cross_dn2 ? dsma_ma2 : na, title="5-Min Down", location=location.absolute, color=color.new(dsma_down_color2, 50), size=size.small, style=shape.diamond)

// //5-Minute Plot buy and sell signals
// plotshape(dsma_show_tf2 and dsma_cross_up2, title="5m-buy signal", location=location.belowbar, color=#c5eac4, size=size.small, style=shape.labelup, text="5m-dsma-↑")
// plotshape(dsma_show_tf2 and dsma_cross_dn2, title="5m-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="5m-dsma-↓")

//5-Minute alerts
alertcondition(dsma_show_tf2 and dsma_cross_up2, "5m-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf2 and dsma_cross_dn2, "5m-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 15-Minute DSMA
plot(dsma_show_tf3 ? dsma_ma3 : na, color = dsma_color3, linewidth = 2)
plotshape(dsma_show_tf3 and dsma_cross_up3 ? dsma_ma3 : na, title="15-Min Up", location=location.absolute, color=color.new(dsma_upper_color3, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf3 and dsma_cross_dn3 ? dsma_ma3 : na, title="15-Min Down", location=location.absolute, color=color.new(dsma_down_color3, 50), size=size.small, style=shape.diamond)

//15-Minute Plot buy and sell signals
// plotshape(dsma_show_tf3 and dsma_cross_up3, title="15m-buy signal", location=location.belowbar, color=#c5eac4, size=size.small, style=shape.labelup, text="15m-dsma-↑")
// plotshape(dsma_show_tf3 and dsma_cross_dn3, title="15m-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="15m-dsma-↓")

//15-Minute alerts
alertcondition(dsma_show_tf3 and dsma_cross_up3, "15m-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf3 and dsma_cross_dn3, "15m-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 1-Hour DSMA
plot(dsma_show_tf4 ? dsma_ma4 : na, color = dsma_color4, linewidth = 2)
plotshape(dsma_show_tf4 and dsma_cross_up4 ? dsma_ma4 : na, title="1-Hour Up", location=location.absolute, color=color.new(dsma_upper_color4, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf4 and dsma_cross_dn4 ? dsma_ma4 : na, title="1-Hour Down", location=location.absolute, color=color.new(dsma_down_color4, 50), size=size.small, style=shape.diamond)


//1-Hout Plot buy and sell signals
// plotshape(dsma_show_tf4 and dsma_cross_up4, title="1h-buy signal", location=location.belowbar, color=#c5eac4, size=size.small, style=shape.labelup, text="1h-dsma-↑")
// plotshape(dsma_show_tf4 and dsma_cross_dn4, title="1h-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="1h-dsma-↓")

//1-Hour alerts
alertcondition(dsma_show_tf4 and dsma_cross_up4, "1h-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf4 and dsma_cross_dn4, "1h-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 4-Hour DSMA
plot(dsma_show_tf5 ? dsma_ma5 : na, color = dsma_color5, linewidth = 2)
plotshape(dsma_show_tf5 and dsma_cross_up5 ? dsma_ma5 : na, title="4-Hour Up", location=location.absolute, color=color.new(dsma_upper_color5, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf5 and dsma_cross_dn5 ? dsma_ma5 : na, title="4-Hour Down", location=location.absolute, color=color.new(dsma_down_color5, 50), size=size.small, style=shape.diamond)

// 1-Day DSMA
plot(dsma_show_tf6 ? dsma_ma6 : na, color = dsma_color6, linewidth = 2)
plotshape(dsma_show_tf6 and dsma_cross_up6 ? dsma_ma6 : na, title="1-Day Up", location=location.absolute, color=color.new(dsma_upper_color6, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf6 and dsma_cross_dn6 ? dsma_ma6 : na, title="1-Day Down", location=location.absolute, color=color.new(dsma_down_color6, 50), size=size.small, style=shape.diamond)





// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// ©LuxAlgo




// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// ©LuxAlgo

//@version=5
// indicator("Inversion Fair Value Gaps (IFVG) [LuxAlgo]", "LuxAlgo - Inversion Fair Value Gaps (IFVG)", overlay = true, max_boxes_count = 500, max_lines_count = 500, max_labels_count = 500)
//---------------------------------------------------------------------------------------------------------------------}
//Settings
//---------------------------------------------------------------------------------------------------------------------{
disp_num = input.int(5, maxval = 100, minval = 1, title = "Show Last", tooltip = "Specifies the amount of most recent inversion FVG to display in Bullish/Bearish pairs, starting at the current and looking back.")
signal_pref = input.string("Close", title = "Signal Preference", options = ["Close","Wick"], tooltip = "Choose to send signals based on Wicks or Close Price.")
wt = signal_pref == "Wick"
atr_multi = input.float(0.25, step = 0.25,minval = 0,  title = "ATR Multiplier", tooltip = "Filters FVGs based on ATR Width, Only displays Inversions that are Greater-Than the ATR*Multiplier.")

//Colors
green = input.color(color.new(#089981, 80), title = "Bull Color", group = "Colors")
red = input.color(color.new(#f23645, 80), title = "Bear Color", group = "Colors")
gray = input.color(#787b86, title = "Midline Color", group = "Colors")
invis = color.rgb(0,0,0,100)

//---------------------------------------------------------------------------------------------------------------------}
//UDT's
//---------------------------------------------------------------------------------------------------------------------{
type lab //Contains Necessary Label Data to Send to Label Function
    int x
    float y
    int dir

type fvg //Contains Necessary FVG Data to Send to Chart.
    int left = na
    float top = na
    int right = na
    float bot = na
    float mid = na
    int dir = na
    int state = na
    array<lab> labs = na
    int x_val = na

//---------------------------------------------------------------------------------------------------------------------}
//Functions
//---------------------------------------------------------------------------------------------------------------------{
//Basic Calcs
buffer = 100 //How many FVGs to keep in memory.
c_top = math.max(open,close)
c_bot = math.min(open,close)

label_maker(_x,_y,_dir) => //Used for making Labels
    switch
        _dir == 1 => label.new(_x,_y,"\n▲", style = label.style_text_outline, color = invis,  textcolor = color.new(green,0), size = size.small, xloc = xloc.bar_time)
        _dir == -1 => label.new(_x,_y, "▼\n", style = label.style_text_outline, color = invis,  textcolor = color.new(red,0), size = size.small, xloc = xloc.bar_time)

fvg_manage(_ary,_inv_ary) => //First step filtering of FVG data, Not all FVGs will be displayed, only inversions.
    if _ary.size() >= buffer
        _ary.shift()

    if _ary.size() > 0
        for i = _ary.size()-1 to 0
            value = _ary.get(i)
            _dir = value.dir

            if _dir == 1 and (c_bot < value.bot)
                value.x_val := time
                _inv_ary.push(_ary.remove(i))
            if _dir == -1 and (c_top > value.top)
                value.x_val := time
                _inv_ary.push(_ary.remove(i))
            

inv_manage(_ary) => //All inversions will be displayed.
    fire = false
    if _ary.size() >= buffer
        _ary.shift()
    if _ary.size() > 0
        for i = _ary.size()-1 to 0
            value = _ary.get(i)
            bx_top = value.top
            bx_bot = value.bot
            _dir = value.dir
            st = value.state

            if (st == 0 and _dir == 1)
                value.state := 1
                value.dir := -1
            if (_dir == -1 and st == 0)
                value.state := 1
                value.dir := 1
            if st >= 1
                value.right := time
            if (_dir == -1 and st == 1 and close < bx_bot and (wt?high:close[1]) >= bx_bot and (wt?high:close[1]) < bx_top)
                value.labs.push(lab.new(time,bx_top,-1))
                fire := true
            if (_dir == 1 and st == 1 and close > bx_top and (wt?low:close[1]) <= bx_top and (wt?low:close[1]) > bx_bot)
                value.labs.push(lab.new(time,bx_bot,1))
                fire := true
            if st >= 1 and ((_dir == -1 and c_top > bx_top) or (_dir == 1 and c_bot < bx_bot))
                _ary.remove(i)
            
    fire

send_it(_ary) => // Draws Everything on the Chart
    last_index = _ary.size()-1
    for [index,value] in _ary
        bx_top = value.top
        bx_bot = value.bot
        bx_left = value.left
        xval = value.x_val
        mid = value.mid
        col = value.dir == -1 ? green : red
        o_col = value.dir == -1 ? red : green

        if index > last_index - disp_num
            box.new(bx_left,bx_top,xval,bx_bot,bgcolor = col, border_color = invis, xloc = xloc.bar_time)
            box.new(xval,bx_top,time,bx_bot, bgcolor = o_col, border_color = invis, xloc = xloc.bar_time)

            line.new(bx_left,mid,time,mid, color = gray, style = line.style_dashed, xloc = xloc.bar_time)
            box.new(bar_index,bx_top,bar_index+50,bx_bot, bgcolor = o_col, border_color = invis)
            line.new(bar_index,mid,bar_index+50,mid, color = gray, style = line.style_dashed)

            for stuff in value.labs
                label_maker(stuff.x,stuff.y,stuff.dir)

//---------------------------------------------------------------------------------------------------------------------}
//Delete drawings
//---------------------------------------------------------------------------------------------------------------------{
for boxes in box.all
    box.delete(boxes)

for lines in line.all
    line.delete(lines)

for labels in label.all
    label.delete(labels)

//---------------------------------------------------------------------------------------------------------------------}
//Data Arrays
//---------------------------------------------------------------------------------------------------------------------{
var bull_fvg_ary = array.new<fvg>(na) // FVG Data, Not all will be Drawn
var bear_fvg_ary = array.new<fvg>(na)

var bull_inv_ary = array.new<fvg>(na) // Inversion Data, All will be Drawn
var bear_inv_ary = array.new<fvg>(na)

//---------------------------------------------------------------------------------------------------------------------}
//FVG Detection
//---------------------------------------------------------------------------------------------------------------------{
atr = nz(ta.atr(200)*atr_multi, ta.cum(high - low) / (bar_index+1))

fvg_up = (low > high[2]) and (close[1] > high[2])
fvg_down = (high < low[2]) and (close[1] < low[2])

if fvg_up and math.abs(low-high[2]) > atr
    array.push(bull_fvg_ary,fvg.new(time[1], low, time, high[2], math.avg(low,high[2]), 1, 0,array.new<lab>(na),na))

if fvg_down and math.abs(low[2]-high) > atr
    array.push(bear_fvg_ary,fvg.new(time[1], low[2], time, high, math.avg(high,low[2]),-1 ,0,array.new<lab>(na),na))

//---------------------------------------------------------------------------------------------------------------------}
//Running Functions
//---------------------------------------------------------------------------------------------------------------------{
// FVG_Data -> Inversion_Data -> Chart
fvg_manage(bull_fvg_ary,bull_inv_ary)
fvg_manage(bear_fvg_ary,bear_inv_ary)

bear_signal = inv_manage(bull_inv_ary)
bull_signal = inv_manage(bear_inv_ary)

if barstate.islast
    send_it(bull_inv_ary)
    send_it(bear_inv_ary)

//Alert Options
alertcondition(bull_signal, "Bullish Signal")
alertcondition(bear_signal, "Bearish Signal")

//---------------------------------------------------------------------------------------------------------------------}