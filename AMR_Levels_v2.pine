//@version=5
indicator("AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500, max_bars_back=5000)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
timezone = input.string("America/New_York", "Time Zone", group=group_settings)
max_periods = input.int(3, "Months to Display", minval=1, maxval=30, tooltip="Number of months to display", group=group_settings)
extend_right = input.int(50, "Extend Lines Right (bars)", minval=0, maxval=500, group=group_settings)

// AMR Settings
group_amr = "AMR Settings"
amr_months = input.int(6, "Months to Average", minval=1, maxval=24, group=group_amr)
show_full_amr = input.bool(true, "Show Full AMR", group=group_amr)
show_one_third_amr = input.bool(true, "Show 1/3 AMR", group=group_amr)
show_two_third_amr = input.bool(false, "Show 2/3 AMR", group=group_amr)
show_half_amr = input.bool(false, "Show 1/2 AMR", group=group_amr)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Month Open", group=group_line)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(false, "Show Price in Label", group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=group_label)

// Color Settings
group_color = "Color Settings"
true_month_open_color = input.color(color.white, "True Month Open Line", group=group_color)
full_amr_color = input.color(color.white, "Full AMR", group=group_color)
one_third_amr_color = input.color(color.white, "1/3 AMR", group=group_color)
two_third_amr_color = input.color(color.white, "2/3 AMR", group=group_color)
half_amr_color = input.color(color.white, "1/2 AMR", group=group_color)

// === VARIABLES ===
// Convert line style input to Pine style
var line_style_value = line.style_solid
if line_style == "Dashed"
    line_style_value := line.style_dashed
else if line_style == "Dotted"
    line_style_value := line.style_dotted

// Convert label size input to Pine size
var label_size_value = size.small
if label_size == "Tiny"
    label_size_value := size.tiny
else if label_size == "Normal"
    label_size_value := size.normal
else if label_size == "Large"
    label_size_value := size.large

// Arrays to store lines and labels
var true_month_open_lines = array.new_line(1, na)
var true_month_open_labels = array.new_label(1, na)
var full_amr_up_lines = array.new_line(1, na)
var full_amr_up_labels = array.new_label(1, na)
var full_amr_down_lines = array.new_line(1, na)
var full_amr_down_labels = array.new_label(1, na)
var one_third_amr_up_lines = array.new_line(1, na)
var one_third_amr_up_labels = array.new_label(1, na)
var one_third_amr_down_lines = array.new_line(1, na)
var one_third_amr_down_labels = array.new_label(1, na)
var two_third_amr_up_lines = array.new_line(1, na)
var two_third_amr_up_labels = array.new_label(1, na)
var two_third_amr_down_lines = array.new_line(1, na)
var two_third_amr_down_labels = array.new_label(1, na)
var half_amr_up_lines = array.new_line(1, na)
var half_amr_up_labels = array.new_label(1, na)
var half_amr_down_lines = array.new_line(1, na)
var half_amr_down_labels = array.new_label(1, na)

// Variables to track current month's values
var float monthly_range_val = na
var float true_month_open_price = na
var bool true_month_line_active = false

// Variables to store AMR levels
var float full_amr_up = na
var float full_amr_down = na
var float one_third_amr_up = na
var float one_third_amr_down = na
var float two_third_amr_up = na
var float two_third_amr_down = na
var float half_amr_up = na
var float half_amr_down = na

// === FUNCTIONS ===
// Calculate AMR
calculate_amr(lookback_period) =>
    // Use higher timeframe with lookahead off
    [mh, ml] = request.security(syminfo.tickerid, "M", [high, low], gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_off)
    mrange = mh - ml
    ta.sma(mrange, lookback_period)

// Function to delete a line if it exists
delete_line_if_exists(line_array, index) =>
    if array.size(line_array) > index
        line_obj = array.get(line_array, index)
        if not na(line_obj)
            line.delete(line_obj)

// Function to delete a label if it exists
delete_label_if_exists(label_array, index) =>
    if array.size(label_array) > index
        label_obj = array.get(label_array, index)
        if not na(label_obj)
            label.delete(label_obj)

// Function to draw a line and store it in an array
draw_line(price, color, style, line_array, index) =>
    if not na(price) and barstate.islast
        // Delete existing line if it exists
        delete_line_if_exists(line_array, index)

        // Draw new line
        line_obj = line.new(x1=bar_index,y1=price,x2=bar_index + extend_right,y2=price,xloc=xloc.bar_index,color=color,style=style,width=line_width)

        // Store the line
        array.set(line_array, index, line_obj)

// Function to draw a label and store it in an array
draw_label(price, text, color, label_array, index) =>
    if not na(price) and barstate.islast and show_labels
        // Delete existing label if it exists
        delete_label_if_exists(label_array, index)

        // Draw new label
        label_text = text + (show_price_in_label ? str.format(" ({0})", price) : "")
        label_obj = label.new(x=bar_index + extend_right,y=price,text=label_text,xloc=xloc.bar_index,style=label.style_label_left,color=color.new(color.black, 100),textcolor=color,size=label_size_value)

        // Store the label
        array.set(label_array, index, label_obj)

// Function to draw a vertical line at month open
draw_vertical_line(bar_idx) =>
    if show_vertical and not na(bar_idx) and barstate.islast
        line.new(x1=bar_idx,y1=low - (high - low) * 0.1,x2=bar_idx,y2=high + (high - low) * 0.1,xloc=xloc.bar_index,color=color.new(true_month_open_color, 50),style=line.style_dashed,width=1)

// === MAIN LOGIC ===
// Calculate AMR
current_amr = calculate_amr(amr_months)

// Check for new month or first bar
is_new_month = timeframe.change("M")

// Update values on new month or first bar
if is_new_month or barstate.isfirst
    if not na(current_amr)
        monthly_range_val := current_amr
        true_month_open_price := barstate.isfirst ? close : open
        true_month_line_active := true

        // Calculate AMR levels
        full_amr_up := true_month_open_price + monthly_range_val
        full_amr_down := true_month_open_price - monthly_range_val
        one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
        one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
        two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
        two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
        half_amr_up := true_month_open_price + (monthly_range_val / 2)
        half_amr_down := true_month_open_price - (monthly_range_val / 2)

        // Draw vertical line at month open if enabled
        // This is critical for 1-minute charts to mark the start of the month
        if show_vertical and barstate.islast
            draw_vertical_line(bar_index)

// Draw lines and labels on the last bar
if barstate.islast
    // Draw true month open
    draw_line(true_month_open_price, true_month_open_color, line_style_value, true_month_open_lines, 0)
    draw_label(true_month_open_price, "TMO", true_month_open_color, true_month_open_labels, 0)

    // Draw vertical line at month open
    if show_vertical
        draw_vertical_line(bar_index - (time - time[1]))

    // Draw Full AMR levels
    if show_full_amr
        draw_line(full_amr_up, full_amr_color, line_style_value, full_amr_up_lines, 0)
        draw_label(full_amr_up, "AMR+", full_amr_color, full_amr_up_labels, 0)

        draw_line(full_amr_down, full_amr_color, line_style_value, full_amr_down_lines, 0)
        draw_label(full_amr_down, "AMR-", full_amr_color, full_amr_down_labels, 0)

    // Draw 1/3 AMR levels
    if show_one_third_amr
        draw_line(one_third_amr_up, one_third_amr_color, line_style_value, one_third_amr_up_lines, 0)
        draw_label(one_third_amr_up, "1/3 AMR+", one_third_amr_color, one_third_amr_up_labels, 0)

        draw_line(one_third_amr_down, one_third_amr_color, line_style_value, one_third_amr_down_lines, 0)
        draw_label(one_third_amr_down, "1/3 AMR-", one_third_amr_color, one_third_amr_down_labels, 0)

    // Draw 2/3 AMR levels
    if show_two_third_amr
        draw_line(two_third_amr_up, two_third_amr_color, line_style_value, two_third_amr_up_lines, 0)
        draw_label(two_third_amr_up, "2/3 AMR+", two_third_amr_color, two_third_amr_up_labels, 0)

        draw_line(two_third_amr_down, two_third_amr_color, line_style_value, two_third_amr_down_lines, 0)
        draw_label(two_third_amr_down, "2/3 AMR-", two_third_amr_color, two_third_amr_down_labels, 0)

    // Draw 1/2 AMR levels
    if show_half_amr
        draw_line(half_amr_up, half_amr_color, line_style_value, half_amr_up_lines, 0)
        draw_label(half_amr_up, "1/2 AMR+", half_amr_color, half_amr_up_labels, 0)

        draw_line(half_amr_down, half_amr_color, line_style_value, half_amr_down_lines, 0)
        draw_label(half_amr_down, "1/2 AMR-", half_amr_color, half_amr_down_labels, 0)

// Plot levels for additional visibility (especially on 1-minute charts)
// These plots will ensure the levels are always visible on all timeframes
// Using the approach from HTF-HL.pine for consistent visibility

// True Month Open plot - always visible on all timeframes
plot(true_month_open_price, "TMO", color=true_month_open_color, style=plot.style_linebr, linewidth=1)

// Full AMR plots
plot(show_full_amr ? full_amr_up : na, "Full AMR+", color=full_amr_color, style=plot.style_linebr, linewidth=1)
plot(show_full_amr ? full_amr_down : na, "Full AMR-", color=full_amr_color, style=plot.style_linebr, linewidth=1)

// 1/3 AMR plots
plot(show_one_third_amr ? one_third_amr_up : na, "1/3 AMR+", color=one_third_amr_color, style=plot.style_linebr, linewidth=1)
plot(show_one_third_amr ? one_third_amr_down : na, "1/3 AMR-", color=one_third_amr_color, style=plot.style_linebr, linewidth=1)

// 2/3 AMR plots
plot(show_two_third_amr ? two_third_amr_up : na, "2/3 AMR+", color=two_third_amr_color, style=plot.style_linebr, linewidth=1)
plot(show_two_third_amr ? two_third_amr_down : na, "2/3 AMR-", color=two_third_amr_color, style=plot.style_linebr, linewidth=1)

// 1/2 AMR plots
plot(show_half_amr ? half_amr_up : na, "1/2 AMR+", color=half_amr_color, style=plot.style_linebr, linewidth=1)
plot(show_half_amr ? half_amr_down : na, "1/2 AMR-", color=half_amr_color, style=plot.style_linebr, linewidth=1)

// Additional circle plots for better visibility at price points
plot(true_month_open_price, "TMO Point", color=true_month_open_color, style=plot.style_circles, linewidth=1)

// Full AMR circle plots
plot(show_full_amr ? full_amr_up : na, "Full AMR+ Point", color=full_amr_color, style=plot.style_circles, linewidth=1)
plot(show_full_amr ? full_amr_down : na, "Full AMR- Point", color=full_amr_color, style=plot.style_circles, linewidth=1)

// 1/3 AMR circle plots
plot(show_one_third_amr ? one_third_amr_up : na, "1/3 AMR+ Point", color=one_third_amr_color, style=plot.style_circles, linewidth=1)
plot(show_one_third_amr ? one_third_amr_down : na, "1/3 AMR- Point", color=one_third_amr_color, style=plot.style_circles, linewidth=1)

// 2/3 AMR circle plots
plot(show_two_third_amr ? two_third_amr_up : na, "2/3 AMR+ Point", color=two_third_amr_color, style=plot.style_circles, linewidth=1)
plot(show_two_third_amr ? two_third_amr_down : na, "2/3 AMR- Point", color=two_third_amr_color, style=plot.style_circles, linewidth=1)

// 1/2 AMR circle plots
plot(show_half_amr ? half_amr_up : na, "1/2 AMR+ Point", color=half_amr_color, style=plot.style_circles, linewidth=1)
plot(show_half_amr ? half_amr_down : na, "1/2 AMR- Point", color=half_amr_color, style=plot.style_circles, linewidth=1)

// Alert conditions
alertcondition(ta.cross(close, true_month_open_price), "Price crossed True Month Open", "Price crossed True Month Open (TMO)")
alertcondition(ta.cross(close, full_amr_up), "Price crossed Full AMR+", "Price crossed Full AMR+")
alertcondition(ta.cross(close, full_amr_down), "Price crossed Full AMR-", "Price crossed Full AMR-")
alertcondition(ta.cross(close, one_third_amr_up), "Price crossed 1/3 AMR+", "Price crossed 1/3 AMR+")
alertcondition(ta.cross(close, one_third_amr_down), "Price crossed 1/3 AMR-", "Price crossed 1/3 AMR-")
