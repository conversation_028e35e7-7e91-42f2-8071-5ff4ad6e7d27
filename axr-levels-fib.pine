//@version=5
indicator("ICT ADR/AWR/AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
show_only_intraday = input.bool(false, "Show Only on Intraday Timeframes", group=group_settings)
timezone = input.string("America/New_York", "Time Zone",['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], group=group_settings)
max_periods = input.int(3, "Periods to Display", minval=1, maxval=30, tooltip="Number of days/weeks/months to display", group=group_settings)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, group=group_time)
week_start_day = input.string("Monday", "Week Start Day", options=["Sunday", "Monday"], group=group_time)
month_start_day = input.int(1, "Month Start Day", minval=1, maxval=31, tooltip="Day of month to start monthly calculations", group=group_time)

// Daily ADR Settings
group_adr = "Daily ADR Settings"
show_daily_levels = input.bool(true, "Show Daily Levels", group=group_adr)
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group=group_adr)
show_full_adr = input.bool(true, "Show Full ADR", group=group_adr)
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group=group_adr)
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group=group_adr)
show_half_adr = input.bool(false, "Show 1/2 ADR", group=group_adr)

// Daily ADR Multiplier Settings
group_adr_mult = "Daily ADR Multiplier Settings"
show_adr_mult_1_5 = input.bool(false, "Show 1.5x ADR", group=group_adr_mult)
show_adr_mult_2_0 = input.bool(false, "Show 2.0x ADR", group=group_adr_mult)

// Daily Fibonacci Extension Settings
group_adr_fib = "Daily Fibonacci Extension Settings"
show_adr_fib_1_272 = input.bool(false, "Show 127.2% ADR", group=group_adr_fib)
show_adr_fib_1_618 = input.bool(false, "Show 161.8% ADR", group=group_adr_fib)

// Weekly AWR Settings
group_awr = "Weekly AWR Settings"
show_weekly_levels = input.bool(true, "Show Weekly Levels", group=group_awr)
awr_weeks = input.int(4, "Weeks to Average", minval=1, maxval=52, group=group_awr)
show_full_awr = input.bool(true, "Show Full AWR", group=group_awr)
show_one_third_awr = input.bool(true, "Show 1/3 AWR", group=group_awr)
show_two_third_awr = input.bool(false, "Show 2/3 AWR", group=group_awr)
show_half_awr = input.bool(false, "Show 1/2 AWR", group=group_awr)

// Weekly AWR Multiplier Settings
group_awr_mult = "Weekly AWR Multiplier Settings"
show_awr_mult_1_5 = input.bool(false, "Show 1.5x AWR", group=group_awr_mult)
show_awr_mult_2_0 = input.bool(false, "Show 2.0x AWR", group=group_awr_mult)

// Weekly Fibonacci Extension Settings
group_awr_fib = "Weekly Fibonacci Extension Settings"
show_awr_fib_1_272 = input.bool(false, "Show 127.2% AWR", group=group_awr_fib)
show_awr_fib_1_618 = input.bool(false, "Show 161.8% AWR", group=group_awr_fib)

// Monthly AMR Settings
group_amr = "Monthly AMR Settings"
show_monthly_levels = input.bool(true, "Show Monthly Levels", group=group_amr)
amr_months = input.int(3, "Months to Average", minval=1, maxval=24, group=group_amr)
show_full_amr = input.bool(true, "Show Full AMR", group=group_amr)
show_one_third_amr = input.bool(true, "Show 1/3 AMR", group=group_amr)
show_two_third_amr = input.bool(false, "Show 2/3 AMR", group=group_amr)
show_half_amr = input.bool(false, "Show 1/2 AMR", group=group_amr)

// Monthly AMR Multiplier Settings
group_amr_mult = "Monthly AMR Multiplier Settings"
show_amr_mult_1_5 = input.bool(false, "Show 1.5x AMR", group=group_amr_mult)
show_amr_mult_2_0 = input.bool(false, "Show 2.0x AMR", group=group_amr_mult)

// Monthly Fibonacci Extension Settings
group_amr_fib = "Monthly Fibonacci Extension Settings"
show_amr_fib_1_272 = input.bool(false, "Show 127.2% AMR", group=group_amr_fib)
show_amr_fib_1_618 = input.bool(false, "Show 161.8% AMR", group=group_amr_fib)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Day Open", group=group_line)

// Color Settings
group_color_daily = "Daily Color Settings"
true_day_open_color = input.color(color.white, "True Day Open Line", group=group_color_daily)
full_adr_color = input.color(color.white, "Full ADR", group=group_color_daily)
one_third_adr_color = input.color(color.white, "1/3 ADR", group=group_color_daily)
two_third_adr_color = input.color(color.white, "2/3 ADR", group=group_color_daily)
half_adr_color = input.color(color.white, "1/2 ADR", group=group_color_daily)
adr_mult_1_5_color = input.color(color.yellow, "1.5x ADR", group=group_color_daily)
adr_mult_2_0_color = input.color(color.orange, "2.0x ADR", group=group_color_daily)
adr_fib_1_272_color = input.color(color.purple, "127.2% ADR", group=group_color_daily)
adr_fib_1_618_color = input.color(color.fuchsia, "161.8% ADR", group=group_color_daily)

// Weekly Color Settings
group_color_weekly = "Weekly Color Settings"
true_week_open_color = input.color(color.white, "True Week Open Line", group=group_color_weekly)
full_awr_color = input.color(color.white, "Full AWR", group=group_color_weekly)
one_third_awr_color = input.color(color.white, "1/3 AWR", group=group_color_weekly)
two_third_awr_color = input.color(color.white, "2/3 AWR", group=group_color_weekly)
half_awr_color = input.color(color.white, "1/2 AWR", group=group_color_weekly)
awr_mult_1_5_color = input.color(color.yellow, "1.5x AWR", group=group_color_weekly)
awr_mult_2_0_color = input.color(color.orange, "2.0x AWR", group=group_color_weekly)
awr_fib_1_272_color = input.color(color.purple, "127.2% AWR", group=group_color_weekly)
awr_fib_1_618_color = input.color(color.fuchsia, "161.8% AWR", group=group_color_weekly)

// Monthly Color Settings
group_color_monthly = "Monthly Color Settings"
true_month_open_color = input.color(color.white, "True Month Open Line", group=group_color_monthly)
full_amr_color = input.color(color.white, "Full AMR", group=group_color_monthly)
one_third_amr_color = input.color(color.white, "1/3 AMR", group=group_color_monthly)
two_third_amr_color = input.color(color.white, "2/3 AMR", group=group_color_monthly)
half_amr_color = input.color(color.white, "1/2 AMR", group=group_color_monthly)
amr_mult_1_5_color = input.color(color.yellow, "1.5x AMR", group=group_color_monthly)
amr_mult_2_0_color = input.color(color.orange, "2.0x AMR", group=group_color_monthly)
amr_fib_1_272_color = input.color(color.purple, "127.2% AMR", group=group_color_monthly)
amr_fib_1_618_color = input.color(color.fuchsia, "161.8% AMR", group=group_color_monthly)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
merge_overlapping_labels = input.bool(true, "Merge Overlapping Labels", tooltip="Combine labels when price levels are very close to each other", group=group_label)
overlap_threshold = input.float(0.1, "Overlap Threshold %", minval=0.01, maxval=1.0, step=0.01, tooltip="% of ATR to consider levels as overlapping", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === TIME LOGIC ===
// Daily time logic
start_of_day = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
end_of_day = timestamp(timezone, year, month, dayofmonth, 23, 59)
is_new_day = ta.change(time("D")) or (barstate.isfirst and show_daily_levels)
in_current_day = time >= start_of_day and time <= end_of_day

// True Day Open logic (ICT methodology - 00:00 UTC)
is_true_day_start = (hour == 0) and (minute == 0)

// Weekly time logic
is_sunday = dayofweek == 1
is_monday = dayofweek == 2
week_start_condition = week_start_day == "Sunday" ? is_sunday : is_monday
week_timestamp = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
start_of_week = week_timestamp and week_start_condition
is_new_week = ta.change(time("W")) or (barstate.isfirst and show_weekly_levels)
in_current_week = true  // We'll always be in the current week, just need to track when a new week starts

// True Week Open logic (ICT methodology - 00:00 UTC on Monday or Sunday)
is_true_week_start = ((week_start_day == "Monday" and dayofweek == 2) or
                     (week_start_day == "Sunday" and dayofweek == 1)) and
                     (hour == 0) and (minute == 0)

// Monthly time logic
is_first_day_of_month = dayofmonth == month_start_day
month_timestamp = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
start_of_month = month_timestamp and is_first_day_of_month
is_new_month = ta.change(time("M")) or (barstate.isfirst and show_monthly_levels)
in_current_month = true  // We'll always be in the current month, just need to track when a new month starts

// True Month Open logic (ICT methodology - 00:00 UTC on first day of month)
// We'll detect the first day of month using dayofmonth == 1
is_true_month_start = (dayofmonth == 1) and (hour == 0) and (minute == 0)

// Function to calculate the Average Daily Range
calculate_adr(lookback_period) =>
    // Request daily high-low range data
    dh = request.security(syminfo.tickerid, "D", high, barmerge.gaps_off, barmerge.lookahead_off)
    dl = request.security(syminfo.tickerid, "D", low, barmerge.gaps_off, barmerge.lookahead_off)
    drange = dh - dl

    // Calculate the average of the daily ranges
    ta.sma(drange, lookback_period)

// Function to calculate the Average Weekly Range
calculate_awr(lookback_period) =>
    // Request weekly high-low range data
    wh = request.security(syminfo.tickerid, "W", high, barmerge.gaps_off, barmerge.lookahead_off)
    wl = request.security(syminfo.tickerid, "W", low, barmerge.gaps_off, barmerge.lookahead_off)
    wrange = wh - wl

    // Calculate the average of the weekly ranges
    ta.sma(wrange, lookback_period)

// Function to calculate the Average Monthly Range
calculate_amr(lookback_period) =>
    // Request monthly high-low range data
    mh = request.security(syminfo.tickerid, "M", high, barmerge.gaps_off, barmerge.lookahead_off)
    ml = request.security(syminfo.tickerid, "M", low, barmerge.gaps_off, barmerge.lookahead_off)
    mrange = mh - ml

    // Calculate the average of the monthly ranges
    ta.sma(mrange, lookback_period)

// === STATE VARIABLES ===
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// === DAILY VARIABLES ===
// True Day Open variables
var line true_day_open_line = na
var label true_day_open_label = na
var line true_day_vertical = na
var float true_day_open_price = na
var bool true_day_line_active = false

// ADR level lines
var line full_adr_up_line = na
var line full_adr_down_line = na
var line one_third_adr_up_line = na
var line one_third_adr_down_line = na
var line two_third_adr_up_line = na
var line two_third_adr_down_line = na
var line half_adr_up_line = na
var line half_adr_down_line = na

// ADR Multiplier level lines
var line adr_mult_1_5_up_line = na
var line adr_mult_1_5_down_line = na
var line adr_mult_2_0_up_line = na
var line adr_mult_2_0_down_line = na

// ADR Fibonacci Extension level lines
var line adr_fib_1_272_up_line = na
var line adr_fib_1_272_down_line = na
var line adr_fib_1_618_up_line = na
var line adr_fib_1_618_down_line = na

// ADR level labels
var label full_adr_up_label = na
var label full_adr_down_label = na
var label one_third_adr_up_label = na
var label one_third_adr_down_label = na
var label two_third_adr_up_label = na
var label two_third_adr_down_label = na
var label half_adr_up_label = na
var label half_adr_down_label = na

// ADR Multiplier level labels
var label adr_mult_1_5_up_label = na
var label adr_mult_1_5_down_label = na
var label adr_mult_2_0_up_label = na
var label adr_mult_2_0_down_label = na

// ADR Fibonacci Extension level labels
var label adr_fib_1_272_up_label = na
var label adr_fib_1_272_down_label = na
var label adr_fib_1_618_up_label = na
var label adr_fib_1_618_down_label = na

// ADR level values
var float daily_range_val = na
var float full_adr_up = na
var float full_adr_down = na
var float one_third_adr_up = na
var float one_third_adr_down = na
var float two_third_adr_up = na
var float two_third_adr_down = na
var float half_adr_up = na
var float half_adr_down = na

// ADR Multiplier level values
var float adr_mult_1_5_up = na
var float adr_mult_1_5_down = na
var float adr_mult_2_0_up = na
var float adr_mult_2_0_down = na

// ADR Fibonacci Extension level values
var float adr_fib_1_272_up = na
var float adr_fib_1_272_down = na
var float adr_fib_1_618_up = na
var float adr_fib_1_618_down = na

// === WEEKLY VARIABLES ===
// True Week Open variables
var line true_week_open_line = na
var label true_week_open_label = na
var line true_week_vertical = na
var float true_week_open_price = na
var bool true_week_line_active = false

// AWR level lines
var line full_awr_up_line = na
var line full_awr_down_line = na
var line one_third_awr_up_line = na
var line one_third_awr_down_line = na
var line two_third_awr_up_line = na
var line two_third_awr_down_line = na
var line half_awr_up_line = na
var line half_awr_down_line = na

// AWR Multiplier level lines
var line awr_mult_1_5_up_line = na
var line awr_mult_1_5_down_line = na
var line awr_mult_2_0_up_line = na
var line awr_mult_2_0_down_line = na

// AWR Fibonacci Extension level lines
var line awr_fib_1_272_up_line = na
var line awr_fib_1_272_down_line = na
var line awr_fib_1_618_up_line = na
var line awr_fib_1_618_down_line = na

// AWR level labels
var label full_awr_up_label = na
var label full_awr_down_label = na
var label one_third_awr_up_label = na
var label one_third_awr_down_label = na
var label two_third_awr_up_label = na
var label two_third_awr_down_label = na
var label half_awr_up_label = na
var label half_awr_down_label = na

// AWR Multiplier level labels
var label awr_mult_1_5_up_label = na
var label awr_mult_1_5_down_label = na
var label awr_mult_2_0_up_label = na
var label awr_mult_2_0_down_label = na

// AWR Fibonacci Extension level labels
var label awr_fib_1_272_up_label = na
var label awr_fib_1_272_down_label = na
var label awr_fib_1_618_up_label = na
var label awr_fib_1_618_down_label = na

// AWR level values
var float weekly_range_val = na
var float full_awr_up = na
var float full_awr_down = na
var float one_third_awr_up = na
var float one_third_awr_down = na
var float two_third_awr_up = na
var float two_third_awr_down = na
var float half_awr_up = na
var float half_awr_down = na

// AWR Multiplier level values
var float awr_mult_1_5_up = na
var float awr_mult_1_5_down = na
var float awr_mult_2_0_up = na
var float awr_mult_2_0_down = na

// AWR Fibonacci Extension level values
var float awr_fib_1_272_up = na
var float awr_fib_1_272_down = na
var float awr_fib_1_618_up = na
var float awr_fib_1_618_down = na

// === MONTHLY VARIABLES ===
// True Month Open variables
var line true_month_open_line = na
var label true_month_open_label = na
var line true_month_vertical = na
var float true_month_open_price = na
var bool true_month_line_active = false

// AMR level lines
var line full_amr_up_line = na
var line full_amr_down_line = na
var line one_third_amr_up_line = na
var line one_third_amr_down_line = na
var line two_third_amr_up_line = na
var line two_third_amr_down_line = na
var line half_amr_up_line = na
var line half_amr_down_line = na

// AMR Multiplier level lines
var line amr_mult_1_5_up_line = na
var line amr_mult_1_5_down_line = na
var line amr_mult_2_0_up_line = na
var line amr_mult_2_0_down_line = na

// AMR Fibonacci Extension level lines
var line amr_fib_1_272_up_line = na
var line amr_fib_1_272_down_line = na
var line amr_fib_1_618_up_line = na
var line amr_fib_1_618_down_line = na

// AMR level labels
var label full_amr_up_label = na
var label full_amr_down_label = na
var label one_third_amr_up_label = na
var label one_third_amr_down_label = na
var label two_third_amr_up_label = na
var label two_third_amr_down_label = na
var label half_amr_up_label = na
var label half_amr_down_label = na

// AMR Multiplier level labels
var label amr_mult_1_5_up_label = na
var label amr_mult_1_5_down_label = na
var label amr_mult_2_0_up_label = na
var label amr_mult_2_0_down_label = na

// AMR Fibonacci Extension level labels
var label amr_fib_1_272_up_label = na
var label amr_fib_1_272_down_label = na
var label amr_fib_1_618_up_label = na
var label amr_fib_1_618_down_label = na

// AMR level values
var float monthly_range_val = na
var float full_amr_up = na
var float full_amr_down = na
var float one_third_amr_up = na
var float one_third_amr_down = na
var float two_third_amr_up = na
var float two_third_amr_down = na
var float half_amr_up = na
var float half_amr_down = na

// AMR Multiplier level values
var float amr_mult_1_5_up = na
var float amr_mult_1_5_down = na
var float amr_mult_2_0_up = na
var float amr_mult_2_0_down = na

// AMR Fibonacci Extension level values
var float amr_fib_1_272_up = na
var float amr_fib_1_272_down = na
var float amr_fib_1_618_up = na
var float amr_fib_1_618_down = na

// === HISTORY ARRAYS ===
// Arrays to store historical range values
var float[] daily_range_history = array.new_float()
var float[] weekly_range_history = array.new_float()
var float[] monthly_range_history = array.new_float()

// === DAILY ARRAYS ===
// Arrays to store historical daily lines by type
var line[] tdo_lines = array.new_line()
var line[] full_adr_up_lines = array.new_line()
var line[] full_adr_down_lines = array.new_line()
var line[] one_third_adr_up_lines = array.new_line()
var line[] one_third_adr_down_lines = array.new_line()
var line[] two_third_adr_up_lines = array.new_line()
var line[] two_third_adr_down_lines = array.new_line()
var line[] half_adr_up_lines = array.new_line()
var line[] half_adr_down_lines = array.new_line()
var line[] daily_vertical_lines = array.new_line()

// ADR Multiplier line arrays
var line[] adr_mult_1_5_up_lines = array.new_line()
var line[] adr_mult_1_5_down_lines = array.new_line()
var line[] adr_mult_2_0_up_lines = array.new_line()
var line[] adr_mult_2_0_down_lines = array.new_line()

// ADR Fibonacci Extension line arrays
var line[] adr_fib_1_272_up_lines = array.new_line()
var line[] adr_fib_1_272_down_lines = array.new_line()
var line[] adr_fib_1_618_up_lines = array.new_line()
var line[] adr_fib_1_618_down_lines = array.new_line()

// Arrays to store historical daily labels by type
var label[] tdo_labels = array.new_label()
var label[] full_adr_up_labels = array.new_label()
var label[] full_adr_down_labels = array.new_label()
var label[] one_third_adr_up_labels = array.new_label()
var label[] one_third_adr_down_labels = array.new_label()
var label[] two_third_adr_up_labels = array.new_label()
var label[] two_third_adr_down_labels = array.new_label()
var label[] half_adr_up_labels = array.new_label()
var label[] half_adr_down_labels = array.new_label()

// ADR Multiplier label arrays
var label[] adr_mult_1_5_up_labels = array.new_label()
var label[] adr_mult_1_5_down_labels = array.new_label()
var label[] adr_mult_2_0_up_labels = array.new_label()
var label[] adr_mult_2_0_down_labels = array.new_label()

// ADR Fibonacci Extension label arrays
var label[] adr_fib_1_272_up_labels = array.new_label()
var label[] adr_fib_1_272_down_labels = array.new_label()
var label[] adr_fib_1_618_up_labels = array.new_label()
var label[] adr_fib_1_618_down_labels = array.new_label()

// === WEEKLY ARRAYS ===
// Arrays to store historical weekly lines by type
var line[] two_lines = array.new_line()
var line[] full_awr_up_lines = array.new_line()
var line[] full_awr_down_lines = array.new_line()
var line[] one_third_awr_up_lines = array.new_line()
var line[] one_third_awr_down_lines = array.new_line()
var line[] two_third_awr_up_lines = array.new_line()
var line[] two_third_awr_down_lines = array.new_line()
var line[] half_awr_up_lines = array.new_line()
var line[] half_awr_down_lines = array.new_line()
var line[] weekly_vertical_lines = array.new_line()

// AWR Multiplier line arrays
var line[] awr_mult_1_5_up_lines = array.new_line()
var line[] awr_mult_1_5_down_lines = array.new_line()
var line[] awr_mult_2_0_up_lines = array.new_line()
var line[] awr_mult_2_0_down_lines = array.new_line()

// AWR Fibonacci Extension line arrays
var line[] awr_fib_1_272_up_lines = array.new_line()
var line[] awr_fib_1_272_down_lines = array.new_line()
var line[] awr_fib_1_618_up_lines = array.new_line()
var line[] awr_fib_1_618_down_lines = array.new_line()

// Arrays to store historical weekly labels by type
var label[] two_labels = array.new_label()
var label[] full_awr_up_labels = array.new_label()
var label[] full_awr_down_labels = array.new_label()
var label[] one_third_awr_up_labels = array.new_label()
var label[] one_third_awr_down_labels = array.new_label()
var label[] two_third_awr_up_labels = array.new_label()
var label[] two_third_awr_down_labels = array.new_label()
var label[] half_awr_up_labels = array.new_label()
var label[] half_awr_down_labels = array.new_label()

// AWR Multiplier label arrays
var label[] awr_mult_1_5_up_labels = array.new_label()
var label[] awr_mult_1_5_down_labels = array.new_label()
var label[] awr_mult_2_0_up_labels = array.new_label()
var label[] awr_mult_2_0_down_labels = array.new_label()

// AWR Fibonacci Extension label arrays
var label[] awr_fib_1_272_up_labels = array.new_label()
var label[] awr_fib_1_272_down_labels = array.new_label()
var label[] awr_fib_1_618_up_labels = array.new_label()
var label[] awr_fib_1_618_down_labels = array.new_label()

// === MONTHLY ARRAYS ===
// Arrays to store historical monthly lines by type
var line[] tmo_lines = array.new_line()
var line[] full_amr_up_lines = array.new_line()
var line[] full_amr_down_lines = array.new_line()
var line[] one_third_amr_up_lines = array.new_line()
var line[] one_third_amr_down_lines = array.new_line()
var line[] two_third_amr_up_lines = array.new_line()
var line[] two_third_amr_down_lines = array.new_line()
var line[] half_amr_up_lines = array.new_line()
var line[] half_amr_down_lines = array.new_line()
var line[] monthly_vertical_lines = array.new_line()

// AMR Multiplier line arrays
var line[] amr_mult_1_5_up_lines = array.new_line()
var line[] amr_mult_1_5_down_lines = array.new_line()
var line[] amr_mult_2_0_up_lines = array.new_line()
var line[] amr_mult_2_0_down_lines = array.new_line()

// AMR Fibonacci Extension line arrays
var line[] amr_fib_1_272_up_lines = array.new_line()
var line[] amr_fib_1_272_down_lines = array.new_line()
var line[] amr_fib_1_618_up_lines = array.new_line()
var line[] amr_fib_1_618_down_lines = array.new_line()

// Arrays to store historical monthly labels by type
var label[] tmo_labels = array.new_label()
var label[] full_amr_up_labels = array.new_label()
var label[] full_amr_down_labels = array.new_label()
var label[] one_third_amr_up_labels = array.new_label()
var label[] one_third_amr_down_labels = array.new_label()
var label[] two_third_amr_up_labels = array.new_label()
var label[] two_third_amr_down_labels = array.new_label()
var label[] half_amr_up_labels = array.new_label()
var label[] half_amr_down_labels = array.new_label()

// AMR Multiplier label arrays
var label[] amr_mult_1_5_up_labels = array.new_label()
var label[] amr_mult_1_5_down_labels = array.new_label()
var label[] amr_mult_2_0_up_labels = array.new_label()
var label[] amr_mult_2_0_down_labels = array.new_label()

// AMR Fibonacci Extension label arrays
var label[] amr_fib_1_272_up_labels = array.new_label()
var label[] amr_fib_1_272_down_labels = array.new_label()
var label[] amr_fib_1_618_up_labels = array.new_label()
var label[] amr_fib_1_618_down_labels = array.new_label()

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to check if two price levels are close enough to be considered overlapping
is_overlapping(price1, price2, threshold) =>
    // Calculate the average true range (ATR) to determine a dynamic threshold
    atr_value = ta.atr(14)
    // Check if the difference between the two prices is less than the threshold percentage of ATR
    math.abs(price1 - price2) <= (atr_value * threshold)

// Function to merge label texts
merge_label_texts(text1, text2) =>
    // Extract price values if they exist
    price_start1 = str.pos(text1, "(")
    price_start2 = str.pos(text2, "(")

    // If both texts have price values, keep them separate
    if price_start1 > 0 and price_start2 > 0
        price_end1 = str.pos(text1, ")")
        price_end2 = str.pos(text2, ")")

        label1 = str.substring(text1, 0, price_start1 - 1)
        label2 = str.substring(text2, 0, price_start2 - 1)

        price1 = str.substring(text1, price_start1, price_end1)
        price2 = str.substring(text2, price_start2, price_end2)

        label1 + " + " + label2 + " " + price1 + "/" + price2 + ")"
    else
        // If no price values, just combine the labels
        text1 + " + " + text2

// Function to find and merge overlapping labels
find_overlapping_label(price, label_text, existing_prices, existing_texts, threshold) =>
    result_index = -1
    result_text = label_text

    if array.size(existing_prices) > 0
        for i = 0 to array.size(existing_prices) - 1
            existing_price = array.get(existing_prices, i)
            if is_overlapping(price, existing_price, threshold)
                result_index = i
                result_text := merge_label_texts(array.get(existing_texts, i), label_text)
                break

    [result_index, result_text]


// === MAIN LOGIC ===
// Calculate range values
float current_adr = calculate_adr(adr_days)
float current_awr = calculate_awr(awr_weeks)
float current_amr = calculate_amr(amr_months)

// Arrays to store price levels and label texts for merging overlapping labels
var float[] daily_price_levels = array.new_float()
var string[] daily_label_texts = array.new_string()
var float[] weekly_price_levels = array.new_float()
var string[] weekly_label_texts = array.new_string()
var float[] monthly_price_levels = array.new_float()
var string[] monthly_label_texts = array.new_string()

// Special initialization for monthly levels on the first bar
if barstate.isfirst and show_monthly_levels and not true_month_line_active
    monthly_range_val := current_amr
    true_month_open_price := close
    true_month_line_active := true

    // Calculate AMR levels
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Create true month open line
    true_month_open_line := line.new(bar_index, true_month_open_price, bar_index, true_month_open_price, color=true_month_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tmo_lines, true_month_open_line)

    // Create true month open label
    if show_labels
        true_month_open_label := label.new(bar_index + label_x_offset_bars, true_month_open_price + label_y_offset, "TMO" + (show_price_in_label ? str.format(" ({0})", true_month_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_month_open_color, color=color.new(color.black, 100))
        array.push(tmo_labels, true_month_open_label)

    // Create AMR level lines
    if show_full_amr
        // Full AMR up line
        full_amr_up_line := line.new(bar_index, full_amr_up, bar_index, full_amr_up, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_up_lines, full_amr_up_line)

        // Full AMR up label
        if show_labels
            full_amr_up_label := label.new(bar_index + label_x_offset_bars, full_amr_up + label_y_offset, "AMR+" + (show_price_in_label ? str.format(" ({0})", full_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_up_labels, full_amr_up_label)

        // Full AMR down line
        full_amr_down_line := line.new(bar_index, full_amr_down, bar_index, full_amr_down, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_down_lines, full_amr_down_line)

        // Full AMR down label
        if show_labels
            full_amr_down_label := label.new(bar_index + label_x_offset_bars, full_amr_down + label_y_offset, "AMR-" + (show_price_in_label ? str.format(" ({0})", full_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_down_labels, full_amr_down_label)

    if show_one_third_amr
        // 1/3 AMR up line
        one_third_amr_up_line := line.new(bar_index, one_third_amr_up, bar_index, one_third_amr_up, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_up_lines, one_third_amr_up_line)

        // 1/3 AMR up label
        if show_labels
            one_third_amr_up_label := label.new(bar_index + label_x_offset_bars, one_third_amr_up + label_y_offset, "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", one_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_up_labels, one_third_amr_up_label)

        // 1/3 AMR down line
        one_third_amr_down_line := line.new(bar_index, one_third_amr_down, bar_index, one_third_amr_down, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_down_lines, one_third_amr_down_line)

        // 1/3 AMR down label
        if show_labels
            one_third_amr_down_label := label.new(bar_index + label_x_offset_bars, one_third_amr_down + label_y_offset, "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", one_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_down_labels, one_third_amr_down_label)

    if show_two_third_amr
        // 2/3 AMR up line
        two_third_amr_up_line := line.new(bar_index, two_third_amr_up, bar_index, two_third_amr_up, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_up_lines, two_third_amr_up_line)

        // 2/3 AMR up label
        if show_labels
            two_third_amr_up_label := label.new(bar_index + label_x_offset_bars, two_third_amr_up + label_y_offset, "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", two_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_up_labels, two_third_amr_up_label)

        // 2/3 AMR down line
        two_third_amr_down_line := line.new(bar_index, two_third_amr_down, bar_index, two_third_amr_down, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_down_lines, two_third_amr_down_line)

        // 2/3 AMR down label
        if show_labels
            two_third_amr_down_label := label.new(bar_index + label_x_offset_bars, two_third_amr_down + label_y_offset, "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", two_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_down_labels, two_third_amr_down_label)

    if show_half_amr
        // 1/2 AMR up line
        half_amr_up_line := line.new(bar_index, half_amr_up, bar_index, half_amr_up, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_up_lines, half_amr_up_line)

        // 1/2 AMR up label
        if show_labels
            half_amr_up_label := label.new(bar_index + label_x_offset_bars, half_amr_up + label_y_offset, "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", half_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_up_labels, half_amr_up_label)

        // 1/2 AMR down line
        half_amr_down_line := line.new(bar_index, half_amr_down, bar_index, half_amr_down, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_down_lines, half_amr_down_line)

        // 1/2 AMR down label
        if show_labels
            half_amr_down_label := label.new(bar_index + label_x_offset_bars, half_amr_down + label_y_offset, "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", half_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_down_labels, half_amr_down_label)

// Check if we're at the start of a new day or true day open
if (is_new_day or (is_true_day_start and not is_true_day_start[1])) and show_daily_levels
    // Store the current ADR value in history and for today's use
    array.unshift(daily_range_history, current_adr)
    if array.size(daily_range_history) > adr_days
        array.pop(daily_range_history)

    // Update the daily range value for today
    daily_range_val := current_adr

    // Update true day open price - using the price at the true day open time (ICT methodology)
    if is_true_day_start and not is_true_day_start[1]
        true_day_open_price := open
        true_day_line_active := true

    // Calculate ADR levels
    full_adr_up := true_day_open_price + daily_range_val
    full_adr_down := true_day_open_price - daily_range_val
    one_third_adr_up := true_day_open_price + (daily_range_val / 3)
    one_third_adr_down := true_day_open_price - (daily_range_val / 3)
    two_third_adr_up := true_day_open_price + (daily_range_val * 2 / 3)
    two_third_adr_down := true_day_open_price - (daily_range_val * 2 / 3)
    half_adr_up := true_day_open_price + (daily_range_val / 2)
    half_adr_down := true_day_open_price - (daily_range_val / 2)

    // Calculate ADR Multiplier levels
    adr_mult_1_5_up := true_day_open_price + (daily_range_val * 1.5)
    adr_mult_1_5_down := true_day_open_price - (daily_range_val * 1.5)
    adr_mult_2_0_up := true_day_open_price + (daily_range_val * 2.0)
    adr_mult_2_0_down := true_day_open_price - (daily_range_val * 2.0)

    // Calculate ADR Fibonacci Extension levels
    adr_fib_1_272_up := true_day_open_price + (daily_range_val * 1.272)
    adr_fib_1_272_down := true_day_open_price - (daily_range_val * 1.272)
    adr_fib_1_618_up := true_day_open_price + (daily_range_val * 1.618)
    adr_fib_1_618_down := true_day_open_price - (daily_range_val * 1.618)

    // Create true day open line
    true_day_open_line := line.new(bar_index, true_day_open_price, bar_index, true_day_open_price, color=true_day_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tdo_lines, true_day_open_line)
    manage_line_history(tdo_lines)

    // Delete old labels when a new TDO appears
    if show_labels
        delete_all_labels(tdo_labels)
        delete_all_labels(full_adr_up_labels)
        delete_all_labels(full_adr_down_labels)
        delete_all_labels(one_third_adr_up_labels)
        delete_all_labels(one_third_adr_down_labels)
        delete_all_labels(two_third_adr_up_labels)
        delete_all_labels(two_third_adr_down_labels)
        delete_all_labels(half_adr_up_labels)
        delete_all_labels(half_adr_down_labels)
        delete_all_labels(adr_mult_1_5_up_labels)
        delete_all_labels(adr_mult_1_5_down_labels)
        delete_all_labels(adr_mult_2_0_up_labels)
        delete_all_labels(adr_mult_2_0_down_labels)
        delete_all_labels(adr_fib_1_272_up_labels)
        delete_all_labels(adr_fib_1_272_down_labels)
        delete_all_labels(adr_fib_1_618_up_labels)
        delete_all_labels(adr_fib_1_618_down_labels)

        // Clear arrays for merging overlapping labels
        array.clear(daily_price_levels)
        array.clear(daily_label_texts)

        // Create true day open label
        label_text = "TDO" + (show_price_in_label ? str.format(" ({0})", true_day_open_price) : "")

        // Store the price level and label text for merging overlapping labels
        array.push(daily_price_levels, true_day_open_price)
        array.push(daily_label_texts, label_text)

        true_day_open_label := label.new(bar_index + label_x_offset_bars, true_day_open_price + label_y_offset, label_text, style=label.style_label_left, size=label_size_value, textcolor=true_day_open_color, color=color.new(color.black, 100))
        array.push(tdo_labels, true_day_open_label)

    // Create vertical line at day open
    if show_vertical
        true_day_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_day_open_color, style=line.style_dashed)
        array.unshift(daily_vertical_lines, true_day_vertical)
        manage_line_history(daily_vertical_lines)

    // Create ADR level lines
    if show_full_adr
        // Full ADR up line
        full_adr_up_line := line.new(bar_index, full_adr_up, bar_index, full_adr_up, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_up_lines, full_adr_up_line)
        manage_line_history(full_adr_up_lines)

        // Full ADR up label
        if show_labels
            label_text = "ADR+" + (show_price_in_label ? str.format(" ({0})", full_adr_up) : "")

            if merge_overlapping_labels
                // Check if this level overlaps with any existing level
                [overlap_index, merged_text] = find_overlapping_label(full_adr_up, label_text, daily_price_levels, daily_label_texts, overlap_threshold)

                if overlap_index >= 0
                    // Update the existing label with merged text
                    array.set(daily_label_texts, overlap_index, merged_text)
                    label_text := merged_text
                else
                    // Add this level to the arrays
                    array.push(daily_price_levels, full_adr_up)
                    array.push(daily_label_texts, label_text)
            else
                // Add this level to the arrays without checking for overlaps
                array.push(daily_price_levels, full_adr_up)
                array.push(daily_label_texts, label_text)

            full_adr_up_label := label.new(bar_index + label_x_offset_bars, full_adr_up + label_y_offset, label_text, style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_up_labels, full_adr_up_label)

        // Full ADR down line
        full_adr_down_line := line.new(bar_index, full_adr_down, bar_index, full_adr_down, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_down_lines, full_adr_down_line)
        manage_line_history(full_adr_down_lines)

        // Full ADR down label
        if show_labels
            label_text = "ADR-" + (show_price_in_label ? str.format(" ({0})", full_adr_down) : "")

            if merge_overlapping_labels
                // Check if this level overlaps with any existing level
                [overlap_index, merged_text] = find_overlapping_label(full_adr_down, label_text, daily_price_levels, daily_label_texts, overlap_threshold)

                if overlap_index >= 0
                    // Update the existing label with merged text
                    array.set(daily_label_texts, overlap_index, merged_text)
                    label_text := merged_text
                else
                    // Add this level to the arrays
                    array.push(daily_price_levels, full_adr_down)
                    array.push(daily_label_texts, label_text)
            else
                // Add this level to the arrays without checking for overlaps
                array.push(daily_price_levels, full_adr_down)
                array.push(daily_label_texts, label_text)

            full_adr_down_label := label.new(bar_index + label_x_offset_bars, full_adr_down + label_y_offset, label_text, style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_down_labels, full_adr_down_label)

    // Create 1/3 ADR level lines
    if show_one_third_adr
        // 1/3 ADR up line
        one_third_adr_up_line := line.new(bar_index, one_third_adr_up, bar_index, one_third_adr_up, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_up_lines, one_third_adr_up_line)
        manage_line_history(one_third_adr_up_lines)

        // 1/3 ADR up label
        if show_labels
            one_third_adr_up_label := label.new(bar_index + label_x_offset_bars, one_third_adr_up + label_y_offset, "1/3 ADR+" + (show_price_in_label ? str.format(" ({0})", one_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_up_labels, one_third_adr_up_label)

        // 1/3 ADR down line
        one_third_adr_down_line := line.new(bar_index, one_third_adr_down, bar_index, one_third_adr_down, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_down_lines, one_third_adr_down_line)
        manage_line_history(one_third_adr_down_lines)

        // 1/3 ADR down label
        if show_labels
            one_third_adr_down_label := label.new(bar_index + label_x_offset_bars, one_third_adr_down + label_y_offset, "1/3 ADR-" + (show_price_in_label ? str.format(" ({0})", one_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_down_labels, one_third_adr_down_label)

    // Create 2/3 ADR level lines
    if show_two_third_adr
        // 2/3 ADR up line
        two_third_adr_up_line := line.new(bar_index, two_third_adr_up, bar_index, two_third_adr_up, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_up_lines, two_third_adr_up_line)
        manage_line_history(two_third_adr_up_lines)

        // 2/3 ADR up label
        if show_labels
            two_third_adr_up_label := label.new(bar_index + label_x_offset_bars, two_third_adr_up + label_y_offset, "2/3 ADR+" + (show_price_in_label ? str.format(" ({0})", two_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_up_labels, two_third_adr_up_label)

        // 2/3 ADR down line
        two_third_adr_down_line := line.new(bar_index, two_third_adr_down, bar_index, two_third_adr_down, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_down_lines, two_third_adr_down_line)
        manage_line_history(two_third_adr_down_lines)

        // 2/3 ADR down label
        if show_labels
            two_third_adr_down_label := label.new(bar_index + label_x_offset_bars, two_third_adr_down + label_y_offset, "2/3 ADR-" + (show_price_in_label ? str.format(" ({0})", two_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_down_labels, two_third_adr_down_label)

    // Create 1/2 ADR level lines
    if show_half_adr
        // 1/2 ADR up line
        half_adr_up_line := line.new(bar_index, half_adr_up, bar_index, half_adr_up, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_up_lines, half_adr_up_line)
        manage_line_history(half_adr_up_lines)

        // 1/2 ADR up label
        if show_labels
            half_adr_up_label := label.new(bar_index + label_x_offset_bars, half_adr_up + label_y_offset, "1/2 ADR+" + (show_price_in_label ? str.format(" ({0})", half_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_up_labels, half_adr_up_label)

        // 1/2 ADR down line
        half_adr_down_line := line.new(bar_index, half_adr_down, bar_index, half_adr_down, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_down_lines, half_adr_down_line)
        manage_line_history(half_adr_down_lines)

        // 1/2 ADR down label
        if show_labels
            half_adr_down_label := label.new(bar_index + label_x_offset_bars, half_adr_down + label_y_offset, "1/2 ADR-" + (show_price_in_label ? str.format(" ({0})", half_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_down_labels, half_adr_down_label)

    // Create 1.5x ADR level lines
    if show_adr_mult_1_5
        // 1.5x ADR up line
        adr_mult_1_5_up_line := line.new(bar_index, adr_mult_1_5_up, bar_index, adr_mult_1_5_up, color=adr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_1_5_up_lines, adr_mult_1_5_up_line)
        manage_line_history(adr_mult_1_5_up_lines)

        // 1.5x ADR up label
        if show_labels
            adr_mult_1_5_up_label := label.new(bar_index + label_x_offset_bars, adr_mult_1_5_up + label_y_offset, "1.5x ADR+" + (show_price_in_label ? str.format(" ({0})", adr_mult_1_5_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(adr_mult_1_5_up_labels, adr_mult_1_5_up_label)

        // 1.5x ADR down line
        adr_mult_1_5_down_line := line.new(bar_index, adr_mult_1_5_down, bar_index, adr_mult_1_5_down, color=adr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_1_5_down_lines, adr_mult_1_5_down_line)
        manage_line_history(adr_mult_1_5_down_lines)

        // 1.5x ADR down label
        if show_labels
            adr_mult_1_5_down_label := label.new(bar_index + label_x_offset_bars, adr_mult_1_5_down + label_y_offset, "1.5x ADR-" + (show_price_in_label ? str.format(" ({0})", adr_mult_1_5_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(adr_mult_1_5_down_labels, adr_mult_1_5_down_label)

    // Create 2.0x ADR level lines
    if show_adr_mult_2_0
        // 2.0x ADR up line
        adr_mult_2_0_up_line := line.new(bar_index, adr_mult_2_0_up, bar_index, adr_mult_2_0_up, color=adr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_2_0_up_lines, adr_mult_2_0_up_line)
        manage_line_history(adr_mult_2_0_up_lines)

        // 2.0x ADR up label
        if show_labels
            adr_mult_2_0_up_label := label.new(bar_index + label_x_offset_bars, adr_mult_2_0_up + label_y_offset, "2.0x ADR+" + (show_price_in_label ? str.format(" ({0})", adr_mult_2_0_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(adr_mult_2_0_up_labels, adr_mult_2_0_up_label)

        // 2.0x ADR down line
        adr_mult_2_0_down_line := line.new(bar_index, adr_mult_2_0_down, bar_index, adr_mult_2_0_down, color=adr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_2_0_down_lines, adr_mult_2_0_down_line)
        manage_line_history(adr_mult_2_0_down_lines)

        // 2.0x ADR down label
        if show_labels
            adr_mult_2_0_down_label := label.new(bar_index + label_x_offset_bars, adr_mult_2_0_down + label_y_offset, "2.0x ADR-" + (show_price_in_label ? str.format(" ({0})", adr_mult_2_0_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(adr_mult_2_0_down_labels, adr_mult_2_0_down_label)

    // Create 127.2% Fibonacci Extension level lines
    if show_adr_fib_1_272
        // 127.2% Fib up line
        adr_fib_1_272_up_line := line.new(bar_index, adr_fib_1_272_up, bar_index, adr_fib_1_272_up, color=adr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(adr_fib_1_272_up_lines, adr_fib_1_272_up_line)
        manage_line_history(adr_fib_1_272_up_lines)

        // 127.2% Fib up label
        if show_labels
            adr_fib_1_272_up_label := label.new(bar_index + label_x_offset_bars, adr_fib_1_272_up + label_y_offset, "127.2% ADR+" + (show_price_in_label ? str.format(" ({0})", adr_fib_1_272_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(adr_fib_1_272_up_labels, adr_fib_1_272_up_label)

        // 127.2% Fib down line
        adr_fib_1_272_down_line := line.new(bar_index, adr_fib_1_272_down, bar_index, adr_fib_1_272_down, color=adr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(adr_fib_1_272_down_lines, adr_fib_1_272_down_line)
        manage_line_history(adr_fib_1_272_down_lines)

        // 127.2% Fib down label
        if show_labels
            adr_fib_1_272_down_label := label.new(bar_index + label_x_offset_bars, adr_fib_1_272_down + label_y_offset, "127.2% ADR-" + (show_price_in_label ? str.format(" ({0})", adr_fib_1_272_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(adr_fib_1_272_down_labels, adr_fib_1_272_down_label)

    // Create 161.8% Fibonacci Extension level lines
    if show_adr_fib_1_618
        // 161.8% Fib up line
        adr_fib_1_618_up_line := line.new(bar_index, adr_fib_1_618_up, bar_index, adr_fib_1_618_up, color=adr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(adr_fib_1_618_up_lines, adr_fib_1_618_up_line)
        manage_line_history(adr_fib_1_618_up_lines)

        // 161.8% Fib up label
        if show_labels
            adr_fib_1_618_up_label := label.new(bar_index + label_x_offset_bars, adr_fib_1_618_up + label_y_offset, "161.8% ADR+" + (show_price_in_label ? str.format(" ({0})", adr_fib_1_618_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(adr_fib_1_618_up_labels, adr_fib_1_618_up_label)

        // 161.8% Fib down line
        adr_fib_1_618_down_line := line.new(bar_index, adr_fib_1_618_down, bar_index, adr_fib_1_618_down, color=adr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(adr_fib_1_618_down_lines, adr_fib_1_618_down_line)
        manage_line_history(adr_fib_1_618_down_lines)

        // 161.8% Fib down label
        if show_labels
            adr_fib_1_618_down_label := label.new(bar_index + label_x_offset_bars, adr_fib_1_618_down + label_y_offset, "161.8% ADR-" + (show_price_in_label ? str.format(" ({0})", adr_fib_1_618_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(adr_fib_1_618_down_labels, adr_fib_1_618_down_label)

    // All lines are now managed by their respective arrays

// Check if we're at the start of a new week or true week open
if (is_new_week or (is_true_week_start and not is_true_week_start[1])) and show_weekly_levels
    // Store the current AWR value in history and for this week's use
    array.unshift(weekly_range_history, current_awr)
    if array.size(weekly_range_history) > awr_weeks
        array.pop(weekly_range_history)

    // Update the weekly range value for this week
    weekly_range_val := current_awr

    // Update true week open price - using the price at the true week open time (ICT methodology)
    if is_true_week_start and not is_true_week_start[1]
        true_week_open_price := open
        true_week_line_active := true

    // Calculate AWR levels
    full_awr_up := true_week_open_price + weekly_range_val
    full_awr_down := true_week_open_price - weekly_range_val
    one_third_awr_up := true_week_open_price + (weekly_range_val / 3)
    one_third_awr_down := true_week_open_price - (weekly_range_val / 3)
    two_third_awr_up := true_week_open_price + (weekly_range_val * 2 / 3)
    two_third_awr_down := true_week_open_price - (weekly_range_val * 2 / 3)
    half_awr_up := true_week_open_price + (weekly_range_val / 2)
    half_awr_down := true_week_open_price - (weekly_range_val / 2)

    // Calculate AWR Multiplier levels
    awr_mult_1_5_up := true_week_open_price + (weekly_range_val * 1.5)
    awr_mult_1_5_down := true_week_open_price - (weekly_range_val * 1.5)
    awr_mult_2_0_up := true_week_open_price + (weekly_range_val * 2.0)
    awr_mult_2_0_down := true_week_open_price - (weekly_range_val * 2.0)

    // Calculate AWR Fibonacci Extension levels
    awr_fib_1_272_up := true_week_open_price + (weekly_range_val * 1.272)
    awr_fib_1_272_down := true_week_open_price - (weekly_range_val * 1.272)
    awr_fib_1_618_up := true_week_open_price + (weekly_range_val * 1.618)
    awr_fib_1_618_down := true_week_open_price - (weekly_range_val * 1.618)

    // Create true week open line
    true_week_open_line := line.new(bar_index, true_week_open_price, bar_index, true_week_open_price, color=true_week_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(two_lines, true_week_open_line)
    manage_line_history(two_lines)

    // Delete old labels when a new TWO appears
    if show_labels
        delete_all_labels(two_labels)
        delete_all_labels(full_awr_up_labels)
        delete_all_labels(full_awr_down_labels)
        delete_all_labels(one_third_awr_up_labels)
        delete_all_labels(one_third_awr_down_labels)
        delete_all_labels(two_third_awr_up_labels)
        delete_all_labels(two_third_awr_down_labels)
        delete_all_labels(half_awr_up_labels)
        delete_all_labels(half_awr_down_labels)
        delete_all_labels(awr_mult_1_5_up_labels)
        delete_all_labels(awr_mult_1_5_down_labels)
        delete_all_labels(awr_mult_2_0_up_labels)
        delete_all_labels(awr_mult_2_0_down_labels)
        delete_all_labels(awr_fib_1_272_up_labels)
        delete_all_labels(awr_fib_1_272_down_labels)
        delete_all_labels(awr_fib_1_618_up_labels)
        delete_all_labels(awr_fib_1_618_down_labels)

        // Clear arrays for merging overlapping labels
        array.clear(weekly_price_levels)
        array.clear(weekly_label_texts)

        // Create true week open label
        true_week_open_label := label.new(bar_index + label_x_offset_bars, true_week_open_price + label_y_offset, "TWO" + (show_price_in_label ? str.format(" ({0})", true_week_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_week_open_color, color=color.new(color.black, 100))
        array.push(two_labels, true_week_open_label)

    // Create vertical line at week open
    if show_vertical
        true_week_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_week_open_color, style=line.style_dashed)
        array.unshift(weekly_vertical_lines, true_week_vertical)
        manage_line_history(weekly_vertical_lines)

    // Create AWR level lines
    if show_full_awr
        // Full AWR up line
        full_awr_up_line := line.new(bar_index, full_awr_up, bar_index, full_awr_up, color=full_awr_color, width=line_width, style=line_style_value)
        array.unshift(full_awr_up_lines, full_awr_up_line)
        manage_line_history(full_awr_up_lines)

        // Full AWR up label
        if show_labels
            full_awr_up_label := label.new(bar_index + label_x_offset_bars, full_awr_up + label_y_offset, "AWR+" + (show_price_in_label ? str.format(" ({0})", full_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_awr_color, color=color.new(color.black, 100))
            array.push(full_awr_up_labels, full_awr_up_label)

        // Full AWR down line
        full_awr_down_line := line.new(bar_index, full_awr_down, bar_index, full_awr_down, color=full_awr_color, width=line_width, style=line_style_value)
        array.unshift(full_awr_down_lines, full_awr_down_line)
        manage_line_history(full_awr_down_lines)

        // Full AWR down label
        if show_labels
            full_awr_down_label := label.new(bar_index + label_x_offset_bars, full_awr_down + label_y_offset, "AWR-" + (show_price_in_label ? str.format(" ({0})", full_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_awr_color, color=color.new(color.black, 100))
            array.push(full_awr_down_labels, full_awr_down_label)

    // Create 1/3 AWR level lines
    if show_one_third_awr
        // 1/3 AWR up line
        one_third_awr_up_line := line.new(bar_index, one_third_awr_up, bar_index, one_third_awr_up, color=one_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_awr_up_lines, one_third_awr_up_line)
        manage_line_history(one_third_awr_up_lines)

        // 1/3 AWR up label
        if show_labels
            one_third_awr_up_label := label.new(bar_index + label_x_offset_bars, one_third_awr_up + label_y_offset, "1/3 AWR+" + (show_price_in_label ? str.format(" ({0})", one_third_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_awr_color, color=color.new(color.black, 100))
            array.push(one_third_awr_up_labels, one_third_awr_up_label)

        // 1/3 AWR down line
        one_third_awr_down_line := line.new(bar_index, one_third_awr_down, bar_index, one_third_awr_down, color=one_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_awr_down_lines, one_third_awr_down_line)
        manage_line_history(one_third_awr_down_lines)

        // 1/3 AWR down label
        if show_labels
            one_third_awr_down_label := label.new(bar_index + label_x_offset_bars, one_third_awr_down + label_y_offset, "1/3 AWR-" + (show_price_in_label ? str.format(" ({0})", one_third_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_awr_color, color=color.new(color.black, 100))
            array.push(one_third_awr_down_labels, one_third_awr_down_label)

    // Create 2/3 AWR level lines
    if show_two_third_awr
        // 2/3 AWR up line
        two_third_awr_up_line := line.new(bar_index, two_third_awr_up, bar_index, two_third_awr_up, color=two_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_awr_up_lines, two_third_awr_up_line)
        manage_line_history(two_third_awr_up_lines)

        // 2/3 AWR up label
        if show_labels
            two_third_awr_up_label := label.new(bar_index + label_x_offset_bars, two_third_awr_up + label_y_offset, "2/3 AWR+" + (show_price_in_label ? str.format(" ({0})", two_third_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_awr_color, color=color.new(color.black, 100))
            array.push(two_third_awr_up_labels, two_third_awr_up_label)

        // 2/3 AWR down line
        two_third_awr_down_line := line.new(bar_index, two_third_awr_down, bar_index, two_third_awr_down, color=two_third_awr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_awr_down_lines, two_third_awr_down_line)
        manage_line_history(two_third_awr_down_lines)

        // 2/3 AWR down label
        if show_labels
            two_third_awr_down_label := label.new(bar_index + label_x_offset_bars, two_third_awr_down + label_y_offset, "2/3 AWR-" + (show_price_in_label ? str.format(" ({0})", two_third_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_awr_color, color=color.new(color.black, 100))
            array.push(two_third_awr_down_labels, two_third_awr_down_label)

    // Create 1/2 AWR level lines
    if show_half_awr
        // 1/2 AWR up line
        half_awr_up_line := line.new(bar_index, half_awr_up, bar_index, half_awr_up, color=half_awr_color, width=line_width, style=line_style_value)
        array.unshift(half_awr_up_lines, half_awr_up_line)
        manage_line_history(half_awr_up_lines)

        // 1/2 AWR up label
        if show_labels
            half_awr_up_label := label.new(bar_index + label_x_offset_bars, half_awr_up + label_y_offset, "1/2 AWR+" + (show_price_in_label ? str.format(" ({0})", half_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_awr_color, color=color.new(color.black, 100))
            array.push(half_awr_up_labels, half_awr_up_label)

        // 1/2 AWR down line
        half_awr_down_line := line.new(bar_index, half_awr_down, bar_index, half_awr_down, color=half_awr_color, width=line_width, style=line_style_value)
        array.unshift(half_awr_down_lines, half_awr_down_line)
        manage_line_history(half_awr_down_lines)

        // 1/2 AWR down label
        if show_labels
            half_awr_down_label := label.new(bar_index + label_x_offset_bars, half_awr_down + label_y_offset, "1/2 AWR-" + (show_price_in_label ? str.format(" ({0})", half_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_awr_color, color=color.new(color.black, 100))
            array.push(half_awr_down_labels, half_awr_down_label)

    // Create 1.5x AWR level lines
    if show_awr_mult_1_5
        // 1.5x AWR up line
        awr_mult_1_5_up_line := line.new(bar_index, awr_mult_1_5_up, bar_index, awr_mult_1_5_up, color=awr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(awr_mult_1_5_up_lines, awr_mult_1_5_up_line)
        manage_line_history(awr_mult_1_5_up_lines)

        // 1.5x AWR up label
        if show_labels
            awr_mult_1_5_up_label := label.new(bar_index + label_x_offset_bars, awr_mult_1_5_up + label_y_offset, "1.5x AWR+" + (show_price_in_label ? str.format(" ({0})", awr_mult_1_5_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(awr_mult_1_5_up_labels, awr_mult_1_5_up_label)

        // 1.5x AWR down line
        awr_mult_1_5_down_line := line.new(bar_index, awr_mult_1_5_down, bar_index, awr_mult_1_5_down, color=awr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(awr_mult_1_5_down_lines, awr_mult_1_5_down_line)
        manage_line_history(awr_mult_1_5_down_lines)

        // 1.5x AWR down label
        if show_labels
            awr_mult_1_5_down_label := label.new(bar_index + label_x_offset_bars, awr_mult_1_5_down + label_y_offset, "1.5x AWR-" + (show_price_in_label ? str.format(" ({0})", awr_mult_1_5_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(awr_mult_1_5_down_labels, awr_mult_1_5_down_label)

    // Create 2.0x AWR level lines
    if show_awr_mult_2_0
        // 2.0x AWR up line
        awr_mult_2_0_up_line := line.new(bar_index, awr_mult_2_0_up, bar_index, awr_mult_2_0_up, color=awr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(awr_mult_2_0_up_lines, awr_mult_2_0_up_line)
        manage_line_history(awr_mult_2_0_up_lines)

        // 2.0x AWR up label
        if show_labels
            awr_mult_2_0_up_label := label.new(bar_index + label_x_offset_bars, awr_mult_2_0_up + label_y_offset, "2.0x AWR+" + (show_price_in_label ? str.format(" ({0})", awr_mult_2_0_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(awr_mult_2_0_up_labels, awr_mult_2_0_up_label)

        // 2.0x AWR down line
        awr_mult_2_0_down_line := line.new(bar_index, awr_mult_2_0_down, bar_index, awr_mult_2_0_down, color=awr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(awr_mult_2_0_down_lines, awr_mult_2_0_down_line)
        manage_line_history(awr_mult_2_0_down_lines)

        // 2.0x AWR down label
        if show_labels
            awr_mult_2_0_down_label := label.new(bar_index + label_x_offset_bars, awr_mult_2_0_down + label_y_offset, "2.0x AWR-" + (show_price_in_label ? str.format(" ({0})", awr_mult_2_0_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(awr_mult_2_0_down_labels, awr_mult_2_0_down_label)

    // Create 127.2% Fibonacci Extension level lines
    if show_awr_fib_1_272
        // 127.2% Fib up line
        awr_fib_1_272_up_line := line.new(bar_index, awr_fib_1_272_up, bar_index, awr_fib_1_272_up, color=awr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(awr_fib_1_272_up_lines, awr_fib_1_272_up_line)
        manage_line_history(awr_fib_1_272_up_lines)

        // 127.2% Fib up label
        if show_labels
            awr_fib_1_272_up_label := label.new(bar_index + label_x_offset_bars, awr_fib_1_272_up + label_y_offset, "127.2% AWR+" + (show_price_in_label ? str.format(" ({0})", awr_fib_1_272_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(awr_fib_1_272_up_labels, awr_fib_1_272_up_label)

        // 127.2% Fib down line
        awr_fib_1_272_down_line := line.new(bar_index, awr_fib_1_272_down, bar_index, awr_fib_1_272_down, color=awr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(awr_fib_1_272_down_lines, awr_fib_1_272_down_line)
        manage_line_history(awr_fib_1_272_down_lines)

        // 127.2% Fib down label
        if show_labels
            awr_fib_1_272_down_label := label.new(bar_index + label_x_offset_bars, awr_fib_1_272_down + label_y_offset, "127.2% AWR-" + (show_price_in_label ? str.format(" ({0})", awr_fib_1_272_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(awr_fib_1_272_down_labels, awr_fib_1_272_down_label)

    // Create 161.8% Fibonacci Extension level lines
    if show_awr_fib_1_618
        // 161.8% Fib up line
        awr_fib_1_618_up_line := line.new(bar_index, awr_fib_1_618_up, bar_index, awr_fib_1_618_up, color=awr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(awr_fib_1_618_up_lines, awr_fib_1_618_up_line)
        manage_line_history(awr_fib_1_618_up_lines)

        // 161.8% Fib up label
        if show_labels
            awr_fib_1_618_up_label := label.new(bar_index + label_x_offset_bars, awr_fib_1_618_up + label_y_offset, "161.8% AWR+" + (show_price_in_label ? str.format(" ({0})", awr_fib_1_618_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(awr_fib_1_618_up_labels, awr_fib_1_618_up_label)

        // 161.8% Fib down line
        awr_fib_1_618_down_line := line.new(bar_index, awr_fib_1_618_down, bar_index, awr_fib_1_618_down, color=awr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(awr_fib_1_618_down_lines, awr_fib_1_618_down_line)
        manage_line_history(awr_fib_1_618_down_lines)

        // 161.8% Fib down label
        if show_labels
            awr_fib_1_618_down_label := label.new(bar_index + label_x_offset_bars, awr_fib_1_618_down + label_y_offset, "161.8% AWR-" + (show_price_in_label ? str.format(" ({0})", awr_fib_1_618_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=awr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(awr_fib_1_618_down_labels, awr_fib_1_618_down_label)

// Check if we're at the start of a new month or true month open
if (is_new_month or (is_true_month_start and not is_true_month_start[1])) and show_monthly_levels
    // Store the current AMR value in history and for this month's use
    array.unshift(monthly_range_history, current_amr)
    if array.size(monthly_range_history) > amr_months
        array.pop(monthly_range_history)

    // Update the monthly range value for this month
    monthly_range_val := current_amr

    // Update true month open price - using the price at the true month open time (ICT methodology)
    if is_true_month_start and not is_true_month_start[1]
        true_month_open_price := open
        true_month_line_active := true

    // Calculate AMR levels
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Calculate AMR Multiplier levels
    amr_mult_1_5_up := true_month_open_price + (monthly_range_val * 1.5)
    amr_mult_1_5_down := true_month_open_price - (monthly_range_val * 1.5)
    amr_mult_2_0_up := true_month_open_price + (monthly_range_val * 2.0)
    amr_mult_2_0_down := true_month_open_price - (monthly_range_val * 2.0)

    // Calculate AMR Fibonacci Extension levels
    amr_fib_1_272_up := true_month_open_price + (monthly_range_val * 1.272)
    amr_fib_1_272_down := true_month_open_price - (monthly_range_val * 1.272)
    amr_fib_1_618_up := true_month_open_price + (monthly_range_val * 1.618)
    amr_fib_1_618_down := true_month_open_price - (monthly_range_val * 1.618)

    // Create true month open line
    true_month_open_line := line.new(bar_index, true_month_open_price, bar_index, true_month_open_price, color=true_month_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tmo_lines, true_month_open_line)
    manage_line_history(tmo_lines)

    // Delete old labels when a new TMO appears
    if show_labels
        delete_all_labels(tmo_labels)
        delete_all_labels(full_amr_up_labels)
        delete_all_labels(full_amr_down_labels)
        delete_all_labels(one_third_amr_up_labels)
        delete_all_labels(one_third_amr_down_labels)
        delete_all_labels(two_third_amr_up_labels)
        delete_all_labels(two_third_amr_down_labels)
        delete_all_labels(half_amr_up_labels)
        delete_all_labels(half_amr_down_labels)
        delete_all_labels(amr_mult_1_5_up_labels)
        delete_all_labels(amr_mult_1_5_down_labels)
        delete_all_labels(amr_mult_2_0_up_labels)
        delete_all_labels(amr_mult_2_0_down_labels)
        delete_all_labels(amr_fib_1_272_up_labels)
        delete_all_labels(amr_fib_1_272_down_labels)
        delete_all_labels(amr_fib_1_618_up_labels)
        delete_all_labels(amr_fib_1_618_down_labels)

        // Clear arrays for merging overlapping labels
        array.clear(monthly_price_levels)
        array.clear(monthly_label_texts)

        // Create true month open label
        true_month_open_label := label.new(bar_index + label_x_offset_bars, true_month_open_price + label_y_offset, "TMO" + (show_price_in_label ? str.format(" ({0})", true_month_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_month_open_color, color=color.new(color.black, 100))
        array.push(tmo_labels, true_month_open_label)

    // Create vertical line at month open
    if show_vertical
        true_month_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_month_open_color, style=line.style_dashed)
        array.unshift(monthly_vertical_lines, true_month_vertical)
        manage_line_history(monthly_vertical_lines)

    // Create AMR level lines
    if show_full_amr
        // Full AMR up line
        full_amr_up_line := line.new(bar_index, full_amr_up, bar_index, full_amr_up, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_up_lines, full_amr_up_line)
        manage_line_history(full_amr_up_lines)

        // Full AMR up label
        if show_labels
            full_amr_up_label := label.new(bar_index + label_x_offset_bars, full_amr_up + label_y_offset, "AMR+" + (show_price_in_label ? str.format(" ({0})", full_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_up_labels, full_amr_up_label)

        // Full AMR down line
        full_amr_down_line := line.new(bar_index, full_amr_down, bar_index, full_amr_down, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_down_lines, full_amr_down_line)
        manage_line_history(full_amr_down_lines)

        // Full AMR down label
        if show_labels
            full_amr_down_label := label.new(bar_index + label_x_offset_bars, full_amr_down + label_y_offset, "AMR-" + (show_price_in_label ? str.format(" ({0})", full_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_down_labels, full_amr_down_label)

    // Create 1/3 AMR level lines
    if show_one_third_amr
        // 1/3 AMR up line
        one_third_amr_up_line := line.new(bar_index, one_third_amr_up, bar_index, one_third_amr_up, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_up_lines, one_third_amr_up_line)
        manage_line_history(one_third_amr_up_lines)

        // 1/3 AMR up label
        if show_labels
            one_third_amr_up_label := label.new(bar_index + label_x_offset_bars, one_third_amr_up + label_y_offset, "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", one_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_up_labels, one_third_amr_up_label)

        // 1/3 AMR down line
        one_third_amr_down_line := line.new(bar_index, one_third_amr_down, bar_index, one_third_amr_down, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_down_lines, one_third_amr_down_line)
        manage_line_history(one_third_amr_down_lines)

        // 1/3 AMR down label
        if show_labels
            one_third_amr_down_label := label.new(bar_index + label_x_offset_bars, one_third_amr_down + label_y_offset, "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", one_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_down_labels, one_third_amr_down_label)

    // Create 2/3 AMR level lines
    if show_two_third_amr
        // 2/3 AMR up line
        two_third_amr_up_line := line.new(bar_index, two_third_amr_up, bar_index, two_third_amr_up, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_up_lines, two_third_amr_up_line)
        manage_line_history(two_third_amr_up_lines)

        // 2/3 AMR up label
        if show_labels
            two_third_amr_up_label := label.new(bar_index + label_x_offset_bars, two_third_amr_up + label_y_offset, "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", two_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_up_labels, two_third_amr_up_label)

        // 2/3 AMR down line
        two_third_amr_down_line := line.new(bar_index, two_third_amr_down, bar_index, two_third_amr_down, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_down_lines, two_third_amr_down_line)
        manage_line_history(two_third_amr_down_lines)

        // 2/3 AMR down label
        if show_labels
            two_third_amr_down_label := label.new(bar_index + label_x_offset_bars, two_third_amr_down + label_y_offset, "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", two_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_down_labels, two_third_amr_down_label)

    // Create 1/2 AMR level lines
    if show_half_amr
        // 1/2 AMR up line
        half_amr_up_line := line.new(bar_index, half_amr_up, bar_index, half_amr_up, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_up_lines, half_amr_up_line)
        manage_line_history(half_amr_up_lines)

        // 1/2 AMR up label
        if show_labels
            half_amr_up_label := label.new(bar_index + label_x_offset_bars, half_amr_up + label_y_offset, "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", half_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_up_labels, half_amr_up_label)

        // 1/2 AMR down line
        half_amr_down_line := line.new(bar_index, half_amr_down, bar_index, half_amr_down, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_down_lines, half_amr_down_line)
        manage_line_history(half_amr_down_lines)

        // 1/2 AMR down label
        if show_labels
            half_amr_down_label := label.new(bar_index + label_x_offset_bars, half_amr_down + label_y_offset, "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", half_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_down_labels, half_amr_down_label)

    // Create 1.5x AMR level lines
    if show_amr_mult_1_5
        // 1.5x AMR up line
        amr_mult_1_5_up_line := line.new(bar_index, amr_mult_1_5_up, bar_index, amr_mult_1_5_up, color=amr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_1_5_up_lines, amr_mult_1_5_up_line)
        manage_line_history(amr_mult_1_5_up_lines)

        // 1.5x AMR up label
        if show_labels
            amr_mult_1_5_up_label := label.new(bar_index + label_x_offset_bars, amr_mult_1_5_up + label_y_offset, "1.5x AMR+" + (show_price_in_label ? str.format(" ({0})", amr_mult_1_5_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(amr_mult_1_5_up_labels, amr_mult_1_5_up_label)

        // 1.5x AMR down line
        amr_mult_1_5_down_line := line.new(bar_index, amr_mult_1_5_down, bar_index, amr_mult_1_5_down, color=amr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_1_5_down_lines, amr_mult_1_5_down_line)
        manage_line_history(amr_mult_1_5_down_lines)

        // 1.5x AMR down label
        if show_labels
            amr_mult_1_5_down_label := label.new(bar_index + label_x_offset_bars, amr_mult_1_5_down + label_y_offset, "1.5x AMR-" + (show_price_in_label ? str.format(" ({0})", amr_mult_1_5_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(amr_mult_1_5_down_labels, amr_mult_1_5_down_label)

    // Create 2.0x AMR level lines
    if show_amr_mult_2_0
        // 2.0x AMR up line
        amr_mult_2_0_up_line := line.new(bar_index, amr_mult_2_0_up, bar_index, amr_mult_2_0_up, color=amr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_2_0_up_lines, amr_mult_2_0_up_line)
        manage_line_history(amr_mult_2_0_up_lines)

        // 2.0x AMR up label
        if show_labels
            amr_mult_2_0_up_label := label.new(bar_index + label_x_offset_bars, amr_mult_2_0_up + label_y_offset, "2.0x AMR+" + (show_price_in_label ? str.format(" ({0})", amr_mult_2_0_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(amr_mult_2_0_up_labels, amr_mult_2_0_up_label)

        // 2.0x AMR down line
        amr_mult_2_0_down_line := line.new(bar_index, amr_mult_2_0_down, bar_index, amr_mult_2_0_down, color=amr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(amr_mult_2_0_down_lines, amr_mult_2_0_down_line)
        manage_line_history(amr_mult_2_0_down_lines)

        // 2.0x AMR down label
        if show_labels
            amr_mult_2_0_down_label := label.new(bar_index + label_x_offset_bars, amr_mult_2_0_down + label_y_offset, "2.0x AMR-" + (show_price_in_label ? str.format(" ({0})", amr_mult_2_0_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(amr_mult_2_0_down_labels, amr_mult_2_0_down_label)

    // Create 127.2% Fibonacci Extension level lines
    if show_amr_fib_1_272
        // 127.2% Fib up line
        amr_fib_1_272_up_line := line.new(bar_index, amr_fib_1_272_up, bar_index, amr_fib_1_272_up, color=amr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_272_up_lines, amr_fib_1_272_up_line)
        manage_line_history(amr_fib_1_272_up_lines)

        // 127.2% Fib up label
        if show_labels
            amr_fib_1_272_up_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_272_up + label_y_offset, "127.2% AMR+" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_272_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_272_up_labels, amr_fib_1_272_up_label)

        // 127.2% Fib down line
        amr_fib_1_272_down_line := line.new(bar_index, amr_fib_1_272_down, bar_index, amr_fib_1_272_down, color=amr_fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_272_down_lines, amr_fib_1_272_down_line)
        manage_line_history(amr_fib_1_272_down_lines)

        // 127.2% Fib down label
        if show_labels
            amr_fib_1_272_down_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_272_down + label_y_offset, "127.2% AMR-" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_272_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_272_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_272_down_labels, amr_fib_1_272_down_label)

    // Create 161.8% Fibonacci Extension level lines
    if show_amr_fib_1_618
        // 161.8% Fib up line
        amr_fib_1_618_up_line := line.new(bar_index, amr_fib_1_618_up, bar_index, amr_fib_1_618_up, color=amr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_618_up_lines, amr_fib_1_618_up_line)
        manage_line_history(amr_fib_1_618_up_lines)

        // 161.8% Fib up label
        if show_labels
            amr_fib_1_618_up_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_618_up + label_y_offset, "161.8% AMR+" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_618_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_618_up_labels, amr_fib_1_618_up_label)

        // 161.8% Fib down line
        amr_fib_1_618_down_line := line.new(bar_index, amr_fib_1_618_down, bar_index, amr_fib_1_618_down, color=amr_fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(amr_fib_1_618_down_lines, amr_fib_1_618_down_line)
        manage_line_history(amr_fib_1_618_down_lines)

        // 161.8% Fib down label
        if show_labels
            amr_fib_1_618_down_label := label.new(bar_index + label_x_offset_bars, amr_fib_1_618_down + label_y_offset, "161.8% AMR-" + (show_price_in_label ? str.format(" ({0})", amr_fib_1_618_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=amr_fib_1_618_color, color=color.new(color.black, 100))
            array.push(amr_fib_1_618_down_labels, amr_fib_1_618_down_label)

// Update lines during the current day
if true_day_line_active
    // Update true day open line
    line.set_x2(true_day_open_line, bar_index)
    line.set_y2(true_day_open_line, true_day_open_price)

    // Update true day open label
    if show_labels
        label.set_x(true_day_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_day_open_label, true_day_open_price + label_y_offset)

    // Update ADR level lines
    if show_full_adr
        line.set_x2(full_adr_up_line, bar_index)
        line.set_y2(full_adr_up_line, full_adr_up)
        line.set_x2(full_adr_down_line, bar_index)
        line.set_y2(full_adr_down_line, full_adr_down)

        if show_labels
            label.set_x(full_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_up_label, full_adr_up + label_y_offset)
            label.set_x(full_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_down_label, full_adr_down + label_y_offset)

    if show_one_third_adr
        line.set_x2(one_third_adr_up_line, bar_index)
        line.set_y2(one_third_adr_up_line, one_third_adr_up)
        line.set_x2(one_third_adr_down_line, bar_index)
        line.set_y2(one_third_adr_down_line, one_third_adr_down)

        if show_labels
            label.set_x(one_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_up_label, one_third_adr_up + label_y_offset)
            label.set_x(one_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_down_label, one_third_adr_down + label_y_offset)

    if show_two_third_adr
        line.set_x2(two_third_adr_up_line, bar_index)
        line.set_y2(two_third_adr_up_line, two_third_adr_up)
        line.set_x2(two_third_adr_down_line, bar_index)
        line.set_y2(two_third_adr_down_line, two_third_adr_down)

        if show_labels
            label.set_x(two_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_up_label, two_third_adr_up + label_y_offset)
            label.set_x(two_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_down_label, two_third_adr_down + label_y_offset)

    if show_half_adr
        line.set_x2(half_adr_up_line, bar_index)
        line.set_y2(half_adr_up_line, half_adr_up)
        line.set_x2(half_adr_down_line, bar_index)
        line.set_y2(half_adr_down_line, half_adr_down)

        if show_labels
            label.set_x(half_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_up_label, half_adr_up + label_y_offset)
            label.set_x(half_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_down_label, half_adr_down + label_y_offset)

    // Update ADR Multiplier lines
    if show_adr_mult_1_5
        line.set_x2(adr_mult_1_5_up_line, bar_index)
        line.set_y2(adr_mult_1_5_up_line, adr_mult_1_5_up)
        line.set_x2(adr_mult_1_5_down_line, bar_index)
        line.set_y2(adr_mult_1_5_down_line, adr_mult_1_5_down)

        if show_labels
            label.set_x(adr_mult_1_5_up_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_1_5_up_label, adr_mult_1_5_up + label_y_offset)
            label.set_x(adr_mult_1_5_down_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_1_5_down_label, adr_mult_1_5_down + label_y_offset)

    if show_adr_mult_2_0
        line.set_x2(adr_mult_2_0_up_line, bar_index)
        line.set_y2(adr_mult_2_0_up_line, adr_mult_2_0_up)
        line.set_x2(adr_mult_2_0_down_line, bar_index)
        line.set_y2(adr_mult_2_0_down_line, adr_mult_2_0_down)

        if show_labels
            label.set_x(adr_mult_2_0_up_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_2_0_up_label, adr_mult_2_0_up + label_y_offset)
            label.set_x(adr_mult_2_0_down_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_2_0_down_label, adr_mult_2_0_down + label_y_offset)

    // Update ADR Fibonacci Extension lines
    if show_adr_fib_1_272
        line.set_x2(adr_fib_1_272_up_line, bar_index)
        line.set_y2(adr_fib_1_272_up_line, adr_fib_1_272_up)
        line.set_x2(adr_fib_1_272_down_line, bar_index)
        line.set_y2(adr_fib_1_272_down_line, adr_fib_1_272_down)

        if show_labels
            label.set_x(adr_fib_1_272_up_label, bar_index + label_x_offset_bars)
            label.set_y(adr_fib_1_272_up_label, adr_fib_1_272_up + label_y_offset)
            label.set_x(adr_fib_1_272_down_label, bar_index + label_x_offset_bars)
            label.set_y(adr_fib_1_272_down_label, adr_fib_1_272_down + label_y_offset)

    if show_adr_fib_1_618
        line.set_x2(adr_fib_1_618_up_line, bar_index)
        line.set_y2(adr_fib_1_618_up_line, adr_fib_1_618_up)
        line.set_x2(adr_fib_1_618_down_line, bar_index)
        line.set_y2(adr_fib_1_618_down_line, adr_fib_1_618_down)

        if show_labels
            label.set_x(adr_fib_1_618_up_label, bar_index + label_x_offset_bars)
            label.set_y(adr_fib_1_618_up_label, adr_fib_1_618_up + label_y_offset)
            label.set_x(adr_fib_1_618_down_label, bar_index + label_x_offset_bars)
            label.set_y(adr_fib_1_618_down_label, adr_fib_1_618_down + label_y_offset)

// Update lines during the current week
if true_week_line_active and show_weekly_levels
    // Update true week open line
    line.set_x2(true_week_open_line, bar_index)
    line.set_y2(true_week_open_line, true_week_open_price)

    // Update true week open label
    if show_labels
        label.set_x(true_week_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_week_open_label, true_week_open_price + label_y_offset)

    // Update AWR level lines
    if show_full_awr
        line.set_x2(full_awr_up_line, bar_index)
        line.set_y2(full_awr_up_line, full_awr_up)
        line.set_x2(full_awr_down_line, bar_index)
        line.set_y2(full_awr_down_line, full_awr_down)

        if show_labels
            label.set_x(full_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_awr_up_label, full_awr_up + label_y_offset)
            label.set_x(full_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_awr_down_label, full_awr_down + label_y_offset)

    if show_one_third_awr
        line.set_x2(one_third_awr_up_line, bar_index)
        line.set_y2(one_third_awr_up_line, one_third_awr_up)
        line.set_x2(one_third_awr_down_line, bar_index)
        line.set_y2(one_third_awr_down_line, one_third_awr_down)

        if show_labels
            label.set_x(one_third_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_awr_up_label, one_third_awr_up + label_y_offset)
            label.set_x(one_third_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_awr_down_label, one_third_awr_down + label_y_offset)

    if show_two_third_awr
        line.set_x2(two_third_awr_up_line, bar_index)
        line.set_y2(two_third_awr_up_line, two_third_awr_up)
        line.set_x2(two_third_awr_down_line, bar_index)
        line.set_y2(two_third_awr_down_line, two_third_awr_down)

        if show_labels
            label.set_x(two_third_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_awr_up_label, two_third_awr_up + label_y_offset)
            label.set_x(two_third_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_awr_down_label, two_third_awr_down + label_y_offset)

    if show_half_awr
        line.set_x2(half_awr_up_line, bar_index)
        line.set_y2(half_awr_up_line, half_awr_up)
        line.set_x2(half_awr_down_line, bar_index)
        line.set_y2(half_awr_down_line, half_awr_down)

        if show_labels
            label.set_x(half_awr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_awr_up_label, half_awr_up + label_y_offset)
            label.set_x(half_awr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_awr_down_label, half_awr_down + label_y_offset)

    // Update AWR Multiplier lines
    if show_awr_mult_1_5
        line.set_x2(awr_mult_1_5_up_line, bar_index)
        line.set_y2(awr_mult_1_5_up_line, awr_mult_1_5_up)
        line.set_x2(awr_mult_1_5_down_line, bar_index)
        line.set_y2(awr_mult_1_5_down_line, awr_mult_1_5_down)

        if show_labels
            label.set_x(awr_mult_1_5_up_label, bar_index + label_x_offset_bars)
            label.set_y(awr_mult_1_5_up_label, awr_mult_1_5_up + label_y_offset)
            label.set_x(awr_mult_1_5_down_label, bar_index + label_x_offset_bars)
            label.set_y(awr_mult_1_5_down_label, awr_mult_1_5_down + label_y_offset)

    if show_awr_mult_2_0
        line.set_x2(awr_mult_2_0_up_line, bar_index)
        line.set_y2(awr_mult_2_0_up_line, awr_mult_2_0_up)
        line.set_x2(awr_mult_2_0_down_line, bar_index)
        line.set_y2(awr_mult_2_0_down_line, awr_mult_2_0_down)

        if show_labels
            label.set_x(awr_mult_2_0_up_label, bar_index + label_x_offset_bars)
            label.set_y(awr_mult_2_0_up_label, awr_mult_2_0_up + label_y_offset)
            label.set_x(awr_mult_2_0_down_label, bar_index + label_x_offset_bars)
            label.set_y(awr_mult_2_0_down_label, awr_mult_2_0_down + label_y_offset)

    // Update AWR Fibonacci Extension lines
    if show_awr_fib_1_272
        line.set_x2(awr_fib_1_272_up_line, bar_index)
        line.set_y2(awr_fib_1_272_up_line, awr_fib_1_272_up)
        line.set_x2(awr_fib_1_272_down_line, bar_index)
        line.set_y2(awr_fib_1_272_down_line, awr_fib_1_272_down)

        if show_labels
            label.set_x(awr_fib_1_272_up_label, bar_index + label_x_offset_bars)
            label.set_y(awr_fib_1_272_up_label, awr_fib_1_272_up + label_y_offset)
            label.set_x(awr_fib_1_272_down_label, bar_index + label_x_offset_bars)
            label.set_y(awr_fib_1_272_down_label, awr_fib_1_272_down + label_y_offset)

    if show_awr_fib_1_618
        line.set_x2(awr_fib_1_618_up_line, bar_index)
        line.set_y2(awr_fib_1_618_up_line, awr_fib_1_618_up)
        line.set_x2(awr_fib_1_618_down_line, bar_index)
        line.set_y2(awr_fib_1_618_down_line, awr_fib_1_618_down)

        if show_labels
            label.set_x(awr_fib_1_618_up_label, bar_index + label_x_offset_bars)
            label.set_y(awr_fib_1_618_up_label, awr_fib_1_618_up + label_y_offset)
            label.set_x(awr_fib_1_618_down_label, bar_index + label_x_offset_bars)
            label.set_y(awr_fib_1_618_down_label, awr_fib_1_618_down + label_y_offset)

// Update lines during the current month
if true_month_line_active and show_monthly_levels
    // Update true month open line
    line.set_x2(true_month_open_line, bar_index)
    line.set_y2(true_month_open_line, true_month_open_price)

    // Update true month open label
    if show_labels
        label.set_x(true_month_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_month_open_label, true_month_open_price + label_y_offset)

    // Update AMR level lines
    if show_full_amr
        line.set_x2(full_amr_up_line, bar_index)
        line.set_y2(full_amr_up_line, full_amr_up)
        line.set_x2(full_amr_down_line, bar_index)
        line.set_y2(full_amr_down_line, full_amr_down)

        if show_labels
            label.set_x(full_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_amr_up_label, full_amr_up + label_y_offset)
            label.set_x(full_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_amr_down_label, full_amr_down + label_y_offset)

    if show_one_third_amr
        line.set_x2(one_third_amr_up_line, bar_index)
        line.set_y2(one_third_amr_up_line, one_third_amr_up)
        line.set_x2(one_third_amr_down_line, bar_index)
        line.set_y2(one_third_amr_down_line, one_third_amr_down)

        if show_labels
            label.set_x(one_third_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_amr_up_label, one_third_amr_up + label_y_offset)
            label.set_x(one_third_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_amr_down_label, one_third_amr_down + label_y_offset)

    if show_two_third_amr
        line.set_x2(two_third_amr_up_line, bar_index)
        line.set_y2(two_third_amr_up_line, two_third_amr_up)
        line.set_x2(two_third_amr_down_line, bar_index)
        line.set_y2(two_third_amr_down_line, two_third_amr_down)

        if show_labels
            label.set_x(two_third_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_amr_up_label, two_third_amr_up + label_y_offset)
            label.set_x(two_third_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_amr_down_label, two_third_amr_down + label_y_offset)

    if show_half_amr
        line.set_x2(half_amr_up_line, bar_index)
        line.set_y2(half_amr_up_line, half_amr_up)
        line.set_x2(half_amr_down_line, bar_index)
        line.set_y2(half_amr_down_line, half_amr_down)

        if show_labels
            label.set_x(half_amr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_amr_up_label, half_amr_up + label_y_offset)
            label.set_x(half_amr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_amr_down_label, half_amr_down + label_y_offset)

    // Update AMR Multiplier lines
    if show_amr_mult_1_5
        line.set_x2(amr_mult_1_5_up_line, bar_index)
        line.set_y2(amr_mult_1_5_up_line, amr_mult_1_5_up)
        line.set_x2(amr_mult_1_5_down_line, bar_index)
        line.set_y2(amr_mult_1_5_down_line, amr_mult_1_5_down)

        if show_labels
            label.set_x(amr_mult_1_5_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_1_5_up_label, amr_mult_1_5_up + label_y_offset)
            label.set_x(amr_mult_1_5_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_1_5_down_label, amr_mult_1_5_down + label_y_offset)

    if show_amr_mult_2_0
        line.set_x2(amr_mult_2_0_up_line, bar_index)
        line.set_y2(amr_mult_2_0_up_line, amr_mult_2_0_up)
        line.set_x2(amr_mult_2_0_down_line, bar_index)
        line.set_y2(amr_mult_2_0_down_line, amr_mult_2_0_down)

        if show_labels
            label.set_x(amr_mult_2_0_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_2_0_up_label, amr_mult_2_0_up + label_y_offset)
            label.set_x(amr_mult_2_0_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_mult_2_0_down_label, amr_mult_2_0_down + label_y_offset)

    // Update AMR Fibonacci Extension lines
    if show_amr_fib_1_272
        line.set_x2(amr_fib_1_272_up_line, bar_index)
        line.set_y2(amr_fib_1_272_up_line, amr_fib_1_272_up)
        line.set_x2(amr_fib_1_272_down_line, bar_index)
        line.set_y2(amr_fib_1_272_down_line, amr_fib_1_272_down)

        if show_labels
            label.set_x(amr_fib_1_272_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_272_up_label, amr_fib_1_272_up + label_y_offset)
            label.set_x(amr_fib_1_272_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_272_down_label, amr_fib_1_272_down + label_y_offset)

    if show_amr_fib_1_618
        line.set_x2(amr_fib_1_618_up_line, bar_index)
        line.set_y2(amr_fib_1_618_up_line, amr_fib_1_618_up)
        line.set_x2(amr_fib_1_618_down_line, bar_index)
        line.set_y2(amr_fib_1_618_down_line, amr_fib_1_618_down)

        if show_labels
            label.set_x(amr_fib_1_618_up_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_618_up_label, amr_fib_1_618_up + label_y_offset)
            label.set_x(amr_fib_1_618_down_label, bar_index + label_x_offset_bars)
            label.set_y(amr_fib_1_618_down_label, amr_fib_1_618_down + label_y_offset)

// === TIMEFRAME RESTRICTION ===
// Only apply timeframe restriction if show_only_intraday is true and we're on a daily or higher timeframe
if show_only_intraday and timeframe.isdaily
    // Daily levels
    line.delete(true_day_open_line)
    label.delete(true_day_open_label)
    line.delete(true_day_vertical)

    if show_full_adr
        line.delete(full_adr_up_line)
        line.delete(full_adr_down_line)
        label.delete(full_adr_up_label)
        label.delete(full_adr_down_label)

    if show_one_third_adr
        line.delete(one_third_adr_up_line)
        line.delete(one_third_adr_down_line)
        label.delete(one_third_adr_up_label)
        label.delete(one_third_adr_down_label)

    if show_two_third_adr
        line.delete(two_third_adr_up_line)
        line.delete(two_third_adr_down_line)
        label.delete(two_third_adr_up_label)
        label.delete(two_third_adr_down_label)

    if show_half_adr
        line.delete(half_adr_up_line)
        line.delete(half_adr_down_line)
        label.delete(half_adr_up_label)
        label.delete(half_adr_down_label)

    if show_adr_mult_1_5
        line.delete(adr_mult_1_5_up_line)
        line.delete(adr_mult_1_5_down_line)
        label.delete(adr_mult_1_5_up_label)
        label.delete(adr_mult_1_5_down_label)

    if show_adr_mult_2_0
        line.delete(adr_mult_2_0_up_line)
        line.delete(adr_mult_2_0_down_line)
        label.delete(adr_mult_2_0_up_label)
        label.delete(adr_mult_2_0_down_label)

    if show_adr_fib_1_272
        line.delete(adr_fib_1_272_up_line)
        line.delete(adr_fib_1_272_down_line)
        label.delete(adr_fib_1_272_up_label)
        label.delete(adr_fib_1_272_down_label)

    if show_adr_fib_1_618
        line.delete(adr_fib_1_618_up_line)
        line.delete(adr_fib_1_618_down_line)
        label.delete(adr_fib_1_618_up_label)
        label.delete(adr_fib_1_618_down_label)

    // Delete all daily labels in arrays
    delete_all_labels(tdo_labels)
    delete_all_labels(full_adr_up_labels)
    delete_all_labels(full_adr_down_labels)
    delete_all_labels(one_third_adr_up_labels)
    delete_all_labels(one_third_adr_down_labels)
    delete_all_labels(two_third_adr_up_labels)
    delete_all_labels(two_third_adr_down_labels)
    delete_all_labels(half_adr_up_labels)
    delete_all_labels(half_adr_down_labels)
    delete_all_labels(adr_mult_1_5_up_labels)
    delete_all_labels(adr_mult_1_5_down_labels)
    delete_all_labels(adr_mult_2_0_up_labels)
    delete_all_labels(adr_mult_2_0_down_labels)
    delete_all_labels(adr_fib_1_272_up_labels)
    delete_all_labels(adr_fib_1_272_down_labels)
    delete_all_labels(adr_fib_1_618_up_labels)
    delete_all_labels(adr_fib_1_618_down_labels)

    true_day_line_active := false

    // Weekly levels
    line.delete(true_week_open_line)
    label.delete(true_week_open_label)
    line.delete(true_week_vertical)

    if show_full_awr
        line.delete(full_awr_up_line)
        line.delete(full_awr_down_line)
        label.delete(full_awr_up_label)
        label.delete(full_awr_down_label)

    if show_one_third_awr
        line.delete(one_third_awr_up_line)
        line.delete(one_third_awr_down_line)
        label.delete(one_third_awr_up_label)
        label.delete(one_third_awr_down_label)

    if show_two_third_awr
        line.delete(two_third_awr_up_line)
        line.delete(two_third_awr_down_line)
        label.delete(two_third_awr_up_label)
        label.delete(two_third_awr_down_label)

    if show_half_awr
        line.delete(half_awr_up_line)
        line.delete(half_awr_down_line)
        label.delete(half_awr_up_label)
        label.delete(half_awr_down_label)

    if show_awr_mult_1_5
        line.delete(awr_mult_1_5_up_line)
        line.delete(awr_mult_1_5_down_line)
        label.delete(awr_mult_1_5_up_label)
        label.delete(awr_mult_1_5_down_label)

    if show_awr_mult_2_0
        line.delete(awr_mult_2_0_up_line)
        line.delete(awr_mult_2_0_down_line)
        label.delete(awr_mult_2_0_up_label)
        label.delete(awr_mult_2_0_down_label)

    if show_awr_fib_1_272
        line.delete(awr_fib_1_272_up_line)
        line.delete(awr_fib_1_272_down_line)
        label.delete(awr_fib_1_272_up_label)
        label.delete(awr_fib_1_272_down_label)

    if show_awr_fib_1_618
        line.delete(awr_fib_1_618_up_line)
        line.delete(awr_fib_1_618_down_line)
        label.delete(awr_fib_1_618_up_label)
        label.delete(awr_fib_1_618_down_label)

    // Delete all weekly labels in arrays
    delete_all_labels(two_labels)
    delete_all_labels(full_awr_up_labels)
    delete_all_labels(full_awr_down_labels)
    delete_all_labels(one_third_awr_up_labels)
    delete_all_labels(one_third_awr_down_labels)
    delete_all_labels(two_third_awr_up_labels)
    delete_all_labels(two_third_awr_down_labels)
    delete_all_labels(half_awr_up_labels)
    delete_all_labels(half_awr_down_labels)
    delete_all_labels(awr_mult_1_5_up_labels)
    delete_all_labels(awr_mult_1_5_down_labels)
    delete_all_labels(awr_mult_2_0_up_labels)
    delete_all_labels(awr_mult_2_0_down_labels)
    delete_all_labels(awr_fib_1_272_up_labels)
    delete_all_labels(awr_fib_1_272_down_labels)
    delete_all_labels(awr_fib_1_618_up_labels)
    delete_all_labels(awr_fib_1_618_down_labels)

    true_week_line_active := false

    // Monthly levels
    line.delete(true_month_open_line)
    label.delete(true_month_open_label)
    line.delete(true_month_vertical)

    if show_full_amr
        line.delete(full_amr_up_line)
        line.delete(full_amr_down_line)
        label.delete(full_amr_up_label)
        label.delete(full_amr_down_label)

    if show_one_third_amr
        line.delete(one_third_amr_up_line)
        line.delete(one_third_amr_down_line)
        label.delete(one_third_amr_up_label)
        label.delete(one_third_amr_down_label)

    if show_two_third_amr
        line.delete(two_third_amr_up_line)
        line.delete(two_third_amr_down_line)
        label.delete(two_third_amr_up_label)
        label.delete(two_third_amr_down_label)

    if show_half_amr
        line.delete(half_amr_up_line)
        line.delete(half_amr_down_line)
        label.delete(half_amr_up_label)
        label.delete(half_amr_down_label)

    if show_amr_mult_1_5
        line.delete(amr_mult_1_5_up_line)
        line.delete(amr_mult_1_5_down_line)
        label.delete(amr_mult_1_5_up_label)
        label.delete(amr_mult_1_5_down_label)

    if show_amr_mult_2_0
        line.delete(amr_mult_2_0_up_line)
        line.delete(amr_mult_2_0_down_line)
        label.delete(amr_mult_2_0_up_label)
        label.delete(amr_mult_2_0_down_label)

    if show_amr_fib_1_272
        line.delete(amr_fib_1_272_up_line)
        line.delete(amr_fib_1_272_down_line)
        label.delete(amr_fib_1_272_up_label)
        label.delete(amr_fib_1_272_down_label)

    if show_amr_fib_1_618
        line.delete(amr_fib_1_618_up_line)
        line.delete(amr_fib_1_618_down_line)
        label.delete(amr_fib_1_618_up_label)
        label.delete(amr_fib_1_618_down_label)

    // Delete all monthly labels in arrays
    delete_all_labels(tmo_labels)
    delete_all_labels(full_amr_up_labels)
    delete_all_labels(full_amr_down_labels)
    delete_all_labels(one_third_amr_up_labels)
    delete_all_labels(one_third_amr_down_labels)
    delete_all_labels(two_third_amr_up_labels)
    delete_all_labels(two_third_amr_down_labels)
    delete_all_labels(half_amr_up_labels)
    delete_all_labels(half_amr_down_labels)
    delete_all_labels(amr_mult_1_5_up_labels)
    delete_all_labels(amr_mult_1_5_down_labels)
    delete_all_labels(amr_mult_2_0_up_labels)
    delete_all_labels(amr_mult_2_0_down_labels)
    delete_all_labels(amr_fib_1_272_up_labels)
    delete_all_labels(amr_fib_1_272_down_labels)
    delete_all_labels(amr_fib_1_618_up_labels)
    delete_all_labels(amr_fib_1_618_down_labels)

    true_month_line_active := false

// === ALERT CONDITIONS ===
// Alert conditions for price touching ADR levels
alertcondition(ta.crossover(high, full_adr_up) or ta.crossunder(low, full_adr_up), "Price touched Full ADR+", "Price touched Full ADR+ level")
alertcondition(ta.crossover(high, full_adr_down) or ta.crossunder(low, full_adr_down), "Price touched Full ADR-", "Price touched Full ADR- level")
alertcondition(ta.crossover(high, one_third_adr_up) or ta.crossunder(low, one_third_adr_up), "Price touched 1/3 ADR+", "Price touched 1/3 ADR+ level")
alertcondition(ta.crossover(high, one_third_adr_down) or ta.crossunder(low, one_third_adr_down), "Price touched 1/3 ADR-", "Price touched 1/3 ADR- level")
alertcondition(ta.crossover(high, two_third_adr_up) or ta.crossunder(low, two_third_adr_up), "Price touched 2/3 ADR+", "Price touched 2/3 ADR+ level")
alertcondition(ta.crossover(high, two_third_adr_down) or ta.crossunder(low, two_third_adr_down), "Price touched 2/3 ADR-", "Price touched 2/3 ADR- level")
alertcondition(ta.crossover(high, half_adr_up) or ta.crossunder(low, half_adr_up), "Price touched 1/2 ADR+", "Price touched 1/2 ADR+ level")
alertcondition(ta.crossover(high, half_adr_down) or ta.crossunder(low, half_adr_down), "Price touched 1/2 ADR-", "Price touched 1/2 ADR- level")

// Alert conditions for price touching AWR levels
alertcondition(ta.crossover(high, full_awr_up) or ta.crossunder(low, full_awr_up), "Price touched Full AWR+", "Price touched Full AWR+ level")
alertcondition(ta.crossover(high, full_awr_down) or ta.crossunder(low, full_awr_down), "Price touched Full AWR-", "Price touched Full AWR- level")
alertcondition(ta.crossover(high, one_third_awr_up) or ta.crossunder(low, one_third_awr_up), "Price touched 1/3 AWR+", "Price touched 1/3 AWR+ level")
alertcondition(ta.crossover(high, one_third_awr_down) or ta.crossunder(low, one_third_awr_down), "Price touched 1/3 AWR-", "Price touched 1/3 AWR- level")
alertcondition(ta.crossover(high, two_third_awr_up) or ta.crossunder(low, two_third_awr_up), "Price touched 2/3 AWR+", "Price touched 2/3 AWR+ level")
alertcondition(ta.crossover(high, two_third_awr_down) or ta.crossunder(low, two_third_awr_down), "Price touched 2/3 AWR-", "Price touched 2/3 AWR- level")
alertcondition(ta.crossover(high, half_awr_up) or ta.crossunder(low, half_awr_up), "Price touched 1/2 AWR+", "Price touched 1/2 AWR+ level")
alertcondition(ta.crossover(high, half_awr_down) or ta.crossunder(low, half_awr_down), "Price touched 1/2 AWR-", "Price touched 1/2 AWR- level")

// Alert conditions for price touching AMR levels
alertcondition(ta.crossover(high, full_amr_up) or ta.crossunder(low, full_amr_up), "Price touched Full AMR+", "Price touched Full AMR+ level")
alertcondition(ta.crossover(high, full_amr_down) or ta.crossunder(low, full_amr_down), "Price touched Full AMR-", "Price touched Full AMR- level")
alertcondition(ta.crossover(high, one_third_amr_up) or ta.crossunder(low, one_third_amr_up), "Price touched 1/3 AMR+", "Price touched 1/3 AMR+ level")
alertcondition(ta.crossover(high, one_third_amr_down) or ta.crossunder(low, one_third_amr_down), "Price touched 1/3 AMR-", "Price touched 1/3 AMR- level")
alertcondition(ta.crossover(high, two_third_amr_up) or ta.crossunder(low, two_third_amr_up), "Price touched 2/3 AMR+", "Price touched 2/3 AMR+ level")
alertcondition(ta.crossover(high, two_third_amr_down) or ta.crossunder(low, two_third_amr_down), "Price touched 2/3 AMR-", "Price touched 2/3 AMR- level")
alertcondition(ta.crossover(high, half_amr_up) or ta.crossunder(low, half_amr_up), "Price touched 1/2 AMR+", "Price touched 1/2 AMR+ level")
alertcondition(ta.crossover(high, half_amr_down) or ta.crossunder(low, half_amr_down), "Price touched 1/2 AMR-", "Price touched 1/2 AMR- level")

// Alert conditions for ADR Multipliers
alertcondition(ta.crossover(high, adr_mult_1_5_up) or ta.crossunder(low, adr_mult_1_5_up), "Price touched 1.5x ADR+", "Price touched 1.5x ADR+ level")
alertcondition(ta.crossover(high, adr_mult_1_5_down) or ta.crossunder(low, adr_mult_1_5_down), "Price touched 1.5x ADR-", "Price touched 1.5x ADR- level")
alertcondition(ta.crossover(high, adr_mult_2_0_up) or ta.crossunder(low, adr_mult_2_0_up), "Price touched 2.0x ADR+", "Price touched 2.0x ADR+ level")
alertcondition(ta.crossover(high, adr_mult_2_0_down) or ta.crossunder(low, adr_mult_2_0_down), "Price touched 2.0x ADR-", "Price touched 2.0x ADR- level")

// Alert conditions for ADR Fibonacci Extensions
alertcondition(ta.crossover(high, adr_fib_1_272_up) or ta.crossunder(low, adr_fib_1_272_up), "Price touched 127.2% ADR+", "Price touched 127.2% ADR+ level")
alertcondition(ta.crossover(high, adr_fib_1_272_down) or ta.crossunder(low, adr_fib_1_272_down), "Price touched 127.2% ADR-", "Price touched 127.2% ADR- level")
alertcondition(ta.crossover(high, adr_fib_1_618_up) or ta.crossunder(low, adr_fib_1_618_up), "Price touched 161.8% ADR+", "Price touched 161.8% ADR+ level")
alertcondition(ta.crossover(high, adr_fib_1_618_down) or ta.crossunder(low, adr_fib_1_618_down), "Price touched 161.8% ADR-", "Price touched 161.8% ADR- level")

// Alert conditions for AWR Multipliers
alertcondition(ta.crossover(high, awr_mult_1_5_up) or ta.crossunder(low, awr_mult_1_5_up), "Price touched 1.5x AWR+", "Price touched 1.5x AWR+ level")
alertcondition(ta.crossover(high, awr_mult_1_5_down) or ta.crossunder(low, awr_mult_1_5_down), "Price touched 1.5x AWR-", "Price touched 1.5x AWR- level")
alertcondition(ta.crossover(high, awr_mult_2_0_up) or ta.crossunder(low, awr_mult_2_0_up), "Price touched 2.0x AWR+", "Price touched 2.0x AWR+ level")
alertcondition(ta.crossover(high, awr_mult_2_0_down) or ta.crossunder(low, awr_mult_2_0_down), "Price touched 2.0x AWR-", "Price touched 2.0x AWR- level")

// Alert conditions for AWR Fibonacci Extensions
alertcondition(ta.crossover(high, awr_fib_1_272_up) or ta.crossunder(low, awr_fib_1_272_up), "Price touched 127.2% AWR+", "Price touched 127.2% AWR+ level")
alertcondition(ta.crossover(high, awr_fib_1_272_down) or ta.crossunder(low, awr_fib_1_272_down), "Price touched 127.2% AWR-", "Price touched 127.2% AWR- level")
alertcondition(ta.crossover(high, awr_fib_1_618_up) or ta.crossunder(low, awr_fib_1_618_up), "Price touched 161.8% AWR+", "Price touched 161.8% AWR+ level")
alertcondition(ta.crossover(high, awr_fib_1_618_down) or ta.crossunder(low, awr_fib_1_618_down), "Price touched 161.8% AWR-", "Price touched 161.8% AWR- level")

// Alert conditions for AMR Multipliers
alertcondition(ta.crossover(high, amr_mult_1_5_up) or ta.crossunder(low, amr_mult_1_5_up), "Price touched 1.5x AMR+", "Price touched 1.5x AMR+ level")
alertcondition(ta.crossover(high, amr_mult_1_5_down) or ta.crossunder(low, amr_mult_1_5_down), "Price touched 1.5x AMR-", "Price touched 1.5x AMR- level")
alertcondition(ta.crossover(high, amr_mult_2_0_up) or ta.crossunder(low, amr_mult_2_0_up), "Price touched 2.0x AMR+", "Price touched 2.0x AMR+ level")
alertcondition(ta.crossover(high, amr_mult_2_0_down) or ta.crossunder(low, amr_mult_2_0_down), "Price touched 2.0x AMR-", "Price touched 2.0x AMR- level")

// Alert conditions for AMR Fibonacci Extensions
alertcondition(ta.crossover(high, amr_fib_1_272_up) or ta.crossunder(low, amr_fib_1_272_up), "Price touched 127.2% AMR+", "Price touched 127.2% AMR+ level")
alertcondition(ta.crossover(high, amr_fib_1_272_down) or ta.crossunder(low, amr_fib_1_272_down), "Price touched 127.2% AMR-", "Price touched 127.2% AMR- level")
alertcondition(ta.crossover(high, amr_fib_1_618_up) or ta.crossunder(low, amr_fib_1_618_up), "Price touched 161.8% AMR+", "Price touched 161.8% AMR+ level")
alertcondition(ta.crossover(high, amr_fib_1_618_down) or ta.crossunder(low, amr_fib_1_618_down), "Price touched 161.8% AMR-", "Price touched 161.8% AMR- level")