//@version=5
indicator("Multi-AMR-Levels", overlay=true, max_lines_count=500, max_labels_count=500, max_bars_back=5000, dynamic_requests=true)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
timezone = input.string("America/New_York", "Time Zone", group=group_settings)
max_periods = input.int(3, "Months to Display", minval=1, maxval=30, tooltip="Number of months to display", group=group_settings)

// AMR Settings
group_amr = "AMR Settings"
// Multiple AMR period options
show_amr_3m = input.bool(true, "Show 3-Month AMR", group=group_amr)
show_amr_6m = input.bool(true, "Show 6-Month AMR", group=group_amr)
show_amr_9m = input.bool(false, "Show 9-Month AMR", group=group_amr)
show_amr_12m = input.bool(false, "Show 12-Month AMR", group=group_amr)

// Level options
show_full_amr = input.bool(true, "Show Full AMR", group=group_amr)
show_one_third_amr = input.bool(true, "Show 1/3 AMR", group=group_amr)
show_two_third_amr = input.bool(false, "Show 2/3 AMR", group=group_amr)
show_half_amr = input.bool(false, "Show 1/2 AMR", group=group_amr)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Month Open", group=group_line)
extend_right = input.int(50, "Extend Lines Right (bars)", minval=0, maxval=500, group=group_line)
extend_left = input.int(0, "Extend Lines Left (bars)", minval=0, maxval=500, group=group_line)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(false, "Show Price in Label", group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=group_label)
label_x_offset_bars = input.int(50, "Label X Offset (bars)", minval=0, maxval=50, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset", minval=-1000, maxval=1000, step=0.1, group=group_label)

// Color Settings
group_color = "Color Settings"
true_month_open_color = input.color(color.white, "True Month Open Line", group=group_color)

// AMR Period Colors
amr_3m_color = input.color(color.rgb(0, 255, 0), "3-Month AMR", group=group_color)
amr_6m_color = input.color(color.rgb(255, 255, 0), "6-Month AMR", group=group_color)
amr_9m_color = input.color(color.rgb(255, 128, 0), "9-Month AMR", group=group_color)
amr_12m_color = input.color(color.rgb(255, 0, 0), "12-Month AMR", group=group_color)

// AMR Level Colors
full_amr_color = input.color(color.white, "Full AMR", group=group_color)
one_third_amr_color = input.color(color.white, "1/3 AMR", group=group_color)
two_third_amr_color = input.color(color.white, "2/3 AMR", group=group_color)
half_amr_color = input.color(color.white, "1/2 AMR", group=group_color)

// === VARIABLES ===
// Convert line style input to Pine style
var line_style_value = line.style_solid
if line_style == "Dashed"
    line_style_value := line.style_dashed
else if line_style == "Dotted"
    line_style_value := line.style_dotted

// Convert label size input to Pine size
var label_size_value = size.small
if label_size == "Tiny"
    label_size_value := size.tiny
else if label_size == "Normal"
    label_size_value := size.normal
else if label_size == "Large"
    label_size_value := size.large

// Arrays to store lines and labels
var true_month_open_lines = array.new_line()
var true_month_open_labels = array.new_label()
var full_amr_up_lines = array.new_line()
var full_amr_up_labels = array.new_label()
var full_amr_down_lines = array.new_line()
var full_amr_down_labels = array.new_label()
var one_third_amr_up_lines = array.new_line()
var one_third_amr_up_labels = array.new_label()
var one_third_amr_down_lines = array.new_line()
var one_third_amr_down_labels = array.new_label()
var two_third_amr_up_lines = array.new_line()
var two_third_amr_up_labels = array.new_label()
var two_third_amr_down_lines = array.new_line()
var two_third_amr_down_labels = array.new_label()
var half_amr_up_lines = array.new_line()
var half_amr_up_labels = array.new_label()
var half_amr_down_lines = array.new_line()
var half_amr_down_labels = array.new_label()

// Arrays to store monthly values
var monthly_timestamps = array.new_int()
var monthly_bar_indices = array.new_int()
var monthly_tmo_values = array.new_float()

// 3-Month AMR arrays
var monthly_3m_full_up_values = array.new_float()
var monthly_3m_full_down_values = array.new_float()
var monthly_3m_one_third_up_values = array.new_float()
var monthly_3m_one_third_down_values = array.new_float()
var monthly_3m_two_third_up_values = array.new_float()
var monthly_3m_two_third_down_values = array.new_float()
var monthly_3m_half_up_values = array.new_float()
var monthly_3m_half_down_values = array.new_float()

// 6-Month AMR arrays
var monthly_6m_full_up_values = array.new_float()
var monthly_6m_full_down_values = array.new_float()
var monthly_6m_one_third_up_values = array.new_float()
var monthly_6m_one_third_down_values = array.new_float()
var monthly_6m_two_third_up_values = array.new_float()
var monthly_6m_two_third_down_values = array.new_float()
var monthly_6m_half_up_values = array.new_float()
var monthly_6m_half_down_values = array.new_float()

// 9-Month AMR arrays
var monthly_9m_full_up_values = array.new_float()
var monthly_9m_full_down_values = array.new_float()
var monthly_9m_one_third_up_values = array.new_float()
var monthly_9m_one_third_down_values = array.new_float()
var monthly_9m_two_third_up_values = array.new_float()
var monthly_9m_two_third_down_values = array.new_float()
var monthly_9m_half_up_values = array.new_float()
var monthly_9m_half_down_values = array.new_float()

// 12-Month AMR arrays
var monthly_12m_full_up_values = array.new_float()
var monthly_12m_full_down_values = array.new_float()
var monthly_12m_one_third_up_values = array.new_float()
var monthly_12m_one_third_down_values = array.new_float()
var monthly_12m_two_third_up_values = array.new_float()
var monthly_12m_two_third_down_values = array.new_float()
var monthly_12m_half_up_values = array.new_float()
var monthly_12m_half_down_values = array.new_float()

// Variables to track current month's values
var float true_month_open_price = na
var bool true_month_line_active = false
var line true_month_open_line = na
var label true_month_open_label = na

// 3-Month AMR variables
var float amr_3m_val = na
var float amr_3m_full_up = na
var float amr_3m_full_down = na
var float amr_3m_one_third_up = na
var float amr_3m_one_third_down = na
var float amr_3m_two_third_up = na
var float amr_3m_two_third_down = na
var float amr_3m_half_up = na
var float amr_3m_half_down = na

// 6-Month AMR variables
var float amr_6m_val = na
var float amr_6m_full_up = na
var float amr_6m_full_down = na
var float amr_6m_one_third_up = na
var float amr_6m_one_third_down = na
var float amr_6m_two_third_up = na
var float amr_6m_two_third_down = na
var float amr_6m_half_up = na
var float amr_6m_half_down = na

// 9-Month AMR variables
var float amr_9m_val = na
var float amr_9m_full_up = na
var float amr_9m_full_down = na
var float amr_9m_one_third_up = na
var float amr_9m_one_third_down = na
var float amr_9m_two_third_up = na
var float amr_9m_two_third_down = na
var float amr_9m_half_up = na
var float amr_9m_half_down = na

// 12-Month AMR variables
var float amr_12m_val = na
var float amr_12m_full_up = na
var float amr_12m_full_down = na
var float amr_12m_one_third_up = na
var float amr_12m_one_third_down = na
var float amr_12m_two_third_up = na
var float amr_12m_two_third_down = na
var float amr_12m_half_up = na
var float amr_12m_half_down = na

// === FUNCTIONS ===
// Enhanced Security Function for 1-Minute Charts
calculate_amr(lookback_period) =>
    // Use higher timeframe with lookahead off
    [mh, ml] = request.security(syminfo.tickerid, "M", [high, low], gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_off)
    mrange = mh - ml
    ta.sma(mrange, lookback_period)
// Function to safely delete lines
safe_delete_lines(line_array) =>
    if array.size(line_array) > 0
        for i = 0 to array.size(line_array) - 1
            line_to_delete = array.get(line_array, i)
            if not na(line_to_delete)
                line.delete(line_to_delete)
        array.clear(line_array)

// Function to safely delete labels
safe_delete_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Time-Based Vertical Line
draw_vertical_line(ts) =>
    if show_vertical and not na(ts)
        line.new(x1=ts,y1=low,x2=ts,y2=high,xloc=xloc.bar_time,color=color.new(true_month_open_color, 50),style=line.style_dashed,width=1)

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) > max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to manage label arrays based on max_periods
manage_label_history(label_array) =>
    if array.size(label_array) > max_periods
        // Delete oldest label
        oldest_label = array.get(label_array, array.size(label_array) - 1)
        if not na(oldest_label)
            label.delete(oldest_label)
        array.pop(label_array)

// Enhanced Line Drawing with Dynamic Extension
draw_improved_line(price_level, color, style, timestamp) =>
    if not na(price_level) and not na(timestamp)
        // Calculate extension in milliseconds
        extension_ms = extend_right * timeframe.in_seconds() * 1000
        left_extension_ms = extend_left * timeframe.in_seconds() * 1000

        // Dynamic extension for current month
        is_current = timestamp == array.get(monthly_timestamps, 0)
        end_time = is_current ? time + extension_ms : timestamp + extension_ms
        start_time = timestamp - left_extension_ms

        line.new(x1=start_time,y1=price_level,x2=end_time,y2=price_level,xloc=xloc.bar_time,color=color,style=style,width=line_width,extend=extend.none)

// Draw labels at the right end of lines
draw_improved_label(price_level, t, color, timestamp) =>
    if not na(price_level) and show_labels and not na(timestamp) and barstate.islast
        // For label positioning, use the current bar_index plus offset
        // This ensures labels are always visible on the right side
        label_obj = label.new(x=bar_index + label_x_offset_bars,y=price_level + label_y_offset,text=t + (show_price_in_label ? str.format(" ({0})", price_level) : ""),style=label.style_label_left,size=label_size_value,textcolor=color,color=color.new(color.black, 100),xloc=xloc.bar_index)

        label_obj
                // Function to draw monthly levels efficiently for all timeframes

// Function to draw AMR levels for a specific period
draw_amr_levels(period_name, period_color, full_up_values, full_down_values, one_third_up_values, one_third_down_values, two_third_up_values, two_third_down_values, half_up_values, half_down_values) =>
    if array.size(monthly_tmo_values) > 0
        // Draw lines for each month
        for i = 0 to math.min(array.size(monthly_tmo_values) - 1, max_periods - 1)
            int month_timestamp = array.get(monthly_timestamps, i)

            // Draw Full AMR levels
            if show_full_amr and i < array.size(full_up_values)
                float full_up = array.get(full_up_values, i)
                float full_down = array.get(full_down_values, i)

                full_up_line = draw_improved_line(full_up, period_color, line_style_value, month_timestamp)
                array.push(full_amr_up_lines, full_up_line)

                // Draw full AMR+ label directly
                if barstate.islast and show_labels
                    full_up_label = label.new(x=bar_index + label_x_offset_bars,y=full_up + label_y_offset,text=period_name + " AMR+" + (show_price_in_label ? str.format(" ({0})", full_up) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(full_amr_up_labels, full_up_label)

                full_down_line = draw_improved_line(full_down, period_color, line_style_value, month_timestamp)
                array.push(full_amr_down_lines, full_down_line)

                // Draw full AMR- label directly
                if barstate.islast and show_labels
                    full_down_label = label.new(x=bar_index + label_x_offset_bars,y=full_down + label_y_offset,text=period_name + " AMR-" + (show_price_in_label ? str.format(" ({0})", full_down) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(full_amr_down_labels, full_down_label)

            // Draw 1/3 AMR levels
            if show_one_third_amr and i < array.size(one_third_up_values)
                float one_third_up = array.get(one_third_up_values, i)
                float one_third_down = array.get(one_third_down_values, i)

                one_third_up_line = draw_improved_line(one_third_up, period_color, line_style_value, month_timestamp)
                array.push(one_third_amr_up_lines, one_third_up_line)

                // Draw 1/3 AMR+ label directly
                if barstate.islast and show_labels
                    one_third_up_label = label.new(x=bar_index + label_x_offset_bars,y=one_third_up + label_y_offset,text=period_name + " 1/3+" + (show_price_in_label ? str.format(" ({0})", one_third_up) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(one_third_amr_up_labels, one_third_up_label)

                one_third_down_line = draw_improved_line(one_third_down, period_color, line_style_value, month_timestamp)
                array.push(one_third_amr_down_lines, one_third_down_line)

                // Draw 1/3 AMR- label directly
                if barstate.islast and show_labels
                    one_third_down_label = label.new(x=bar_index + label_x_offset_bars,y=one_third_down + label_y_offset,text=period_name + " 1/3-" + (show_price_in_label ? str.format(" ({0})", one_third_down) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(one_third_amr_down_labels, one_third_down_label)

            // Draw 2/3 AMR levels
            if show_two_third_amr and i < array.size(two_third_up_values)
                float two_third_up = array.get(two_third_up_values, i)
                float two_third_down = array.get(two_third_down_values, i)

                two_third_up_line = draw_improved_line(two_third_up, period_color, line_style_value, month_timestamp)
                array.push(two_third_amr_up_lines, two_third_up_line)

                // Draw 2/3 AMR+ label directly
                if barstate.islast and show_labels
                    two_third_up_label = label.new(x=bar_index + label_x_offset_bars,y=two_third_up + label_y_offset,text=period_name + " 2/3+" + (show_price_in_label ? str.format(" ({0})", two_third_up) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(two_third_amr_up_labels, two_third_up_label)

                two_third_down_line = draw_improved_line(two_third_down, period_color, line_style_value, month_timestamp)
                array.push(two_third_amr_down_lines, two_third_down_line)

                // Draw 2/3 AMR- label directly
                if barstate.islast and show_labels
                    two_third_down_label = label.new(x=bar_index + label_x_offset_bars,y=two_third_down + label_y_offset,text=period_name + " 2/3-" + (show_price_in_label ? str.format(" ({0})", two_third_down) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(two_third_amr_down_labels, two_third_down_label)

            // Draw 1/2 AMR levels
            if show_half_amr and i < array.size(half_up_values)
                float half_up = array.get(half_up_values, i)
                float half_down = array.get(half_down_values, i)

                half_up_line = draw_improved_line(half_up, period_color, line_style_value, month_timestamp)
                array.push(half_amr_up_lines, half_up_line)

                // Draw 1/2 AMR+ label directly
                if barstate.islast and show_labels
                    half_up_label = label.new(x=bar_index + label_x_offset_bars,y=half_up + label_y_offset,text=period_name + " 1/2+" + (show_price_in_label ? str.format(" ({0})", half_up) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(half_amr_up_labels, half_up_label)

                half_down_line = draw_improved_line(half_down, period_color, line_style_value, month_timestamp)
                array.push(half_amr_down_lines, half_down_line)

                // Draw 1/2 AMR- label directly
                if barstate.islast and show_labels
                    half_down_label = label.new(x=bar_index + label_x_offset_bars,y=half_down + label_y_offset,text=period_name + " 1/2-" + (show_price_in_label ? str.format(" ({0})", half_down) : ""),style=label.style_label_left,size=label_size_value,textcolor=period_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                    array.push(half_amr_down_labels, half_down_label)

// Main drawing function
draw_monthly_levels() =>
    if array.size(monthly_tmo_values) > 0
        // Clear existing monthly lines
        safe_delete_lines(true_month_open_lines)
        safe_delete_lines(full_amr_up_lines)
        safe_delete_lines(full_amr_down_lines)
        safe_delete_lines(one_third_amr_up_lines)
        safe_delete_lines(one_third_amr_down_lines)
        safe_delete_lines(two_third_amr_up_lines)
        safe_delete_lines(two_third_amr_down_lines)
        safe_delete_lines(half_amr_up_lines)
        safe_delete_lines(half_amr_down_lines)

        // Clear existing monthly labels
        safe_delete_labels(true_month_open_labels)
        safe_delete_labels(full_amr_up_labels)
        safe_delete_labels(full_amr_down_labels)
        safe_delete_labels(one_third_amr_up_labels)
        safe_delete_labels(one_third_amr_down_labels)
        safe_delete_labels(two_third_amr_up_labels)
        safe_delete_labels(two_third_amr_down_labels)
        safe_delete_labels(half_amr_up_labels)
        safe_delete_labels(half_amr_down_labels)

        // Draw lines for each month
        for i = 0 to math.min(array.size(monthly_tmo_values) - 1, max_periods - 1)
            int month_timestamp = array.get(monthly_timestamps, i)
            float tmo = array.get(monthly_tmo_values, i)

            // Draw true month open line
            tmo_line = draw_improved_line(tmo, true_month_open_color, line_style_value, month_timestamp)
            array.push(true_month_open_lines, tmo_line)

            // Draw true month open label directly
            if barstate.islast and show_labels
                tmo_label = label.new(x=bar_index + label_x_offset_bars,y=tmo + label_y_offset,text="TMO" + (show_price_in_label ? str.format(" ({0})", tmo) : ""),style=label.style_label_left,size=label_size_value,textcolor=true_month_open_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                array.push(true_month_open_labels, tmo_label)

        // Draw AMR levels for each period
        if show_amr_3m
            draw_amr_levels("3M", amr_3m_color, monthly_3m_full_up_values, monthly_3m_full_down_values,
                           monthly_3m_one_third_up_values, monthly_3m_one_third_down_values,
                           monthly_3m_two_third_up_values, monthly_3m_two_third_down_values,
                           monthly_3m_half_up_values, monthly_3m_half_down_values)

        if show_amr_6m
            draw_amr_levels("6M", amr_6m_color, monthly_6m_full_up_values, monthly_6m_full_down_values,
                           monthly_6m_one_third_up_values, monthly_6m_one_third_down_values,
                           monthly_6m_two_third_up_values, monthly_6m_two_third_down_values,
                           monthly_6m_half_up_values, monthly_6m_half_down_values)

        if show_amr_9m
            draw_amr_levels("9M", amr_9m_color, monthly_9m_full_up_values, monthly_9m_full_down_values,
                           monthly_9m_one_third_up_values, monthly_9m_one_third_down_values,
                           monthly_9m_two_third_up_values, monthly_9m_two_third_down_values,
                           monthly_9m_half_up_values, monthly_9m_half_down_values)

        if show_amr_12m
            draw_amr_levels("12M", amr_12m_color, monthly_12m_full_up_values, monthly_12m_full_down_values,
                           monthly_12m_one_third_up_values, monthly_12m_one_third_down_values,
                           monthly_12m_two_third_up_values, monthly_12m_two_third_down_values,
                           monthly_12m_half_up_values, monthly_12m_half_down_values)

// === MAIN LOGIC ===
// Calculate AMR values for different periods
amr_3m = calculate_amr(3)
amr_6m = calculate_amr(6)
amr_9m = calculate_amr(9)
amr_12m = calculate_amr(12)
is_new_month = timeframe.change("M")

if is_new_month or barstate.isfirst
    true_month_open_price := barstate.isfirst ? close : open
    true_month_line_active := true

    // Calculate 3-Month AMR levels
    if not na(amr_3m) and show_amr_3m
        amr_3m_val := amr_3m
        amr_3m_full_up := true_month_open_price + amr_3m_val
        amr_3m_full_down := true_month_open_price - amr_3m_val
        amr_3m_one_third_up := true_month_open_price + (amr_3m_val / 3)
        amr_3m_one_third_down := true_month_open_price - (amr_3m_val / 3)
        amr_3m_two_third_up := true_month_open_price + (amr_3m_val * 2 / 3)
        amr_3m_two_third_down := true_month_open_price - (amr_3m_val * 2 / 3)
        amr_3m_half_up := true_month_open_price + (amr_3m_val / 2)
        amr_3m_half_down := true_month_open_price - (amr_3m_val / 2)

        array.unshift(monthly_3m_full_up_values, amr_3m_full_up)
        array.unshift(monthly_3m_full_down_values, amr_3m_full_down)
        array.unshift(monthly_3m_one_third_up_values, amr_3m_one_third_up)
        array.unshift(monthly_3m_one_third_down_values, amr_3m_one_third_down)
        array.unshift(monthly_3m_two_third_up_values, amr_3m_two_third_up)
        array.unshift(monthly_3m_two_third_down_values, amr_3m_two_third_down)
        array.unshift(monthly_3m_half_up_values, amr_3m_half_up)
        array.unshift(monthly_3m_half_down_values, amr_3m_half_down)

    // Calculate 6-Month AMR levels
    if not na(amr_6m) and show_amr_6m
        amr_6m_val := amr_6m
        amr_6m_full_up := true_month_open_price + amr_6m_val
        amr_6m_full_down := true_month_open_price - amr_6m_val
        amr_6m_one_third_up := true_month_open_price + (amr_6m_val / 3)
        amr_6m_one_third_down := true_month_open_price - (amr_6m_val / 3)
        amr_6m_two_third_up := true_month_open_price + (amr_6m_val * 2 / 3)
        amr_6m_two_third_down := true_month_open_price - (amr_6m_val * 2 / 3)
        amr_6m_half_up := true_month_open_price + (amr_6m_val / 2)
        amr_6m_half_down := true_month_open_price - (amr_6m_val / 2)

        array.unshift(monthly_6m_full_up_values, amr_6m_full_up)
        array.unshift(monthly_6m_full_down_values, amr_6m_full_down)
        array.unshift(monthly_6m_one_third_up_values, amr_6m_one_third_up)
        array.unshift(monthly_6m_one_third_down_values, amr_6m_one_third_down)
        array.unshift(monthly_6m_two_third_up_values, amr_6m_two_third_up)
        array.unshift(monthly_6m_two_third_down_values, amr_6m_two_third_down)
        array.unshift(monthly_6m_half_up_values, amr_6m_half_up)
        array.unshift(monthly_6m_half_down_values, amr_6m_half_down)

    // Calculate 9-Month AMR levels
    if not na(amr_9m) and show_amr_9m
        amr_9m_val := amr_9m
        amr_9m_full_up := true_month_open_price + amr_9m_val
        amr_9m_full_down := true_month_open_price - amr_9m_val
        amr_9m_one_third_up := true_month_open_price + (amr_9m_val / 3)
        amr_9m_one_third_down := true_month_open_price - (amr_9m_val / 3)
        amr_9m_two_third_up := true_month_open_price + (amr_9m_val * 2 / 3)
        amr_9m_two_third_down := true_month_open_price - (amr_9m_val * 2 / 3)
        amr_9m_half_up := true_month_open_price + (amr_9m_val / 2)
        amr_9m_half_down := true_month_open_price - (amr_9m_val / 2)

        array.unshift(monthly_9m_full_up_values, amr_9m_full_up)
        array.unshift(monthly_9m_full_down_values, amr_9m_full_down)
        array.unshift(monthly_9m_one_third_up_values, amr_9m_one_third_up)
        array.unshift(monthly_9m_one_third_down_values, amr_9m_one_third_down)
        array.unshift(monthly_9m_two_third_up_values, amr_9m_two_third_up)
        array.unshift(monthly_9m_two_third_down_values, amr_9m_two_third_down)
        array.unshift(monthly_9m_half_up_values, amr_9m_half_up)
        array.unshift(monthly_9m_half_down_values, amr_9m_half_down)

    // Calculate 12-Month AMR levels
    if not na(amr_12m) and show_amr_12m
        amr_12m_val := amr_12m
        amr_12m_full_up := true_month_open_price + amr_12m_val
        amr_12m_full_down := true_month_open_price - amr_12m_val
        amr_12m_one_third_up := true_month_open_price + (amr_12m_val / 3)
        amr_12m_one_third_down := true_month_open_price - (amr_12m_val / 3)
        amr_12m_two_third_up := true_month_open_price + (amr_12m_val * 2 / 3)
        amr_12m_two_third_down := true_month_open_price - (amr_12m_val * 2 / 3)
        amr_12m_half_up := true_month_open_price + (amr_12m_val / 2)
        amr_12m_half_down := true_month_open_price - (amr_12m_val / 2)

        array.unshift(monthly_12m_full_up_values, amr_12m_full_up)
        array.unshift(monthly_12m_full_down_values, amr_12m_full_down)
        array.unshift(monthly_12m_one_third_up_values, amr_12m_one_third_up)
        array.unshift(monthly_12m_one_third_down_values, amr_12m_one_third_down)
        array.unshift(monthly_12m_two_third_up_values, amr_12m_two_third_up)
        array.unshift(monthly_12m_two_third_down_values, amr_12m_two_third_down)
        array.unshift(monthly_12m_half_up_values, amr_12m_half_up)
        array.unshift(monthly_12m_half_down_values, amr_12m_half_down)

    // Store common values
    array.unshift(monthly_tmo_values, true_month_open_price)
    array.unshift(monthly_timestamps, int(time))
    array.unshift(monthly_bar_indices, bar_index)

    // Manage array sizes
    if array.size(monthly_tmo_values) > max_periods
        array.pop(monthly_tmo_values)
        array.pop(monthly_timestamps)
        array.pop(monthly_bar_indices)

        // 3-Month arrays
        if array.size(monthly_3m_full_up_values) > 0
            array.pop(monthly_3m_full_up_values)
            array.pop(monthly_3m_full_down_values)
            array.pop(monthly_3m_one_third_up_values)
            array.pop(monthly_3m_one_third_down_values)
            array.pop(monthly_3m_two_third_up_values)
            array.pop(monthly_3m_two_third_down_values)
            array.pop(monthly_3m_half_up_values)
            array.pop(monthly_3m_half_down_values)

        // 6-Month arrays
        if array.size(monthly_6m_full_up_values) > 0
            array.pop(monthly_6m_full_up_values)
            array.pop(monthly_6m_full_down_values)
            array.pop(monthly_6m_one_third_up_values)
            array.pop(monthly_6m_one_third_down_values)
            array.pop(monthly_6m_two_third_up_values)
            array.pop(monthly_6m_two_third_down_values)
            array.pop(monthly_6m_half_up_values)
            array.pop(monthly_6m_half_down_values)

        // 9-Month arrays
        if array.size(monthly_9m_full_up_values) > 0
            array.pop(monthly_9m_full_up_values)
            array.pop(monthly_9m_full_down_values)
            array.pop(monthly_9m_one_third_up_values)
            array.pop(monthly_9m_one_third_down_values)
            array.pop(monthly_9m_two_third_up_values)
            array.pop(monthly_9m_two_third_down_values)
            array.pop(monthly_9m_half_up_values)
            array.pop(monthly_9m_half_down_values)

        // 12-Month arrays
        if array.size(monthly_12m_full_up_values) > 0
            array.pop(monthly_12m_full_up_values)
            array.pop(monthly_12m_full_down_values)
            array.pop(monthly_12m_one_third_up_values)
            array.pop(monthly_12m_one_third_down_values)
            array.pop(monthly_12m_two_third_up_values)
            array.pop(monthly_12m_two_third_down_values)
            array.pop(monthly_12m_half_up_values)
            array.pop(monthly_12m_half_down_values)

    // Add safety check before drawing
    if array.size(monthly_timestamps) > 0
        if show_vertical
            line.new(x1=array.get(monthly_timestamps, 0),y1=low,x2=array.get(monthly_timestamps, 0),y2=high,xloc=xloc.bar_time,color=color.new(true_month_open_color, 50),style=line.style_dashed,width=1)
        draw_monthly_levels()

// Add safety check for last bar update
if barstate.islast and array.size(monthly_timestamps) > 0
    // Clear existing labels before redrawing
    safe_delete_labels(true_month_open_labels)
    safe_delete_labels(full_amr_up_labels)
    safe_delete_labels(full_amr_down_labels)
    safe_delete_labels(one_third_amr_up_labels)
    safe_delete_labels(one_third_amr_down_labels)
    safe_delete_labels(two_third_amr_up_labels)
    safe_delete_labels(two_third_amr_down_labels)
    safe_delete_labels(half_amr_up_labels)
    safe_delete_labels(half_amr_down_labels)

    draw_monthly_levels()
    if show_vertical
        line.new(x1=array.get(monthly_timestamps, 0),y1=low,x2=array.get(monthly_timestamps, 0),y2=high,xloc=xloc.bar_time,color=color.new(true_month_open_color, 50),style=line.style_dashed,width=1)