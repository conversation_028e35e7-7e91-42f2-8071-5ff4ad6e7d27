//@version=5
indicator("True Day Open (LuxAlgo-style Label with X/Y Offset)", overlay = true, max_lines_count = 500, max_labels_count = 500)

// === INPUTS ===
group_settings               = "Settings"
show_only_intraday           = input.bool(true, "Show Only on Intraday Timeframes", group = group_settings)
timezone                     = input.string("America/New_York", "Time Zone", group = group_settings)
line_color                   = input.color(color.orange, "Line Color", group = group_settings)
line_width                   = input.int(1, "Line Width", minval = 1, maxval = 5, group = group_settings)
line_style                   = input.string("Dotted", "Line Style", options = ["Solid", "Dotted", "Dashed"], group = group_settings)
show_label                   = input.bool(true, "Show Label", group = group_settings)
show_vertical                = input.bool(true, "Show Vertical Line at Start", group = group_settings)
label_x_offset_bars          = input.int(1, "Label X Offset (Bars Right)", minval = 0, group = group_settings)
label_y_offset               = input.float(0.0, "Label Y Offset (Price)", step = 0.1, group = group_settings)

get_line_style(styleStr) => styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

// === STATE ===
var line  dayOpenLine        = na
var label dayOpenLabel       = na
var line  dayVertical        = na
var float dayOpenPrice       = na
var bool  lineActive         = false

// === TIME LOGIC ===
start_of_day                 = timestamp(timezone, year, month, dayofmonth, 0, 0)
end_of_day                   = timestamp(timezone, year, month, dayofmonth, 23, 59)
is_new_day                   = time == start_of_day
in_current_day               = time >= start_of_day and time <= end_of_day

// === MAIN LOGIC ===
if is_new_day
    dayOpenPrice            := open
    dayOpenLine             := line.new(bar_index, dayOpenPrice, bar_index, dayOpenPrice, xloc = xloc.bar_index, color = line_color, width = line_width, style = get_line_style(line_style))
    if show_label
        dayOpenLabel        := label.new(bar_index + label_x_offset_bars, dayOpenPrice + label_y_offset, "TDO", xloc = xloc.bar_index, style = label.style_label_left, size = size.small, textcolor = line_color, color = na)
    if show_vertical
        dayVertical         := line.new(bar_index, high, bar_index, low, xloc = xloc.bar_index, extend = extend.both, color = line_color, style = line.style_dashed)
    lineActive := true

if in_current_day and lineActive
    line.set_x2(dayOpenLine, bar_index)
    line.set_y2(dayOpenLine, dayOpenPrice)
    if show_label
        label.set_x(dayOpenLabel, bar_index + label_x_offset_bars)
        label.set_y(dayOpenLabel, dayOpenPrice + label_y_offset)

// === TIMEFRAME RESTRICTION ===
if show_only_intraday and timeframe.isdwm
    line.delete(dayOpenLine)
    label.delete(dayOpenLabel)
    line.delete(dayVertical)
    lineActive := false
