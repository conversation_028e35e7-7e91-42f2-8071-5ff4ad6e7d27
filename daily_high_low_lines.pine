//@version=5
indicator("Daily High/Low & Day Open Lines", overlay=true, max_lines_count=500)

// === INPUTS ===
// General Settings
group_general = "General Settings"
show_daily_high = input.bool(true, "Show Daily High Line", group=group_general)
show_daily_low = input.bool(true, "Show Daily Low Line", group=group_general)
show_day_open_line = input.bool(true, "Show Day Open Vertical Line", group=group_general)
show_labels = input.bool(true, "Show Labels", group=group_general)
max_periods = input.int(10, "Max Periods to Display", minval=1, maxval=50, tooltip="Number of days to keep on chart", group=group_general)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, tooltip="Hour when new day starts (ICT: 0 UTC)", group=group_time)

// Style Settings
group_style = "Style Settings"
high_color = input.color(color.white, "Daily High Color", group=group_style)
low_color = input.color(color.white, "Daily Low Color", group=group_style)
day_open_color = input.color(color.gray, "Day Open Line Color", group=group_style)
line_width = input.int(1, "Line Width", minval=1, maxval=3, group=group_style)
extend_lines = input.string("Right", "Extend Lines", options=["None", "Right", "Left", "Both"], group=group_style)

// Label Settings
group_labels = "Label Settings"
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=group_labels)
label_text_color = input.color(color.white, "Label Text Color", group=group_labels)

// === HELPER FUNCTIONS ===
get_extend_type(extend_type) =>
    switch extend_type
        "None" => extend.none
        "Right" => extend.right
        "Left" => extend.left
        "Both" => extend.both
        => extend.right

get_label_size(size_str) =>
    switch size_str
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        => size.small

// === VARIABLES ===
var extend_setting = get_extend_type(extend_lines)
var label_size_setting = get_label_size(label_size)

// Arrays to store lines and labels
var daily_high_lines = array.new_line()
var daily_low_lines = array.new_line()
var day_open_lines = array.new_line()
var daily_high_labels = array.new_label()
var daily_low_labels = array.new_label()

// === CORE LOGIC ===
// Detect new day
is_new_day = timeframe.change("1D")

// Variables to store daily levels
var float current_day_high = na
var float current_day_low = na
var int day_start_bar = na

// Update daily levels
if is_new_day or barstate.isfirst
    // Store the previous day's levels before updating
    if not na(current_day_high) and not na(current_day_low) and not na(day_start_bar)
        // Create historical lines for the completed day
        if show_daily_high
            historical_high_line = line.new(x1=day_start_bar, y1=current_day_high,x2=bar_index - 1, y2=current_day_high,color=high_color,width=line_width,style=line.style_solid,extend=extend.none)
            array.unshift(daily_high_lines, historical_high_line)

            // Add label at the right end of the line
            if show_labels
                high_label = label.new(x=bar_index - 1, y=current_day_high, text="DH: " + str.tostring(current_day_high, "#.####"), style=label.style_label_left, color=color.new(high_color, 80), textcolor=label_text_color, size=label_size_setting)
                array.unshift(daily_high_labels, high_label)

        if show_daily_low
            historical_low_line = line.new(x1=day_start_bar, y1=current_day_low,x2=bar_index - 1, y2=current_day_low,color=low_color,width=line_width,style=line.style_solid,extend=extend.none)
            array.unshift(daily_low_lines, historical_low_line)

            // Add label at the right end of the line
            if show_labels
                low_label = label.new(x=bar_index - 1, y=current_day_low, text="DL: " + str.tostring(current_day_low, "#.####"), style=label.style_label_left, color=color.new(low_color, 80), textcolor=label_text_color, size=label_size_setting)
                array.unshift(daily_low_labels, low_label)

    // Start new day tracking
    current_day_high := high
    current_day_low := low
    day_start_bar := bar_index
else
    // Update current day's high and low
    current_day_high := math.max(current_day_high, high)
    current_day_low := math.min(current_day_low, low)

// Function to manage array size for lines
manage_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_item = array.pop(arr)
        if not na(old_item)
            line.delete(old_item)

// Function to manage array size for labels
manage_label_array_size(arr, max_size) =>
    while array.size(arr) > max_size
        old_item = array.pop(arr)
        if not na(old_item)
            label.delete(old_item)

// Variables for current day's extending lines and labels
var line current_high_line = na
var line current_low_line = na
var label current_high_label = na
var label current_low_label = na

// === DRAWING LOGIC ===
// Draw vertical line at day open
if is_new_day and show_day_open_line
    day_open_line = line.new(x1=bar_index, y1=low,x2=bar_index, y2=high,color=day_open_color,width=line_width,style=line.style_dashed,extend=extend.both)
    array.unshift(day_open_lines, day_open_line)
    manage_array_size(day_open_lines, max_periods)

// Manage historical line and label arrays
manage_array_size(daily_high_lines, max_periods)
manage_array_size(daily_low_lines, max_periods)
manage_label_array_size(daily_high_labels, max_periods)
manage_label_array_size(daily_low_labels, max_periods)

// Draw/update current day's extending lines and labels
if show_daily_high and not na(current_day_high) and not na(day_start_bar)
    // Delete previous current line and label
    if not na(current_high_line)
        line.delete(current_high_line)
    if not na(current_high_label)
        label.delete(current_high_label)

    // Create new extending line for current day
    current_high_line := line.new(x1=day_start_bar, y1=current_day_high,x2=bar_index, y2=current_day_high,color=high_color,width=line_width,style=line.style_solid,extend=extend_setting)

    // Create label at the right end of current day's line
    if show_labels
        current_high_label := label.new(x=bar_index, y=current_day_high, text="DH: " + str.tostring(current_day_high, "#.####"), style=label.style_label_left, color=color.new(high_color, 80), textcolor=label_text_color, size=label_size_setting)

if show_daily_low and not na(current_day_low) and not na(day_start_bar)
    // Delete previous current line and label
    if not na(current_low_line)
        line.delete(current_low_line)
    if not na(current_low_label)
        label.delete(current_low_label)

    // Create new extending line for current day
    current_low_line := line.new(x1=day_start_bar, y1=current_day_low,x2=bar_index, y2=current_day_low,color=low_color,width=line_width,style=line.style_solid,extend=extend_setting)

    // Create label at the right end of current day's line
    if show_labels
        current_low_label := label.new(x=bar_index, y=current_day_low, text="DL: " + str.tostring(current_day_low, "#.####"), style=label.style_label_left, color=color.new(low_color, 80), textcolor=label_text_color, size=label_size_setting)
