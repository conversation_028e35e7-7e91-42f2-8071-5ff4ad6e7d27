//@version=5
indicator("Upcoming Sessions", "Upcoming Sessions", overlay=true, max_lines_count=500)

// ---------------------------------------- Input Parameters --------------------------------------------------
var g_SETTINGS = "General Settings"
max_days = input.int(3, "Days to Display", 1, tooltip = "Number of days of session lines to keep on the chart", group = g_SETTINGS)
gmt_tz = input.string('America/New_York', "Timezone", options = ['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], tooltip = "Note GMT is not adjusted to reflect Daylight Saving Time changes", group = g_SETTINGS)
line_style = input.string('Dashed', "Line Style", options = ['Solid', 'Dotted', 'Dashed'], group = g_SETTINGS)
line_width = input.int(1, "Line Width", 1, 5, group = g_SETTINGS)
show_labels = input.bool(true, "Show Session Labels", group = g_SETTINGS)
label_size = input.string('Normal', "Label Size", options = ['Tiny', 'Small', 'Normal', 'Large', 'Huge'], group = g_SETTINGS)

var g_SESSIONS = "Session Settings"
use_asia = input.bool(true, "", inline = "ASIA", group = g_SESSIONS)
asia_txt = input.string("Asia", "", inline = "ASIA", group = g_SESSIONS)
asia_session = input.session("2000-0000", "", inline = "ASIA", group = g_SESSIONS)
asia_color = input.color(color.blue, "", inline = "ASIA", group = g_SESSIONS)

use_london = input.bool(true, "", inline = "LONDON", group = g_SESSIONS)
london_txt = input.string("London", "", inline = "LONDON", group = g_SESSIONS)
london_session = input.session("0200-0500", "", inline = "LONDON", group = g_SESSIONS)
london_color = input.color(color.red, "", inline = "LONDON", group = g_SESSIONS)

use_nyam = input.bool(true, "", inline = "NYAM", group = g_SESSIONS)
nyam_txt = input.string("NY AM", "", inline = "NYAM", group = g_SESSIONS)
nyam_session = input.session("0930-1100", "", inline = "NYAM", group = g_SESSIONS)
nyam_color = input.color(#089981, "", inline = "NYAM", group = g_SESSIONS)

use_nylu = input.bool(true, "", inline = "NYLU", group = g_SESSIONS)
nylu_txt = input.string("NY Lunch", "", inline = "NYLU", group = g_SESSIONS)
nylu_session = input.session("1200-1300", "", inline = "NYLU", group = g_SESSIONS)
nylu_color = input.color(color.yellow, "", inline = "NYLU", group = g_SESSIONS)

use_nypm = input.bool(true, "", inline = "NYPM", group = g_SESSIONS)
nypm_txt = input.string("NY PM", "", inline = "NYPM", group = g_SESSIONS)
nypm_session = input.session("1330-1600", "", inline = "NYPM", group = g_SESSIONS)
nypm_color = input.color(color.purple, "", inline = "NYPM", group = g_SESSIONS)

// ---------------------------------------- Helper Functions --------------------------------------------------
// Convert string line style to Pine line style
get_line_style(string style) =>
    switch style
        'Solid' => line.style_solid
        'Dotted' => line.style_dotted
        'Dashed' => line.style_dashed

// Convert string label size to Pine label size
get_label_size(string size) =>
    switch size
        'Tiny' => size.tiny
        'Small' => size.small
        'Normal' => size.normal
        'Large' => size.large
        'Huge' => size.huge
        => size.normal

// ---------------------------------------- Variables & Constants --------------------------------------------------
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// Arrays to store session lines and labels
var asia_lines = array.new_line()
var london_lines = array.new_line()
var nyam_lines = array.new_line()
var nylu_lines = array.new_line()
var nypm_lines = array.new_line()

var asia_labels = array.new_label()
var london_labels = array.new_label()
var nyam_labels = array.new_label()
var nylu_labels = array.new_label()
var nypm_labels = array.new_label()

// ---------------------------------------- Session Time Functions --------------------------------------------------
// Function to get the timestamp for a session start on the current day
get_session_timestamp(string session_str) =>
    // Extract hours and minutes from session string (format: "HHMM-HHMM")
    hour_str = str.substring(session_str, 0, 2)
    minute_str = str.substring(session_str, 2, 4)
    
    hour = str.tonumber(hour_str)
    minute = str.tonumber(minute_str)
    
    // Get current day's timestamp at midnight
    current_day_midnight = timestamp(year, month, dayofmonth, 0, 0, 0)
    
    // Add hours and minutes to get session start time
    session_timestamp = current_day_midnight + (hour * 3600 + minute * 60) * 1000
    
    session_timestamp

// Function to check if we're at the start of a new day
is_new_day() =>
    change_day = dayofmonth != dayofmonth[1] or month != month[1] or year != year[1]
    first_bar = barstate.isfirst
    
    change_day or first_bar

// Function to manage line history (keep only max_days of lines)
manage_line_history(line[] line_array, label[] label_array) =>
    if line_array.size() > max_days
        line_array.pop().delete()
        if label_array.size() > 0
            label_array.pop().delete()

// ---------------------------------------- Line Drawing Functions --------------------------------------------------
// Function to draw a session line for a specific timestamp
draw_session_line(int timestamp, string session_name, color line_color, line[] line_array, label[] label_array) =>
    // Create a new line at the specified timestamp
    new_line = line.new(
        x1=timestamp, 
        y1=low * 0.999, 
        x2=timestamp, 
        y2=high * 1.001, 
        xloc=xloc.bar_time, 
        color=line_color, 
        style=line_style_value, 
        width=line_width, 
        extend=extend.both
    )
    
    // Add the line to the array
    line_array.unshift(new_line)
    
    // Create a label if enabled
    if show_labels
        new_label = label.new(
            x=timestamp, 
            y=high * 1.002, 
            text=session_name, 
            xloc=xloc.bar_time, 
            color=color.new(color.black, 100), 
            style=label.style_label_down, 
            textcolor=line_color, 
            size=label_size_value
        )
        
        label_array.unshift(new_label)
    
    // Manage line history
    manage_line_history(line_array, label_array)

// Function to draw all session lines for the current day
draw_all_session_lines() =>
    // Get session start timestamps for the current day
    if use_asia
        asia_start = get_session_timestamp(str.substring(asia_session, 0, 4))
        draw_session_line(asia_start, asia_txt, asia_color, asia_lines, asia_labels)
    
    if use_london
        london_start = get_session_timestamp(str.substring(london_session, 0, 4))
        draw_session_line(london_start, london_txt, london_color, london_lines, london_labels)
    
    if use_nyam
        nyam_start = get_session_timestamp(str.substring(nyam_session, 0, 4))
        draw_session_line(nyam_start, nyam_txt, nyam_color, nyam_lines, nyam_labels)
    
    if use_nylu
        nylu_start = get_session_timestamp(str.substring(nylu_session, 0, 4))
        draw_session_line(nylu_start, nylu_txt, nylu_color, nylu_lines, nylu_labels)
    
    if use_nypm
        nypm_start = get_session_timestamp(str.substring(nypm_session, 0, 4))
        draw_session_line(nypm_start, nypm_txt, nypm_color, nypm_lines, nypm_labels)

// ---------------------------------------- Main Logic --------------------------------------------------
// Draw session lines at the start of each day
if is_new_day()
    draw_all_session_lines()
