//@version=5
indicator("Important Time Levels", overlay=true, max_lines_count=500, max_labels_count=500)

// Input parameters
var color LEVEL_COLOR = color.new(#FFD700, 70)  // Golden color with transparency
var color LABEL_COLOR = color.new(#FFD700, 0)   // Solid golden color for labels

// Time inputs (all in EST/EDT)
show_midnight = input.bool(true, "Show Midnight (00:00)", group="Time Levels")
show_news = input.bool(true, "Show News Time (08:30)", group="Time Levels")
show_nyse = input.bool(true, "Show NYSE Open (09:30)", group="Time Levels")
show_4h_1 = input.bool(true, "Show 4H Open 1 (10:00)", group="Time Levels")
show_4h_2 = input.bool(true, "Show 4H Open 2 (14:00)", group="Time Levels")

// Store the previous levels
var float midnight_level = na
var float news_level = na
var float nyse_level = na
var float h4_1_level = na
var float h4_2_level = na

// Function to check if current bar is at specific hour and minute
isTimeMatch(hr, min) =>
    t = time("America/New_York", "0000")
    hour == hr and minute == min

// Update levels when time matches
if show_midnight and isTimeMatch(0, 0)
    midnight_level := open
if show_news and isTimeMatch(8, 30)
    news_level := open
if show_nyse and isTimeMatch(9, 30)
    nyse_level := open
if show_4h_1 and isTimeMatch(10, 0)
    h4_1_level := open
if show_4h_2 and isTimeMatch(14, 0)
    h4_2_level := open

// Plot the levels
plot(midnight_level, "Midnight Level", color=LEVEL_COLOR, style=plot.style_line, linewidth=2)
plot(news_level, "News Level", color=LEVEL_COLOR, style=plot.style_line, linewidth=2)
plot(nyse_level, "NYSE Level", color=LEVEL_COLOR, style=plot.style_line, linewidth=2)
plot(h4_1_level, "4H-1 Level", color=LEVEL_COLOR, style=plot.style_line, linewidth=2)
plot(h4_2_level, "4H-2 Level", color=LEVEL_COLOR, style=plot.style_line, linewidth=2)

// Add labels on the last bar
if barstate.islast
    if not na(midnight_level)
        label.new(bar_index, midnight_level, "00:00", color=LABEL_COLOR, textcolor=color.white, style=label.style_label_right)
    if not na(news_level)
        label.new(bar_index, news_level, "08:30", color=LABEL_COLOR, textcolor=color.white, style=label.style_label_right)
    if not na(nyse_level)
        label.new(bar_index, nyse_level, "09:30", color=LABEL_COLOR, textcolor=color.white, style=label.style_label_right)
    if not na(h4_1_level)
        label.new(bar_index, h4_1_level, "10:00", color=LABEL_COLOR, textcolor=color.white, style=label.style_label_right)
    if not na(h4_2_level)
        label.new(bar_index, h4_2_level, "14:00", color=LABEL_COLOR, textcolor=color.white, style=label.style_label_right)
