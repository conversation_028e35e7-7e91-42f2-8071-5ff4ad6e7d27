//@version=5
indicator("ATT Intraday Indicator", overlay=true)

// ------------------------------------------------------------------------------------------------
// Settings
// ------------------------------------------------------------------------------------------------
group_att = "ATT Settings"
showATT = input.bool(true, "Show ATT Markers", group=group_att)
attHighColor = input.color(#00ff00, "ATT High Marker Color", group=group_att)
attLowColor = input.color(#ff0000, "ATT Low Marker Color", group=group_att)
labelSize = input.string("Small", "Marker & Label Size", options=["Tiny", "Small", "Normal"], group=group_att)
showSwingPoints = input.bool(true, "Highlight Swing Points Only", tooltip="When enabled, only shows markers at swing highs/lows", group=group_att)
lookbackBars = input.int(3, "Swing Point Lookback Bars", minval=1, maxval=10, group=group_att)
labelOffset = input.float(0.0003, "Label Offset", minval=0.0001, maxval=0.01, step=0.001, group=group_att)

// Hour Anchor Settings
group_hour = "Hour Anchor Settings"
showHourLines = input.bool(true, "Show Hour Anchor Lines", group=group_hour)
hourLineColor = input.color(#2196F3, "Hour Line Color", group=group_hour)
hourLineWidth = input.int(1, "Hour Line Width", minval=1, maxval=4, group=group_hour)
showHourBackground = input.bool(true, "Show Hour Background", group=group_hour)

// ATT Minutes to highlight
group_att_minutes = "ATT Minutes"
show_3min = input.bool(true, "3", inline="min1", group=group_att_minutes)
show_11min = input.bool(true, "11", inline="min1", group=group_att_minutes)
show_17min = input.bool(true, "17", inline="min1", group=group_att_minutes)
show_29min = input.bool(true, "29", inline="min2", group=group_att_minutes)
show_41min = input.bool(true, "41", inline="min2", group=group_att_minutes)
show_47min = input.bool(true, "47", inline="min3", group=group_att_minutes)
show_53min = input.bool(true, "53", inline="min3", group=group_att_minutes)
show_59min = input.bool(true, "59", inline="min3", group=group_att_minutes)

// ------------------------------------------------------------------------------------------------
// ATT Logic
// ------------------------------------------------------------------------------------------------

// Create ATT Minutes Array based on user selections
var int[] attMinutes = array.new_int()

// Initialize ATT minutes array on first bar
if barstate.isfirst
    array.clear(attMinutes)
    if show_3min
        array.push(attMinutes, 3)
    if show_11min
        array.push(attMinutes, 11)
    if show_17min
        array.push(attMinutes, 17)
    if show_29min
        array.push(attMinutes, 29)
    if show_41min
        array.push(attMinutes, 41)
    if show_47min
        array.push(attMinutes, 47)
    if show_53min
        array.push(attMinutes, 53)
    if show_59min
        array.push(attMinutes, 59)

// Function to detect swing points
isSwingHigh(lookback) =>
    high > ta.highest(high[1], lookback)

isSwingLow(lookback) =>
    low < ta.lowest(low[1], lookback)

// Detect New Hour (for vertical line)
isNewHour = ta.change(time("60"))

// Calculate Current Minute
currentMinute = minute(time)
currentHourStart = timestamp(year, month, dayofmonth, hour, 0, 0)

// Plot Hourly Vertical Lines
var line hLine = na
if isNewHour and showHourLines
    // Delete previous line
    if not na(hLine)
        line.delete(hLine)

    // Create new hour line
    hLine := line.new(bar_index,low - ta.tr,bar_index,high + ta.tr,color=hourLineColor,width=hourLineWidth)

    // Add hour label at the top of the line
    label.new(bar_index,high + ta.tr,text=str.tostring(hour) + ":00",color=color.new(hourLineColor, 80),style=label.style_label_down,textcolor=hourLineColor,size=size.normal)

// Store ATT labels in arrays
var label[] highLabels = array.new_label()
var label[] lowLabels = array.new_label()

// Clear old labels on new bar
if barstate.isfirst
    if array.size(highLabels) > 0
        for i = 0 to array.size(highLabels) - 1
            label.delete(array.get(highLabels, i))
        array.clear(highLabels)

    if array.size(lowLabels) > 0
        for i = 0 to array.size(lowLabels) - 1
            label.delete(array.get(lowLabels, i))
        array.clear(lowLabels)

// Variables for ATT detection
var bool isATT3Bar = false
var bool isATT11Bar = false
var bool isATT17Bar = false
var bool isATT29Bar = false
var bool isATT41Bar = false
var bool isATT47Bar = false
var bool isATT53Bar = false
var bool isATT59Bar = false

// Reset ATT detection variables
isATT3Bar := false
isATT11Bar := false
isATT17Bar := false
isATT29Bar := false
isATT41Bar := false
isATT47Bar := false
isATT53Bar := false
isATT59Bar := false

// Check for swing points
isSwingH = isSwingHigh(lookbackBars)
isSwingL = isSwingLow(lookbackBars)

// Set label size based on user preference
labelTextSize = labelSize == "Tiny" ? size.tiny : labelSize == "Small" ? size.small : size.normal

// Check for ATT Minutes and create labels
if showATT and timeframe.isintraday
    for i = 0 to array.size(attMinutes) - 1
        targetMinute = array.get(attMinutes, i)
        targetTime = currentHourStart + targetMinute * 60 * 1000

        // Check if this is the exact ATT minute bar
        isATTBar = time >= targetTime and time[1] < targetTime

        // Set the appropriate ATT detection variable
        if isATTBar
            if targetMinute == 3
                isATT3Bar := true
            else if targetMinute == 11
                isATT11Bar := true
            else if targetMinute == 17
                isATT17Bar := true
            else if targetMinute == 29
                isATT29Bar := true
            else if targetMinute == 41
                isATT41Bar := true
            else if targetMinute == 47
                isATT47Bar := true
            else if targetMinute == 53
                isATT53Bar := true
            else if targetMinute == 59
                isATT59Bar := true

            // Draw high marker if showing all points or if it's a swing high
            if not showSwingPoints or isSwingH
                // Create circle with number (all on one line)
                highLabel = label.new(bar_index, high + (high * labelOffset), text=str.tostring(targetMinute), color=attHighColor, style=label.style_circle, textcolor=color.white, size=labelTextSize)
                array.push(highLabels, highLabel)

            // Draw low marker if showing all points or if it's a swing low
            if not showSwingPoints or isSwingL
                // Create circle with number (all on one line)
                lowLabel = label.new(bar_index, low - (low * labelOffset), text=str.tostring(targetMinute), color=attLowColor, style=label.style_circle, textcolor=color.white, size=labelTextSize)
                array.push(lowLabels, lowLabel)

// Plot ATT markers in global scope - with separate calls for each size option
// Tiny size markers
// 3 minute markers - Tiny
plotshape(isATT3Bar and showATT and (not showSwingPoints or isSwingH) and show_3min and labelSize == "Tiny", title="ATT 3 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT3Bar and showATT and (not showSwingPoints or isSwingL) and show_3min and labelSize == "Tiny", title="ATT 3 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// 11 minute markers - Tiny
plotshape(isATT11Bar and showATT and (not showSwingPoints or isSwingH) and show_11min and labelSize == "Tiny", title="ATT 11 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT11Bar and showATT and (not showSwingPoints or isSwingL) and show_11min and labelSize == "Tiny", title="ATT 11 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// 17 minute markers - Tiny
plotshape(isATT17Bar and showATT and (not showSwingPoints or isSwingH) and show_17min and labelSize == "Tiny", title="ATT 17 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT17Bar and showATT and (not showSwingPoints or isSwingL) and show_17min and labelSize == "Tiny", title="ATT 17 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// 29 minute markers - Tiny
plotshape(isATT29Bar and showATT and (not showSwingPoints or isSwingH) and show_29min and labelSize == "Tiny", title="ATT 29 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT29Bar and showATT and (not showSwingPoints or isSwingL) and show_29min and labelSize == "Tiny", title="ATT 29 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// 41 minute markers - Tiny
plotshape(isATT41Bar and showATT and (not showSwingPoints or isSwingH) and show_41min and labelSize == "Tiny", title="ATT 41 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT41Bar and showATT and (not showSwingPoints or isSwingL) and show_41min and labelSize == "Tiny", title="ATT 41 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// 47 minute markers - Tiny
plotshape(isATT47Bar and showATT and (not showSwingPoints or isSwingH) and show_47min and labelSize == "Tiny", title="ATT 47 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT47Bar and showATT and (not showSwingPoints or isSwingL) and show_47min and labelSize == "Tiny", title="ATT 47 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// 53 minute markers - Tiny
plotshape(isATT53Bar and showATT and (not showSwingPoints or isSwingH) and show_53min and labelSize == "Tiny", title="ATT 53 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT53Bar and showATT and (not showSwingPoints or isSwingL) and show_53min and labelSize == "Tiny", title="ATT 53 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// 59 minute markers - Tiny
plotshape(isATT59Bar and showATT and (not showSwingPoints or isSwingH) and show_59min and labelSize == "Tiny", title="ATT 59 High Tiny", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.tiny)
plotshape(isATT59Bar and showATT and (not showSwingPoints or isSwingL) and show_59min and labelSize == "Tiny", title="ATT 59 Low Tiny", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.tiny)

// Small size markers
// 3 minute markers - Small
plotshape(isATT3Bar and showATT and (not showSwingPoints or isSwingH) and show_3min and labelSize == "Small", title="ATT 3 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT3Bar and showATT and (not showSwingPoints or isSwingL) and show_3min and labelSize == "Small", title="ATT 3 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// 11 minute markers - Small
plotshape(isATT11Bar and showATT and (not showSwingPoints or isSwingH) and show_11min and labelSize == "Small", title="ATT 11 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT11Bar and showATT and (not showSwingPoints or isSwingL) and show_11min and labelSize == "Small", title="ATT 11 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// 17 minute markers - Small
plotshape(isATT17Bar and showATT and (not showSwingPoints or isSwingH) and show_17min and labelSize == "Small", title="ATT 17 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT17Bar and showATT and (not showSwingPoints or isSwingL) and show_17min and labelSize == "Small", title="ATT 17 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// 29 minute markers - Small
plotshape(isATT29Bar and showATT and (not showSwingPoints or isSwingH) and show_29min and labelSize == "Small", title="ATT 29 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT29Bar and showATT and (not showSwingPoints or isSwingL) and show_29min and labelSize == "Small", title="ATT 29 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// 41 minute markers - Small
plotshape(isATT41Bar and showATT and (not showSwingPoints or isSwingH) and show_41min and labelSize == "Small", title="ATT 41 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT41Bar and showATT and (not showSwingPoints or isSwingL) and show_41min and labelSize == "Small", title="ATT 41 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// 47 minute markers - Small
plotshape(isATT47Bar and showATT and (not showSwingPoints or isSwingH) and show_47min and labelSize == "Small", title="ATT 47 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT47Bar and showATT and (not showSwingPoints or isSwingL) and show_47min and labelSize == "Small", title="ATT 47 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// 53 minute markers - Small
plotshape(isATT53Bar and showATT and (not showSwingPoints or isSwingH) and show_53min and labelSize == "Small", title="ATT 53 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT53Bar and showATT and (not showSwingPoints or isSwingL) and show_53min and labelSize == "Small", title="ATT 53 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// 59 minute markers - Small
plotshape(isATT59Bar and showATT and (not showSwingPoints or isSwingH) and show_59min and labelSize == "Small", title="ATT 59 High Small", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.small)
plotshape(isATT59Bar and showATT and (not showSwingPoints or isSwingL) and show_59min and labelSize == "Small", title="ATT 59 Low Small", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.small)

// Normal size markers
// 3 minute markers - Normal
plotshape(isATT3Bar and showATT and (not showSwingPoints or isSwingH) and show_3min and labelSize == "Normal", title="ATT 3 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT3Bar and showATT and (not showSwingPoints or isSwingL) and show_3min and labelSize == "Normal", title="ATT 3 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// 11 minute markers - Normal
plotshape(isATT11Bar and showATT and (not showSwingPoints or isSwingH) and show_11min and labelSize == "Normal", title="ATT 11 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT11Bar and showATT and (not showSwingPoints or isSwingL) and show_11min and labelSize == "Normal", title="ATT 11 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// 17 minute markers - Normal
plotshape(isATT17Bar and showATT and (not showSwingPoints or isSwingH) and show_17min and labelSize == "Normal", title="ATT 17 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT17Bar and showATT and (not showSwingPoints or isSwingL) and show_17min and labelSize == "Normal", title="ATT 17 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// 29 minute markers - Normal
plotshape(isATT29Bar and showATT and (not showSwingPoints or isSwingH) and show_29min and labelSize == "Normal", title="ATT 29 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT29Bar and showATT and (not showSwingPoints or isSwingL) and show_29min and labelSize == "Normal", title="ATT 29 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// 41 minute markers - Normal
plotshape(isATT41Bar and showATT and (not showSwingPoints or isSwingH) and show_41min and labelSize == "Normal", title="ATT 41 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT41Bar and showATT and (not showSwingPoints or isSwingL) and show_41min and labelSize == "Normal", title="ATT 41 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// 47 minute markers - Normal
plotshape(isATT47Bar and showATT and (not showSwingPoints or isSwingH) and show_47min and labelSize == "Normal", title="ATT 47 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT47Bar and showATT and (not showSwingPoints or isSwingL) and show_47min and labelSize == "Normal", title="ATT 47 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// 53 minute markers - Normal
plotshape(isATT53Bar and showATT and (not showSwingPoints or isSwingH) and show_53min and labelSize == "Normal", title="ATT 53 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT53Bar and showATT and (not showSwingPoints or isSwingL) and show_53min and labelSize == "Normal", title="ATT 53 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// 59 minute markers - Normal
plotshape(isATT59Bar and showATT and (not showSwingPoints or isSwingH) and show_59min and labelSize == "Normal", title="ATT 59 High Normal", style=shape.circle, location=location.abovebar, color=attHighColor, size=size.normal)
plotshape(isATT59Bar and showATT and (not showSwingPoints or isSwingL) and show_59min and labelSize == "Normal", title="ATT 59 Low Normal", style=shape.circle, location=location.belowbar, color=attLowColor, size=size.normal)

// Background for New Hour
bgcolor(isNewHour and showHourBackground ? color.new(color.blue, 90) : na)