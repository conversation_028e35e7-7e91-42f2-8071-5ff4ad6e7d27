//@version=5
// Single Period AMR Levels Indicator
// This indicator draws Average Monthly Range (AMR) levels for a single configurable period
// Features:
// 1. Draws AMR levels of a single period
// 2. Draws true month opening horizontal and vertical lines
// 3. When new true month opening is detected, old lines stay but end at the new true month open
//    and new lines start with labels

indicator("Single Period AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500, max_bars_back=5000,dynamic_requests=true)

// === INPUTS ===
// General settings
amr_period = input.int(6, title="AMR Period (Months)", minval=1, maxval=12, group="AMR Settings")
show_full_amr = input.bool(true, title="Show Full AMR", group="AMR Levels")
show_one_third_amr = input.bool(true, title="Show 1/3 AMR", group="AMR Levels")
show_two_third_amr = input.bool(true, title="Show 2/3 AMR", group="AMR Levels")
show_half_amr = input.bool(true, title="Show 1/2 AMR", group="AMR Levels")

// True month open settings
true_month_open_hour = input.int(4, title="True Month Open Hour (UTC)", minval=0, maxval=23, group="True Month Open")
use_exchange_time = input.bool(false, title="Use Exchange Time Instead of UTC", group="True Month Open")
show_vertical_month = input.bool(true, title="Show Vertical Line at Month Open", group="True Month Open")

// Visual settings
amr_color = input.color(color.yellow, title="AMR Level Color", group="Visual Settings")
true_month_open_color = input.color(color.red, title="True Month Open Color", group="Visual Settings")
line_style_value = input.string("Dotted", title="Line Style", options=["Solid", "Dotted", "Dashed"], group="Visual Settings")
line_width = input.int(1, title="Line Width", minval=1, maxval=4, group="Visual Settings")
extend_right = input.int(1, title="Extend Lines Right (days)", minval=0, maxval=10, group="Visual Settings")
extend_left = input.int(0, title="Extend Lines Left (days)", minval=0, maxval=10, group="Visual Settings")
show_labels = input.bool(true, title="Show Labels", group="Visual Settings")
show_price_in_label = input.bool(false, title="Show Price in Label", group="Visual Settings")
label_size_value = input.string("Small", title="Label Size", options=["Tiny", "Small", "Normal", "Large"], group="Visual Settings")
label_y_offset = input.float(0.0, title="Label Y Offset", step=0.1, group="Visual Settings")
show_historical = input.bool(true, title="Show Historical Levels", group="Visual Settings")
max_periods = input.int(12, title="Maximum Periods to Display", minval=1, maxval=24, group="Visual Settings")

// === VARIABLES ===
// Line style mapping
var line_style = line.style_solid
if line_style_value == "Dotted"
    line_style := line.style_dotted
else if line_style_value == "Dashed"
    line_style := line.style_dashed

// Label size mapping
var label_size = size.small
if label_size_value == "Tiny"
    label_size := size.tiny
else if label_size_value == "Normal"
    label_size := size.normal
else if label_size_value == "Large"
    label_size := size.large

// Arrays to store monthly data
var monthly_open_times = array.new_int(0)  // Store the timestamp of each month's open
var monthly_open_prices = array.new_float(0)  // Store the price at each month's open
var monthly_amr_values = array.new_float(0)  // Store the AMR value for each month
var monthly_open_barindex = array.new_int(0)  // Store the bar index of each month's open

// Arrays to store line IDs for each month
var monthly_vertical_lines = array.new_line(0)  // Vertical lines at month open
var monthly_tmo_lines = array.new_line(0)  // True month open horizontal lines
var monthly_full_up_lines = array.new_line(0)  // Full AMR up lines
var monthly_full_down_lines = array.new_line(0)  // Full AMR down lines
var monthly_one_third_up_lines = array.new_line(0)  // 1/3 AMR up lines
var monthly_one_third_down_lines = array.new_line(0)  // 1/3 AMR down lines
var monthly_two_third_up_lines = array.new_line(0)  // 2/3 AMR up lines
var monthly_two_third_down_lines = array.new_line(0)  // 2/3 AMR down lines
var monthly_half_up_lines = array.new_line(0)  // 1/2 AMR up lines
var monthly_half_down_lines = array.new_line(0)  // 1/2 AMR down lines

// Arrays to store label IDs for the current month only
var tmo_label = label.new(na, na, "", xloc=xloc.bar_index)
var full_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var full_down_label = label.new(na, na, "", xloc=xloc.bar_index)
var one_third_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var one_third_down_label = label.new(na, na, "", xloc=xloc.bar_index)
var two_third_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var two_third_down_label = label.new(na, na, "", xloc=xloc.bar_index)
var half_up_label = label.new(na, na, "", xloc=xloc.bar_index)
var half_down_label = label.new(na, na, "", xloc=xloc.bar_index)

// Store the last AMR period to detect changes
var int last_amr_period = amr_period

// Variable to track if we need to force a redraw
var bool force_redraw = false
period_changed = last_amr_period != amr_period
// === FUNCTIONS ===
// Function to reset all arrays and labels
reset_labels_and_arrays() =>
    // Clear all line arrays
    array.clear(monthly_tmo_lines)
    array.clear(monthly_full_up_lines)
    array.clear(monthly_full_down_lines)
    array.clear(monthly_one_third_up_lines)
    array.clear(monthly_one_third_down_lines)
    array.clear(monthly_two_third_up_lines)
    array.clear(monthly_two_third_down_lines)
    array.clear(monthly_half_up_lines)
    array.clear(monthly_half_down_lines)

    // Delete all existing labels
    label.delete(tmo_label)
    label.delete(full_up_label)
    label.delete(full_down_label)
    label.delete(one_third_up_label)
    label.delete(one_third_down_label)
    label.delete(two_third_up_label)
    label.delete(two_third_down_label)
    label.delete(half_up_label)
    label.delete(half_down_label)

    // Create new label objects
    new_tmo_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_full_up_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_full_down_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_one_third_up_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_one_third_down_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_two_third_up_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_two_third_down_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_half_up_label = label.new(na, na, "", xloc=xloc.bar_index)
    new_half_down_label = label.new(na, na, "", xloc=xloc.bar_index)

    // Clear data arrays
    array.clear(monthly_amr_values)
    array.clear(monthly_open_times)
    array.clear(monthly_open_prices)
    array.clear(monthly_open_barindex)

    // Return the new label objects
    [new_tmo_label, new_full_up_label, new_full_down_label, new_one_third_up_label, new_one_third_down_label, new_two_third_up_label, new_two_third_down_label, new_half_up_label, new_half_down_label]
// Calculate AMR (Average Monthly Range)
calculate_amr(lookback_period) =>
    // Request historical monthly high/low data with extended lookback
    // We request 12 months of data to ensure we have enough for any period setting up to 12 months

    // Get monthly high/low data for the extended lookback period
    [m_high, m_low, m_open] = request.security(syminfo.tickerid,"M",[high, low, open],lookahead=barmerge.lookahead_off)

    // Store monthly ranges in an array
    var ranges = array.new_float(0)

    // On first bar or period change, initialize the ranges array with historical data
    if barstate.isfirst or period_changed
        array.clear(ranges)

        // Get historical monthly ranges using a single security call
        // We request historical high and low data for multiple months at once

        // Request historical data for the past months
        [hist_high_0, hist_low_0] = request.security(syminfo.tickerid, "M", [high[0], low[0]], lookahead=barmerge.lookahead_off)
        [hist_high_1, hist_low_1] = request.security(syminfo.tickerid, "M", [high[1], low[1]], lookahead=barmerge.lookahead_off)
        [hist_high_2, hist_low_2] = request.security(syminfo.tickerid, "M", [high[2], low[2]], lookahead=barmerge.lookahead_off)
        [hist_high_3, hist_low_3] = request.security(syminfo.tickerid, "M", [high[3], low[3]], lookahead=barmerge.lookahead_off)
        [hist_high_4, hist_low_4] = request.security(syminfo.tickerid, "M", [high[4], low[4]], lookahead=barmerge.lookahead_off)
        [hist_high_5, hist_low_5] = request.security(syminfo.tickerid, "M", [high[5], low[5]], lookahead=barmerge.lookahead_off)
        [hist_high_6, hist_low_6] = request.security(syminfo.tickerid, "M", [high[6], low[6]], lookahead=barmerge.lookahead_off)
        [hist_high_7, hist_low_7] = request.security(syminfo.tickerid, "M", [high[7], low[7]], lookahead=barmerge.lookahead_off)
        [hist_high_8, hist_low_8] = request.security(syminfo.tickerid, "M", [high[8], low[8]], lookahead=barmerge.lookahead_off)
        [hist_high_9, hist_low_9] = request.security(syminfo.tickerid, "M", [high[9], low[9]], lookahead=barmerge.lookahead_off)
        [hist_high_10, hist_low_10] = request.security(syminfo.tickerid, "M", [high[10], low[10]], lookahead=barmerge.lookahead_off)
        [hist_high_11, hist_low_11] = request.security(syminfo.tickerid, "M", [high[11], low[11]], lookahead=barmerge.lookahead_off)

        // Calculate and store ranges
        range_0 = hist_high_0 - hist_low_0
        range_1 = hist_high_1 - hist_low_1
        range_2 = hist_high_2 - hist_low_2
        range_3 = hist_high_3 - hist_low_3
        range_4 = hist_high_4 - hist_low_4
        range_5 = hist_high_5 - hist_low_5
        range_6 = hist_high_6 - hist_low_6
        range_7 = hist_high_7 - hist_low_7
        range_8 = hist_high_8 - hist_low_8
        range_9 = hist_high_9 - hist_low_9
        range_10 = hist_high_10 - hist_low_10
        range_11 = hist_high_11 - hist_low_11

        // Add ranges to the array in reverse order (oldest first)
        if not na(range_11)
            array.push(ranges, range_11)
        if not na(range_10)
            array.push(ranges, range_10)
        if not na(range_9)
            array.push(ranges, range_9)
        if not na(range_8)
            array.push(ranges, range_8)
        if not na(range_7)
            array.push(ranges, range_7)
        if not na(range_6)
            array.push(ranges, range_6)
        if not na(range_5)
            array.push(ranges, range_5)
        if not na(range_4)
            array.push(ranges, range_4)
        if not na(range_3)
            array.push(ranges, range_3)
        if not na(range_2)
            array.push(ranges, range_2)
        if not na(range_1)
            array.push(ranges, range_1)
        if not na(range_0)
            array.push(ranges, range_0)

    // Calculate the current month's range
    current_range = m_high - m_low

    // Add the current month's range if it's valid and we're at a new month or first bar
    if not na(current_range) and (barstate.isfirst or timeframe.change("M"))
        array.unshift(ranges, current_range)

        // Keep only what we need plus buffer
        while array.size(ranges) > 12
            array.pop(ranges)

    // Calculate the average of the ranges for the specified lookback period
    sum_range = 0.0
    count = 0

    // Make sure we have enough data for the requested period
    available_periods = math.min(array.size(ranges), lookback_period)

    // Sum up the available ranges
    for i = 0 to available_periods - 1
        if i < array.size(ranges)
            range_val = array.get(ranges, i)
            if not na(range_val)
                sum_range := sum_range + range_val
                count := count + 1

    // Calculate the average range
    avg_range = count > 0 ? sum_range / count : na

    // Return the average range
    avg_range

// Create a horizontal line
create_horizontal_line(price, color, style, x1, x2) =>
    line.new(x1=x1, y1=price, x2=x2, y2=price, color=color, style=style, width=line_width, xloc=xloc.bar_time)

// Create a vertical line
create_vertical_line(bar_pos, color) =>
    line.new(x1=bar_pos, y1=low - (high-low)*0.5, x2=bar_pos, y2=high + (high-low)*0.5,
             color=color, style=line.style_dashed, width=2, extend=extend.both, xloc=xloc.bar_index)

// Create or update a label
create_or_update_label(label_obj, price, t, color, x_pos) =>
    if show_labels
        if na(label_obj) or na(label.get_x(label_obj))
            new_label = label.new(x=x_pos, y=price + label_y_offset,
                     text=t + (show_price_in_label ? str.format(" ({0})", price) : ""),
                     style=label.style_label_left, size=label_size,
                     textcolor=color, color=color.new(color.black, 100), xloc=xloc.bar_index)
            new_label
        else
            label.set_x(label_obj, x_pos)
            label.set_y(label_obj, price + label_y_offset)
            label.set_text(label_obj, t + (show_price_in_label ? str.format(" ({0})", price) : ""))
            label.set_textcolor(label_obj, color)
            label_obj
    else
        label_obj

// === MAIN LOGIC ===
// Define session for true month open detection
true_month_hour_str = true_month_open_hour < 10 ? "0" + str.tostring(true_month_open_hour) : str.tostring(true_month_open_hour)
true_month_next_hour = (true_month_open_hour + 1) % 24
true_month_next_hour_str = true_month_next_hour < 10 ? "0" + str.tostring(true_month_next_hour) : str.tostring(true_month_next_hour)
true_month_session = true_month_hour_str + "00-" + true_month_next_hour_str + "00"

// Get current time at the close of the bar for label positioning
time_close = time

// Determine if we're at a new month based on configurable true open time
is_true_month_start = not na(time(timeframe.period, true_month_session, "GMT+0")) and
                     na(time(timeframe.period, true_month_session, "GMT+0")[1]) and
                     dayofmonth(time, "GMT+0") == 1

// Use either exchange time or configurable true month open time
is_new_month = use_exchange_time ? timeframe.change("M") : is_true_month_start

// Check for period change on every bar
var float amr_value = na

// Set force_redraw flag if period changed
if period_changed
    force_redraw := true

// Force recalculation on period change, new month, first bar, or forced redraw
if period_changed or is_new_month or barstate.isfirst or force_redraw
    // Always recalculate AMR value
    amr_value := calculate_amr(amr_period)

    // Handle period change specifically
    if period_changed
        // Use the reset function to clear all arrays and labels
        [new_tmo_label, new_full_up_label, new_full_down_label, new_one_third_up_label, new_one_third_down_label, new_two_third_up_label, new_two_third_down_label, new_half_up_label, new_half_down_label] = reset_labels_and_arrays()

        // Assign the new label objects to the global variables
        tmo_label := new_tmo_label
        full_up_label := new_full_up_label
        full_down_label := new_full_down_label
        one_third_up_label := new_one_third_up_label
        one_third_down_label := new_one_third_down_label
        two_third_up_label := new_two_third_up_label
        two_third_down_label := new_two_third_down_label
        half_up_label := new_half_up_label
        half_down_label := new_half_down_label

        // Add current month data back with the new AMR value
        array.push(monthly_open_times, int(time))
        array.push(monthly_open_prices, open)
        array.push(monthly_amr_values, amr_value)
        array.push(monthly_open_barindex, bar_index)

        // Update the stored period value
        last_amr_period := amr_period
    else
        // Just update the period if it wasn't a change
        last_amr_period := amr_period

// Handle true month open
if is_new_month or barstate.isfirst
    // Get the true month open price
    true_month_open_price = barstate.isfirst ? close : open

    // Calculate AMR value for this specific month
    current_amr = calculate_amr(amr_period)

    // Store month open values
    array.unshift(monthly_open_times, int(time))
    array.unshift(monthly_open_prices, true_month_open_price)
    array.unshift(monthly_amr_values, current_amr)
    array.unshift(monthly_open_barindex, bar_index)  // Store the current bar index

    // Draw vertical line at month open if needed
    if show_vertical_month
        // Create new vertical line at month open
        month_vertical = create_vertical_line(bar_index, true_month_open_color)
        array.unshift(monthly_vertical_lines, month_vertical)

    // Manage array sizes
    if array.size(monthly_open_times) > max_periods
        array.pop(monthly_open_times)
        array.pop(monthly_open_prices)
        array.pop(monthly_amr_values)
        array.pop(monthly_open_barindex)

        // Remove oldest vertical line
        if array.size(monthly_vertical_lines) > max_periods
            oldest_line = array.get(monthly_vertical_lines, array.size(monthly_vertical_lines) - 1)
            if not na(oldest_line)
                line.delete(oldest_line)
            array.pop(monthly_vertical_lines)

// Draw or update lines on the last bar or when forced to redraw
if (barstate.islast or force_redraw) and array.size(monthly_open_times) > 0
    // Find the end times for each month's lines
    var end_times = array.new_int(0)
    array.clear(end_times)

    // Current month extends to current time + configurable right extension
    array.push(end_times, time + 60 * 60 * 24 * extend_right * 1000) // Add days based on user setting

    // Find end times for previous months (which is the start of the next month)
    for i = 1 to array.size(monthly_open_times) - 1
        // Use the timestamp of the previous month as the end time
        if i - 1 < array.size(monthly_open_times)
            array.push(end_times, array.get(monthly_open_times, i - 1))
        else
            // Fallback if we don't have the timestamp
            array.push(end_times, time)

    // Make sure we have enough end times for all months
    while array.size(end_times) < array.size(monthly_open_prices)
        array.push(end_times, time + 60 * 60 * 24 * extend_right * 1000) // Add days based on user setting

    // Reset the force_redraw flag since we're about to redraw everything
    force_redraw := false

    // Draw lines for each month
    for i = 0 to math.min(array.size(monthly_open_prices) - 1, max_periods - 1)
        // Get month data
        month_time = array.get(monthly_open_times, i)
        tmo = array.get(monthly_open_prices, i)

        // Get end time for this month's lines
        end_time = i < array.size(end_times) ? array.get(end_times, i) : time + 60 * 60 * 24 * extend_right * 1000

        // Only draw if we have valid start and end times
        if not na(month_time) and not na(end_time) and month_time <= end_time
            // Calculate start time with left extension
            start_time = month_time - 60 * 60 * 24 * extend_left * 1000

            // Draw true month open line
            tmo_line = create_horizontal_line(tmo, true_month_open_color, line_style, start_time, end_time)

            // Store or update the line
            if i < array.size(monthly_tmo_lines)
                old_line = array.get(monthly_tmo_lines, i)
                if not na(old_line)
                    line.delete(old_line)
                array.set(monthly_tmo_lines, i, tmo_line)
            else
                array.push(monthly_tmo_lines, tmo_line)

            // Draw true month open label (only for current month)
            if i == 0
                // Calculate label position at the end of the line
                label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                tmo_label := create_or_update_label(tmo_label, tmo, "TMO", true_month_open_color, label_pos)

            // Draw AMR levels if we have the range data
            if i < array.size(monthly_amr_values)
                amr = array.get(monthly_amr_values, i)

                if not na(amr)
                    // Calculate levels for this month
                    full_up = tmo + amr
                    full_down = tmo - amr
                    one_third_up = tmo + (amr / 3)
                    one_third_down = tmo - (amr / 3)
                    two_third_up = tmo + (amr * 2 / 3)
                    two_third_down = tmo - (amr * 2 / 3)
                    half_up = tmo + (amr / 2)
                    half_down = tmo - (amr / 2)

                    // Draw full AMR levels
                    if show_full_amr
                        // Draw full AMR+ line
                        full_up_line = create_horizontal_line(full_up, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_full_up_lines)
                            old_line = array.get(monthly_full_up_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_full_up_lines, i, full_up_line)
                        else
                            array.push(monthly_full_up_lines, full_up_line)

                        // Draw full AMR+ label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            full_up_label := create_or_update_label(full_up_label, full_up, str.format("{0}M AMR+", amr_period), amr_color, label_pos)

                        // Draw full AMR- line
                        full_down_line = create_horizontal_line(full_down, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_full_down_lines)
                            old_line = array.get(monthly_full_down_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_full_down_lines, i, full_down_line)
                        else
                            array.push(monthly_full_down_lines, full_down_line)

                        // Draw full AMR- label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            full_down_label := create_or_update_label(full_down_label, full_down, str.format("{0}M AMR-", amr_period), amr_color, label_pos)

                    // Draw 1/3 AMR levels
                    if show_one_third_amr
                        // Draw 1/3 AMR+ line
                        one_third_up_line = create_horizontal_line(one_third_up, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_one_third_up_lines)
                            old_line = array.get(monthly_one_third_up_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_one_third_up_lines, i, one_third_up_line)
                        else
                            array.push(monthly_one_third_up_lines, one_third_up_line)

                        // Draw 1/3 AMR+ label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            one_third_up_label := create_or_update_label(one_third_up_label, one_third_up, str.format("{0}M 1/3+", amr_period), amr_color, label_pos)

                        // Draw 1/3 AMR- line
                        one_third_down_line = create_horizontal_line(one_third_down, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_one_third_down_lines)
                            old_line = array.get(monthly_one_third_down_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_one_third_down_lines, i, one_third_down_line)
                        else
                            array.push(monthly_one_third_down_lines, one_third_down_line)

                        // Draw 1/3 AMR- label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            one_third_down_label := create_or_update_label(one_third_down_label, one_third_down, str.format("{0}M 1/3-", amr_period), amr_color, label_pos)

                    // Draw 2/3 AMR levels
                    if show_two_third_amr
                        // Draw 2/3 AMR+ line
                        two_third_up_line = create_horizontal_line(two_third_up, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_two_third_up_lines)
                            old_line = array.get(monthly_two_third_up_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_two_third_up_lines, i, two_third_up_line)
                        else
                            array.push(monthly_two_third_up_lines, two_third_up_line)

                        // Draw 2/3 AMR+ label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            two_third_up_label := create_or_update_label(two_third_up_label, two_third_up, str.format("{0}M 2/3+", amr_period), amr_color, label_pos)

                        // Draw 2/3 AMR- line
                        two_third_down_line = create_horizontal_line(two_third_down, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_two_third_down_lines)
                            old_line = array.get(monthly_two_third_down_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_two_third_down_lines, i, two_third_down_line)
                        else
                            array.push(monthly_two_third_down_lines, two_third_down_line)

                        // Draw 2/3 AMR- label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            two_third_down_label := create_or_update_label(two_third_down_label, two_third_down, str.format("{0}M 2/3-", amr_period), amr_color, label_pos)

                    // Draw 1/2 AMR levels
                    if show_half_amr
                        // Draw 1/2 AMR+ line
                        half_up_line = create_horizontal_line(half_up, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_half_up_lines)
                            old_line = array.get(monthly_half_up_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_half_up_lines, i, half_up_line)
                        else
                            array.push(monthly_half_up_lines, half_up_line)

                        // Draw 1/2 AMR+ label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            half_up_label := create_or_update_label(half_up_label, half_up, str.format("{0}M 1/2+", amr_period), amr_color, label_pos)

                        // Draw 1/2 AMR- line
                        half_down_line = create_horizontal_line(half_down, amr_color, line_style, start_time, end_time)

                        // Store or update the line
                        if i < array.size(monthly_half_down_lines)
                            old_line = array.get(monthly_half_down_lines, i)
                            if not na(old_line)
                                line.delete(old_line)
                            array.set(monthly_half_down_lines, i, half_down_line)
                        else
                            array.push(monthly_half_down_lines, half_down_line)

                        // Draw 1/2 AMR- label (only for current month)
                        if i == 0
                            // Calculate label position at the end of the line
                            label_pos = time_close > end_time ? bar_index : time_to_bar_index(end_time)
                            half_down_label := create_or_update_label(half_down_label, half_down, str.format("{0}M 1/2-", amr_period), amr_color, label_pos)
