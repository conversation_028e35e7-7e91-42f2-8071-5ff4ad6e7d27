// This Pine Script® code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © rvdvjn
//@version=5
strategy("VWAP + Divergence + Oscar Strategy",
     overlay=true,
     max_bars_back=5000,
     max_boxes_count = 500,
     max_lines_count=500,
     max_labels_count=500,
     initial_capital=10000,
     default_qty_type=strategy.percent_of_equity,
     default_qty_value=100,
     commission_type=strategy.commission.percent,
     commission_value=0.1,
     pyramiding=0,
     calc_on_every_tick=true,
     calc_on_order_fills=true,
     slippage=10)


// indicator("ICT Killzones & Pivots [Ravi]", "ICT Killzones & Pivots [TFO]", true, max_labels_count = 500, max_lines_count = 500, max_boxes_count = 500)


// ---------------------------------------- Constant Functions --------------------------------------------------
get_line_type(_style) =>
    result = switch _style
        'Solid' => line.style_solid
        'Dotted' => line.style_dotted
        'Dashed' => line.style_dashed
    result

get_size(x) =>
    result = switch x
        'Auto' => size.auto
        'Tiny' => size.tiny
        'Small' => size.small
        'Normal' => size.normal
        'Large' => size.large
        'Huge' => size.huge

get_table_pos(pos) =>
    result = switch pos
        "Bottom Center" => position.bottom_center
        "Bottom Left" => position.bottom_left
        "Bottom Right" => position.bottom_right
        "Middle Center" => position.middle_center
        "Middle Left" => position.middle_left
        "Middle Right" => position.middle_right
        "Top Center" => position.top_center
        "Top Left" => position.top_left
        "Top Right" => position.top_right
// ---------------------------------------- Constant Functions --------------------------------------------------


// ---------------------------------------- Inputs --------------------------------------------------
var g_SETTINGS      = "Settings"
max_days            = input.int(3, "Session Drawing Limit", 1, tooltip = "Only this many drawings will be kept on the chart, for each selected drawing type (killzone boxes, pivot lines, open lines, etc.)", group = g_SETTINGS)
tf_limit            = input.timeframe("30", "Timeframe Limit", tooltip = "Drawings will not appear on timeframes greater than or equal to this", group = g_SETTINGS)
gmt_tz              = input.string('America/New_York', "Timezone", options = ['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], tooltip = "Note GMT is not adjusted to reflect Daylight Saving Time changes", group = g_SETTINGS)
lbl_size            = get_size(input.string('Normal', "Label Size", options = ['Auto', 'Tiny', 'Small', 'Normal', 'Large', 'Huge'], tooltip = "The size of all labels", group = g_SETTINGS))
txt_color           = input.color(color.black, "Text Color", tooltip = "The color of all label and table text", group = g_SETTINGS)
use_cutoff          = input.bool(false, "Drawing Cutoff Time", inline = "CO", tooltip = "When enabled, all pivots and open price lines will stop extending at this time", group = g_SETTINGS)
cutoff              = input.session("1800-1801", "", inline = "CO", group = g_SETTINGS)


var g_KZ            = "Killzones"
show_kz             = input.bool(true, "Show Killzone Boxes", inline = "KZ", group = g_KZ)
show_kz_text        = input.bool(true, "Display Text", inline = "KZ", group = g_KZ)

use_asia            = input.bool(true, "", inline = "ASIA", group = g_KZ)
as_txt              = input.string("Asia", "", inline = "ASIA", group = g_KZ)
asia                = input.session("2000-0000", "", inline = "ASIA", group = g_KZ)
as_color            = input.color(color.blue, "", inline = "ASIA", group = g_KZ)

use_london          = input.bool(true, "", inline = "LONDON", group = g_KZ)
lo_txt              = input.string("London", "", inline = "LONDON", group = g_KZ)
london              = input.session("0200-0500", "", inline = "LONDON", group = g_KZ)
lo_color            = input.color(color.red, "", inline = "LONDON", group = g_KZ)

use_nyam            = input.bool(true, "", inline = "NYAM", group = g_KZ)
na_txt              = input.string("NY AM", "", inline = "NYAM", group = g_KZ)
nyam                = input.session("0930-1100", "", inline = "NYAM", group = g_KZ)
na_color            = input.color(#089981, "", inline = "NYAM", group = g_KZ)

use_nylu            = input.bool(true, "", inline = "NYLU", group = g_KZ)
nl_txt              = input.string("NY Lunch", "", inline = "NYLU", group = g_KZ)
nylu                = input.session("1200-1300", "", inline = "NYLU", group = g_KZ)
nl_color            = input.color(color.yellow, "", inline = "NYLU", group = g_KZ)

use_nypm            = input.bool(true, "", inline = "NYPM", group = g_KZ)
np_txt              = input.string("NY PM", "", inline = "NYPM", group = g_KZ)
nypm                = input.session("1330-1600", "", inline = "NYPM", group = g_KZ)
np_color            = input.color(color.purple, "", inline = "NYPM", group = g_KZ)

box_transparency    = input.int(70, "Box Transparency", 0, 100, group = g_KZ)
text_transparency   = input.int(50, "Text Transparency", 0, 100, group = g_KZ)


var g_LABELS        = "Killzone Pivots"
show_pivots         = input.bool(true, "Show Pivots", inline = "PV", group = g_LABELS)
use_alerts          = input.bool(true, "Alert Broken Pivots", inline = "PV", tooltip = "The desired killzones must be enabled at the time that an alert is created, along with the show pivots option, in order for alerts to work", group = g_LABELS)
show_midpoints      = input.bool(false, "Show Pivot Midpoints", inline = "mp", group = g_LABELS)
stop_midpoints      = input.bool(true, "Stop Once Mitigated", inline = "mp", group = g_LABELS)
show_labels         = input.bool(true, "Show Pivot Labels", inline = "LB", tooltip = "Show labels denoting each killzone's high and low. Optionally choose to show the price of each level. Right side will show labels on the right-hand side of the chart until they are reached", group = g_LABELS)
label_price         = input.bool(false, "Display Price", inline = "LB", group = g_LABELS)
label_right         = input.bool(false, "Right Side", inline = "LB", group = g_LABELS)
ext_pivots          = input.string("Until Mitigated", "Extend Pivots...", options = ['Until Mitigated', 'Past Mitigation'], group = g_LABELS)
ext_which           = input.string("Most Recent", "...From Which Sessions", options = ['Most Recent', 'All'], group = g_LABELS)

ash_str             = input.string("AS.H", "Killzone 1 Labels", inline = "L_AS", group = g_LABELS)
asl_str             = input.string("AS.L", "", inline = "L_AS", group = g_LABELS)

loh_str             = input.string("LO.H", "Killzone 2 Labels", inline = "L_LO", group = g_LABELS)
lol_str             = input.string("LO.L", "", inline = "L_LO", group = g_LABELS)

nah_str             = input.string("NYAM.H", "Killzone 3 Labels", inline = "L_NA", group = g_LABELS)
nal_str             = input.string("NYAM.L", "", inline = "L_NA", group = g_LABELS)

nlh_str             = input.string("NYL.H", "Killzone 4 Labels", inline = "L_NL", group = g_LABELS)
nll_str             = input.string("NYL.L", "", inline = "L_NL", group = g_LABELS)

nph_str             = input.string("NYPM.H", "Killzone 5 Labels", inline = "L_NP", group = g_LABELS)
npl_str             = input.string("NYPM.L", "", inline = "L_NP", group = g_LABELS)

kzp_style           = get_line_type(input.string(defval = 'Solid', title = "Pivot Style", options = ['Solid', 'Dotted', 'Dashed'], inline = "KZP", group = g_LABELS))
kzp_width           = input.int(1, "", inline = "KZP", group = g_LABELS)
kzm_style           = get_line_type(input.string(defval = 'Dotted', title = "Midpoint Style", options = ['Solid', 'Dotted', 'Dashed'], inline = "KZM", group = g_LABELS))
kzm_width           = input.int(1, "", inline = "KZM", group = g_LABELS)


var g_RNG           = "Killzone Range"
show_range          = input.bool(false, "Show Killzone Range", tooltip = "Show the most recent ranges of each selected killzone, from high to low", group = g_RNG)
show_range_avg      = input.bool(true, "Show Average", tooltip = "Show the average range of each selected killzone", group = g_RNG)
range_avg           = input.int(5, "Average Length", 0, tooltip = "This many previous sessions will be used to calculate the average. If there isn't enough data on the current chart, it will use as many sessions as possible", group = g_RNG)
range_pos           = get_table_pos(input.string('Top Right', "Table Position", options = ['Bottom Center', 'Bottom Left', 'Bottom Right', 'Middle Center', 'Middle Left', 'Middle Right', 'Top Center', 'Top Left', 'Top Right'], group = g_RNG))
range_size          = get_size(input.string('Normal', "Table Size", options = ['Auto', 'Tiny', 'Small', 'Normal', 'Large', 'Huge'], group = g_RNG))


var g_DWM           = "Day - Week - Month"
sep_unlimited       = input.bool(false, "Unlimited", tooltip = "Unlimited will show as many of the selected lines as possible. Otherwise, the session drawing limit will be used", group = g_DWM)
alert_HL            = input.bool(false, "Alert High/Low Break", tooltip = "Alert when any selected highs and lows are traded through. The desired timeframe's high/low option must be enabled at the time that an alert is created", group = g_DWM)

show_d_open         = input.bool(false, "D Open", inline = "DO", group = g_DWM)
dhl                 = input.bool(false, "High/Low", inline = "DO", tooltip = "", group = g_DWM)
ds                  = input.bool(false, "Separators", inline = "DO", tooltip = "Mark where a new day begins", group = g_DWM)
d_color             = input.color(color.blue, "", inline = "DO", group = g_DWM)

show_w_open         = input.bool(false, "W Open", inline = "WO", group = g_DWM)
whl                 = input.bool(false, "High/Low", inline = "WO", tooltip = "", group = g_DWM)
ws                  = input.bool(false, "Separators", inline = "WO", tooltip = "Mark where a new week begins", group = g_DWM)
w_color             = input.color(#089981, "", inline = "WO", group = g_DWM)

show_m_open         = input.bool(false, "M Open", inline = "MO", group = g_DWM)
mhl                 = input.bool(false, "High/Low", inline = "MO", tooltip = "", group = g_DWM)
ms                  = input.bool(false, "Separators", inline = "MO", tooltip = "Mark where a new month begins", group = g_DWM)
m_color             = input.color(color.red, "", inline = "MO", group = g_DWM)

htf_style           = get_line_type(input.string(defval = 'Solid', title = "Style", options = ['Solid', 'Dotted', 'Dashed'], inline = "D0", group = g_DWM))
htf_width           = input.int(1, "", inline = "D0", group = g_DWM)

dow_labels          = input.bool(true, "Day of Week Labels", inline = "DOW", group = g_DWM)
dow_yloc            = input.string('Bottom', "", options = ['Top', 'Bottom'], inline = "DOW", group = g_DWM)
dow_xloc            = input.string('Midnight', "", options = ['Midnight', 'Midday'], inline = "DOW", group = g_DWM)
dow_hide_wknd       = input.bool(true, "Hide Weekend Labels", group = g_DWM)


var g_OPEN          = "Opening Prices"
open_unlimited      = input.bool(false, "Unlimited", tooltip = "Unlimited will show as many of the selected lines as possible. Otherwise, the session drawing limit will be used", group = g_OPEN)

use_h1              = input.bool(false, "", inline = "H1", group = g_OPEN)
h1_text             = input.string("True Day Open", "", inline = "H1", group = g_OPEN)
h1                  = input.session("0000-0001", "", inline = "H1", group = g_OPEN)
h1_color            = input.color(color.black, "", inline = "H1", group = g_OPEN)

use_h2              = input.bool(false, "", inline = "H2", group = g_OPEN)
h2_text             = input.string("06:00", "", inline = "H2", group = g_OPEN)
h2                  = input.session("0600-0601", "", inline = "H2", group = g_OPEN)
h2_color            = input.color(color.black, "", inline = "H2", group = g_OPEN)

use_h3              = input.bool(false, "", inline = "H3", group = g_OPEN)
h3_text             = input.string("10:00", "", inline = "H3", group = g_OPEN)
h3                  = input.session("1000-1001", "", inline = "H3", group = g_OPEN)
h3_color            = input.color(color.black, "", inline = "H3", group = g_OPEN)

use_h4              = input.bool(false, "", inline = "H4", group = g_OPEN)
h4_text             = input.string("14:00", "", inline = "H4", group = g_OPEN)
h4                  = input.session("1400-1401", "", inline = "H4", group = g_OPEN)
h4_color            = input.color(color.black, "", inline = "H4", group = g_OPEN)

use_h5              = input.bool(false, "", inline = "H5", group = g_OPEN)
h5_text             = input.string("00:00", "", inline = "H5", group = g_OPEN)
h5                  = input.session("0000-0001", "", inline = "H5", group = g_OPEN)
h5_color            = input.color(color.black, "", inline = "H5", group = g_OPEN)

use_h6              = input.bool(false, "", inline = "H6", group = g_OPEN)
h6_text             = input.string("00:00", "", inline = "H6", group = g_OPEN)
h6                  = input.session("0000-0001", "", inline = "H6", group = g_OPEN)
h6_color            = input.color(color.black, "", inline = "H6", group = g_OPEN)

use_h7              = input.bool(false, "", inline = "H7", group = g_OPEN)
h7_text             = input.string("00:00", "", inline = "H7", group = g_OPEN)
h7                  = input.session("0000-0001", "", inline = "H7", group = g_OPEN)
h7_color            = input.color(color.black, "", inline = "H7", group = g_OPEN)

use_h8              = input.bool(false, "", inline = "H8", group = g_OPEN)
h8_text             = input.string("00:00", "", inline = "H8", group = g_OPEN)
h8                  = input.session("0000-0001", "", inline = "H8", group = g_OPEN)
h8_color            = input.color(color.black, "", inline = "H8", group = g_OPEN)

hz_style            = get_line_type(input.string(defval = 'Dotted', title = "Style", options = ['Solid', 'Dotted', 'Dashed'], inline = "H0", group = g_OPEN))
hz_width            = input.int(1, "", inline = "H0", group = g_OPEN)


var g_VERTICAL      = "Timestamps"
v_unlimited         = input.bool(false, "Unlimited", tooltip = "Unlimited will show as many of the selected lines as possible. Otherwise, the session drawing limit will be used", group = g_VERTICAL)

use_v1              = input.bool(false, "", inline = "V1", group = g_VERTICAL)
v1                  = input.session("0000-0001", "", inline = "V1", group = g_VERTICAL)
v1_color            = input.color(color.black, "", inline = "V1", group = g_VERTICAL)

use_v2              = input.bool(false, "", inline = "V2", group = g_VERTICAL)
v2                  = input.session("0800-0801", "", inline = "V2", group = g_VERTICAL)
v2_color            = input.color(color.black, "", inline = "V2", group = g_VERTICAL)

use_v3              = input.bool(false, "", inline = "V3", group = g_VERTICAL)
v3                  = input.session("1000-1001", "", inline = "V3", group = g_VERTICAL)
v3_color            = input.color(color.black, "", inline = "V3", group = g_VERTICAL)

use_v4              = input.bool(false, "", inline = "V4", group = g_VERTICAL)
v4                  = input.session("1200-1201", "", inline = "V4", group = g_VERTICAL)
v4_color            = input.color(color.black, "", inline = "V4", group = g_VERTICAL)

vl_style            = get_line_type(input.string(defval = 'Dotted', title = "Style", options = ['Solid', 'Dotted', 'Dashed'], inline = "V0", group = g_VERTICAL))
vl_width            = input.int(1, "", inline = "V0", group = g_VERTICAL)

// ---------------------------------------- Strategy Settings --------------------------------------------------
var g_STRATEGY      = "Strategy Settings"
vwap_deviation      = input.float(0.1, "VWAP Deviation (%)", minval=0.01, step=0.01, group = g_STRATEGY)
min_divergences     = input.int(1, "Minimum Number of Divergences", minval=1, maxval=10, group = g_STRATEGY)
check_multiple_indicators = input.bool(true, "Check Multiple Indicators", group = g_STRATEGY)

// Risk Management
var g_RISK          = "Risk Management"
tick_size           = input.float(0.1, "Tick Size", minval=0.00001, step=0.01, group = g_RISK)
stop_loss_ticks     = input.int(250, "Stop Loss (ticks)", minval=10, group = g_RISK)
take_profit_ticks   = input.int(750, "Take Profit (ticks)", minval=10, group = g_RISK)
max_trades_per_day  = input.int(2, "Max Trades Per Day", minval=1, maxval=10, group = g_RISK)
max_daily_loss      = input.float(-3.0, "Max Daily Loss %", minval=-10.0, maxval=0.0, group = g_RISK)
// ---------------------------------------- Inputs --------------------------------------------------


// ---------------------------------------- Variables & Constants --------------------------------------------------
type kz
    string _title

    box[] _box

    line[] _hi_line
    line[] _md_line
    line[] _lo_line

    label[] _hi_label
    label[] _lo_label

    bool[] _hi_valid
    bool[] _md_valid
    bool[] _lo_valid

    float[] _range_store
    float _range_current

type hz
    line[] LN
    label[] LB
    bool[] CO

type dwm_hl
    line[] hi_line
    line[] lo_line
    label[] hi_label
    label[] lo_label
    bool hit_high = false
    bool hit_low = false

type dwm_info
    string tf
    float o = na
    float h = na
    float l = na
    float ph = na
    float pl = na

var as_kz = kz.new(as_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool(), array.new_float())
var lo_kz = kz.new(lo_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool(), array.new_float())
var na_kz = kz.new(na_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool(), array.new_float())
var nl_kz = kz.new(nl_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool(), array.new_float())
var np_kz = kz.new(np_txt, array.new_box(), array.new_line(), array.new_line(), array.new_line(), array.new_label(), array.new_label(), array.new_bool(), array.new_bool(), array.new_bool(), array.new_float())

var hz_1 = hz.new(array.new_line(), array.new_label(), array.new_bool())
var hz_2 = hz.new(array.new_line(), array.new_label(), array.new_bool())
var hz_3 = hz.new(array.new_line(), array.new_label(), array.new_bool())
var hz_4 = hz.new(array.new_line(), array.new_label(), array.new_bool())
var hz_5 = hz.new(array.new_line(), array.new_label(), array.new_bool())
var hz_6 = hz.new(array.new_line(), array.new_label(), array.new_bool())
var hz_7 = hz.new(array.new_line(), array.new_label(), array.new_bool())
var hz_8 = hz.new(array.new_line(), array.new_label(), array.new_bool())

var d_hl = dwm_hl.new(array.new_line(), array.new_line(), array.new_label(), array.new_label())
var w_hl = dwm_hl.new(array.new_line(), array.new_line(), array.new_label(), array.new_label())
var m_hl = dwm_hl.new(array.new_line(), array.new_line(), array.new_label(), array.new_label())

var d_info = dwm_info.new("D")
var w_info = dwm_info.new("W")
var m_info = dwm_info.new("M")

// Strategy-specific variables
var strategy_had_bullish_div = false
var strategy_had_bearish_div = false
var strategy_trades_today = 0
var strategy_last_trade_day = 0
var strategy_daily_profit = 0.0
var strategy_last_bullish_div_bar = 0
var strategy_last_bearish_div_bar = 0
var strategy_last_volume_spike_bar = 0

// VWAP variables
var float strategy_sessionVwap = na
var float strategy_fourHVwap = na
var float strategy_weeklyVwap = na

t_as = not na(time("", asia, gmt_tz))
t_lo = not na(time("", london, gmt_tz))
t_na = not na(time("", nyam, gmt_tz))
t_nl = not na(time("", nylu, gmt_tz))
t_np = not na(time("", nypm, gmt_tz))
t_co = not na(time("", cutoff, gmt_tz))

t_h1 = not na(time("", h1, gmt_tz))
t_h2 = not na(time("", h2, gmt_tz))
t_h3 = not na(time("", h3, gmt_tz))
t_h4 = not na(time("", h4, gmt_tz))
t_h5 = not na(time("", h5, gmt_tz))
t_h6 = not na(time("", h6, gmt_tz))
t_h7 = not na(time("", h7, gmt_tz))
t_h8 = not na(time("", h8, gmt_tz))

t_v1 = not na(time("", v1, gmt_tz))
t_v2 = not na(time("", v2, gmt_tz))
t_v3 = not na(time("", v3, gmt_tz))
t_v4 = not na(time("", v4, gmt_tz))

var d_sep_line = array.new_line()
var w_sep_line = array.new_line()
var m_sep_line = array.new_line()

var d_line = array.new_line()
var w_line = array.new_line()
var m_line = array.new_line()

var d_label = array.new_label()
var w_label = array.new_label()
var m_label = array.new_label()

var v1_line = array.new_line()
var v2_line = array.new_line()
var v3_line = array.new_line()
var v4_line = array.new_line()

var transparent = #ffffff00
var ext_current = ext_which == 'Most Recent'
var ext_past = ext_pivots == 'Past Mitigation'

update_dwm_info(dwm_info n) =>
    if timeframe.change(n.tf)
        n.ph := n.h
        n.pl := n.l
        n.o := open
        n.h := high
        n.l := low
    else
        n.h := math.max(high, n.h)
        n.l := math.min(low,  n.l)

if dhl or show_d_open
    update_dwm_info(d_info)
if whl or show_w_open
    update_dwm_info(w_info)
if mhl or show_m_open
    update_dwm_info(m_info)
// ---------------------------------------- Variables & Constants --------------------------------------------------


// ---------------------------------------- Functions --------------------------------------------------
get_box_color(color c) =>
    result = color.new(c, box_transparency)

get_text_color(color c) =>
    result = color.new(c, text_transparency)
// ---------------------------------------- Functions --------------------------------------------------


// ---------------------------------------- Core Logic --------------------------------------------------
dwm_sep(string tf, bool use, line[] arr, color col) =>
    if use
        if timeframe.change(tf)
            arr.unshift(line.new(bar_index, high*1.0001, bar_index, low, style = htf_style, width = htf_width, extend = extend.both, color = col))
            if not sep_unlimited and arr.size() > max_days
                arr.pop().delete()


dwm_open(string tf, bool use, line[] lns, label[] lbls, dwm_info n, color col) =>
    if use
        if lns.size() > 0
            lns.get(0).set_x2(time)
            lbls.get(0).set_x(time)
        if timeframe.change(tf)
            lns.unshift(line.new(time, n.o, time, n.o, xloc = xloc.bar_time, style = htf_style, width = htf_width, color = col))
            lbls.unshift(label.new(time, n.o, tf + " OPEN", xloc = xloc.bar_time, style = label.style_label_left, color = transparent, textcolor = txt_color, size = lbl_size))
            if not sep_unlimited and lns.size() > max_days
                lns.pop().delete()
                lbls.pop().delete()


dwm_hl(string tf, bool use, dwm_hl hl, dwm_info n, color col) =>
    if use
        if hl.hi_line.size() > 0
            hl.hi_line.get(0).set_x2(time)
            hl.lo_line.get(0).set_x2(time)
            hl.hi_label.get(0).set_x(time)
            hl.lo_label.get(0).set_x(time)
        if timeframe.change(tf)
            hl.hi_line.unshift(line.new(time, n.ph, time, n.ph, xloc = xloc.bar_time, style = htf_style, width = htf_width, color = col))
            hl.lo_line.unshift(line.new(time, n.pl, time, n.pl, xloc = xloc.bar_time, style = htf_style, width = htf_width, color = col))
            hl.hi_label.unshift(label.new(time, n.ph, "P"+tf+"H", xloc = xloc.bar_time, style = label.style_label_left, color = transparent, textcolor = txt_color, size = lbl_size))
            hl.lo_label.unshift(label.new(time, n.pl, "P"+tf+"L", xloc = xloc.bar_time, style = label.style_label_left, color = transparent, textcolor = txt_color, size = lbl_size))
            hl.hit_high := false
            hl.hit_low := false
            if not sep_unlimited and hl.hi_line.size() > max_days
                hl.hi_line.pop().delete()
                hl.lo_line.pop().delete()
                hl.hi_label.pop().delete()
                hl.lo_label.pop().delete()
        if hl.hi_line.size() > 0 and alert_HL
            if not hl.hit_high and high > hl.hi_line.get(0).get_y1()
                hl.hit_high := true
                alert(str.format("Hit P{0}H", tf))
            if not hl.hit_low and low < hl.lo_line.get(0).get_y1()
                hl.hit_low := true
                alert(str.format("Hit P{0}L", tf))


dwm() =>
    if timeframe.in_seconds("") <= timeframe.in_seconds(tf_limit)
        // DWM - Separators
        dwm_sep("D", ds, d_sep_line, d_color)
        dwm_sep("W", ws, w_sep_line, w_color)
        dwm_sep("M", ms, m_sep_line, m_color)

        // DWM - Open Lines
        dwm_open("D", show_d_open, d_line, d_label, d_info, d_color)
        dwm_open("W", show_w_open, w_line, w_label, w_info, w_color)
        dwm_open("M", show_m_open, m_line, m_label, m_info, m_color)

        // DWM - Highs and Lows
        dwm_hl("D", dhl, d_hl, d_info, d_color)
        dwm_hl("W", whl, w_hl, w_info, w_color)
        dwm_hl("M", mhl, m_hl, m_info, m_color)


vline(bool use, bool t, line[] arr, color col) =>
    if use
        if t and not t[1]
            arr.unshift(line.new(bar_index, high*1.0001, bar_index, low, style = vl_style, width = vl_width, extend = extend.both, color = col))
        if not v_unlimited
            if arr.size() > max_days
                arr.pop().delete()


vlines() =>
    if timeframe.in_seconds("") <= timeframe.in_seconds(tf_limit)
        vline(use_v1, t_v1, v1_line, v1_color)
        vline(use_v2, t_v2, v2_line, v2_color)
        vline(use_v3, t_v3, v3_line, v3_color)
        vline(use_v4, t_v4, v4_line, v4_color)


hz_line(bool use, bool t, hz hz, string txt, color col) =>
    if use
        if t and not t[1]
            hz.LN.unshift(line.new(bar_index, open, bar_index, open, style = hz_style, width = hz_width, color = col))
            hz.LB.unshift(label.new(bar_index, open, txt, style = label.style_label_left, color = transparent, textcolor = txt_color, size = lbl_size))
            array.unshift(hz.CO, false)
            if not open_unlimited and hz.LN.size() > max_days
                hz.LN.pop().delete()
                hz.LB.pop().delete()
                hz.CO.pop()
        if not t and hz.CO.size() > 0
            if not hz.CO.get(0)
                hz.LN.get(0).set_x2(bar_index)
                hz.LB.get(0).set_x(bar_index)
                if (use_cutoff ? t_co : false)
                    hz.CO.set(0, true)


hz_lines() =>
    if timeframe.in_seconds("") <= timeframe.in_seconds(tf_limit)
        hz_line(use_h1, t_h1, hz_1, h1_text, h1_color)
        hz_line(use_h2, t_h2, hz_2, h2_text, h2_color)
        hz_line(use_h3, t_h3, hz_3, h3_text, h3_color)
        hz_line(use_h4, t_h4, hz_4, h4_text, h4_color)
        hz_line(use_h5, t_h5, hz_5, h5_text, h5_color)
        hz_line(use_h6, t_h6, hz_6, h6_text, h6_color)
        hz_line(use_h7, t_h7, hz_7, h7_text, h7_color)
        hz_line(use_h8, t_h8, hz_8, h8_text, h8_color)


del_kz(kz k) =>
    if k._box.size() > max_days
        k._box.pop().delete()
    if k._hi_line.size() > max_days
        k._hi_line.pop().delete()
        k._lo_line.pop().delete()
        k._hi_valid.pop()
        k._lo_valid.pop()
        if show_midpoints
            k._md_line.pop().delete()
            k._md_valid.pop()
    if k._hi_label.size() > max_days
        k._hi_label.pop().delete()
        k._lo_label.pop().delete()

update_price_string(label L, float P) =>
    S = L.get_text()
    pre = str.substring(S, 0, str.pos(S, " "))
    str.trim(pre)
    L.set_text(str.format("{0} ({1})", pre, P))

adjust_in_kz(kz kz, bool t) =>
    if t
        kz._box.get(0).set_right(time)
        kz._box.get(0).set_top(math.max(kz._box.get(0).get_top(), high))
        kz._box.get(0).set_bottom(math.min(kz._box.get(0).get_bottom(), low))

        kz._range_current := kz._box.get(0).get_top() - kz._box.get(0).get_bottom()

        if show_pivots and kz._hi_line.size() > 0
            kz._hi_line.get(0).set_x2(time)
            if high > kz._hi_line.get(0).get_y1()
                kz._hi_line.get(0).set_xy1(time, high)
                kz._hi_line.get(0).set_xy2(time, high)

            kz._lo_line.get(0).set_x2(time)
            if low < kz._lo_line.get(0).get_y1()
                kz._lo_line.get(0).set_xy1(time, low)
                kz._lo_line.get(0).set_xy2(time, low)

            if show_midpoints
                kz._md_line.get(0).set_x2(time)
                kz._md_line.get(0).set_xy1(time, math.avg(kz._hi_line.get(0).get_y2(), kz._lo_line.get(0).get_y2()))
                kz._md_line.get(0).set_xy2(time, math.avg(kz._hi_line.get(0).get_y2(), kz._lo_line.get(0).get_y2()))

        if show_labels and kz._hi_label.size() > 0
            if label_right
                kz._hi_label.get(0).set_x(time)
                kz._lo_label.get(0).set_x(time)
            if high > kz._hi_label.get(0).get_y()
                kz._hi_label.get(0).set_xy(time, high)
                if label_price
                    update_price_string(kz._hi_label.get(0), high)
            if low < kz._lo_label.get(0).get_y()
                kz._lo_label.get(0).set_xy(time, low)
                if label_price
                    update_price_string(kz._lo_label.get(0), low)


adjust_out_kz(kz kz, bool t) =>
    if not t and kz._box.size() > 0
        if t[1]
            array.unshift(kz._range_store, kz._range_current)
            if kz._range_store.size() > range_avg
                kz._range_store.pop()

    if kz._box.size() > 0 and show_pivots
        for i = 0 to kz._box.size() - 1
            if not ext_current or (ext_current and i == 0)
                if ext_past ? true : (kz._hi_valid.get(i) == true)
                    kz._hi_line.get(i).set_x2(time)
                    if show_labels and label_right
                        kz._hi_label.get(i).set_x(time)
                if high > kz._hi_line.get(i).get_y1() and kz._hi_valid.get(i) == true
                    if use_alerts and i == 0
                        alert("Broke "+kz._title+" High", alert.freq_once_per_bar)
                    kz._hi_valid.set(i, false)
                    if show_labels and label_right
                        kz._hi_label.get(0).set_style(label.style_label_down)
                else if (use_cutoff ? t_co : false)
                    kz._hi_valid.set(i, false)

                if ext_past ? true : (kz._lo_valid.get(i) == true)
                    kz._lo_line.get(i).set_x2(time)
                    if show_labels and label_right
                        kz._lo_label.get(i).set_x(time)
                if low < kz._lo_line.get(i).get_y1() and kz._lo_valid.get(i) == true
                    if use_alerts and i == 0
                        alert("Broke "+kz._title+" Low", alert.freq_once_per_bar)
                    kz._lo_valid.set(i, false)
                    if show_labels and label_right
                        kz._lo_label.get(0).set_style(label.style_label_up)
                else if (use_cutoff ? t_co : false)
                    kz._lo_valid.set(i, false)

                if show_midpoints and not t
                    if stop_midpoints ? (kz._md_valid.get(i) == true) : true
                        kz._md_line.get(i).set_x2(time)
                        if kz._md_valid.get(i) == true and low <= kz._md_line.get(i).get_y1() and high >= kz._md_line.get(i).get_y1()
                            kz._md_valid.set(i, false)

            else
                break


manage_kz(kz kz, bool use, bool t, color c, string box_txt, string hi_txt, string lo_txt) =>
    if timeframe.in_seconds("") <= timeframe.in_seconds(tf_limit) and use
        if t and not t[1]
            _c = get_box_color(c)
            _t = get_text_color(c)
            kz._box.unshift(box.new(time, high, time, low, xloc = xloc.bar_time, border_color = show_kz ? _c : na, bgcolor = show_kz ? _c : na, text = (show_kz and show_kz_text) ? box_txt : na, text_color = _t))

            if show_pivots
                kz._hi_line.unshift(line.new(time, high, time, high, xloc = xloc.bar_time, style = kzp_style, color = c, width = kzp_width))
                kz._lo_line.unshift(line.new(time, low, time, low, xloc = xloc.bar_time, style = kzp_style, color = c, width = kzp_width))
                if show_midpoints
                    kz._md_line.unshift(line.new(time, math.avg(high, low), time, math.avg(high, low), xloc = xloc.bar_time, style = kzm_style, color = c, width = kzm_width))
                    array.unshift(kz._md_valid, true)

                array.unshift(kz._hi_valid, true)
                array.unshift(kz._lo_valid, true)

                if show_labels
                    _hi_txt = label_price ? str.format("{0} ({1})", hi_txt, high) : hi_txt
                    _lo_txt = label_price ? str.format("{0} ({1})", lo_txt, low)  : lo_txt
                    if label_right
                        kz._hi_label.unshift(label.new(time, high, _hi_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_left, size = lbl_size))
                        kz._lo_label.unshift(label.new(time, low,  _lo_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_left, size = lbl_size))
                    else
                        kz._hi_label.unshift(label.new(time, high, _hi_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_down, size = lbl_size))
                        kz._lo_label.unshift(label.new(time, low,  _lo_txt, xloc = xloc.bar_time, color = transparent, textcolor = txt_color, style = label.style_label_up, size = lbl_size))

            del_kz(kz)
        adjust_in_kz(kz, t)
        adjust_out_kz(kz, t)


manage_kz(as_kz, use_asia, t_as, as_color, as_txt, ash_str, asl_str)
manage_kz(lo_kz, use_london, t_lo, lo_color, lo_txt, loh_str, lol_str)
manage_kz(na_kz, use_nyam, t_na, na_color, na_txt, nah_str, nal_str)
manage_kz(nl_kz, use_nylu, t_nl, nl_color, nl_txt, nlh_str, nll_str)
manage_kz(np_kz, use_nypm, t_np, np_color, np_txt, nph_str, npl_str)

dwm()
vlines()
hz_lines()

new_dow_time = dow_xloc == 'Midday' ? time - timeframe.in_seconds("D") / 2 * 1000 : time
new_day = dayofweek(new_dow_time, gmt_tz) != dayofweek(new_dow_time, gmt_tz)[1]

var dow_top = dow_yloc == 'Top'

var saturday = "SATURDAY"
var sunday = "SUNDAY"
var monday = "MONDAY"
var tuesday = "TUESDAY"
var wednesday = "WEDNESDAY"
var thursday = "THURSDAY"
var friday = "FRIDAY"

plotchar(dow_labels and timeframe.isintraday and dayofweek(new_dow_time, gmt_tz) == 1 and new_day and not dow_hide_wknd, location = dow_top ? location.top : location.bottom, char = "", textcolor = txt_color, text = sunday)
plotchar(dow_labels and timeframe.isintraday and dayofweek(new_dow_time, gmt_tz) == 2 and new_day, location = dow_top ? location.top : location.bottom, char = "", textcolor = txt_color, text = monday)
plotchar(dow_labels and timeframe.isintraday and dayofweek(new_dow_time, gmt_tz) == 3 and new_day, location = dow_top ? location.top : location.bottom, char = "", textcolor = txt_color, text = tuesday)
plotchar(dow_labels and timeframe.isintraday and dayofweek(new_dow_time, gmt_tz) == 4 and new_day, location = dow_top ? location.top : location.bottom, char = "", textcolor = txt_color, text = wednesday)
plotchar(dow_labels and timeframe.isintraday and dayofweek(new_dow_time, gmt_tz) == 5 and new_day, location = dow_top ? location.top : location.bottom, char = "", textcolor = txt_color, text = thursday)
plotchar(dow_labels and timeframe.isintraday and dayofweek(new_dow_time, gmt_tz) == 6 and new_day, location = dow_top ? location.top : location.bottom, char = "", textcolor = txt_color, text = friday)
plotchar(dow_labels and timeframe.isintraday and dayofweek(new_dow_time, gmt_tz) == 7 and new_day and not dow_hide_wknd, location = dow_top ? location.top : location.bottom, char = "", textcolor = txt_color, text = saturday)

get_min_days_stored() =>
    store = array.new_int()
    if as_kz._range_store.size() > 0
        store.push(as_kz._range_store.size())
    if lo_kz._range_store.size() > 0
        store.push(lo_kz._range_store.size())
    if na_kz._range_store.size() > 0
        store.push(na_kz._range_store.size())
    if nl_kz._range_store.size() > 0
        store.push(nl_kz._range_store.size())
    if np_kz._range_store.size() > 0
        store.push(np_kz._range_store.size())
    result = store.min()

set_table(table tbl, kz kz, int row, string txt, bool use, bool t, color col) =>
    if use
        table.cell(tbl, 0, row, txt, text_size = range_size, bgcolor = get_box_color(col), text_color = txt_color)
        table.cell(tbl, 1, row, str.tostring(kz._range_current), text_size = range_size, bgcolor = t ? get_box_color(col) : na, text_color = txt_color)
        if show_range_avg
            table.cell(tbl, 2, row, str.tostring(kz._range_store.avg()), text_size = range_size, text_color = txt_color)

if show_range and barstate.islast
    var tbl = table.new(range_pos, 10, 10, chart.bg_color, chart.fg_color, 2, chart.fg_color, 1)

    table.cell(tbl, 0, 0, "Killzone", text_size = range_size, text_color = txt_color)
    table.cell(tbl, 1, 0, "Range", text_size = range_size, text_color = txt_color)
    if show_range_avg
        table.cell(tbl, 2, 0, "Avg ("+str.tostring(get_min_days_stored())+")", text_size = range_size, text_color = txt_color)

    set_table(tbl, as_kz, 1, as_txt, use_asia, t_as, as_color)
    set_table(tbl, lo_kz, 2, lo_txt, use_london, t_lo, lo_color)
    set_table(tbl, na_kz, 3, na_txt, use_nyam, t_na, na_color)
    set_table(tbl, nl_kz, 4, nl_txt, use_nylu, t_nl, nl_color)
    set_table(tbl, np_kz, 5, np_txt, use_nypm, t_np, np_color)
// ---------------------------------------- Core Logic --------------------------------------------------








//@version=5
// indicator("Divergence for Many Indicators v5", overlay = true, max_bars_back = 1000, max_lines_count = 400, max_labels_count = 400)
prd = input.int(defval = 5, title = "Pivot Period", minval = 1, maxval = 50)
source = input.string(defval = "Close", title = "Source for Pivot Points", options = ["Close", "High/Low"])
searchdiv = input.string(defval = "Regular", title = "Divergence Type", options = ["Regular", "Hidden", "Regular/Hidden"])
showindis = input.string(defval = "Don't Show", title = "Show Indicator Names", options = ["Full", "First Letter", "Don't Show"])
showlimit = input.int(1, title="Minimum Number of Divergence", minval = 1, maxval = 11)
maxpp = input.int(defval = 10, title = "Maximum Pivot Points to Check", minval = 1, maxval = 20)
maxbars = input.int(defval = 100, title = "Maximum Bars to Check", minval = 30, maxval = 200)
shownum = input.bool(defval = true, title = "Show Divergence Number")
showlast = input.bool(defval = false, title = "Show Only Last Divergence")
dontconfirm = input.bool(defval = false, title = "Don't Wait for Confirmation")
showlines = input.bool(defval = false, title = "Show Divergence Lines")
showpivot = input.bool(defval = false, title = "Show Pivot Points")
calcmacd = input.bool(defval = true, title = "MACD")
calcmacda = input.bool(defval = true, title = "MACD Histogram")
calcrsi = input.bool(defval = true, title = "RSI")
calcstoc = input.bool(defval = true, title = "Stochastic")
calccci = input.bool(defval = true, title = "CCI")
calcmom = input.bool(defval = true, title = "Momentum")
calcobv = input.bool(defval = true, title = "OBV")
calcvwmacd = input.bool(true, title = "VWmacd")
calccmf = input.bool(true, title = "Chaikin Money Flow")
calcmfi = input.bool(true, title = "Money Flow Index")
calcext = input.bool(false, title = "Check External Indicator")
externalindi = input.source(defval = close, title = "External Indicator")
pos_reg_div_col = input.color(defval = color.yellow, title = "Positive Regular Divergence")
neg_reg_div_col = input.color(defval = color.navy, title = "Negative Regular Divergence")
pos_hid_div_col = input.color(defval = color.lime, title = "Positive Hidden Divergence")
neg_hid_div_col = input.color(defval = color.red, title = "Negative Hidden Divergence")
pos_div_text_col = input.color(defval = color.black, title = "Positive Divergence Text Color")
neg_div_text_col = input.color(defval = color.white, title = "Negative Divergence Text Color")
reg_div_l_style_ = input.string(defval = "Solid", title = "Regular Divergence Line Style", options = ["Solid", "Dashed", "Dotted"])
hid_div_l_style_ = input.string(defval = "Dashed", title = "Hidden Divergence Line Style", options = ["Solid", "Dashed", "Dotted"])
reg_div_l_width = input.int(defval = 2, title = "Regular Divergence Line Width", minval = 1, maxval = 5)
hid_div_l_width = input.int(defval = 1, title = "Hidden Divergence Line Width", minval = 1, maxval = 5)
showmas = input.bool(defval = false, title = "Show MAs 50 & 200", inline = "ma12")
cma1col = input.color(defval = color.lime, title = "", inline = "ma12")
cma2col = input.color(defval = color.red, title = "", inline = "ma12")

plot(showmas ? ta.sma(close, 50) : na, color = showmas ? cma1col : na)
plot(showmas ? ta.sma(close, 200) : na, color = showmas ? cma2col: na)

// set line styles
var reg_div_l_style = reg_div_l_style_ == "Solid" ? line.style_solid :
                       reg_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted
var hid_div_l_style = hid_div_l_style_ == "Solid" ? line.style_solid :
                       hid_div_l_style_ == "Dashed" ? line.style_dashed :
                       line.style_dotted


// get indicators
rsi = ta.rsi(close, 14) // RSI
[macd, signal, deltamacd] = ta.macd(close, 12, 26, 9) // MACD
moment = ta.mom(close, 10) // Momentum
cci = ta.cci(close, 10) // CCI
Obv = ta.obv // OBV
stk = ta.sma(ta.stoch(close, high, low, 14), 3) // Stoch
maFast = ta.vwma(close, 12), maSlow = ta.vwma(close, 26), vwmacd = maFast - maSlow // volume weighted macd
Cmfm = ((close-low) - (high-close)) / (high - low), Cmfv = Cmfm * volume, cmf = ta.sma(Cmfv, 21) / ta.sma(volume,21) // Chaikin money flow
Mfi = ta.mfi(close, 14) // Money Flow Index

// keep indicators names and colors in arrays
var indicators_name = array.new_string(11)
var div_colors = array.new_color(4)
if barstate.isfirst
    // names
    array.set(indicators_name, 0, showindis == "Full" ? "MACD" : "M")
    array.set(indicators_name, 1, showindis == "Full" ? "Hist" : "H")
    array.set(indicators_name, 2, showindis == "Full" ? "RSI" : "E")
    array.set(indicators_name, 3, showindis == "Full" ? "Stoch" : "S")
    array.set(indicators_name, 4, showindis == "Full" ? "CCI" : "C")
    array.set(indicators_name, 5, showindis == "Full" ? "MOM" : "M")
    array.set(indicators_name, 6, showindis == "Full" ? "OBV" : "O")
    array.set(indicators_name, 7, showindis == "Full" ? "VWMACD" : "V")
    array.set(indicators_name, 8, showindis == "Full" ? "CMF" : "C")
    array.set(indicators_name, 9, showindis == "Full" ? "MFI" : "M")
    array.set(indicators_name,10, showindis == "Full" ? "Extrn" : "X")
    //colors
    array.set(div_colors, 0, pos_reg_div_col)
    array.set(div_colors, 1, neg_reg_div_col)
    array.set(div_colors, 2, pos_hid_div_col)
    array.set(div_colors, 3, neg_hid_div_col)

// Check if we get new Pivot High Or Pivot Low
float ph = ta.pivothigh((source == "Close" ? close : high), prd, prd)
float pl = ta.pivotlow((source == "Close" ? close : low), prd, prd)
plotshape(ph and showpivot, text = "H",  style = shape.labeldown, color = color.new(color.white, 100), textcolor = color.red, location = location.abovebar, offset = -prd)
plotshape(pl and showpivot, text = "L",  style = shape.labelup, color = color.new(color.white, 100), textcolor = color.lime, location = location.belowbar, offset = -prd)

// keep values and positions of Pivot Highs/Lows in the arrays
var int maxarraysize = 20
var ph_positions = array.new_int(maxarraysize, 0)
var pl_positions = array.new_int(maxarraysize, 0)
var ph_vals = array.new_float(maxarraysize, 0.)
var pl_vals = array.new_float(maxarraysize, 0.)

// add PHs to the array
if ph
    array.unshift(ph_positions, bar_index)
    array.unshift(ph_vals, ph)
    if array.size(ph_positions) > maxarraysize
        array.pop(ph_positions)
        array.pop(ph_vals)

// add PLs to the array
if pl
    array.unshift(pl_positions, bar_index)
    array.unshift(pl_vals, pl)
    if array.size(pl_positions) > maxarraysize
        array.pop(pl_positions)
        array.pop(pl_vals)

// functions to check Regular Divergences and Hidden Divergences

// function to check positive regular or negative hidden divergence
// cond == 1 => positive_regular, cond == 2=> negative_hidden
positive_regular_positive_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : low
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src > src[1] or close > close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(pl_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(pl_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(pl_vals, x))) or
               (cond == 2 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(pl_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] < virtual_line1 or close[y] < virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// function to check negative regular or positive hidden divergence
// cond == 1 => negative_regular, cond == 2=> positive_hidden
negative_regular_negative_hidden_divergence(src, cond)=>
    divlen = 0
    prsc = source == "Close" ? close : high
    // if indicators higher than last value and close price is higher than las close
    if dontconfirm or src < src[1] or close < close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // we search last 15 PPs
        for x = 0 to maxpp - 1
            len = bar_index - array.get(ph_positions, x) + prd
            // if we reach non valued array element or arrived 101. or previous bars then we don't search more
            if array.get(ph_positions, x) == 0 or len > maxbars
                break
            if len > 5 and
               ((cond == 1 and src[startpoint] < src[len] and prsc[startpoint] > (array.get(ph_vals, x))) or
               (cond == 2 and src[startpoint] > src[len] and prsc[startpoint] < (array.get(ph_vals, x))))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1
                    if src[y] > virtual_line1 or close[y] > virtual_line2
                        arrived := false
                        break
                    virtual_line1 := virtual_line1 - slope1
                    virtual_line2 := virtual_line2 - slope2

                if arrived
                    divlen := len
                    break
    divlen

// calculate 4 types of divergence if enabled in the options and return divergences in an array
calculate_divs(cond, indicator)=>
    divs = array.new_int(4, 0)
    array.set(divs, 0, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? positive_regular_positive_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 1, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? negative_regular_negative_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 2, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? positive_regular_positive_hidden_divergence(indicator, 2) : 0)
    array.set(divs, 3, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? negative_regular_negative_hidden_divergence(indicator, 2) : 0)
    divs

// array to keep all divergences
var all_divergences = array.new_int(44) // 11 indicators * 4 divergence = 44 elements
// set related array elements
array_set_divs(div_pointer, index)=>
    for x = 0 to 3
        array.set(all_divergences, index * 4 + x, array.get(div_pointer, x))

// set divergences array
array_set_divs(calculate_divs(calcmacd, macd), 0)
array_set_divs(calculate_divs(calcmacda, deltamacd), 1)
array_set_divs(calculate_divs(calcrsi, rsi), 2)
array_set_divs(calculate_divs(calcstoc, stk), 3)
array_set_divs(calculate_divs(calccci, cci), 4)
array_set_divs(calculate_divs(calcmom, moment), 5)
array_set_divs(calculate_divs(calcobv, Obv), 6)
array_set_divs(calculate_divs(calcvwmacd, vwmacd), 7)
array_set_divs(calculate_divs(calccmf, cmf), 8)
array_set_divs(calculate_divs(calcmfi, Mfi), 9)
array_set_divs(calculate_divs(calcext, externalindi), 10)

// check minimum number of divergence, if less than showlimit then delete all divergence
total_div = 0
for x = 0 to array.size(all_divergences) - 1
    total_div := total_div + math.round(math.sign(array.get(all_divergences, x)))

if total_div < showlimit
    array.fill(all_divergences, 0)

// keep line in an array
var pos_div_lines = array.new_line(0)
var neg_div_lines = array.new_line(0)
var pos_div_labels = array.new_label(0)
var neg_div_labels = array.new_label(0)

// remove old lines and labels if showlast option is enabled
delete_old_pos_div_lines()=>
    if array.size(pos_div_lines) > 0
        for j = 0 to array.size(pos_div_lines) - 1
            line.delete(array.get(pos_div_lines, j))
        array.clear(pos_div_lines)

delete_old_neg_div_lines()=>
    if array.size(neg_div_lines) > 0
        for j = 0 to array.size(neg_div_lines) - 1
            line.delete(array.get(neg_div_lines, j))
        array.clear(neg_div_lines)

delete_old_pos_div_labels()=>
    if array.size(pos_div_labels) > 0
        for j = 0 to array.size(pos_div_labels) - 1
            label.delete(array.get(pos_div_labels, j))
        array.clear(pos_div_labels)

delete_old_neg_div_labels()=>
    if array.size(neg_div_labels) > 0
        for j = 0 to array.size(neg_div_labels) - 1
            label.delete(array.get(neg_div_labels, j))
        array.clear(neg_div_labels)

// delete last creted lines and labels until we met new PH/PV
delete_last_pos_div_lines_label(n)=>
    if n > 0 and array.size(pos_div_lines) >= n
        asz = array.size(pos_div_lines)
        for j = 1 to n
            line.delete(array.get(pos_div_lines, asz - j))
            array.pop(pos_div_lines)
        if array.size(pos_div_labels) > 0
            label.delete(array.get(pos_div_labels, array.size(pos_div_labels) - 1))
            array.pop(pos_div_labels)

delete_last_neg_div_lines_label(n)=>
    if n > 0 and array.size(neg_div_lines) >= n
        asz = array.size(neg_div_lines)
        for j = 1 to n
            line.delete(array.get(neg_div_lines, asz - j))
            array.pop(neg_div_lines)
        if array.size(neg_div_labels) > 0
            label.delete(array.get(neg_div_labels, array.size(neg_div_labels) - 1))
            array.pop(neg_div_labels)

// variables for Alerts
pos_reg_div_detected = false
neg_reg_div_detected = false
pos_hid_div_detected = false
neg_hid_div_detected = false

// to remove lines/labels until we met new // PH/PL
var last_pos_div_lines = 0
var last_neg_div_lines = 0
var remove_last_pos_divs = false
var remove_last_neg_divs = false
if pl
    remove_last_pos_divs := false
    last_pos_div_lines := 0
if ph
    remove_last_neg_divs := false
    last_neg_div_lines := 0

// draw divergences lines and labels
divergence_text_top = ""
divergence_text_bottom = ""
distances = array.new_int(0)
dnumdiv_top = 0
dnumdiv_bottom = 0
top_label_col = color.white
bottom_label_col = color.white
old_pos_divs_can_be_removed = true
old_neg_divs_can_be_removed = true
startpoint = dontconfirm ? 0 : 1 // used for don't confirm option

for x = 0 to 10
    div_type = -1
    for y = 0 to 3
        if array.get(all_divergences, x * 4 + y) > 0 // any divergence?
            div_type := y
            if (y % 2) == 1
                dnumdiv_top := dnumdiv_top + 1
                top_label_col := array.get(div_colors, y)
            if (y % 2) == 0
                dnumdiv_bottom := dnumdiv_bottom + 1
                bottom_label_col := array.get(div_colors, y)
            if not array.includes(distances, array.get(all_divergences, x * 4 + y))  // line not exist ?
                array.push(distances, array.get(all_divergences, x * 4 + y))
                new_line = showlines ? line.new(x1 = bar_index - array.get(all_divergences, x * 4 + y),
                          y1 = (source == "Close" ? close[array.get(all_divergences, x * 4 + y)] :
                                           (y % 2) == 0 ? low[array.get(all_divergences, x * 4 + y)] :
                                                          high[array.get(all_divergences, x * 4 + y)]),
                          x2 = bar_index - startpoint,
                          y2 = (source == "Close" ? close[startpoint] :
                                           (y % 2) == 0 ? low[startpoint] :
                                                          high[startpoint]),
                          color = array.get(div_colors, y),
                          style = y < 2 ? reg_div_l_style : hid_div_l_style,
                          width = y < 2 ? reg_div_l_width : hid_div_l_width
                          )
                          : na
                if (y % 2) == 0
                    if old_pos_divs_can_be_removed
                        old_pos_divs_can_be_removed := false
                        if not showlast and remove_last_pos_divs
                            delete_last_pos_div_lines_label(last_pos_div_lines)
                            last_pos_div_lines := 0
                        if showlast
                            delete_old_pos_div_lines()
                    array.push(pos_div_lines, new_line)
                    last_pos_div_lines := last_pos_div_lines + 1
                    remove_last_pos_divs := true

                if (y % 2) == 1
                    if old_neg_divs_can_be_removed
                        old_neg_divs_can_be_removed := false
                        if not showlast and remove_last_neg_divs
                            delete_last_neg_div_lines_label(last_neg_div_lines)
                            last_neg_div_lines := 0
                        if showlast
                            delete_old_neg_div_lines()
                    array.push(neg_div_lines, new_line)
                    last_neg_div_lines := last_neg_div_lines + 1
                    remove_last_neg_divs := true

            // set variables for alerts
            if y == 0
                pos_reg_div_detected := true
            if y == 1
                neg_reg_div_detected := true
            if y == 2
                pos_hid_div_detected := true
            if y == 3
                neg_hid_div_detected := true
    // get text for labels
    if div_type >= 0
        divergence_text_top    := divergence_text_top    + ((div_type % 2) == 1 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")
        divergence_text_bottom := divergence_text_bottom + ((div_type % 2) == 0 ? (showindis != "Don't Show" ? array.get(indicators_name, x) + "\n" : "") : "")


// draw labels
if showindis != "Don't Show" or shownum
    if shownum and dnumdiv_top > 0
        divergence_text_top := divergence_text_top + str.tostring(dnumdiv_top)
    if shownum and dnumdiv_bottom > 0
        divergence_text_bottom := divergence_text_bottom + str.tostring(dnumdiv_bottom)
    if divergence_text_top != ""
        if showlast
            delete_old_neg_div_labels()
        array.push(neg_div_labels,
                      label.new( x = bar_index,
                                 y = math.max(high, high[1]),
                                 text = divergence_text_top,
                                 color = top_label_col,
                                 textcolor = neg_div_text_col,
                                 style = label.style_label_down
                                 ))

    if divergence_text_bottom != ""
        if showlast
            delete_old_pos_div_labels()
        array.push(pos_div_labels,
                      label.new( x = bar_index,
                                 y = math.min(low, low[1]),
                                 text = divergence_text_bottom,
                                 color = bottom_label_col,
                                 textcolor = pos_div_text_col,
                                 style = label.style_label_up
                                 ))


alertcondition(pos_reg_div_detected, title='Positive Regular Divergence Detected', message='Positive Regular Divergence Detected')
alertcondition(neg_reg_div_detected, title='Negative Regular Divergence Detected', message='Negative Regular Divergence Detected')
alertcondition(pos_hid_div_detected, title='Positive Hidden Divergence Detected', message='Positive Hidden Divergence Detected')
alertcondition(neg_hid_div_detected, title='Negative Hidden Divergence Detected', message='Negative Hidden Divergence Detected')

alertcondition(pos_reg_div_detected or pos_hid_div_detected, title='Positive Divergence Detected', message='Positive Divergence Detected')
alertcondition(neg_reg_div_detected or neg_hid_div_detected, title='Negative Divergence Detected', message='Negative Divergence Detected')




//@version=5
// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GenZai 18-08-2021 v5
// Modified to plot arrows on candles

// indicator("OSCAR with Candle Arrows", overlay=true)

// Input parameters
len = input.int(title="Oscar Candles", defval=8)
slowLen = input.int(title="Slow Oscar Candles", defval=16)
smoothing = input.string(title="Oscar Smoothing", defval="RMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"])
slowOscarSmoothing = input.string(title="Slow Oscar Smoothing", defval="WMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"])
crossSignalSensitivity = input.float(title="Cross Signal Sensitivity", defval=0.5, step=0.1)
OscarOfIndicator = input.source(title="Choose input type", defval=close)
bottomSignalLine = input.float(title="Bottom Signal Line", defval=35, step=5)
topSignalLine = input.float(title="Top Signal Line", defval=65, step=5)
showValues = input.bool(title="Show Indicator Values", defval=false)

// Moving average function
ma(smoothingType, OscarParam, length) =>
    if smoothingType == "RMA"
        ta.rma(OscarParam, length)
    else if smoothingType == "SMA"
        ta.sma(OscarParam, length)
    else if smoothingType == "EMA"
        ta.ema(OscarParam, length)
    else if smoothingType == "WMA"
        ta.wma(OscarParam, length)
    else if smoothingType == "LSMA"
        ta.linreg(OscarParam, length, 0)
    else if smoothingType == "HMA"
        ta.hma(OscarParam, length)
    else
        OscarParam

// Oscar calculation
A = ta.highest(OscarOfIndicator, len)
B = ta.lowest(OscarOfIndicator, len)
OscarRough = (OscarOfIndicator - B) / (A - B) * 100
Oscar1 = (OscarRough[1] / 3) * 2
OscarThird = OscarRough / 3
Oscar = Oscar1 + OscarThird
smoothedOscarRough = ma(smoothing, OscarRough, len)
smoothedOscar = ma(smoothing, Oscar, len)

// Slow Oscar calculation
slowOscarMa(slowOscarSmoothing, slowOscarParam, slowLen) =>
    if slowOscarSmoothing == "RMA"
        ta.rma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "SMA"
        ta.sma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "EMA"
        ta.ema(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "WMA"
        ta.wma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "LSMA"
        ta.linreg(slowOscarParam, slowLen, 0)
    else if slowOscarSmoothing == "HMA"
        ta.hma(slowOscarParam, slowLen)
    else
        slowOscarParam

slowA = ta.highest(OscarOfIndicator, slowLen)
slowB = ta.lowest(OscarOfIndicator, slowLen)
slowOscarRough = (OscarOfIndicator - slowB) / (slowA - slowB) * 100
slowOscar1 = (slowOscarRough[1] / 3) * 2
slowOscarThird = slowOscarRough / 3
slowOscar = slowOscar1 + slowOscarThird
smoothedSlowOscar = slowOscarMa(slowOscarSmoothing, slowOscar, slowLen)

// Cross sensitivity calculation
crossSensitivity = math.max(smoothedOscarRough, smoothedOscar) - math.min(smoothedOscarRough, smoothedOscar)

// Signal conditions
buySignal1 = ta.crossover(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough < bottomSignalLine
buySignal2 = ta.crossover(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > smoothedOscar and smoothedOscar < bottomSignalLine

sellSignal1 = ta.crossunder(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > topSignalLine
sellSignal2 = ta.crossunder(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscar > smoothedOscarRough and smoothedOscar > topSignalLine

// Plot arrows on candles with enhanced visibility
// Buy Signal 1 - Triangle Up
plotshape(series=buySignal1,title="Buy Signal 1",style=shape.triangleup,location=location.belowbar,color=color.green,size=size.huge)
if buySignal1
    label.new(bar_index, low, "OSCAR BUY", style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)

// Buy Signal 2 - Arrow Up
plotshape(series=buySignal2,title="Buy Signal 2",style=shape.arrowup,location=location.belowbar,color=color.rgb(0, 255, 0),size=size.huge)

// Sell Signal 1 - Triangle Down
plotshape(series=sellSignal1,title="Sell Signal 1",style=shape.triangledown,location=location.abovebar,color=color.red,size=size.huge)
if sellSignal1
    label.new(bar_index, high, "OSCAR SELL", style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)

// Sell Signal 2 - Arrow Down
plotshape(series=sellSignal2,title="Sell Signal 2",style=shape.arrowdown,location=location.abovebar,color=color.rgb(255, 0, 0),size=size.huge)

// Display indicator values as labels
var label valueLabel = na
if showValues
    valueLabel := label.new(x=bar_index,y=high,text="Oscar: " + str.tostring(smoothedOscar, "#.##") +     "\nOscar Rough: " + str.tostring(smoothedOscarRough, "#.##") +     "\nSensitivity: " + str.tostring(crossSensitivity, "#.##"),style=label.style_label_down,color=color.rgb(0, 0, 0, 80),textcolor=color.white,yloc=yloc.price)
    label.delete(valueLabel[1])





//VWAP

// VWAP Settings Group
hideonDWM = input(false, title="Hide VWAP on 1D or Above", group="VWAP Settings", display = display.data_window)
src = input(title = "Source", defval = hlc3, group="VWAP Settings", display = display.data_window)
offset = input.int(0, title="Offset", group="VWAP Settings", minval=0, display = display.data_window)

// Anchor Period Settings
show_session = input(true, title="Session", group="Anchor Periods", inline="session")
session_color = input.color(color.rgb(155, 244, 137), title="", group="Anchor Periods", inline="session")

show_week = input(false, title="Week", group="Anchor Periods", inline="week")
week_color = input.color(color.rgb(244, 155, 137), title="", group="Anchor Periods", inline="week")

show_month = input(false, title="Month", group="Anchor Periods", inline="month")
month_color = input.color(color.rgb(137, 155, 244), title="", group="Anchor Periods", inline="month")

show_quarter = input(false, title="Quarter", group="Anchor Periods", inline="quarter")
quarter_color = input.color(color.rgb(244, 137, 155), title="", group="Anchor Periods", inline="quarter")

show_year = input(false, title="Year", group="Anchor Periods", inline="year")
year_color = input.color(color.rgb(137, 244, 155), title="", group="Anchor Periods", inline="year")

cumVolume = ta.cum(volume)
if barstate.islast and cumVolume == 0
    runtime.error("No volume is provided by the data vendor.")

// Define new period conditions for each anchor period
isNewSession = timeframe.change("D")
isNewWeek = timeframe.change("W")
isNewMonth = timeframe.change("M")
isNewQuarter = timeframe.change("3M")
isNewYear = timeframe.change("12M")

// Handle data gaps
if na(src[1])
    isNewSession := true
    isNewWeek := true
    isNewMonth := true
    isNewQuarter := true
    isNewYear := true

// Initialize VWAP variables
float sessionVwap = na
float weekVwap = na
float monthVwap = na
float quarterVwap = na
float yearVwap = na

// Calculate VWAPs for each selected anchor period if not on daily or higher timeframe when hidden
if not (hideonDWM and timeframe.isdwm)
    // Session VWAP
    if show_session
        var float sumSrc = 0.0
        var float sumVol = 0.0

        if isNewSession
            sumSrc := src * volume
            sumVol := volume
        else
            sumSrc += src * volume
            sumVol += volume

        sessionVwap := sumVol != 0 ? sumSrc / sumVol : na

    // Week VWAP
    if show_week
        var float sumSrcWeek = 0.0
        var float sumVolWeek = 0.0

        if isNewWeek
            sumSrcWeek := src * volume
            sumVolWeek := volume
        else
            sumSrcWeek += src * volume
            sumVolWeek += volume

        weekVwap := sumVolWeek != 0 ? sumSrcWeek / sumVolWeek : na

    // Month VWAP
    if show_month
        var float sumSrcMonth = 0.0
        var float sumVolMonth = 0.0

        if isNewMonth
            sumSrcMonth := src * volume
            sumVolMonth := volume
        else
            sumSrcMonth += src * volume
            sumVolMonth += volume

        monthVwap := sumVolMonth != 0 ? sumSrcMonth / sumVolMonth : na

    // Quarter VWAP
    if show_quarter
        var float sumSrcQuarter = 0.0
        var float sumVolQuarter = 0.0

        if isNewQuarter
            sumSrcQuarter := src * volume
            sumVolQuarter := volume
        else
            sumSrcQuarter += src * volume
            sumVolQuarter += volume

        quarterVwap := sumVolQuarter != 0 ? sumSrcQuarter / sumVolQuarter : na

    // Year VWAP
    if show_year
        var float sumSrcYear = 0.0
        var float sumVolYear = 0.0

        if isNewYear
            sumSrcYear := src * volume
            sumVolYear := volume
        else
            sumSrcYear += src * volume
            sumVolYear += volume

        yearVwap := sumVolYear != 0 ? sumSrcYear / sumVolYear : na

// Plot each VWAP line with its own color
plot(show_session ? sessionVwap : na, title = "Session VWAP", color = session_color, offset = offset, linewidth = 2)
plot(show_week ? weekVwap : na, title = "Week VWAP", color = week_color, offset = offset, linewidth = 2)
plot(show_month ? monthVwap : na, title = "Month VWAP", color = month_color, offset = offset, linewidth = 2)
plot(show_quarter ? quarterVwap : na, title = "Quarter VWAP", color = quarter_color, offset = offset, linewidth = 2)
plot(show_year ? yearVwap : na, title = "Year VWAP", color = year_color, offset = offset, linewidth = 2)


// ---------------------------------------------------------------------------------------------- //
// Inputs  -------------------------------------------------------------------------------------- //

// HTF Candles Box Settings
group_candle = 'HTF Box Settings'
htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP', group=group_candle)
htfUser1  = input.string('4 Hours', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP', group=group_candle)
htfCndl2  = input.bool(false, '2nd HTF Box', inline='TYP3', group=group_candle)
htfUser2  = input.string('1 Hour', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP3', group=group_candle)
bullC     = input.color(#26a69a, 'Box : Bull', inline='CNDL', group=group_candle)
bearC     = input.color(#ef5350, 'Bear', inline='CNDL', group=group_candle)
trans     = input.int(85, 'Transp', inline='CNDL', minval=65, maxval=95, group=group_candle)
lineWidth = input.int(1, 'Line Width', inline='CNDL', minval=1, maxval=4, group=group_candle)

// Optional 3rd HTF Box
htfCndl3  = input.bool(false, '3rd HTF Box', inline='TYP4', group=group_candle)
htfUser3  = input.string('1 Month', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP4', group=group_candle)


// ---------------------------------------------------------------------------------------------- //
// Functions  ----------------------------------------------------------------------------------- //

checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 :  _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)

    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0.,     htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c]

f_getTF(_htf) =>
    htf = _htf == '3 Mins' ? '3' : _htf == '5 Mins' ? '5' : _htf == '10 Mins' ? '10' : _htf == '15 Mins' ? '15' : _htf == '30 Mins' ? '30' : _htf == '45 Mins' ? '45' : _htf == '1 Hour' ? '60' : _htf == '2 Hours' ? '120' : _htf == '3 Hours' ? '180' : _htf == '4 Hours' ? '240' : _htf == '1 Day' ? 'D' : _htf == '1 Week' ? 'W' : _htf == '1 Month' ? 'M' : _htf == '3 Months' ? '3M' : _htf == '6 Months' ? '6M' : _htf == '1 Year' ? '12M' : na
    htf

f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width) =>
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0] = f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)
                true
            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)
                true

// ---------------------------------------------------------------------------------------------- //
// Main Logic ---------------------------------------------------------------------------------- //

htf1 = f_getTF(htfUser1)
htf2 = f_getTF(htfUser2)
htf3 = f_getTF(htfUser3)
supported1 = checkIf(timeframe.period, htf1)
supported2 = checkIf(timeframe.period, htf2)
supported3 = checkIf(timeframe.period, htf3)

// Process HTF Boxes
if chart.is_standard
    if supported1
        f_processCandles(htfCndl1, htf1, bullC, bearC, trans, lineWidth)
    if supported2
        f_processCandles(htfCndl2, htf2, bullC, bearC, trans, lineWidth)
    if supported3
        f_processCandles(htfCndl3, htf3, bullC, bearC, trans, lineWidth)

// ==================== Strategy Implementation ==================================================
// Using existing session detection and HTF candle logic

// Volume spike detection removed for simplicity

// Check if we're in an ICT Killzone session or at the start of a 4hr candle
// Using the existing session detection variables from your code
strategy_in_killzone = t_as or t_lo or t_na  // Asia, London, or NY AM sessions
strategy_is_4h_start = timeframe.change("240")  // Start of 4hr candle
strategy_in_session = strategy_in_killzone or strategy_is_4h_start

// Reset trades counter and daily profit on new day
if dayofweek != dayofweek[1]
    strategy_trades_today := 0
    strategy_last_trade_day := dayofweek
    strategy_daily_profit := 0.0

// Update daily profit
strategy_daily_profit := strategy_daily_profit + (strategy.netprofit - strategy.netprofit[1])

// Track divergences
if pos_reg_div_detected or pos_hid_div_detected
    strategy_had_bullish_div := true
    strategy_last_bullish_div_bar := bar_index
    // Add a visual marker for bullish divergence detection
    label.new(bar_index, low, "BULL DIV", style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)

if neg_reg_div_detected or neg_hid_div_detected
    strategy_had_bearish_div := true
    strategy_last_bearish_div_bar := bar_index
    // Add a visual marker for bearish divergence detection
    label.new(bar_index, high, "BEAR DIV", style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)

// Reset divergence flags after a certain number of bars
if strategy_had_bullish_div and bar_index - strategy_last_bullish_div_bar > 20
    strategy_had_bullish_div := false

if strategy_had_bearish_div and bar_index - strategy_last_bearish_div_bar > 20
    strategy_had_bearish_div := false

// Volume between divergences check removed for simplicity

// Check if price is near VWAP using existing VWAP variables from the code
strategy_price_near_session_vwap = show_session and not na(sessionVwap) ? math.abs(close - sessionVwap) / sessionVwap * 100 <= vwap_deviation : false

strategy_price_near_week_vwap = show_week and not na(weekVwap) ? math.abs(close - weekVwap) / weekVwap * 100 <= vwap_deviation : false

strategy_price_near_month_vwap = show_month and not na(monthVwap) ? math.abs(close - monthVwap) / monthVwap * 100 <= vwap_deviation : false

strategy_price_near_quarter_vwap = show_quarter and not na(quarterVwap) ? math.abs(close - quarterVwap) / quarterVwap * 100 <= vwap_deviation : false

// Price is near any VWAP
strategy_price_near_vwap = strategy_price_near_session_vwap or strategy_price_near_week_vwap or
                          strategy_price_near_month_vwap or strategy_price_near_quarter_vwap

// Check if we can trade based on max daily loss
strategy_can_trade = strategy_daily_profit > (strategy.initial_capital * max_daily_loss / 100)

// Track session and 4hr candle starts
var int last_session_start_bar = 0
var int last_4hr_start_bar = 0

// Update session start trackers
if strategy_in_killzone and (last_session_start_bar != bar_index)
    last_session_start_bar := bar_index

if strategy_is_4h_start
    last_4hr_start_bar := bar_index

// Check if we're within the first 30 minutes of a session or 4hr candle start
// Assuming each bar is 5 minutes, 30 minutes = 6 bars
strategy_within_30min_of_session = (bar_index - last_session_start_bar) <= 6 and last_session_start_bar > 0
strategy_within_30min_of_4hr = (bar_index - last_4hr_start_bar) <= 6 and last_4hr_start_bar > 0

// Only allow one signal per session/4hr start
var bool strategy_signal_taken_this_session = false
var bool strategy_signal_taken_this_4hr = false

// Reset signal flags at new session/4hr
if strategy_in_killzone and (last_session_start_bar == bar_index)
    strategy_signal_taken_this_session := false

if strategy_is_4h_start
    strategy_signal_taken_this_4hr := false

// Entry conditions - for testing, we'll make them more lenient
// Add a toggle to switch between strict and lenient conditions
use_strict_conditions = input.bool(false, "Use Strict Entry Conditions", group="Strategy Settings")

// Strict conditions - one signal per session/4hr, within first 30 minutes
strategy_long_condition_strict = (
    (strategy_within_30min_of_session and not strategy_signal_taken_this_session) or
    (strategy_within_30min_of_4hr and not strategy_signal_taken_this_4hr)
) and
ta.crossover(smoothedOscarRough, smoothedOscar) and
crossSensitivity > crossSignalSensitivity and
strategy_had_bullish_div and
strategy_price_near_vwap

strategy_short_condition_strict = (
    (strategy_within_30min_of_session and not strategy_signal_taken_this_session) or
    (strategy_within_30min_of_4hr and not strategy_signal_taken_this_4hr)
) and
ta.crossunder(smoothedOscarRough, smoothedOscar) and
crossSensitivity > crossSignalSensitivity and
strategy_had_bearish_div and
strategy_price_near_vwap

// Lenient conditions - just check for Oscar signals and divergences
strategy_long_condition_lenient =
ta.crossover(smoothedOscarRough, smoothedOscar) and
crossSensitivity > crossSignalSensitivity and
strategy_had_bullish_div

strategy_short_condition_lenient =
ta.crossunder(smoothedOscarRough, smoothedOscar) and
crossSensitivity > crossSignalSensitivity and
strategy_had_bearish_div

// Use either strict or lenient conditions based on the toggle
strategy_long_condition = use_strict_conditions ? strategy_long_condition_strict : strategy_long_condition_lenient
strategy_short_condition = use_strict_conditions ? strategy_short_condition_strict : strategy_short_condition_lenient

// Update signal taken flags
if strategy_long_condition or strategy_short_condition
    if strategy_within_30min_of_session
        strategy_signal_taken_this_session := true
    if strategy_within_30min_of_4hr
        strategy_signal_taken_this_4hr := true

// For actual trading, you might want to use the more restrictive conditions:
// strategy_long_condition = strategy_had_bullish_div and buySignal1 and strategy_price_near_vwap and strategy_in_session and
//                  strategy_trades_today < max_trades_per_day and (require_volume_spike ? strategy_has_volume_between_divs : true) and
//                  strategy_can_trade and close > open
//
// strategy_short_condition = strategy_had_bearish_div and sellSignal1 and strategy_price_near_vwap and strategy_in_session and
//                   strategy_trades_today < max_trades_per_day and (require_volume_spike ? strategy_has_volume_between_divs : true) and
//                   strategy_can_trade and close < open

// Calculate stop loss and take profit levels
strategy_stop_loss_long = close - (stop_loss_ticks * tick_size)
strategy_take_profit_long = close + (take_profit_ticks * tick_size)
strategy_stop_loss_short = close + (stop_loss_ticks * tick_size)
strategy_take_profit_short = close - (take_profit_ticks * tick_size)

// Execute strategy
if strategy_long_condition
    strategy.entry("Long", strategy.long)
    strategy.exit("Long Exit", "Long", stop=strategy_stop_loss_long, limit=strategy_take_profit_long)
    strategy_trades_today := strategy_trades_today + 1
    strategy_had_bullish_div := false

if strategy_short_condition
    strategy.entry("Short", strategy.short)
    strategy.exit("Short Exit", "Short", stop=strategy_stop_loss_short, limit=strategy_take_profit_short)
    strategy_trades_today := strategy_trades_today + 1
    strategy_had_bearish_div := false

// Close all positions if max daily loss is hit
if strategy_daily_profit < (strategy.initial_capital * max_daily_loss / 100)
    strategy.close_all("Max Daily Loss")

// Plot entry signals with MAXIMUM visibility
// Buy signals - just one clear signal but EXTREMELY visible
plotshape(strategy_long_condition, title="Long Entry", style=shape.labelup, location=location.belowbar, color=color.green, size=size.huge, text="BUY")
plotarrow(strategy_long_condition ? 1 : 0, title="Long Arrow", colorup=color.green, colordown=color.red, minheight=100, maxheight=100)
if strategy_long_condition
    label.new(bar_index, low - (low * 0.001), "STRATEGY BUY SIGNAL", style=label.style_label_up, color=color.green, textcolor=color.white, size=size.large)

// Sell signals - just one clear signal but EXTREMELY visible
plotshape(strategy_short_condition, title="Short Entry", style=shape.labeldown, location=location.abovebar, color=color.red, size=size.huge, text="SELL")
plotarrow(strategy_short_condition ? -1 : 0, title="Short Arrow", colorup=color.green, colordown=color.red, minheight=100, maxheight=100)
if strategy_short_condition
    label.new(bar_index, high + (high * 0.001), "STRATEGY SELL SIGNAL", style=label.style_label_down, color=color.red, textcolor=color.white, size=size.large)

// Plot stop loss and take profit levels when a position is entered
if strategy.position_size > 0  // Long position
    line.new(bar_index, strategy_stop_loss_long, bar_index, strategy_stop_loss_long, extend=extend.right, color=color.red, style=line.style_dashed, width=2)
    line.new(bar_index, strategy_take_profit_long, bar_index, strategy_take_profit_long, extend=extend.right, color=color.green, style=line.style_dashed, width=2)
    label.new(bar_index, strategy_stop_loss_long, text="SL", color=color.red, style=label.style_label_down, textcolor=color.white)
    label.new(bar_index, strategy_take_profit_long, text="TP", color=color.green, style=label.style_label_up, textcolor=color.white)

if strategy.position_size < 0  // Short position
    line.new(bar_index, strategy_stop_loss_short, bar_index, strategy_stop_loss_short, extend=extend.right, color=color.red, style=line.style_dashed, width=2)
    line.new(bar_index, strategy_take_profit_short, bar_index, strategy_take_profit_short, extend=extend.right, color=color.green, style=line.style_dashed, width=2)
    label.new(bar_index, strategy_stop_loss_short, text="SL", color=color.red, style=label.style_label_up, textcolor=color.white)
    label.new(bar_index, strategy_take_profit_short, text="TP", color=color.green, style=label.style_label_down, textcolor=color.white)

// Add debugging information
if barstate.islast
    label.new(bar_index, high,
              text="STRATEGY STATUS:\n" +
              "Price Near VWAP: " + (strategy_price_near_vwap ? "Yes" : "No") + "\n" +
              "In Killzone: " + (strategy_in_killzone ? "Yes" : "No") + "\n" +
              "At 4hr Start: " + (strategy_is_4h_start ? "Yes" : "No") + "\n" +
              "Within 30min of Session: " + (strategy_within_30min_of_session ? "Yes" : "No") + "\n" +
              "Within 30min of 4hr: " + (strategy_within_30min_of_4hr ? "Yes" : "No") + "\n" +
              "Signal Taken This Session: " + (strategy_signal_taken_this_session ? "Yes" : "No") + "\n" +
              "Signal Taken This 4hr: " + (strategy_signal_taken_this_4hr ? "Yes" : "No") + "\n" +
              "Bullish Div: " + (strategy_had_bullish_div ? "Yes" : "No") + "\n" +
              "Bearish Div: " + (strategy_had_bearish_div ? "Yes" : "No") + "\n" +
              "Oscar Buy: " + (buySignal1 ? "Yes" : "No") + "\n" +
              "Oscar Sell: " + (sellSignal1 ? "Yes" : "No") + "\n" +
              "Oscar Cross Sensitivity: " + str.tostring(crossSensitivity) + "\n" +
              "Required Sensitivity: " + str.tostring(crossSignalSensitivity) + "\n" +
              "Trades Today: " + str.tostring(strategy_trades_today),
              style=label.style_label_down,
              color=color.rgb(0, 0, 0, 50),
              textcolor=color.white,
              size=size.large)

// Add strategy alert conditions
alertcondition(strategy_long_condition, "Buy Signal", "VWAP + Divergence + Oscar Buy Signal")
alertcondition(strategy_short_condition, "Sell Signal", "VWAP + Divergence + Oscar Sell Signal")