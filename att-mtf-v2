//@version=5
indicator("ATT-MTF-round-number [Ravi]", overlay=true)

// Add this line after the other inputs, before the ATT Numbers section
labelOffset = input.float(0.0003, "Label Offset", minval=0.0001, maxval=0.01, step=0.0001)

// Timeframe inputs
show_current_tf = input.bool(true, "Show Current Timeframe ATT Points", group="Timeframe Settings")

// Timeframe 1
show_tf1 = input.bool(true, "Show Timeframe 1", inline="tf1", group="Timeframe Settings")
tf1 = input.timeframe("15", "TF1", inline="tf1", group="Timeframe Settings")
tf1_color = input.color(color.new(#f6c309, 0), "Color", inline="tf1", group="Timeframe Settings")

// Timeframe 2
show_tf2 = input.bool(true, "Show Timeframe 2", inline="tf2", group="Timeframe Settings")
tf2 = input.timeframe("30", "TF2", inline="tf2", group="Timeframe Settings")
tf2_color = input.color(color.new(#ff5d00, 0), "Color", inline="tf2", group="Timeframe Settings")

// Timeframe 3
show_tf3 = input.bool(true, "Show Timeframe 3", inline="tf3", group="Timeframe Settings")
tf3 = input.timeframe("45", "TF3", inline="tf3", group="Timeframe Settings")
tf3_color = input.color(color.new(#ff0057, 0), "Color", inline="tf3", group="Timeframe Settings")

// Timeframe 4
show_tf4 = input.bool(true, "Show Timeframe 4", inline="tf4", group="Timeframe Settings")
tf4 = input.timeframe("60", "TF4", inline="tf4", group="Timeframe Settings")
tf4_color = input.color(color.new(#b200ff, 0), "Color", inline="tf4", group="Timeframe Settings")

// New Timeframe 5
show_tf5 = input.bool(true, "Show Timeframe 5", inline="tf5", group="Timeframe Settings")
tf5 = input.timeframe("240", "TF5", inline="tf5", group="Timeframe Settings")
tf5_color = input.color(color.new(#00b3ff, 0), "Color", inline="tf5", group="Timeframe Settings")

// New Timeframe 6
show_tf6 = input.bool(true, "Show Timeframe 6", inline="tf6", group="Timeframe Settings")
tf6 = input.timeframe("480", "TF6", inline="tf6", group="Timeframe Settings")
tf6_color = input.color(color.new(#4CAF50, 0), "Color", inline="tf6", group="Timeframe Settings")
                                  
// New Timeframe 7
show_tf7 = input.bool(true, "Show Timeframe 7", inline="tf7", group="Timeframe Settings")
tf7 = input.timeframe("960", "TF7", inline="tf7", group="Timeframe Settings")
tf7_color = input.color(color.new(#FF9800, 0), "Color", inline="tf7", group="Timeframe Settings")

// New Timeframe 8
show_tf8 = input.bool(true, "Show Timeframe 8", inline="tf8", group="Timeframe Settings")
tf8 = input.timeframe("D", "TF8", inline="tf8", group="Timeframe Settings")
tf8_color = input.color(color.new(#9C27B0, 0), "Color", inline="tf8", group="Timeframe Settings")

// New Timeframe 9
show_tf9 = input.bool(true, "Show Timeframe 9", inline="tf9", group="Timeframe Settings")
tf9 = input.timeframe("W", "TF9", inline="tf9", group="Timeframe Settings")
tf9_color = input.color(color.new(#2196F3, 0), "Color", inline="tf9", group="Timeframe Settings")

// New Timeframe 10
show_tf10 = input.bool(true, "Show Timeframe 10", inline="tf10", group="Timeframe Settings")
tf10 = input.timeframe("M", "TF10", inline="tf10", group="Timeframe Settings")
tf10_color = input.color(color.new(#E91E63, 0), "Color", inline="tf10", group="Timeframe Settings")
// ATT Numbers
var att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

// Function to calculate ATT position
getAttPosition(tf) =>
    math.floor(bar_index % 60)

// Function to check if current bar is ATT number
isAttNumber(pos) =>
    result = false
    for num in att_numbers
        if pos == num
            result := true
    result

// Current timeframe calculations
adjustedPosition = math.floor(bar_index % 60)

// Modify swing detection for higher timeframes
swingHighCondition = high > high[1] and high > high[2] and high[1] > high[2]
swingLowCondition = low < low[1] and low < low[2] and low[1] < low[2]

// Process higher timeframes
get_htf_data(tf) =>
    [htf_high, htf_low, htf_time] = request.security(syminfo.tickerid, tf, [high[1], low[1], time])
    [htf_high2, htf_low2] = request.security(syminfo.tickerid, tf, [high[2], low[2]])
    htf_position = request.security(syminfo.tickerid, tf, getAttPosition(tf))
    htf_changed = htf_time != htf_time[1]
    htf_swingHigh = htf_changed and high[1] > htf_high and high[1] > htf_high2
    htf_swingLow = htf_changed and low[1] < htf_low and low[1] < htf_low2
    [htf_position, htf_swingHigh, htf_swingLow]



// Helper function to get timeframe in minutes
tfToMinutes(tf) =>
    int minutes = 0
    if tf == 'D' or tf == '1D'
        minutes := 1440
    else if tf == 'W' or tf == '1W'
        minutes := 10080
    else if tf == 'M' or tf == '1M'
        minutes := 43200
    else
        minutes := str.tonumber(tf)
    minutes

// Get current chart's timeframe in minutes
var currentTFMinutes = timeframe.in_minutes()

[tf1_pos, tf1_swingHigh, tf1_swingLow] = get_htf_data(tf1)
[tf2_pos, tf2_swingHigh, tf2_swingLow] = get_htf_data(tf2)
[tf3_pos, tf3_swingHigh, tf3_swingLow] = get_htf_data(tf3)
[tf4_pos, tf4_swingHigh, tf4_swingLow] = get_htf_data(tf4)
[tf5_pos, tf5_swingHigh, tf5_swingLow] = get_htf_data(tf5)
[tf6_pos, tf6_swingHigh, tf6_swingLow] = get_htf_data(tf6)
[tf7_pos, tf7_swingHigh, tf7_swingLow] = get_htf_data(tf7)
[tf8_pos, tf8_swingHigh, tf8_swingLow] = get_htf_data(tf8)
[tf9_pos, tf9_swingHigh, tf9_swingLow] = get_htf_data(tf9)
[tf10_pos, tf10_swingHigh, tf10_swingLow] = get_htf_data(tf10)

// Plot current timeframe ATT points with numbers
plotshape(show_current_tf and isAttNumber(adjustedPosition) and swingHighCondition, 
         title="Current TF ATT High", 
         style=shape.circle, 
         location=location.abovebar, 
         color=color.yellow, 
         size=size.tiny)

plotshape(show_current_tf and isAttNumber(adjustedPosition) and swingLowCondition, 
         title="Current TF ATT Low", 
         style=shape.circle, 
         location=location.belowbar, 
         color=color.yellow, 
         size=size.tiny)


// Plot higher timeframe points
plotshape(show_tf1 and isAttNumber(tf1_pos) and tf1_swingHigh, title="TF1 ATT High", style=shape.triangledown, location=location.abovebar, color=tf1_color, size=size.small)
plotshape(show_tf1 and isAttNumber(tf1_pos) and tf1_swingLow, title="TF1 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf1_color, size=size.small)





plotshape(show_tf2 and isAttNumber(tf2_pos) and tf2_swingHigh, title="TF2 ATT High", style=shape.triangledown, location=location.abovebar, color=tf2_color, size=size.small)
plotshape(show_tf2 and isAttNumber(tf2_pos) and tf2_swingLow, title="TF2 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf2_color, size=size.small)

plotshape(show_tf3 and isAttNumber(tf3_pos) and tf3_swingHigh, title="TF3 ATT High", style=shape.triangledown, location=location.abovebar, color=tf3_color, size=size.small)
plotshape(show_tf3 and isAttNumber(tf3_pos) and tf3_swingLow, title="TF3 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf3_color, size=size.small)

plotshape(show_tf4 and isAttNumber(tf4_pos) and tf4_swingHigh, title="TF4 ATT High", style=shape.triangledown, location=location.abovebar, color=tf4_color, size=size.small)
plotshape(show_tf4 and isAttNumber(tf4_pos) and tf4_swingLow, title="TF4 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf4_color, size=size.small)

plotshape(show_tf5 and isAttNumber(tf5_pos) and tf5_swingHigh, title="TF5 ATT High", style=shape.triangledown, location=location.abovebar, color=tf5_color, size=size.small)
plotshape(show_tf5 and isAttNumber(tf5_pos) and tf5_swingLow, title="TF5 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf5_color, size=size.small)

plotshape(show_tf6 and isAttNumber(tf6_pos) and tf6_swingHigh, title="TF6 ATT High", style=shape.triangledown, location=location.abovebar, color=tf6_color, size=size.small)
plotshape(show_tf6 and isAttNumber(tf6_pos) and tf6_swingLow, title="TF6 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf6_color, size=size.small)


plotshape(show_tf7 and isAttNumber(tf7_pos) and tf7_swingHigh, title="TF7 ATT High", style=shape.triangledown, location=location.abovebar, color=tf7_color, size=size.small)
plotshape(show_tf7 and isAttNumber(tf7_pos) and tf7_swingLow, title="TF7 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf7_color, size=size.small)

plotshape(show_tf8 and isAttNumber(tf8_pos) and tf8_swingHigh, title="TF8 ATT High", style=shape.triangledown, location=location.abovebar, color=tf8_color, size=size.small)
plotshape(show_tf8 and isAttNumber(tf8_pos) and tf8_swingLow, title="TF8 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf8_color, size=size.small)

plotshape(show_tf9 and isAttNumber(tf9_pos) and tf9_swingHigh, title="TF9 ATT High", style=shape.triangledown, location=location.abovebar, color=tf9_color, size=size.small)
plotshape(show_tf9 and isAttNumber(tf9_pos) and tf9_swingLow, title="TF9 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf9_color, size=size.small)

plotshape(show_tf10 and isAttNumber(tf10_pos) and tf10_swingHigh, title="TF10 ATT High", style=shape.triangledown, location=location.abovebar, color=tf10_color, size=size.small)
plotshape(show_tf10 and isAttNumber(tf10_pos) and tf10_swingLow, title="TF10 ATT Low", style=shape.triangleup, location=location.belowbar, color=tf10_color, size=size.small)

// Labels
if show_current_tf and isAttNumber(adjustedPosition)
    if swingHighCondition
        label.new(bar_index, high + (high * labelOffset), str.tostring(adjustedPosition), color=color.yellow, style=label.style_none, textcolor=color.white)
    if swingLowCondition
        label.new(bar_index, low - (low * labelOffset), str.tostring(adjustedPosition), color=color.yellow, style=label.style_none, textcolor=color.white)

if show_tf1 and isAttNumber(tf1_pos)
    if tf1_swingHigh
        label.new(bar_index, high + (high * labelOffset * 2), str.tostring(tf1_pos), color=tf1_color, style=label.style_none, textcolor=tf1_color)
    if tf1_swingLow
        label.new(bar_index, low - (low * labelOffset * 2), str.tostring(tf1_pos), color=tf1_color, style=label.style_none, textcolor=tf1_color)

if show_tf2 and isAttNumber(tf2_pos)
    if tf2_swingHigh
        label.new(bar_index, high + (high * labelOffset * 3), str.tostring(tf2_pos), color=tf2_color, style=label.style_none, textcolor=tf2_color)
    if tf2_swingLow
        label.new(bar_index, low - (low * labelOffset * 3), str.tostring(tf2_pos), color=tf2_color, style=label.style_none, textcolor=tf2_color)

if show_tf3 and isAttNumber(tf3_pos)
    if tf3_swingHigh
        label.new(bar_index, high + (high * labelOffset * 4), str.tostring(tf3_pos), color=tf3_color, style=label.style_none, textcolor=tf3_color)
    if tf3_swingLow
        label.new(bar_index, low - (low * labelOffset * 4), str.tostring(tf3_pos), color=tf3_color, style=label.style_none, textcolor=tf3_color)

if show_tf4 and isAttNumber(tf4_pos)
    if tf4_swingHigh
        label.new(bar_index, high + (high * labelOffset * 5), str.tostring(tf4_pos), color=tf4_color, style=label.style_none, textcolor=tf4_color)
    if tf4_swingLow
        label.new(bar_index, low - (low * labelOffset * 5), str.tostring(tf4_pos), color=tf4_color, style=label.style_none, textcolor=tf4_color)

if show_tf5 and isAttNumber(tf5_pos)
    if tf5_swingHigh
        label.new(bar_index, high + (high * labelOffset * 6), str.tostring(tf5_pos), color=tf5_color, style=label.style_none, textcolor=tf5_color)
    if tf5_swingLow
        label.new(bar_index, low - (low * labelOffset * 6), str.tostring(tf5_pos), color=tf5_color, style=label.style_none, textcolor=tf5_color)

if show_tf6 and isAttNumber(tf6_pos)
    if tf6_swingHigh
        label.new(bar_index, high + (high * labelOffset * 7), str.tostring(tf6_pos), color=tf6_color, style=label.style_none, textcolor=tf6_color)
    if tf6_swingLow
        label.new(bar_index, low - (low * labelOffset * 7), str.tostring(tf6_pos), color=tf6_color, style=label.style_none, textcolor=tf6_color)


// Round Numbers Settings Group
showRoundNumbers = input.bool(false, "Show Round Numbers", group="Round Numbers", inline="rn_toggle")
roundNumbersExpanded = input.bool(false, "⚙", group="Round Numbers", inline="rn_toggle")

// Only show these inputs if roundNumbersExpanded is true
numLevels = input.int(20, "Number of Levels (Above/Below)", minval=1, maxval=50, 
         group="Round Numbers Settings", inline="rn_levels")
priceInterval = input.int(5, "Price Interval", minval=1, 
         tooltip="Draw lines at every X price points", 
         group="Round Numbers Settings", inline="rn_levels")

// Line Settings (only visible when roundNumbersExpanded is true)
extendLeft = input.int(500, "Extend Left", minval=0, 
         group="Round Numbers Settings")
extendRight = input.int(500, "Extend Right", minval=0, 
         group="Round Numbers Settings")
lineColor = input.color(#eadf10dc, "Line Color", 
         group="Round Numbers Settings")
lineStyle = input.string("Solid", "Line Style", 
         options=["Solid", "Dashed", "Dotted"], 
         group="Round Numbers Settings")
lineWidth = input.int(2, "Line Width", minval=1, maxval=4, 
         group="Round Numbers Settings")
showLabels = input.bool(true, "Show Labels", 
         group="Round Numbers Settings")

// Convert line style input to Pine style
var lineStyleDict = line.style_solid
if lineStyle == "Dashed"
    lineStyleDict := line.style_dashed
else if lineStyle == "Dotted"
    lineStyleDict := line.style_dotted

// Function to determine if the number is a big round number
isBigRoundNumber(num) =>
    num % priceInterval == 0

// Store lines in array
var line[] linesArray = array.new_line()

// Clear previous lines and draw new ones on the last bar
if barstate.islast and showRoundNumbers
    // Get current price and round it to nearest interval
    currentPrice = close
    basePrice = math.round(currentPrice / priceInterval) * priceInterval
    
    // Calculate start and end prices based on current price
    startPrice = basePrice - (numLevels * priceInterval)
    endPrice = basePrice + (numLevels * priceInterval)
    
    // Clear previous lines
    if array.size(linesArray) > 0
        for i = array.size(linesArray) - 1 to 0
            line.delete(array.get(linesArray, i))
        array.clear(linesArray)
    
    // Plot new lines
    for i = startPrice to endPrice by priceInterval
        if isBigRoundNumber(i)
            newLine = line.new(
                 x1=bar_index - extendLeft, 
                 y1=i, 
                 x2=bar_index + extendRight, 
                 y2=i, 
                 color=lineColor,
                 style=lineStyleDict,
                 width=lineWidth)
            array.push(linesArray, newLine)
            
            // Add labels on both ends if enabled
            if showLabels
                label.new(
                     bar_index - extendLeft, 
                     i, 
                     str.tostring(i),
                     style=label.style_label_right,
                     color=color.new(lineColor, 100),
                     textcolor=color.new(lineColor, 0))
                label.new(
                     bar_index + extendRight, 
                     i, 
                     str.tostring(i),
                     style=label.style_label_left,
                     color=color.new(lineColor, 100),
                     textcolor=color.new(lineColor, 0))