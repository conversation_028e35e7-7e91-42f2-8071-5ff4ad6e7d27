//@version=5
// Single Period AMR Levels Indicator
// This indicator draws Average Monthly Range (AMR) levels for a single configurable period
// Features:
// 1. Draws AMR levels of a single period
// 2. Draws true month opening horizontal and vertical lines
// 3. When new true month opening is detected, old lines stay but end at the new true month open
//    and new lines start with labels

indicator("Single Period AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500, max_bars_back=5000)

// === INPUTS ===
// General settings
amr_period = input.int(6, title="AMR Period (Months)", minval=1, maxval=12, group="AMR Settings")
show_full_amr = input.bool(true, title="Show Full AMR", group="AMR Levels")
show_one_third_amr = input.bool(true, title="Show 1/3 AMR", group="AMR Levels")
show_two_third_amr = input.bool(true, title="Show 2/3 AMR", group="AMR Levels")
show_half_amr = input.bool(true, title="Show 1/2 AMR", group="AMR Levels")

// True month open settings
true_month_open_hour = input.int(4, title="True Month Open Hour (UTC)", minval=0, maxval=23, group="True Month Open")
use_exchange_time = input.bool(false, title="Use Exchange Time Instead of UTC", group="True Month Open")
show_vertical_month = input.bool(true, title="Show Vertical Line at Month Open", group="True Month Open")

// Visual settings
amr_color = input.color(color.yellow, title="AMR Level Color", group="Visual Settings")
true_month_open_color = input.color(color.red, title="True Month Open Color", group="Visual Settings")
line_style_value = input.string("Dotted", title="Line Style", options=["Solid", "Dotted", "Dashed"], group="Visual Settings")
line_width = input.int(1, title="Line Width", minval=1, maxval=4, group="Visual Settings")
show_labels = input.bool(true, title="Show Labels", group="Visual Settings")
show_price_in_label = input.bool(false, title="Show Price in Label", group="Visual Settings")
label_size_value = input.string("Small", title="Label Size", options=["Tiny", "Small", "Normal", "Large"], group="Visual Settings")
label_y_offset = input.float(0.0, title="Label Y Offset", step=0.1, group="Visual Settings")
show_historical = input.bool(true, title="Show Historical Levels", group="Visual Settings")
max_periods = input.int(12, title="Maximum Periods to Display", minval=1, maxval=24, group="Visual Settings")

// === VARIABLES ===
// Line style mapping
var line_style = line.style_solid
if line_style_value == "Dotted"
    line_style := line.style_dotted
else if line_style_value == "Dashed"
    line_style := line.style_dashed

// Label size mapping
var label_size = size.small
if label_size_value == "Tiny"
    label_size := size.tiny
else if label_size_value == "Normal"
    label_size := size.normal
else if label_size_value == "Large"
    label_size := size.large

// Arrays to store monthly data
var monthly_timestamps = array.new_int(0)
var monthly_tmo_values = array.new_float(0)
var monthly_ranges = array.new_float(0)

// Arrays to store lines
var tmo_lines = array.new_line(0)
var vertical_lines = array.new_line(0)
var full_amr_up_lines = array.new_line(0)
var full_amr_down_lines = array.new_line(0)
var one_third_amr_up_lines = array.new_line(0)
var one_third_amr_down_lines = array.new_line(0)
var two_third_amr_up_lines = array.new_line(0)
var two_third_amr_down_lines = array.new_line(0)
var half_amr_up_lines = array.new_line(0)
var half_amr_down_lines = array.new_line(0)

// Arrays to store labels
var tmo_labels = array.new_label(0)
var full_amr_up_labels = array.new_label(0)
var full_amr_down_labels = array.new_label(0)
var one_third_amr_up_labels = array.new_label(0)
var one_third_amr_down_labels = array.new_label(0)
var two_third_amr_up_labels = array.new_label(0)
var two_third_amr_down_labels = array.new_label(0)
var half_amr_up_labels = array.new_label(0)
var half_amr_down_labels = array.new_label(0)

// === FUNCTIONS ===
// Function to safely delete lines
safe_delete_lines(line_array) =>
    if array.size(line_array) > 0
        for i = 0 to array.size(line_array) - 1
            line_obj = array.get(line_array, i)
            if not na(line_obj)
                line.delete(line_obj)
        array.clear(line_array)

// Function to safely delete labels
safe_delete_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_obj = array.get(label_array, i)
            if not na(label_obj)
                label.delete(label_obj)
        array.clear(label_array)

// Calculate AMR (Average Monthly Range)
calculate_amr(lookback_period) =>
    // Request monthly high, low values
    [mh, ml, mo] = request.security(syminfo.tickerid, "M", [high, low, open], gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_off)

    // Calculate the monthly range
    mrange = mh - ml

    // Create an array to store the last N monthly ranges
    var ranges = array.new_float(0)

    // Add the current range to the array
    if not na(mrange)
        array.unshift(ranges, mrange)

    // Keep only the lookback_period number of ranges
    while array.size(ranges) > lookback_period
        array.pop(ranges)

    // Calculate the sum of ranges
    sum_range = 0.0
    count = 0

    // Sum up the available ranges (up to lookback_period)
    for i = 0 to math.min(array.size(ranges) - 1, lookback_period - 1)
        range_val = array.get(ranges, i)
        if not na(range_val)
            sum_range := sum_range + range_val
            count := count + 1

    // Calculate the average
    avg_range = count > 0 ? sum_range / count : na

    // Return the average range
    avg_range

// Draw a horizontal line
draw_line(price_level, color, style, x1, x2) =>
    if not na(price_level)
        line_obj = line.new(x1=x1,y1=price_level,x2=x2,y2=price_level,color=color,style=style,width=line_width,xloc=xloc.bar_index)
        line_obj

// Draw a label at the end of a line
draw_label(price_level, t, color, x_pos) =>
    if not na(price_level) and show_labels
        label_obj = label.new(x=x_pos,y=price_level + label_y_offset,text=t + (show_price_in_label ? str.format(" ({0})", price_level) : ""),style=label.style_label_left,size=label_size,textcolor=color,color=color.new(color.black, 100),xloc=xloc.bar_index)
        label_obj

// === MAIN LOGIC ===
// Define session for true month open detection
true_month_hour_str = true_month_open_hour < 10 ? "0" + str.tostring(true_month_open_hour) : str.tostring(true_month_open_hour)
true_month_next_hour = (true_month_open_hour + 1) % 24
true_month_next_hour_str = true_month_next_hour < 10 ? "0" + str.tostring(true_month_next_hour) : str.tostring(true_month_next_hour)
true_month_session = true_month_hour_str + "00-" + true_month_next_hour_str + "00"

// Determine if we're at a new month based on configurable true open time
is_true_month_start = not na(time(timeframe.period, true_month_session, "GMT+0")) and
                     na(time(timeframe.period, true_month_session, "GMT+0")[1]) and
                     dayofmonth(time, "GMT+0") == 1

// Use either exchange time or configurable true month open time
is_new_month = use_exchange_time ? timeframe.change("M") : is_true_month_start

// Calculate AMR value when needed
var float amr_value = na
if is_new_month or barstate.isfirst or last_amr_period != amr_period
    amr_value := calculate_amr(amr_period)
    last_amr_period := amr_period

// Handle true month open
if is_new_month or barstate.isfirst
    // Get the true month open price
    true_month_open_price = barstate.isfirst ? close : open

    // Store month open values
    array.unshift(monthly_timestamps, int(time))
    array.unshift(monthly_tmo_values, true_month_open_price)

    // Calculate AMR levels
    if not na(amr_value)
        // Calculate levels using standard ratios
        full_up = true_month_open_price + amr_value
        full_down = true_month_open_price - amr_value
        one_third_up = true_month_open_price + (amr_value / 3)
        one_third_down = true_month_open_price - (amr_value / 3)
        two_third_up = true_month_open_price + (amr_value * 2 / 3)
        two_third_down = true_month_open_price - (amr_value * 2 / 3)
        half_up = true_month_open_price + (amr_value / 2)
        half_down = true_month_open_price - (amr_value / 2)

        // Store the monthly range
        array.unshift(monthly_ranges, amr_value)

    // Manage array sizes
    if array.size(monthly_timestamps) > max_periods
        array.pop(monthly_timestamps)
        array.pop(monthly_tmo_values)
        array.pop(monthly_ranges)

    // Draw vertical line at month open if needed
    if show_vertical_month
        // Create new vertical line at month open
        month_vertical = line.new(
            x1=bar_index,
            y1=low - (high-low)*0.5,
            x2=bar_index,
            y2=high + (high-low)*0.5,
            extend=extend.both,
            color=true_month_open_color,
            style=line.style_dashed,
            width=2)
        array.unshift(vertical_lines, month_vertical)

        // Limit the number of vertical lines
        if array.size(vertical_lines) > max_periods
            oldest_line = array.get(vertical_lines, array.size(vertical_lines) - 1)
            if not na(oldest_line)
                line.delete(oldest_line)
            array.pop(vertical_lines)

    // Clear old labels when a new month starts
    // We only want to keep the current month's labels
    if not barstate.isfirst
        safe_delete_labels(tmo_labels)
        safe_delete_labels(full_amr_up_labels)
        safe_delete_labels(full_amr_down_labels)
        safe_delete_labels(one_third_amr_up_labels)
        safe_delete_labels(one_third_amr_down_labels)
        safe_delete_labels(two_third_amr_up_labels)
        safe_delete_labels(two_third_amr_down_labels)
        safe_delete_labels(half_amr_up_labels)
        safe_delete_labels(half_amr_down_labels)

// Store whether we've already drawn lines for this bar
var bool lines_drawn_this_bar = false
// Store the last AMR period used to detect changes
var int last_amr_period = amr_period

// Draw or update lines on the last bar, but only once per bar or when settings change
if (barstate.islast or (show_historical and barstate.ishistory)) and array.size(monthly_timestamps) > 0 and
   (not lines_drawn_this_bar or barstate.isconfirmed or last_amr_period != amr_period)
    // Reset the flag at the beginning of a new bar
    if barstate.isconfirmed
        lines_drawn_this_bar := false

    // Set the flag to indicate we've drawn lines for this bar
    if not lines_drawn_this_bar
        lines_drawn_this_bar := true

        // Get the current and previous month timestamps
        current_month_time = array.get(monthly_timestamps, 0)

        // End position for previous month's lines
        var int prev_end_bar = na

        // If we have more than one month, set the end position for previous month's lines
        if array.size(monthly_timestamps) > 1
            prev_month_time = array.get(monthly_timestamps, 1)
            for i = 0 to math.min(bar_index, 9999)  // Limit to 9999 to avoid history-referencing error
                if time[i] <= current_month_time and time[i] > prev_month_time
                    prev_end_bar := bar_index - i
                    break

        // Only clear and redraw lines when necessary (new month, first calculation, or AMR period change)
        if is_new_month or barstate.isfirst or array.size(tmo_lines) == 0 or last_amr_period != amr_period
            // Clear all existing lines before redrawing
            safe_delete_lines(tmo_lines)
            safe_delete_lines(full_amr_up_lines)
            safe_delete_lines(full_amr_down_lines)
            safe_delete_lines(one_third_amr_up_lines)
            safe_delete_lines(one_third_amr_down_lines)
            safe_delete_lines(two_third_amr_up_lines)
            safe_delete_lines(two_third_amr_down_lines)
            safe_delete_lines(half_amr_up_lines)
            safe_delete_lines(half_amr_down_lines)

        // Draw lines for each month
        for i = 0 to math.min(array.size(monthly_tmo_values) - 1, max_periods - 1)
            // Get month data
            month_time = array.get(monthly_timestamps, i)
            tmo = array.get(monthly_tmo_values, i)

            // Find the start bar for this month
            start_bar = bar_index
            for j = 0 to math.min(bar_index, 9999)  // Limit to 9999 to avoid history-referencing error
                if time[j] <= month_time
                    start_bar := bar_index - j
                    break

            // Determine end bar for this month's lines
            end_bar = i == 0 ? bar_index + 5 :
                     na(prev_end_bar) ? bar_index : prev_end_bar  // Current month extends 5 bars to the right, previous months end at next month start

            // Only draw if we have valid start and end positions
            if not na(start_bar) and not na(end_bar) and start_bar <= end_bar
                // Draw true month open line
                tmo_line = draw_line(tmo, true_month_open_color, line_style, start_bar, end_bar)
                if not na(tmo_line)
                    array.push(tmo_lines, tmo_line)

                // Draw true month open label (only for current month)
                if i == 0 and show_labels
                    // Create label if it doesn't exist, otherwise update existing label
                    if array.size(tmo_labels) == 0
                        tmo_label = draw_label(tmo, "TMO", true_month_open_color, end_bar)
                        if not na(tmo_label)
                            array.push(tmo_labels, tmo_label)
                    else
                        existing_label = array.get(tmo_labels, 0)
                        if not na(existing_label)
                            label.set_x(existing_label, end_bar)
                            label.set_y(existing_label, tmo + label_y_offset)

                // Draw AMR levels if we have the range data
                if i < array.size(monthly_ranges)
                    float amr = array.get(monthly_ranges, i)

                    // Calculate levels for this month
                    float full_up = tmo + amr
                    float full_down = tmo - amr
                    float one_third_up = tmo + (amr / 3)
                    float one_third_down = tmo - (amr / 3)
                    float two_third_up = tmo + (amr * 2 / 3)
                    float two_third_down = tmo - (amr * 2 / 3)
                    float half_up = tmo + (amr / 2)
                    float half_down = tmo - (amr / 2)

                    // Draw full AMR levels
                    if show_full_amr
                        // Draw full AMR+ line
                        full_up_line = draw_line(full_up, amr_color, line_style, start_bar, end_bar)
                        if not na(full_up_line)
                            array.push(full_amr_up_lines, full_up_line)

                        // Draw full AMR+ label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(full_amr_up_labels) == 0
                                full_up_label = draw_label(full_up, "AMR+", amr_color, end_bar)
                                if not na(full_up_label)
                                    array.push(full_amr_up_labels, full_up_label)
                            else
                                existing_label = array.get(full_amr_up_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, full_up + label_y_offset)

                        // Draw full AMR- line
                        full_down_line = draw_line(full_down, amr_color, line_style, start_bar, end_bar)
                        if not na(full_down_line)
                            array.push(full_amr_down_lines, full_down_line)

                        // Draw full AMR- label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(full_amr_down_labels) == 0
                                full_down_label = draw_label(full_down, "AMR-", amr_color, end_bar)
                                if not na(full_down_label)
                                    array.push(full_amr_down_labels, full_down_label)
                            else
                                existing_label = array.get(full_amr_down_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, full_down + label_y_offset)

                    // Draw 1/3 AMR levels
                    if show_one_third_amr
                        // Draw 1/3 AMR+ line
                        one_third_up_line = draw_line(one_third_up, amr_color, line_style, start_bar, end_bar)
                        if not na(one_third_up_line)
                            array.push(one_third_amr_up_lines, one_third_up_line)

                        // Draw 1/3 AMR+ label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(one_third_amr_up_labels) == 0
                                one_third_up_label = draw_label(one_third_up, "1/3+", amr_color, end_bar)
                                if not na(one_third_up_label)
                                    array.push(one_third_amr_up_labels, one_third_up_label)
                            else
                                existing_label = array.get(one_third_amr_up_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, one_third_up + label_y_offset)

                        // Draw 1/3 AMR- line
                        one_third_down_line = draw_line(one_third_down, amr_color, line_style, start_bar, end_bar)
                        if not na(one_third_down_line)
                            array.push(one_third_amr_down_lines, one_third_down_line)

                        // Draw 1/3 AMR- label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(one_third_amr_down_labels) == 0
                                one_third_down_label = draw_label(one_third_down, "1/3-", amr_color, end_bar)
                                if not na(one_third_down_label)
                                    array.push(one_third_amr_down_labels, one_third_down_label)
                            else
                                existing_label = array.get(one_third_amr_down_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, one_third_down + label_y_offset)

                    // Draw 2/3 AMR levels
                    if show_two_third_amr
                        // Draw 2/3 AMR+ line
                        two_third_up_line = draw_line(two_third_up, amr_color, line_style, start_bar, end_bar)
                        if not na(two_third_up_line)
                            array.push(two_third_amr_up_lines, two_third_up_line)

                        // Draw 2/3 AMR+ label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(two_third_amr_up_labels) == 0
                                two_third_up_label = draw_label(two_third_up, "2/3+", amr_color, end_bar)
                                if not na(two_third_up_label)
                                    array.push(two_third_amr_up_labels, two_third_up_label)
                            else
                                existing_label = array.get(two_third_amr_up_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, two_third_up + label_y_offset)

                        // Draw 2/3 AMR- line
                        two_third_down_line = draw_line(two_third_down, amr_color, line_style, start_bar, end_bar)
                        if not na(two_third_down_line)
                            array.push(two_third_amr_down_lines, two_third_down_line)

                        // Draw 2/3 AMR- label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(two_third_amr_down_labels) == 0
                                two_third_down_label = draw_label(two_third_down, "2/3-", amr_color, end_bar)
                                if not na(two_third_down_label)
                                    array.push(two_third_amr_down_labels, two_third_down_label)
                            else
                                existing_label = array.get(two_third_amr_down_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, two_third_down + label_y_offset)

                    // Draw 1/2 AMR levels
                    if show_half_amr
                        // Draw 1/2 AMR+ line
                        half_up_line = draw_line(half_up, amr_color, line_style, start_bar, end_bar)
                        if not na(half_up_line)
                            array.push(half_amr_up_lines, half_up_line)

                        // Draw 1/2 AMR+ label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(half_amr_up_labels) == 0
                                half_up_label = draw_label(half_up, "1/2+", amr_color, end_bar)
                                if not na(half_up_label)
                                    array.push(half_amr_up_labels, half_up_label)
                            else
                                existing_label = array.get(half_amr_up_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, half_up + label_y_offset)

                        // Draw 1/2 AMR- line
                        half_down_line = draw_line(half_down, amr_color, line_style, start_bar, end_bar)
                        if not na(half_down_line)
                            array.push(half_amr_down_lines, half_down_line)

                        // Draw 1/2 AMR- label (only for current month)
                        if i == 0 and show_labels
                            // Create label if it doesn't exist, otherwise update existing label
                            if array.size(half_amr_down_labels) == 0
                                half_down_label = draw_label(half_down, "1/2-", amr_color, end_bar)
                                if not na(half_down_label)
                                    array.push(half_amr_down_labels, half_down_label)
                            else
                                existing_label = array.get(half_amr_down_labels, 0)
                                if not na(existing_label)
                                    label.set_x(existing_label, end_bar)
                                    label.set_y(existing_label, half_down + label_y_offset)
