//@version=5
indicator("Multi-Timeframe Moving Averages", overlay=true)

// Inputs for Moving Averages
group_ma = 'Moving Averages Settings'
showMA = input.bool(true, 'Show Moving Averages', group=group_ma)
source = input.source(close, 'Source', group=group_ma)

// Current Timeframe Moving Averages
i_masw = input.bool(true, 'Show Current TF MAs', inline='MA', group=group_ma)
i_ma20C = input.color(color.blue, 'SMA 20 Color', inline='MA', group=group_ma)
i_ma50C = input.color(color.orange, 'SMA 50 Color', inline='MA', group=group_ma)
i_ma100C = input.color(color.green, 'SMA 100 Color', inline='MA', group=group_ma)
i_ma200C = input.color(color.red, 'SMA 200 Color', inline='MA', group=group_ma)

// Higher Timeframe Moving Averages
i_maswh_15 = input.bool(false, '15 Min', inline='HTF1', group=group_ma)
i_maswh_1h = input.bool(false, '1 Hour', inline='HTF2', group=group_ma)
i_maswh_2h = input.bool(false, '2 Hour', inline='HTF3', group=group_ma)
i_maswh_4h = input.bool(false, '4 Hour', inline='HTF4', group=group_ma)
i_maswh_1d = input.bool(false, '1 Day', inline='HTF5', group=group_ma)
i_ma20Ch  = input.color(#311B92, '', inline='MAHTF', group=group_ma)
i_ma50Ch  = input.color(#2196F3, '', inline='MAHTF', group=group_ma)
i_ma100Ch = input.color(#00BCD4, '', inline='MAHTF', group=group_ma)
i_ma200Ch = input.color(#FF9800, '', inline='MAHTF', group=group_ma)

maAlarm = input.bool(true, 'Enable Moving Average Alerts', group=group_ma)

// Function to calculate HTF MAs
f_get_htf_ma(htf) =>
    [ta.sma(source, 20), ta.sma(source, 50), ta.sma(source, 100), ta.sma(source, 200)]

// Request HTF MAs
[hsma20_15, hsma50_15, hsma100_15, hsma200_15] = request.security(syminfo.tickerid, '15', f_get_htf_ma('15'))
[hsma20_1h, hsma50_1h, hsma100_1h, hsma200_1h] = request.security(syminfo.tickerid, '60', f_get_htf_ma('60'))
[hsma20_2h, hsma50_2h, hsma100_2h, hsma200_2h] = request.security(syminfo.tickerid, '120', f_get_htf_ma('120'))
[hsma20_4h, hsma50_4h, hsma100_4h, hsma200_4h] = request.security(syminfo.tickerid, '240', f_get_htf_ma('240'))
[hsma20_1d, hsma50_1d, hsma100_1d, hsma200_1d] = request.security(syminfo.tickerid, 'D', f_get_htf_ma('D'))

// Plot Current Timeframe MAs
sma20 = ta.sma(source, 20)
sma50 = ta.sma(source, 50)
sma100 = ta.sma(source, 100)
sma200 = ta.sma(source, 200)
plot(showMA and i_masw ? sma20 : na, title='SMA 20', color=i_ma20C, linewidth=1)
plot(showMA and i_masw ? sma50 : na, title='SMA 50', color=i_ma50C, linewidth=1)
plot(showMA and i_masw ? sma100 : na, title='SMA 100', color=i_ma100C, linewidth=1)
plot(showMA and i_masw ? sma200 : na, title='SMA 200', color=i_ma200C, linewidth=1)

// Multipliers for each timeframe in minutes
multiplier_15 = 15
multiplier_1h = 60
multiplier_2h = 120
multiplier_4h = 240
multiplier_1d = 1440

// Plot Higher Timeframe MAs (15 Min)
plot(i_maswh_15 and 20 * multiplier_15 / timeframe.multiplier < 5000 ? ta.sma(source, 20 * multiplier_15 / timeframe.multiplier) : na, 
     title='HTF Smooth SMA (20)(15 Min)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_15 and 20 * multiplier_15 / timeframe.multiplier > 5000 ? hsma20_15 : na, 
     title='HTF SMA 20 (15 Min)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_15 and 50 * multiplier_15 / timeframe.multiplier > 5000 ? hsma50_15 : na, 
     title='HTF SMA 50 (15 Min)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_15 and 100 * multiplier_15 / timeframe.multiplier > 5000 ? hsma100_15 : na, 
     title='HTF SMA 100 (15 Min)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_15 and 200 * multiplier_15 / timeframe.multiplier > 5000 ? hsma200_15 : na, 
     title='HTF SMA 200 (15 Min)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (1 Hour)
plot(i_maswh_1h and 20 * multiplier_1h / timeframe.multiplier < 5000 ? ta.sma(source, 20 * multiplier_1h / timeframe.multiplier) : na, 
     title='HTF Smooth SMA (20)(1 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_1h and 20 * multiplier_1h / timeframe.multiplier > 5000 ? hsma20_1h : na, 
     title='HTF SMA 20 (1 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_1h and 50 * multiplier_1h / timeframe.multiplier > 5000 ? hsma50_1h : na, 
     title='HTF SMA 50 (1 Hour)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_1h and 100 * multiplier_1h / timeframe.multiplier > 5000 ? hsma100_1h : na, 
     title='HTF SMA 100 (1 Hour)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_1h and 200 * multiplier_1h / timeframe.multiplier > 5000 ? hsma200_1h : na, 
     title='HTF SMA 200 (1 Hour)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (2 Hour)
plot(i_maswh_2h and 20 * multiplier_2h / timeframe.multiplier < 5000 ? ta.sma(source, 20 * multiplier_2h / timeframe.multiplier) : na, 
     title='HTF Smooth SMA (20)(2 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_2h and 20 * multiplier_2h / timeframe.multiplier > 5000 ? hsma20_2h : na, 
     title='HTF SMA 20 (2 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_2h and 50 * multiplier_2h / timeframe.multiplier > 5000 ? hsma50_2h : na, 
     title='HTF SMA 50 (2 Hour)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_2h and 100 * multiplier_2h / timeframe.multiplier > 5000 ? hsma100_2h : na, 
     title='HTF SMA 100 (2 Hour)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_2h and 200 * multiplier_2h / timeframe.multiplier > 5000 ? hsma200_2h : na, 
     title='HTF SMA 200 (2 Hour)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (4 Hour)
plot(i_maswh_4h and 20 * multiplier_4h / timeframe.multiplier < 5000 ? ta.sma(source, 20 * multiplier_4h / timeframe.multiplier) : na, 
     title='HTF Smooth SMA (20)(4 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_4h and 20 * multiplier_4h / timeframe.multiplier > 5000 ? hsma20_4h : na, 
     title='HTF SMA 20 (4 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_4h and 50 * multiplier_4h / timeframe.multiplier > 5000 ? hsma50_4h : na, 
     title='HTF SMA 50 (4 Hour)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_4h and 100 * multiplier_4h / timeframe.multiplier > 5000 ? hsma100_4h : na, 
     title='HTF SMA 100 (4 Hour)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_4h and 200 * multiplier_4h / timeframe.multiplier > 5000 ? hsma200_4h : na, 
     title='HTF SMA 200 (4 Hour)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (1 Day)
plot(i_maswh_1d and 20 * multiplier_1d / timeframe.multiplier < 5000 ? ta.sma(source, 20 * multiplier_1d / timeframe.multiplier) : na, 
     title='HTF Smooth SMA (20)(1 Day)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_1d and 20 * multiplier_1d / timeframe.multiplier > 5000 ? hsma20_1d : na, 
     title='HTF SMA 20 (1 Day)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_1d and 50 * multiplier_1d / timeframe.multiplier > 5000 ? hsma50_1d : na, 
     title='HTF SMA 50 (1 Day)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_1d and 100 * multiplier_1d / timeframe.multiplier > 5000 ? hsma100_1d : na, 
     title='HTF SMA 100 (1 Day)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_1d and 200 * multiplier_1d / timeframe.multiplier > 5000 ? hsma200_1d : na, 
     title='HTF SMA 200 (1 Day)', color=i_ma200Ch, linewidth=2)