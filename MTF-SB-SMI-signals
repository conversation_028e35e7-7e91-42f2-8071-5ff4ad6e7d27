//@version=5
indicator("Silver Bullet - Combined FVG & Signals", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// === Inputs ===
selectedTF         = input.string("1", "Selected FVG Timeframe", options=["1", "3", "5", "15"], group="Session")
showSB             = input.bool(true,     "Show 10–11 AM Session",        group="Session")
showHistoricalFVGs = input.bool(true,     "Show Historical FVGs",         group="Session")
extendLine         = input.bool(true,     "Extend Vertical Separators",   group="Session")
extendHLine        = input.bool(true,     "Extend Historical Lines",      group="Session & FVG")
boxShift           = input.int(1,         "Shift FVG Box Left", minval=0, group="Session")
historyDays        = input.int(5,         "Look-back (Days)", minval=1, maxval=500, group="Session & FVG")
labelSizeOpt       = input.string("Tiny", "Session Label Size", options=["Auto","Tiny","Small","Normal","Large","Huge"], group="Session")
labelColor         = input.color(color.white, "Session Label Color",       group="Session")
labelXOffset       = input.int(20,        "Session Label X Offset", minval=0,   group="Session")
labelYOffset       = input.float(0.0,     "Session Label Y Offset",           group="Session")
flipSource         = input.string("Wick", "Flip Source", options=["Wick","Close"], group="FVG Style")

// === Trading Signal Inputs ===
tradeLabelSize     = input.string("Normal", "Trade Label Size", options=["Tiny","Small","Normal","Large","Huge"], group="Trading Signals")
debugLabels        = input.bool(false,    "Show Debug Labels",                 group="Debug")
debugNumbering     = input.bool(false,    "Show Numbering Debug",              group="Debug")
showEngulfingDebug = input.bool(false,    "Show Engulfing Debug",              group="Debug")

// === FVG Styles ===
currentFVGCol = input.color(#e8ed5e89,    "Current FVG Color",   group="Colors")
histFVGCol    = input.color(color.rgb(94, 134, 237, 80), "Historical FVG Color", group="Colors")
midlineColor  = input.color(#787b86,      "Midline Color", group="Colors")
invisBorder   = color.new(color.black, 100)

// === Label size mapping ===
labelSizeMap(x) =>
    switch x
        'Auto'   => size.auto
        'Tiny'   => size.tiny
        'Small'  => size.small
        'Normal' => size.normal
        'Large'  => size.large
        'Huge'   => size.huge

// === Session logic ===
timeSess(tf, s) => time(tf, s, "America/New_York")
SB_AM   = timeSess(timeframe.period, "1000-1100")
strAM   = SB_AM and not SB_AM[1]
n       = bar_index
minT    = syminfo.mintick
inAM    = timeSess(selectedTF, "1000-1100")
after10 = inAM and not strAM

// === Pull wick/close from selectedTF ===
[lf_low, lf_high, lf_close] = request.security(syminfo.tickerid, selectedTF, [low, high, close])

// === Session separators & storage ===
var line    vLine        = na
var line[]  sessHLines   = array.new_line()
var int[]   sessHTimes   = array.new_int()
var float[] sessHPrices  = array.new_float()
var label[] sessHLabels  = array.new_label()

// === Current session-line vars ===
var line   hLine   = na
var label  hLabel  = na
var float  hPrice  = na
var int    hStart  = na

// ── On new 10:00 AM session ──
if strAM and showSB
    vLine := line.new(n, close, n, close + minT, color=color.white, extend=extendLine ? extend.both : extend.none)
    if not na(hLine)
        line.set_extend(hLine, extend.none)
        line.set_x2(hLine, n)
        line.set_y2(hLine, hPrice)
        array.push(sessHLines, hLine)
        array.push(sessHTimes, time)
        array.push(sessHPrices, hPrice)
        array.push(sessHLabels, hLabel)
    hStart := n
    hPrice := open
    hLine  := line.new(hStart, hPrice, n, hPrice, color=color.white, style=line.style_dotted, extend=extend.none)
    d = dayofmonth < 10 ? "0"+str.tostring(dayofmonth) : str.tostring(dayofmonth)
    m = month      < 10 ? "0"+str.tostring(month)      : str.tostring(month)
    y = (year%100) < 10  ? "0"+str.tostring(year%100)   : str.tostring(year%100)
    dateStr = d + "/" + m + "/" + y
    hLabel := label.new(x=n+labelXOffset, y=hPrice+labelYOffset, text=dateStr, xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_right, size=labelSizeMap(labelSizeOpt), textcolor=labelColor, color=color.new(color.white,100))

// ── Update current session horizontal each bar ──
if not na(hLine)
    line.set_extend(hLine, extend.none)
    line.set_x2(hLine, n)
    line.set_y2(hLine, hPrice)
    label.set_x(hLabel, n+labelXOffset)
    label.set_y(hLabel, hPrice+labelYOffset)

// === FVG detection on 1-min ===
fvgDetect() =>
    bull = low > high[2] and close[1] > high[2]
    bear = high < low[2]  and close[1] < low[2]
    top  = bull ? low     : bear ? low[2]     : na
    bot  = bull ? high[2] : bear ? high        : na
    [bull, bear, top, bot]
[bull, bear, top, bot] = request.security(syminfo.tickerid, selectedTF, fvgDetect())

// === Historical FVG storage ===
var box[]   fvgBoxes = array.new_box()
var line[]  fvgMids  = array.new_line()
var bool[]  fvgBull  = array.new_bool()
var int[]   fvgTimes = array.new_int()

// === Single-session FVG state ===
var box    currBox  = na
var line   currMid  = na
var bool   drawn    = false
var bool   flipped  = false
var float  fvgTop   = na
var float  fvgBot   = na
var bool   isBull   = na

// === Trading Signal State Variables ===
var bool   swingConfirmed = false
var bool   tapped       = false
var int    tapBar       = na
var bool   waitEngulfing = false
var int    candlesSinceTap = 0
var float  lastUpLow     = na  // low of last up‑close candle
var float  lastDownHigh  = na  // high of last down‑close candle

// ── At 10:00 AM stash last FVG & reset ──
if strAM
    if not na(currBox)
        array.push(fvgBoxes, currBox)
        array.push(fvgMids,   currMid)
        array.push(fvgBull,   isBull)
        array.push(fvgTimes,  time)
    drawn   := false
    flipped := false
    currBox := na
    currMid := na
    fvgTop  := na
    fvgBot  := na
    isBull  := na
    // Reset trading signal variables
    swingConfirmed := false
    tapped         := false
    waitEngulfing  := false
    candlesSinceTap := 0
    lastUpLow      := na
    lastDownHigh   := na

// ── Draw first FVG after 10:00 AM ──
if after10 and not drawn and (bull or bear)
    isBull  := bull
    fvgTop  := top
    fvgBot  := bot
    currBox := box.new(n-boxShift, fvgTop, n, fvgBot, border_color=invisBorder, bgcolor=currentFVGCol)
    currMid := line.new(n-boxShift, (fvgTop+fvgBot)/2, n, (fvgTop+fvgBot)/2, color=midlineColor, style=line.style_dashed)
    drawn   := true
    flipped := false
    if debugLabels
        label.new(bar_index,
                  (fvgTop+fvgBot)/2,
                  isBull ? "Bull FVG" : "Bear FVG",
                  xloc.bar_index,
                  yloc.price,
                  style     = label.style_label_center,
                  textcolor = color.white,
                  color     = isBull ? color.blue : color.orange,
                  size      = labelSizeMap("Small"))

// ── Extend & flip FVG mid-line ──
if drawn and not na(currBox)
    box.set_right(currBox, n)
    line.set_x2(currMid, n)
    if not flipped
        srcLow  = flipSource == "Wick" ? lf_low   : lf_close
        srcHigh = flipSource == "Wick" ? lf_high  : lf_close
        if isBull and srcLow < fvgBot
            box.set_bgcolor(currBox, currentFVGCol)
            box.set_border_color(currBox, invisBorder)
            flipped := true
        else if not isBull and srcHigh > fvgTop
            box.set_bgcolor(currBox, currentFVGCol)
            box.set_border_color(currBox, invisBorder)
            flipped := true

// ── Detect the FVG "tap" for trading signals ──
if drawn and not tapped and after10
    // first confirm swing‑away
    if not swingConfirmed and ((isBull and close > fvgTop) or (not isBull and close < fvgBot))
        swingConfirmed := true
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Swing Confirmed",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.black,
                      color     = color.yellow,
                      size      = labelSizeMap("Small"))
    // then the actual tap
    else if swingConfirmed and ((isBull and low <= fvgTop) or (not isBull and high >= fvgBot))
        tapBar           := bar_index
        waitEngulfing    := true
        candlesSinceTap  := 0
        tapped           := true
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Tapped FVG",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.blue,
                      color     = color.new(color.white,90),
                      size      = labelSizeMap("Small"))
        if debugNumbering
            label.new(bar_index,
                      high + 20 * minT,
                      "TAP: Starting numbering from bar " + str.tostring(bar_index),
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.white,
                      color     = color.purple,
                      size      = labelSizeMap("Small"))

// ── WAIT UP TO 10 BARS FOR TRUE ENGULFING ──
if waitEngulfing
    barsSince = bar_index - tapBar

    // update last opposite‑close extremes
    if close > open
        lastUpLow := low
    else if close < open
        lastDownHigh := high

    if barsSince <= 10
        // BUY: bullish engulf = green close above last down-candle's high
        if isBull and close > open and not na(lastDownHigh) and close >= lastDownHigh
            label.new(bar_index, low - 10*minT,
                      "Buy",
                      style=label.style_label_up,
                      textcolor=color.white,
                      color=color.green,
                      size=labelSizeMap(tradeLabelSize))
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "Bullish Engulfing: Green close above last red high",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.green,
                         size      = labelSizeMap("Small"))

        // SELL: bearish engulf = red close below last up-candle's low
        else if not isBull and close < open and not na(lastUpLow) and close <= lastUpLow
            label.new(bar_index, high + 10*minT,
                      "Sell",
                      style=label.style_label_down,
                      textcolor=color.white,
                      color=color.red,
                      size=labelSizeMap(tradeLabelSize))
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "Bearish Engulfing: Red close below last green low",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.red,
                         size      = labelSizeMap("Small"))

    else
        // no engulf within 10 bars → give up
        waitEngulfing := false
        lastUpLow := na
        lastDownHigh := na
        if showEngulfingDebug
            label.new(bar_index, (fvgTop+fvgBot)/2, "No Engulfing Found - Stopped after 10 candles",
                     xloc.bar_index, yloc.price,
                     style     = label.style_label_center,
                     textcolor = color.white,
                     color     = color.gray,
                     size      = labelSizeMap("Small"))

// ── Render last X days of historical FVGs ──
if array.size(fvgBoxes) > 0 and showHistoricalFVGs
    cutoffTime = time - historyDays * 86400000
    for i = 0 to array.size(fvgBoxes) - 1
        if array.get(fvgTimes, i) >= cutoffTime
            b = array.get(fvgBoxes, i)
            m = array.get(fvgMids,  i)
            box.set_right(b, n)
            box.set_bgcolor(b, histFVGCol)
            line.set_x2(m, n)

// ── Extend & label last X days of session horizontals ──
if array.size(sessHLines) > 0 and extendHLine
    cutoffSess = time - historyDays * 86400000
    for i = 0 to array.size(sessHLines) - 1
        if array.get(sessHTimes, i) >= cutoffSess
            ln    = array.get(sessHLines,  i)
            price = array.get(sessHPrices, i)
            lb    = array.get(sessHLabels, i)
            line.set_extend(ln, extend.none)
            line.set_x2(ln, n)
            line.set_y2(ln, price)
            label.set_x(lb, n + labelXOffset)
            label.set_y(lb, price + labelYOffset)

// ── Timeframe warning ──
var table tab = table.new(position=position.top_right, columns=1, rows=1)
if barstate.islast and timeframe.in_seconds(timeframe.period) > 15 * 60
    table.cell(tab, 0, 0, "Use timeframe ≤ 15 min", text_color=color.red)




//smi indicator
x = input.int(25, "Index Period", minval = 1)
rr = input.int(14, "Volume Flow Period", minval = 1)
peakslen = input.int(500, "Normalization Period", minval = 1)
thr = input.float(0.9, "High Interest Threshold", minval = 0.01, maxval = 0.99)
green = input.color(#00ffbb, "Up Color")
red = input.color(#ff1100, "Down Color")

dumb = ta.pvi-ta.ema(ta.pvi,255)
smart = ta.nvi-ta.ema(ta.nvi,255)

drsi = ta.rsi(dumb, rr)
srsi = ta.rsi(smart, rr)

r = srsi/drsi //ratio shows if smart money is buying from dumb money selling and vice versa

sums = math.sum(r, x)
peak = ta.highest(sums, peakslen)

index = sums/peak

condition = index > thr
// barcolor(condition ? green : na)

// Main Panel Arrows
plotshape(series= condition ? 1 : na, title="High Smart Money Interest", color=color.rgb(233, 239, 233), style=shape.arrowup, size=size.normal, location=location.belowbar, force_overlay=true)

// Alert condition
alertcondition(condition=condition, title="High Smart Money Interest Alert", message="High Smart Money Interest detected!")



