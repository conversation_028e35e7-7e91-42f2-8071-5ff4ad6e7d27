//@version=5
// ==================== VWAP-FVG-Oscar Intraday Strategy ==================================================
// Description: This strategy combines volume-based mean reversion (VWAP), institutional value areas (Fair Value Gaps),
// smart entry confirmation (Oscar signals), and time-based precision (ICT Killzones) to identify high-probability trade setups.
//
// Buy Setup:
// 1. Timing: Trade setup must occur at start of a 4-hour candle or start of a kill zone session.
// 2. Price Action:
//    - Price must go below the VWAP.
//    - Tap into a bullish Fair Value Gap (FVG).
//    - Form a bullish Oscar signal (up arrow or triangle).
// 3. Entry: Enter the trade on confirmation of Oscar signal.
// 4. Take Profit:
//    - If previous candle closed bearish (opposite direction): Aim for Risk:Reward of 1:3.
//    - If previous candle closed bullish (same direction): Target nearest session high.
// 5. Stop Loss: 250 ticks
//
// Sell Setup:
// 1. Timing: Trade setup must occur at start of a 4-hour candle or start of a kill zone session.
// 2. Price Action:
//    - Price must go above the VWAP.
//    - Tap into a bearish Fair Value Gap (FVG).
//    - Form a bearish Oscar signal (down arrow or triangle).
// 3. Entry: Enter the trade on confirmation of Oscar signal.
// 4. Take Profit:
//    - If previous candle closed bullish (opposite direction): Aim for Risk:Reward of 1:3.
//    - If previous candle closed bearish (same direction): Target nearest session low.
// 5. Stop Loss: 250 ticks
//
// Author: Trading Strategy Developer
// Date: 2023

strategy("VWAP-FVG-Oscar Intraday Strategy",
     overlay=true,
     max_bars_back=5000,
     max_boxes_count=500,
     max_lines_count=500,
     max_labels_count=500,
     initial_capital=10000,
     default_qty_type=strategy.percent_of_equity,
     default_qty_value=100,
     commission_type=strategy.commission.percent,
     commission_value=0.1,
     pyramiding=0,
     calc_on_every_tick=true,
     calc_on_order_fills=true,
     slippage=10)

// ==================== Input Parameters ==================================================

// Strategy Settings
var strategy_group = "Strategy Settings"
tick_size = input.float(0.01, "Tick Size", minval=0.00001, group=strategy_group)
stop_loss_ticks = input.int(250, "Stop Loss (Ticks)", minval=1, group=strategy_group)
risk_reward_ratio = input.float(3.0, "Risk:Reward Ratio", minval=0.1, step=0.1, group=strategy_group)
max_trades_per_day = input.int(2, "Max Trades Per Day", minval=1, maxval=10, group=strategy_group)
max_daily_loss = input.float(-2.0, "Max Daily Loss %", maxval=0, group=strategy_group)
show_debug = input.bool(true, "Show Debug Info", group=strategy_group)

// VWAP Settings
var vwap_group = "VWAP Settings"
enable_vwap = input.bool(true, "Enable VWAP", group=vwap_group)
src = input.source(hlc3, "Source", group=vwap_group)
hideonDWM = input.bool(false, "Hide on Daily/Weekly/Monthly", group=vwap_group)
vwap_deviation = input.float(0.1, "VWAP Deviation %", minval=0.01, step=0.01, group=vwap_group)

// Session VWAP
show_session = input.bool(true, "Session VWAP", inline="session_vwap", group=vwap_group)
session_color = input.color(#2962FF, "", inline="session_vwap", group=vwap_group)

// Week VWAP
show_week = input.bool(false, "Week VWAP", inline="week_vwap", group=vwap_group)
week_color = input.color(#B71C1C, "", inline="week_vwap", group=vwap_group)

// Month VWAP
show_month = input.bool(false, "Month VWAP", inline="month_vwap", group=vwap_group)
month_color = input.color(#00796B, "", inline="month_vwap", group=vwap_group)

// Oscar Settings
var oscar_group = "Oscar Settings"
enable_oscar = input.bool(true, "Enable Oscar", group=oscar_group)
len = input.int(8, "Oscar Candles", minval=1, group=oscar_group)
smoothing = input.string("RMA", "Oscar Smoothing", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"], group=oscar_group)
crossSignalSensitivity = input.float(0.5, "Cross Signal Sensitivity", step=0.1, group=oscar_group)
OscarOfIndicator = input.source(close, "Choose input type", group=oscar_group)
bottomSignalLine = input.int(35, "Bottom Signal Line", step=5, group=oscar_group)
topSignalLine = input.int(65, "Top Signal Line", step=5, group=oscar_group)
showValues = input.bool(false, "Show Oscar Values", group=oscar_group)

// FVG Settings
var fvg_group = "Fair Value Gap Settings"
enable_fvg = input.bool(true, "Enable FVG", group=fvg_group)
fvg_tf = input.timeframe("", "FVG Timeframe", group=fvg_group)
fvg_extend = input.int(20, "FVG Extension", minval=0, group=fvg_group)
fvg_auto = input.bool(true, "Auto Threshold", group=fvg_group)
bull_fvg_css = input.color(color.new(#089981, 70), "Bullish FVG", group=fvg_group)
bear_fvg_css = input.color(color.new(#f23645, 70), "Bearish FVG", group=fvg_group)

// ICT Killzone Settings
var killzone_group = "ICT Killzone Settings"
enable_killzones = input.bool(true, "Enable Killzones", group=killzone_group)
gmt_tz = input.string('America/New_York', "Timezone", options=['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], group=killzone_group)

// Asia Session
use_asia = input.bool(true, "", inline="ASIA", group=killzone_group)
as_txt = input.string("Asia", "", inline="ASIA", group=killzone_group)
asia = input.session("2000-0000", "", inline="ASIA", group=killzone_group)
as_color = input.color(color.blue, "", inline="ASIA", group=killzone_group)

// London Session
use_london = input.bool(true, "", inline="LONDON", group=killzone_group)
lo_txt = input.string("London", "", inline="LONDON", group=killzone_group)
london = input.session("0200-0500", "", inline="LONDON", group=killzone_group)
lo_color = input.color(color.red, "", inline="LONDON", group=killzone_group)

// NY AM Session
use_nyam = input.bool(true, "", inline="NYAM", group=killzone_group)
na_txt = input.string("NY AM", "", inline="NYAM", group=killzone_group)
nyam = input.session("0930-1100", "", inline="NYAM", group=killzone_group)
na_color = input.color(#089981, "", inline="NYAM", group=killzone_group)

// ==================== Variable Declarations ==================================================

// Time variables
var int last_session_start_bar = 0
var int last_4hr_start_bar = 0
var int strategy_trades_today = 0
var float strategy_daily_profit = 0.0
var int strategy_last_trade_day = 0
var int last_trade_time = 0

// VWAP variables
float sessionVwap = na
float weekVwap = na
float monthVwap = na

// Oscar variables
var label valueLabel = na

// FVG variables
var bullish_fvg_max = array.new<box>()
var bullish_fvg_min = array.new<box>()
var bearish_fvg_max = array.new<box>()
var bearish_fvg_min = array.new<box>()

// ==================== Function Definitions ==================================================

// MA function for Oscar
ma(smoothing, OscarParam, len) =>
    if smoothing == "RMA"
        ta.rma(OscarParam, len)
    else if smoothing == "SMA"
        ta.sma(OscarParam, len)
    else if smoothing == "EMA"
        ta.ema(OscarParam, len)
    else if smoothing == "WMA"
        ta.wma(OscarParam, len)
    else if smoothing == "LSMA"
        ta.linreg(OscarParam, len, 0)
    else if smoothing == "HMA"
        ta.hma(OscarParam, len)
    else
        OscarParam

// ==================== Main Calculations ==================================================

// Define new period conditions for VWAP
isNewSession = timeframe.change("D")
isNewWeek = timeframe.change("W")
isNewMonth = timeframe.change("M")

// Handle data gaps
if na(src[1])
    isNewSession := true
    isNewWeek := true
    isNewMonth := true

// Calculate VWAPs if enabled
if enable_vwap and not (hideonDWM and timeframe.isdwm)
    // Session VWAP
    if show_session
        var float sumSrc = 0.0
        var float sumVol = 0.0

        if isNewSession
            sumSrc := src * volume
            sumVol := volume
        else
            sumSrc += src * volume
            sumVol += volume

        sessionVwap := sumVol != 0 ? sumSrc / sumVol : na

    // Week VWAP
    if show_week
        var float sumSrcWeek = 0.0
        var float sumVolWeek = 0.0

        if isNewWeek
            sumSrcWeek := src * volume
            sumVolWeek := volume
        else
            sumSrcWeek += src * volume
            sumVolWeek += volume

        weekVwap := sumVolWeek != 0 ? sumSrcWeek / sumVolWeek : na

    // Month VWAP
    if show_month
        var float sumSrcMonth = 0.0
        var float sumVolMonth = 0.0

        if isNewMonth
            sumSrcMonth := src * volume
            sumVolMonth := volume
        else
            sumSrcMonth += src * volume
            sumVolMonth += volume

        monthVwap := sumVolMonth != 0 ? sumSrcMonth / sumVolMonth : na

// Oscar calculation
A = ta.highest(OscarOfIndicator, len)
B = ta.lowest(OscarOfIndicator, len)
OscarRough = (OscarOfIndicator - B) / (A - B) * 100
Oscar1 = (OscarRough[1] / 3) * 2
OscarThird = OscarRough / 3
Oscar = Oscar1 + OscarThird
smoothedOscarRough = ma(smoothing, OscarRough, len)
smoothedOscar = ma(smoothing, Oscar, len)

// Cross sensitivity calculation
crossSensitivity = math.max(smoothedOscarRough, smoothedOscar) - math.min(smoothedOscarRough, smoothedOscar)

// Oscar Signal conditions
buySignal1 = ta.crossover(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough < bottomSignalLine
buySignal2 = ta.crossover(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > smoothedOscar and smoothedOscar < bottomSignalLine

sellSignal1 = ta.crossunder(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > topSignalLine
sellSignal2 = ta.crossunder(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscar > smoothedOscarRough and smoothedOscar > topSignalLine

// FVG detection
if enable_fvg
    src_o1 = request.security(syminfo.tickerid, fvg_tf, open[1])
    src_h1 = request.security(syminfo.tickerid, fvg_tf, high[1])
    src_l1 = request.security(syminfo.tickerid, fvg_tf, low[1])
    src_c1 = request.security(syminfo.tickerid, fvg_tf, close[1])
    src_h2 = request.security(syminfo.tickerid, fvg_tf, high[2])
    src_l2 = request.security(syminfo.tickerid, fvg_tf, low[2])
    src_h = request.security(syminfo.tickerid, fvg_tf, high)
    src_l = request.security(syminfo.tickerid, fvg_tf, low)

    delta_per = (src_c1 - src_o1) / src_o1 * 100
    change_tf = timeframe.change(fvg_tf)
    threshold = fvg_auto ? ta.cum(math.abs(change_tf ? delta_per : 0)) / bar_index * 2 : 0

    // FVG conditions
    bullish_fvg_cnd = src_l > src_h2 and src_c1 > src_h2 and delta_per > threshold and change_tf
    bearish_fvg_cnd = src_h < src_l2 and src_c1 < src_l2 and -delta_per > threshold and change_tf

    // FVG Areas
    if bullish_fvg_cnd
        array.unshift(bullish_fvg_max, box.new(bar_index-1, src_l, bar_index + fvg_extend, math.avg(src_l, src_h2), border_color = bull_fvg_css, bgcolor = bull_fvg_css))
        array.unshift(bullish_fvg_min, box.new(bar_index-1, math.avg(src_l, src_h2), bar_index + fvg_extend, src_h2, border_color = bull_fvg_css, bgcolor = bull_fvg_css))

    if bearish_fvg_cnd
        array.unshift(bearish_fvg_max, box.new(bar_index-1, src_l2, bar_index + fvg_extend, math.avg(src_h, src_l2), border_color = bear_fvg_css, bgcolor = bear_fvg_css))
        array.unshift(bearish_fvg_min, box.new(bar_index-1, math.avg(src_h, src_l2), bar_index + fvg_extend, src_h, border_color = bear_fvg_css, bgcolor = bear_fvg_css))

// ICT Killzone session detection
t_as = not na(time(timeframe.period, asia, gmt_tz))
t_lo = not na(time(timeframe.period, london, gmt_tz))
t_na = not na(time(timeframe.period, nyam, gmt_tz))

// 4-hour candle start detection
is_4h_start = timeframe.change("240")

// ==================== Strategy Implementation ==================================================

// Reset trades counter and daily profit on new day
if dayofweek != dayofweek[1]
    strategy_trades_today := 0
    strategy_last_trade_day := dayofweek
    strategy_daily_profit := 0.0

// Check if we're in an ICT Killzone session or at the start of a 4hr candle
strategy_in_killzone = enable_killzones and (t_as or t_lo or t_na)
strategy_is_4h_start = is_4h_start
strategy_in_session = strategy_in_killzone or strategy_is_4h_start

// Update session start trackers
if strategy_in_killzone and (last_session_start_bar != bar_index)
    last_session_start_bar := bar_index

if strategy_is_4h_start
    last_4hr_start_bar := bar_index

// Check if we're within 30 minutes of session or 4hr candle start
bars_per_30min = 30 * 60 * 1000 / timeframe.in_seconds() / 1000
strategy_within_30min_of_session = (bar_index - last_session_start_bar) < bars_per_30min
strategy_within_30min_of_4hr = (bar_index - last_4hr_start_bar) < bars_per_30min

// Track if we've already taken a signal in this session/4hr period
var bool strategy_signal_taken_this_session = false
var bool strategy_signal_taken_this_4hr = false

// Reset signal flags at new session/4hr
if strategy_in_killzone and not strategy_in_killzone[1]
    strategy_signal_taken_this_session := false

if strategy_is_4h_start
    strategy_signal_taken_this_4hr := false

// Check if price is near VWAP
strategy_price_near_session_vwap = show_session and not na(sessionVwap) ? math.abs(close - sessionVwap) / sessionVwap * 100 <= vwap_deviation : false
strategy_price_near_week_vwap = show_week and not na(weekVwap) ? math.abs(close - weekVwap) / weekVwap * 100 <= vwap_deviation : false
strategy_price_near_month_vwap = show_month and not na(monthVwap) ? math.abs(close - monthVwap) / monthVwap * 100 <= vwap_deviation : false

// Price is near any VWAP
strategy_price_near_vwap = strategy_price_near_session_vwap or strategy_price_near_week_vwap or strategy_price_near_month_vwap

// Check if price is tapping into FVG
strategy_in_bullish_fvg = false
strategy_in_bearish_fvg = false

// Simple check if current price is within any FVG
if enable_fvg
    // Check for bullish FVG
    if bullish_fvg_max.size() > 0
        for i = 0 to math.min(bullish_fvg_max.size() - 1, 5)  // Check only the 5 most recent FVGs
            box_max = bullish_fvg_max.get(i)
            box_min = bullish_fvg_min.get(i)

            box_top = box.get_top(box_max)
            box_bottom = box.get_bottom(box_min)

            if low <= box_top and high >= box_bottom
                strategy_in_bullish_fvg := true
                break

    // Check for bearish FVG
    if bearish_fvg_max.size() > 0
        for i = 0 to math.min(bearish_fvg_max.size() - 1, 5)  // Check only the 5 most recent FVGs
            box_max = bearish_fvg_max.get(i)
            box_min = bearish_fvg_min.get(i)

            box_top = box.get_top(box_max)
            box_bottom = box.get_bottom(box_min)

            if low <= box_top and high >= box_bottom
                strategy_in_bearish_fvg := true
                break

// Check if we can trade based on max daily loss
strategy_can_trade = strategy_daily_profit > (strategy.initial_capital * max_daily_loss / 100)

// Check if we're above or below VWAP
strategy_above_vwap = close > sessionVwap
strategy_below_vwap = close < sessionVwap

// Entry conditions
strategy_long_condition = (
    (strategy_within_30min_of_session and not strategy_signal_taken_this_session) or
    (strategy_within_30min_of_4hr and not strategy_signal_taken_this_4hr)
) and
buySignal1 and
strategy_below_vwap and
strategy_in_bullish_fvg and
strategy_can_trade and
strategy_trades_today < max_trades_per_day

strategy_short_condition = (
    (strategy_within_30min_of_session and not strategy_signal_taken_this_session) or
    (strategy_within_30min_of_4hr and not strategy_signal_taken_this_4hr)
) and
sellSignal1 and
strategy_above_vwap and
strategy_in_bearish_fvg and
strategy_can_trade and
strategy_trades_today < max_trades_per_day

// Calculate stop loss and take profit levels
strategy_stop_loss_long = close - (stop_loss_ticks * tick_size)
strategy_take_profit_long = close + (stop_loss_ticks * tick_size * risk_reward_ratio)

strategy_stop_loss_short = close + (stop_loss_ticks * tick_size)
strategy_take_profit_short = close - (stop_loss_ticks * tick_size * risk_reward_ratio)

// Adjust take profit based on previous candle direction
strategy_prev_candle_bullish = close[1] > open[1]
strategy_prev_candle_bearish = close[1] < open[1]

// For long entries, if previous candle was bearish, use risk:reward ratio
// If previous candle was bullish, target session high
if strategy_prev_candle_bearish
    strategy_take_profit_long := close + (stop_loss_ticks * tick_size * risk_reward_ratio)
else
    // Target session high (simplified for now)
    strategy_take_profit_long := ta.highest(high, 24)

// For short entries, if previous candle was bullish, use risk:reward ratio
// If previous candle was bearish, target session low
if strategy_prev_candle_bullish
    strategy_take_profit_short := close - (stop_loss_ticks * tick_size * risk_reward_ratio)
else
    // Target session low (simplified for now)
    strategy_take_profit_short := ta.lowest(low, 24)

// Execute strategy
if strategy_long_condition
    strategy.entry("Long", strategy.long)
    strategy.exit("Long Exit", "Long", stop=strategy_stop_loss_long, limit=strategy_take_profit_long)
    strategy_trades_today := strategy_trades_today + 1
    strategy_signal_taken_this_session := strategy_within_30min_of_session
    strategy_signal_taken_this_4hr := strategy_within_30min_of_4hr
    last_trade_time := time

if strategy_short_condition
    strategy.entry("Short", strategy.short)
    strategy.exit("Short Exit", "Short", stop=strategy_stop_loss_short, limit=strategy_take_profit_short)
    strategy_trades_today := strategy_trades_today + 1
    strategy_signal_taken_this_session := strategy_within_30min_of_session
    strategy_signal_taken_this_4hr := strategy_within_30min_of_4hr
    last_trade_time := time

// Update daily profit
strategy_daily_profit := strategy.netprofit

// ==================== Plotting ==================================================

// Plot VWAP lines
plot(show_session ? sessionVwap : na, title="Session VWAP", color=session_color, linewidth=2)
plot(show_week ? weekVwap : na, title="Week VWAP", color=week_color, linewidth=2)
plot(show_month ? monthVwap : na, title="Month VWAP", color=month_color, linewidth=2)

// Plot Oscar signals if enabled
if enable_oscar
    // Buy Signal 1 - Triangle Up
    plotshape(series=buySignal1, title="Buy Signal 1", style=shape.triangleup, location=location.belowbar, color=color.green, size=size.large)

    // Buy Signal 2 - Arrow Up
    plotshape(series=buySignal2, title="Buy Signal 2", style=shape.arrowup, location=location.belowbar, color=color.rgb(239, 247, 239), size=size.large)

    // Sell Signal 1 - Triangle Down
    plotshape(series=sellSignal1, title="Sell Signal 1", style=shape.triangledown, location=location.abovebar, color=color.red, size=size.large)

    // Sell Signal 2 - Arrow Down
    plotshape(series=sellSignal2, title="Sell Signal 2", style=shape.arrowdown, location=location.abovebar, color=#f7eeee, size=size.large)

    // Display indicator values as labels
    if showValues
        valueLabel := label.new(x=bar_index, y=high, text="Oscar: " + str.tostring(smoothedOscar, "#.##") +
            "\nOscar Rough: " + str.tostring(smoothedOscarRough, "#.##") +
            "\nSensitivity: " + str.tostring(crossSensitivity, "#.##"),
            style=label.style_label_down, color=color.rgb(0, 0, 0, 80), textcolor=color.white, yloc=yloc.price)
        label.delete(valueLabel[1])

// Plot strategy signals
plotshape(strategy_long_condition, title="Long Entry", style=shape.labelup, location=location.belowbar, color=color.green, size=size.huge, text="BUY")
plotarrow(strategy_long_condition ? 1 : 0, title="Long Arrow", colorup=color.green, colordown=color.red, minheight=100, maxheight=100)

plotshape(strategy_short_condition, title="Short Entry", style=shape.labeldown, location=location.abovebar, color=color.red, size=size.huge, text="SELL")
plotarrow(strategy_short_condition ? -1 : 0, title="Short Arrow", colorup=color.green, colordown=color.red, minheight=100, maxheight=100)

// Add strategy alert conditions
alertcondition(strategy_long_condition, "Buy Signal", "VWAP-FVG-Oscar Buy Signal")
alertcondition(strategy_short_condition, "Sell Signal", "VWAP-FVG-Oscar Sell Signal")

// Display debug information
if show_debug and barstate.islast
    var table debugTable = table.new(position.top_right, 2, 12, color.rgb(0, 0, 0, 80), color.white, 1, color.white, 1)

    table.cell(debugTable, 0, 0, "VWAP-FVG-Oscar Strategy", text_color=color.white, bgcolor=color.rgb(0, 0, 0, 100), text_size=size.small)
    table.cell(debugTable, 1, 0, "Status", text_color=color.white, bgcolor=color.rgb(0, 0, 0, 100), text_size=size.small)

    table.cell(debugTable, 0, 1, "In Killzone/4H Start", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 1, strategy_in_session ? "Yes" : "No", text_color=strategy_in_session ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 2, "Within 30min Window", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 2, (strategy_within_30min_of_session or strategy_within_30min_of_4hr) ? "Yes" : "No",
               text_color=(strategy_within_30min_of_session or strategy_within_30min_of_4hr) ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 3, "Price Near VWAP", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 3, strategy_price_near_vwap ? "Yes" : "No", text_color=strategy_price_near_vwap ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 4, "Above VWAP", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 4, strategy_above_vwap ? "Yes" : "No", text_color=strategy_above_vwap ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 5, "Below VWAP", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 5, strategy_below_vwap ? "Yes" : "No", text_color=strategy_below_vwap ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 6, "In Bullish FVG", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 6, strategy_in_bullish_fvg ? "Yes" : "No", text_color=strategy_in_bullish_fvg ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 7, "In Bearish FVG", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 7, strategy_in_bearish_fvg ? "Yes" : "No", text_color=strategy_in_bearish_fvg ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 8, "Oscar Buy Signal", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 8, buySignal1 ? "Yes" : "No", text_color=buySignal1 ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 9, "Oscar Sell Signal", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 9, sellSignal1 ? "Yes" : "No", text_color=sellSignal1 ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 10, "Trades Today", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 10, str.tostring(strategy_trades_today) + "/" + str.tostring(max_trades_per_day),
               text_color=strategy_trades_today < max_trades_per_day ? color.green : color.red, text_size=size.small)

    table.cell(debugTable, 0, 11, "Can Trade (Daily Loss)", text_color=color.white, text_size=size.small)
    table.cell(debugTable, 1, 11, strategy_can_trade ? "Yes" : "No", text_color=strategy_can_trade ? color.green : color.red, text_size=size.small)
