// chart.js

// Create the chart
const chart = LightweightCharts.createChart(document.getElementById('chart'), {
    width: 800,
    height: 600,
    layout: {
        backgroundColor: '#ffffff',
        textColor: '#000000',
    },
    grid: {
        vertLines: {
            color: '#eeeeee',
        },
        horzLines: {
            color: '#eeeeee',
        },
    },
    crosshair: {
        mode: LightweightCharts.CrosshairMode.Normal,
    },
    rightPriceScale: {
        borderColor: '#cccccc',
    },
    timeScale: {
        borderColor: '#cccccc',
    },
});

// Add a candlestick series
const candleSeries = chart.addCandlestickSeries({
    upColor: 'green',
    downColor: 'red',
    borderUpColor: 'green',
    borderDownColor: 'red',
    wickUpColor: 'green',
    wickDownColor: 'red',
});

// Add a volume series
const volumeSeries = chart.addHistogramSeries({
    color: '#26a69a',
    priceFormat: {
        type: 'volume',
    },
    priceScaleId: '',
    scaleMargins: {
        top: 0.8,
        bottom: 0,
    },
});

// Fetch the data from the JSON file
fetch('http://localhost:8000/gold_intraday_data.json')
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok ' + response.statusText);
        }
        return response.json();
    })
    .then(data => {
        // Parse the JSON data
        const candles = [];
        const volumes = [];

        data.forEach(item => {
            const date = moment(item['Date Time']).format('YYYY-MM-DDTHH:mm:ss');
            const open = parseFloat(item['Open']);
            const high = parseFloat(item['High']);
            const low = parseFloat(item['Low']);
            const close = parseFloat(item['Close']);
            const volume = parseFloat(item['Volume']);

            if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close) || isNaN(volume)) {
                console.warn('Skipping invalid data point:', item);
                return;
            }

            candles.push({ time: date, open, high, low, close });
            volumes.push({ time: date, value: volume, color: open > close ? 'red' : 'green' });
        });

        // Log parsed data
        console.log('Candles:', candles);
        console.log('Volumes:', volumes);

        // Set the data for the series
        candleSeries.setData(candles);
        volumeSeries.setData(volumes);
    })
    .catch(error => console.error('Error fetching or parsing data:', error));
