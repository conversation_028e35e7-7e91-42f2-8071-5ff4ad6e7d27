//@version=5
indicator("ATT", shorttitle="ATT", overlay=true,max_bars_back=5000, max_lines_count = 500, max_polylines_count = 100, max_labels_count = 500, max_boxes_count = 500)
//----IB indicator -----
//-----att indicator-----
// HTF Box Settings
att_group_candle = 'HTF Box Settings'

// Auto HTF Mode Settings
att_useAutoHTF = input.bool(true, 'Use Auto HTF Mode', group=att_group_candle, tooltip='When enabled, automatically calculates HTF timeframes based on chart timeframe')
att_autoMultiplier1 = input.int(60, 'Auto HTF Multiplier', minval=10, maxval=200, group=att_group_candle, tooltip='Multiplier for auto HTF calculation (e.g., 60 = 60x chart timeframe)')

// HTF Box Settings
att_htfCndl1  = input.bool(true, 'HTF Box', inline='TYP1', group=att_group_candle)
att_htfUser1  = input.string('1 Hour', 'Manual TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP1', group=att_group_candle)
att_bullC1    = input.color(#00000000, 'Bull', inline='COL1', group=att_group_candle)
att_bearC1    = input.color(#00000000, 'Bear', inline='COL1', group=att_group_candle)

att_showBoxes = input.bool(true, 'Show HTF Boxes', group=att_group_candle) // New input for toggling boxes

// Common Settings
att_group_common = 'Common Settings'
att_trans        = input.int(85, 'Transparency', inline='STYLE', minval=65, maxval=95, group=att_group_common)
att_lw           = input.int(1, 'Line Width', inline='STYLE', minval=1, maxval=4, group=att_group_common)
att_showNumbers  = input.bool(false, 'Show Candle Numbers', group=att_group_common)
att_numbersColor = input.color(color.white, 'Numbers Color', inline='NUM', group=att_group_common)
att_numbersSize  = input.string(size.small, 'Numbers Size', options=[size.tiny, size.small, size.normal], inline='NUM', group=att_group_common)

// ATT Circle Settings
att_group_att = 'ATT Circle Settings'
att_showATT      = input.bool(true, 'Show ATT Circles', group=att_group_att)
att_showATTNumbers = input.bool(true, 'Show ATT Numbers on Circles', group=att_group_att)
att_showHistoricalATT = input.bool(true, 'Show Historical ATT Circles', group=att_group_att)
att_attColor1    = input.color(#8ee60ac4, 'ATT Color', inline='ATT_COL1', group=att_group_att)

// ATT Prediction Settings
att_group_prediction = 'ATT Prediction Settings'
att_showPrediction = input.bool(true, 'Show ATT Predictions', group=att_group_prediction)
att_predictionDistance = input.int(5, 'Show predictions X candles ahead', minval=1, maxval=20, group=att_group_prediction)
att_predictionColor = input.color(color.yellow, 'Prediction Label Color', group=att_group_prediction)
att_predictionSize = input.string(size.tiny, 'Prediction Label Size', options=[size.tiny, size.small, size.normal], group=att_group_prediction)

// Display Settings
att_group_display = 'Display Settings'
att_showHTFInfo = input.bool(true, 'Show HTF Info Table', group=att_group_display, tooltip='Shows current HTF timeframes being used')
att_tablePosition = input.string('Top Right', 'Table Position', options=['Top Left', 'Top Right', 'Bottom Left', 'Bottom Right'], group=att_group_display)

// The ATT candle numbers where arrow marks will be drawn
var att_att_numbers = array.from(3, 11, 17, 29, 41, 47, 53, 59)

att_checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 : _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)
    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

att_f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0., htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.
    var int candleCount = 0

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        candleCount := 0
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        candleCount += 1
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c, candleCount]

att_f_getTF(_htf) =>
    _htf == '3 Mins' ? '3' :_htf == '5 Mins' ? '5' :_htf == '10 Mins' ? '10' :_htf == '15 Mins' ? '15' :_htf == '30 Mins' ? '30' :_htf == '45 Mins' ? '45' :_htf == '1 Hour' ? '60' :_htf == '2 Hours' ? '120' :_htf == '3 Hours' ? '180' :_htf == '4 Hours' ? '240' :_htf == '1 Day' ? 'D' :_htf == '1 Week' ? 'W' :_htf == '1 Month' ? 'M' :_htf == '3 Months' ? '3M' :_htf == '6 Months' ? '6M' :_htf == '1 Year' ? '12M' : na

// Function to get current chart timeframe in minutes
att_f_getChartTFInMinutes() =>
    chartTF = timeframe.period
    minutes = 0.0

    // For intraday timeframes, timeframe.multiplier gives us the minutes directly
    if timeframe.isintraday
        minutes := timeframe.multiplier
    // For daily timeframes
    else if str.contains(chartTF, 'D')
        multiplier = chartTF == 'D' ? 1.0 : str.tonumber(str.replace(chartTF, 'D', '', 0))
        minutes := multiplier * 1440.0  // 1 day = 1440 minutes
    // For weekly timeframes
    else if str.contains(chartTF, 'W')
        multiplier = chartTF == 'W' ? 1.0 : str.tonumber(str.replace(chartTF, 'W', '', 0))
        minutes := multiplier * 10080.0  // 1 week = 10080 minutes
    // For monthly timeframes
    else if str.contains(chartTF, 'M')
        multiplier = chartTF == 'M' ? 1.0 : str.tonumber(str.replace(chartTF, 'M', '', 0))
        minutes := multiplier * 43200.0  // 1 month = 43200 minutes (30 days)

    minutes

// Function to automatically calculate appropriate HTF timeframe based on chart TF
att_f_getAutoHTF(_multiplier = 60) =>
    chartMinutes = att_f_getChartTFInMinutes()
    targetMinutes = chartMinutes * _multiplier

    // Convert target minutes to appropriate timeframe string
    htfString = ""

    // Less than 1 hour - show in minutes
    if targetMinutes < 60.0
        minutes = math.floor(targetMinutes)
        htfString := str.tostring(minutes) + " Mins"

    // 1 hour to less than 1 day - show in hours if whole hours, otherwise minutes
    else if targetMinutes < 1440.0
        hours = targetMinutes / 60.0
        if math.abs(hours - math.round(hours)) < 0.01  // Close to whole hour
            wholeHours = math.round(hours)
            htfString := str.tostring(wholeHours) + " Hour" + (wholeHours > 1.0 ? "s" : "")
        else
            minutes = math.floor(targetMinutes)
            htfString := str.tostring(minutes) + " Mins"

    // 1 day to less than 1 week - Pine Script only supports single day "D", so use hours for multi-day
    else if targetMinutes < 10080.0
        days = targetMinutes / 1440.0
        if math.abs(days - 1.0) < 0.01  // Close to exactly 1 day
            htfString := "1 Day"
        else
            // For multiple days, use hours since Pine Script doesn't support "2D", "3D", etc.
            hours = math.floor(targetMinutes / 60.0)
            htfString := str.tostring(hours) + " Hours"

    // 1 week to less than 1 month - Pine Script only supports single week "W", so use hours for multi-week
    else if targetMinutes < 43200.0
        weeks = targetMinutes / 10080.0
        if math.abs(weeks - 1.0) < 0.01  // Close to exactly 1 week
            htfString := "1 Week"
        else
            // For multiple weeks, use hours since Pine Script doesn't support "2W", "3W", etc.
            hours = math.floor(targetMinutes / 60.0)
            htfString := str.tostring(hours) + " Hours"

    // 1 month or more - Pine Script only supports single month "M", so use hours for multi-month
    else
        months = targetMinutes / 43200.0
        if math.abs(months - 1.0) < 0.01  // Close to exactly 1 month
            htfString := "1 Month"
        else
            // For multiple months, use hours since Pine Script doesn't support "2M", "3M", etc.
            hours = math.floor(targetMinutes / 60.0)
            htfString := str.tostring(hours) + " Hours"

    htfString

// Function to convert auto-calculated HTF string to Pine Script timeframe format
att_f_convertAutoHTFToPineFormat(_autoHTF) =>
    result = ""

    // Handle minutes
    if str.contains(_autoHTF, " Mins")
        minutes = str.tonumber(str.replace(_autoHTF, " Mins", "", 0))
        result := str.tostring(math.floor(minutes))

    // Handle hours
    else if str.contains(_autoHTF, " Hour")
        hours = str.tonumber(str.replace(str.replace(_autoHTF, " Hours", "", 0), " Hour", "", 0))
        totalMinutes = hours * 60.0
        result := str.tostring(math.floor(totalMinutes))

    // Handle days (only single day supported in Pine Script)
    else if str.contains(_autoHTF, " Day")
        result := "D"  // Pine Script only supports single day

    // Handle weeks (only single week supported in Pine Script)
    else if str.contains(_autoHTF, " Week")
        result := "W"  // Pine Script only supports single week

    // Handle months (only single month supported in Pine Script)
    else if str.contains(_autoHTF, " Month")
        result := "M"  // Pine Script only supports single month

    result

// Function to validate if a Pine Script timeframe is supported
att_f_isValidPineTimeframe(_tf) =>
    // Check if it's a valid intraday timeframe (1-999 minutes)
    if str.tonumber(_tf) != na
        minutes = str.tonumber(_tf)
        minutes >= 1 and minutes <= 999
    // Check if it's a valid daily/weekly/monthly timeframe
    else
        _tf == "D" or _tf == "W" or _tf == "M"

// Global label arrays for HTF box
var label[] att_candleLabelsHTF1 = array.new_label()
var label att_currentPredictionLabel = na

// Function to check if current bar should show ATT circle
att_f_checkATTCondition(_show, _htf) =>
    result = false
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        result := array.includes(att_att_numbers, currentCandle)
    result

// Function to get ATT number for current bar
att_f_getATTNumber(_show, _htf) =>
    attNumber = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1
        if array.includes(att_att_numbers, currentCandle)
            attNumber := currentCandle
    attNumber

// Function to get next ATT number and candles remaining
att_f_getNextATT(_show, _htf) =>
    nextATT = 0
    candlesRemaining = 0
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        currentCandle = candleCount + 1

        // Find the next ATT number
        for i = 0 to array.size(att_att_numbers) - 1
            attNum = array.get(att_att_numbers, i)
            if attNum > currentCandle
                nextATT := attNum
                candlesRemaining := attNum - currentCandle
                break

        // If no ATT number found in current cycle, get the first one from next cycle
        if nextATT == 0
            nextATT := array.get(att_att_numbers, 0)
            candlesRemaining := (60 - currentCandle) + nextATT

    [nextATT, candlesRemaining]

// Function to check if we should show prediction
att_f_shouldShowPrediction(_show, _htf) =>
    shouldShow = false
    if _show and att_showPrediction
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)
        shouldShow := candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
    shouldShow

// Function to draw candle numbers independently
att_f_drawCandleNumbers(_show, _htf, _labelArr) =>
    if _show and att_showNumbers
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        labelPos = low - (high - low) * 0.5
        candleLabel = label.new(bar_index, labelPos, str.tostring(candleCount + 1), style=label.style_label_center, textcolor=att_numbersColor, size=att_numbersSize, yloc=yloc.price, color=na, textalign=text.align_center)
        array.push(_labelArr, candleLabel)

        // Cleanup labels to prevent overload
        if array.size(_labelArr) > 480
            label.delete(array.shift(_labelArr))

// Function to get prediction data (returns values instead of modifying globals)
att_f_getPredictionData(_show, _htf) =>
    showPrediction = false
    predictionText = ""
    labelColor = att_predictionColor

    if _show and att_showPrediction
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)
        [nextATT, candlesRemaining] = att_f_getNextATT(_show, _htf)

        if candlesRemaining > 0 and candlesRemaining <= att_predictionDistance
            showPrediction := true
            predictionText := candlesRemaining == 1 ? str.tostring(nextATT) + " NEXT!" : str.tostring(nextATT) + " in " + str.tostring(candlesRemaining)
            labelColor := candlesRemaining == 1 ? color.orange : att_predictionColor

    [showPrediction, predictionText, labelColor]

att_f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width, _labelArr, _htfNumber) =>
    if _show and att_showBoxes // Only draw boxes if att_showBoxes is true
        [O1, H1, L1, C1, O0, H0, L0, C0, candleCount] = att_f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)

            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)

            // Cleanup labels to prevent overload
            if array.size(_labelArr) > 480
                label.delete(array.shift(_labelArr))

// ------------------------- Main Logic ------------------------- //

// Determine HTF timeframe based on mode - completely independent logic
att_htf1 = ""
att_htf1_valid = false

if att_useAutoHTF
    // Auto mode: calculate HTF based on chart timeframe
    att_autoHTF1String = att_f_getAutoHTF(att_autoMultiplier1)
    att_htf1 := att_f_convertAutoHTFToPineFormat(att_autoHTF1String)
    att_htf1_valid := att_f_isValidPineTimeframe(att_htf1)
else
    // Manual mode: use user-selected timeframe
    att_htf1 := att_f_getTF(att_htfUser1)
    att_htf1_valid := att_htf1 != na

// Check if timeframe is supported (only if valid)
att_supported1 = att_htf1_valid ? att_checkIf(timeframe.period, att_htf1) : false

if chart.is_standard
    // Draw HTF Box
    if att_supported1
        att_f_processCandles(att_htfCndl1, att_htf1, att_bullC1, att_bearC1, att_trans, att_lw, att_candleLabelsHTF1, 1)

// Calculate ATT conditions for HTF timeframe
att_attCondition1 = att_supported1 ? att_f_checkATTCondition(att_htfCndl1, att_htf1) : false
att_attNumber1 = att_supported1 ? att_f_getATTNumber(att_htfCndl1, att_htf1) : 0

// Determine candle color for positioning
att_isRedCandle = close < open
att_isGreenCandle = close >= open

// Calculate if we should show recent ATTs
// Use a simple approach: show if historical is enabled OR if we're within recent bars
att_recentBarsLimit = 120
att_barsFromEnd = last_bar_index - bar_index
att_showRecentATT = att_showHistoricalATT or (att_barsFromEnd <= att_recentBarsLimit)

// Plot ATT circles using plotshape
// ATT - With numbers - Red candles (above bar)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isRedCandle and att_showRecentATT, title="ATT-3 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isRedCandle and att_showRecentATT, title="ATT-11 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isRedCandle and att_showRecentATT, title="ATT-17 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isRedCandle and att_showRecentATT, title="ATT-29 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isRedCandle and att_showRecentATT, title="ATT-41 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isRedCandle and att_showRecentATT, title="ATT-47 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isRedCandle and att_showRecentATT, title="ATT-53 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isRedCandle and att_showRecentATT, title="ATT-59 Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// ATT - With numbers - Green candles (below bar)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 3 and att_isGreenCandle and att_showRecentATT, title="ATT-3 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="3", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 11 and att_isGreenCandle and att_showRecentATT, title="ATT-11 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="11", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 17 and att_isGreenCandle and att_showRecentATT, title="ATT-17 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="17", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 29 and att_isGreenCandle and att_showRecentATT, title="ATT-29 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="29", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 41 and att_isGreenCandle and att_showRecentATT, title="ATT-41 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="41", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 47 and att_isGreenCandle and att_showRecentATT, title="ATT-47 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="47", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 53 and att_isGreenCandle and att_showRecentATT, title="ATT-53 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="53", textcolor=color.white)
plotshape(att_showATT and att_showATTNumbers and att_attCondition1 and att_attNumber1 == 59 and att_isGreenCandle and att_showRecentATT, title="ATT-59 Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small, text="59", textcolor=color.white)

// ATT - Without numbers
plotshape(att_showATT and not att_showATTNumbers and att_attCondition1 and att_isRedCandle and att_showRecentATT, title="ATT Red", style=shape.circle, location=location.abovebar, color=att_attColor1, size=size.small)
plotshape(att_showATT and not att_showATTNumbers and att_attCondition1 and att_isGreenCandle and att_showRecentATT, title="ATT Green", style=shape.circle, location=location.belowbar, color=att_attColor1, size=size.small)

// Draw candle numbers for HTF timeframe
if att_supported1 and att_htfCndl1
    att_f_drawCandleNumbers(true, att_htf1, att_candleLabelsHTF1)

// Handle prediction label in global scope
if not na(att_currentPredictionLabel)
    label.delete(att_currentPredictionLabel)
    att_currentPredictionLabel := na

if att_supported1 and att_htfCndl1
    [showPrediction, predictionText, labelColor] = att_f_getPredictionData(true, att_htf1)
    if showPrediction
        labelPos = (high + low) / 2  // Position at middle of candle
        att_currentPredictionLabel := label.new(bar_index + 1, labelPos, predictionText, style=label.style_label_left, textcolor=color.black, size=att_predictionSize, yloc=yloc.price, color=labelColor, textalign=text.align_left)

// Display HTF Info Table
if att_showHTFInfo and barstate.islast
    tablePos = att_tablePosition == 'Top Left' ? position.top_left : att_tablePosition == 'Top Right' ? position.top_right : att_tablePosition == 'Bottom Left' ? position.bottom_left : position.bottom_right

    var table htfTable = table.new(tablePos, 3, 4, bgcolor=color.new(color.black, 80), border_width=1, border_color=color.gray)

    // Clear existing table content
    table.clear(htfTable, 0, 0, 2, 3)

    // Header
    table.cell(htfTable, 0, 0, "Type", text_color=color.white, text_size=size.small, bgcolor=color.new(color.blue, 70))
    table.cell(htfTable, 1, 0, "Timeframe", text_color=color.white, text_size=size.small, bgcolor=color.new(color.blue, 70))
    table.cell(htfTable, 2, 0, "Status", text_color=color.white, text_size=size.small, bgcolor=color.new(color.blue, 70))

    // Chart TF info with calculation details
    chartTFDisplay = timeframe.period
    chartMinutes = att_f_getChartTFInMinutes()
    chartInfo = chartTFDisplay + " (" + str.tostring(chartMinutes) + "m)"
    table.cell(htfTable, 0, 1, "Chart", text_color=color.white, text_size=size.tiny)
    table.cell(htfTable, 1, 1, chartInfo, text_color=color.white, text_size=size.tiny)
    table.cell(htfTable, 2, 1, "Current", text_color=color.green, text_size=size.tiny)

    // Mode info
    modeDisplay = att_useAutoHTF ? "Auto (" + str.tostring(att_autoMultiplier1) + "x)" : "Manual"
    table.cell(htfTable, 0, 2, "Mode", text_color=color.white, text_size=size.tiny)
    table.cell(htfTable, 1, 2, modeDisplay, text_color=color.yellow, text_size=size.tiny)
    table.cell(htfTable, 2, 2, att_useAutoHTF ? "Calculated" : "Selected", text_color=color.yellow, text_size=size.tiny)

    // HTF info - completely independent display based on current mode
    htfDisplay = ""
    htfStatus = ""
    htfStatusColor = color.red

    if att_useAutoHTF
        // Auto mode: show calculated timeframe and Pine format
        autoHTFString = att_f_getAutoHTF(att_autoMultiplier1)
        pineFormat = att_f_convertAutoHTFToPineFormat(autoHTFString)
        htfDisplay := autoHTFString + " → " + pineFormat
    else
        // Manual mode: show selected timeframe and Pine format
        manualPineFormat = att_f_getTF(att_htfUser1)
        htfDisplay := att_htfUser1 + " → " + manualPineFormat

    // Status is independent of mode
    if att_htfCndl1 and att_supported1
        htfStatus := "✓ Active"
        htfStatusColor := color.green
    else if att_htfCndl1 and not att_supported1
        htfStatus := "✗ Invalid"
        htfStatusColor := color.red
    else
        htfStatus := "○ Off"
        htfStatusColor := color.gray

    table.cell(htfTable, 0, 3, "HTF", text_color=color.white, text_size=size.tiny)
    table.cell(htfTable, 1, 3, htfDisplay, text_color=color.white, text_size=size.tiny)
    table.cell(htfTable, 2, 3, htfStatus, text_color=htfStatusColor, text_size=size.tiny)

//-----att indicator-----