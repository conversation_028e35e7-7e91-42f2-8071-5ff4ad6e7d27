//@version=5
indicator("MSS Detection", overlay=true, max_labels_count=500)

// ----- Original MSS Input Settings (as in ict-concepts.pine) -----
i_mode   = input.string('Present', title='Mode', options=['Present', 'Historical'])
showMS   = input.bool(true, title='Market Structures')
len      = input.int(5, title='Length', minval=3, maxval=10)
iMSS     = input.bool(true, title='MSS')
cMSSbl   = input.color(color.new(#00e6a1, 0), title='bullish')
cMSSbr   = input.color(color.new(#e60400, 0), title='bearish')
iBOS     = input.bool(true, title='BOS')

// ----- Original MSS Logic Variables -----
// (In the original script these arrays and the MSS.dir variable are stored inside a custom type.
// Here we mimic that using simple variables and arrays.)
var int MSS_dir = 0  // 1 = bullish MSS, -1 = bearish MSS, 0 = none
var line[] l_mssBl = array.new_line()
var line[] l_mssBr = array.new_line()
var line[] l_bosBl = array.new_line()
var line[] l_bosBr = array.new_line()
var label[] lbMssBl = array.new_label()
var label[] lbMssBr = array.new_label()
var label[] lbBosBl = array.new_label()
var label[] lbBosBr = array.new_label()

// For demonstration purposes we replicate the aZZ.x and aZZ.y values from ict-concepts.
// In the original script these hold critical price/time data.
// Here we use bar_index for x and high for y as a minimal placeholder.
aZZ_x = bar_index
aZZ_y = high

// ----- Original MSS Logic -----
// (This block is taken from ict-concepts.pine and kept intact as much as possible.)
if showMS
    // Determine indices (in the original code this depends on aZZ.d; here we use placeholders)
    iH = 1  // original: aZZ.d.get(2) == 1 ? 2 : 1
    iL = 1  // original: aZZ.d.get(2) == -1 ? 2 : 1
    
    // MSS Bullish detection
    if close > aZZ_y and MSS_dir < 1
        MSS_dir := 1 
        if i_mode == 'Present'
            // Clear BOS and MSS arrays (originally via clear_aLabLin methods)
            array.clear(l_bosBl)
            array.clear(l_bosBr)
            array.clear(lbBosBl)
            array.clear(lbBosBr)
            array.clear(l_mssBl)
            array.clear(l_mssBr)
            array.clear(lbMssBl)
            array.clear(lbMssBr)
        array.unshift(l_mssBl, line.new(aZZ_x, aZZ_y, bar_index, aZZ_y, color=cMSSbl))
        array.unshift(lbMssBl, label.new(math.round((aZZ_x + bar_index) / 2), aZZ_y, "MSS", 
            style=label.style_label_down, size=size.tiny, color=color.new(na, 0), textcolor=cMSSbl))
    
    // MSS Bearish detection
    if close < aZZ_y and MSS_dir > -1
        MSS_dir := -1 
        if i_mode == 'Present'
            array.clear(l_bosBl)
            array.clear(l_bosBr)
            array.clear(lbBosBl)
            array.clear(lbBosBr)
            array.clear(l_mssBl)
            array.clear(l_mssBr)
            array.clear(lbMssBl)
            array.clear(lbMssBr)
        array.unshift(l_mssBr, line.new(aZZ_x, aZZ_y, bar_index, aZZ_y, color=cMSSbr))
        array.unshift(lbMssBr, label.new(math.round((aZZ_x + bar_index) / 2), aZZ_y, "MSS", 
            style=label.style_label_up, size=size.tiny, color=color.new(na, 0), textcolor=cMSSbr))
    
    // BOS detection for bullish side (original logic preserved)
    if MSS_dir == 1 and close > aZZ_y and iBOS
        if array.size(l_bosBl) > 0
            if aZZ_y != line.get_y2(array.get(l_bosBl, 0)) and aZZ_y != line.get_y2(array.get(l_mssBl, 0))
                array.unshift(l_bosBl, line.new(aZZ_x, aZZ_y, bar_index, aZZ_y, color=cMSSbl))
                array.unshift(lbBosBl, label.new(math.round((aZZ_x + bar_index) / 2), aZZ_y, "BOS", 
                    style=label.style_label_down, size=size.tiny, color=color.new(na, 0), textcolor=cMSSbl))
        else  
            if aZZ_y != line.get_y2(array.get(l_mssBl, 0))
                array.unshift(l_bosBl, line.new(aZZ_x, aZZ_y, bar_index, aZZ_y, color=cMSSbl))
                array.unshift(lbBosBl, label.new(math.round((aZZ_x + bar_index) / 2), aZZ_y, "BOS", 
                    style=label.style_label_down, size=size.tiny, color=color.new(na, 0), textcolor=cMSSbl))
                
    // BOS detection for bearish side
    if MSS_dir == -1 and close < aZZ_y and iBOS
        if array.size(l_bosBr) > 0
            if aZZ_y != line.get_y2(array.get(l_bosBr, 0)) and aZZ_y != line.get_y2(array.get(l_mssBr, 0))
                array.unshift(l_bosBr, line.new(aZZ_x, aZZ_y, bar_index, aZZ_y, color=cMSSbr))
                array.unshift(lbBosBr, label.new(math.round((aZZ_x + bar_index) / 2), aZZ_y, "BOS", 
                    style=label.style_label_up, size=size.tiny, color=color.new(na, 0), textcolor=cMSSbr))
        else
            if aZZ_y != line.get_y2(array.get(l_mssBr, 0))
                array.unshift(l_bosBr, line.new(aZZ_x, aZZ_y, bar_index, aZZ_y, color=cMSSbr))
                array.unshift(lbBosBr, label.new(math.round((aZZ_x + bar_index) / 2), aZZ_y, "BOS", 
                    style=label.style_label_up, size=size.tiny, color=color.new(na, 0), textcolor=cMSSbr))
    
    // If MSS detection is disabled, hide MSS signals
    if not iMSS
        if array.size(l_mssBl) > 0
            line.set_color(array.get(l_mssBl, 0), color(na))
            label.set_textcolor(array.get(lbMssBl, 0), color(na))
        if array.size(l_mssBr) > 0
            line.set_color(array.get(l_mssBr, 0), color(na))
            label.set_textcolor(array.get(lbMssBr, 0), color(na))

// ----- Plot markers for quick visual reference -----
plotchar(MSS_dir == 1, title="Bullish MSS", char='▲', location=location.belowbar, color=cMSSbl, size=size.tiny)
plotchar(MSS_dir == -1, title="Bearish MSS", char='▼', location=location.abovebar, color=cMSSbr, size=size.tiny)