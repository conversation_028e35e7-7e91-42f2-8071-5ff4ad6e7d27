//@version=5
indicator("ADR Levels Pro [ICT]", overlay=true, max_lines_count=500, max_labels_count=500, dynamic_requests=true)

// Input settings based on the image
// ADR Inputs
show_adr1 = input.bool(true, "Show ADR1", group="Inputs")
adr1_timeframe = input.string("1 minute", "", options=["1 minute", "5 minutes", "15 minutes", "30 minutes", "1 hour", "4 hours"], inline="adr1", group="Inputs")
adr1_days = input.string("1 day", "", options=["1 day", "1 week", "1 month"], inline="adr1", group="Inputs")

show_adr2 = input.bool(true, "Show ADR2", group="Inputs")
adr2_timeframe = input.string("1 minute", "", options=["1 minute", "5 minutes", "15 minutes", "30 minutes", "1 hour", "4 hours"], inline="adr2", group="Inputs")
adr2_days = input.string("1 day", "", options=["1 day", "1 week", "1 month"], inline="adr2", group="Inputs")

show_adr3 = input.bool(true, "Show ADR3", group="Inputs")
adr3_timeframe = input.string("1 minute", "", options=["1 minute", "5 minutes", "15 minutes", "30 minutes", "1 hour", "4 hours"], inline="adr3", group="Inputs")
adr3_days = input.string("1 day", "", options=["1 day", "1 week", "1 month"], inline="adr3", group="Inputs")

show_alerts = input.bool(true, "Turn on Average Range Levels Alerts?", group="Inputs")
hide_warnings = input.bool(false, "Hide Warnings?", group="Inputs")

// General Settings
auto_color = input.bool(true, "Auto Color?", group="General Settings")
show_text = input.bool(true, "Show Text?", inline="text", group="General Settings")
text_size = input.string("Small", "", options=["Tiny", "Small", "Normal", "Large"], inline="text", group="General Settings")

show_data_table = input.string("Both", "Show Data Table?", options=["None", "Left", "Right", "Both"], group="General Settings")
table_position = input.string("Normal", "", options=["Normal", "Center", "Bottom"], group="General Settings")

// ADR Settings
adr_days = input.int(5, "Days", minval=1, maxval=30, group="ADR Settings")
use_true_range = input.bool(true, "True Open?", group="ADR Settings")
show_historical = input.bool(true, "Show Historical ADR Levels?", group="ADR Settings")

// ADR Plot Settings
offset = input.int(0, "Offset", group="ADR Plot Settings")
plot_from_anchor = input.bool(true, "Plot from Anchor?", group="ADR Plot Settings")
adr_style = input.string("Solid", "ADR", options=["Solid", "Dashed", "Dotted"], group="ADR Plot Settings")

// Convert timeframe string to Pine timeframe
getTimeframeString(tf_str) =>
    result = "D"
    if tf_str == "1 day"
        result := "D"
    else if tf_str == "1 week"
        result := "W"
    else if tf_str == "1 month"
        result := "M"
    result

// Convert minute timeframe string to Pine timeframe
getMinuteTimeframe(tf_str) =>
    result = "1"
    if tf_str == "1 minute"
        result := "1"
    else if tf_str == "5 minutes"
        result := "5"
    else if tf_str == "15 minutes"
        result := "15"
    else if tf_str == "30 minutes"
        result := "30"
    else if tf_str == "1 hour"
        result := "60"
    else if tf_str == "4 hours"
        result := "240"
    result

// Set up ADR settings based on user inputs
show_daily = (show_adr1 and adr1_days == "1 day") or (show_adr2 and adr2_days == "1 day") or (show_adr3 and adr3_days == "1 day")
show_weekly = (show_adr1 and adr1_days == "1 week") or (show_adr2 and adr2_days == "1 week") or (show_adr3 and adr3_days == "1 week")
show_monthly = (show_adr1 and adr1_days == "1 month") or (show_adr2 and adr2_days == "1 month") or (show_adr3 and adr3_days == "1 month")

// Force at least one timeframe to be shown
if not (show_daily or show_weekly or show_monthly)
    show_daily := true

daily_lookback = adr_days
weekly_lookback = adr_days
monthly_lookback = adr_days

show_full_adr = true
show_one_third_adr = true
show_two_third_adr = false
show_half_adr = false
extend_right = 50

// No debug info needed

// Color settings for Daily levels
daily_full_color = input.color(color.rgb(255, 255, 255, 70), "Daily Full Range", group="Daily Colors")
daily_one_third_color = input.color(color.rgb(255, 255, 255, 70), "Daily 1/3 Range", group="Daily Colors")
daily_two_third_color = input.color(color.rgb(255, 255, 255, 70), "Daily 2/3 Range", group="Daily Colors")
daily_half_color = input.color(color.rgb(255, 255, 255, 70), "Daily 1/2 Range", group="Daily Colors")

// Color settings for Weekly levels
weekly_full_color = input.color(color.rgb(255, 0, 0, 70), "Weekly Full Range", group="Weekly Colors")
weekly_one_third_color = input.color(color.rgb(255, 0, 0, 70), "Weekly 1/3 Range", group="Weekly Colors")
weekly_two_third_color = input.color(color.rgb(255, 0, 0, 70), "Weekly 2/3 Range", group="Weekly Colors")
weekly_half_color = input.color(color.rgb(255, 0, 0, 70), "Weekly 1/2 Range", group="Weekly Colors")

// Color settings for Monthly levels
monthly_full_color = input.color(color.rgb(0, 0, 255, 70), "Monthly Full Range", group="Monthly Colors")
monthly_one_third_color = input.color(color.rgb(0, 0, 255, 70), "Monthly 1/3 Range", group="Monthly Colors")
monthly_two_third_color = input.color(color.rgb(0, 0, 255, 70), "Monthly 2/3 Range", group="Monthly Colors")
monthly_half_color = input.color(color.rgb(0, 0, 255, 70), "Monthly 1/2 Range", group="Monthly Colors")

// Line width setting
line_width = input.int(1, "Line Width", minval=1, maxval=4, group="Line Settings")

// Convert line style input to Pine style
var lineStyleValue = line.style_solid
if adr_style == "Dashed"
    lineStyleValue := line.style_dashed
else if adr_style == "Dotted"
    lineStyleValue := line.style_dotted

// Function to calculate the Average Range for a given timeframe
calculateAverageRange(tf, lookback_period) =>
    // Get high and low values for the lookback period
    high_array = request.security(syminfo.tickerid, tf, high, lookahead=barmerge.lookahead_on)
    low_array = request.security(syminfo.tickerid, tf, low, lookahead=barmerge.lookahead_on)

    // Calculate the sum of ranges
    float range_sum = 0.0
    for i = 1 to lookback_period
        range_sum += (high_array[i] - low_array[i])

    // Return the average
    range_sum / lookback_period

// Function to get the open price for a given timeframe
getTimeframeOpen(tf) =>
    request.security(syminfo.tickerid, tf, open, lookahead=barmerge.lookahead_on)

// Calculate range values for each timeframe
daily_range = calculateAverageRange("D", daily_lookback)
weekly_range = calculateAverageRange("W", weekly_lookback)
monthly_range = calculateAverageRange("M", monthly_lookback)

// Get open prices for each timeframe
daily_open = getTimeframeOpen("D")
weekly_open = getTimeframeOpen("W")
monthly_open = getTimeframeOpen("M")

// Store Daily ADR levels
var float daily_full_up = na
var float daily_full_down = na
var float daily_one_third_up = na
var float daily_one_third_down = na
var float daily_two_third_up = na
var float daily_two_third_down = na
var float daily_half_up = na
var float daily_half_down = na

// Store Weekly AWR levels
var float weekly_full_up = na
var float weekly_full_down = na
var float weekly_one_third_up = na
var float weekly_one_third_down = na
var float weekly_two_third_up = na
var float weekly_two_third_down = na
var float weekly_half_up = na
var float weekly_half_down = na

// Store Monthly AMR levels
var float monthly_full_up = na
var float monthly_full_down = na
var float monthly_one_third_up = na
var float monthly_one_third_down = na
var float monthly_two_third_up = na
var float monthly_two_third_down = na
var float monthly_half_up = na
var float monthly_half_down = na

// Arrays to store historical lines for Daily levels
var line[] daily_full_up_lines = array.new_line()
var line[] daily_full_down_lines = array.new_line()
var line[] daily_one_third_up_lines = array.new_line()
var line[] daily_one_third_down_lines = array.new_line()
var line[] daily_two_third_up_lines = array.new_line()
var line[] daily_two_third_down_lines = array.new_line()
var line[] daily_half_up_lines = array.new_line()
var line[] daily_half_down_lines = array.new_line()

// Arrays to store historical lines for Weekly levels
var line[] weekly_full_up_lines = array.new_line()
var line[] weekly_full_down_lines = array.new_line()
var line[] weekly_one_third_up_lines = array.new_line()
var line[] weekly_one_third_down_lines = array.new_line()
var line[] weekly_two_third_up_lines = array.new_line()
var line[] weekly_two_third_down_lines = array.new_line()
var line[] weekly_half_up_lines = array.new_line()
var line[] weekly_half_down_lines = array.new_line()

// Arrays to store historical lines for Monthly levels
var line[] monthly_full_up_lines = array.new_line()
var line[] monthly_full_down_lines = array.new_line()
var line[] monthly_one_third_up_lines = array.new_line()
var line[] monthly_one_third_down_lines = array.new_line()
var line[] monthly_two_third_up_lines = array.new_line()
var line[] monthly_two_third_down_lines = array.new_line()
var line[] monthly_half_up_lines = array.new_line()
var line[] monthly_half_down_lines = array.new_line()

// Function to clear old lines
clearOldLines(line_array) =>
    if array.size(line_array) > 0
        for i = array.size(line_array) - 1 to 0
            line.delete(array.get(line_array, i))
        array.clear(line_array)

// Function to draw ADR level and return the line
drawADRLevel(price_level, color, label_text) =>
    var line result = na
    if not na(price_level)
        // Calculate start and end bar positions
        startBar = bar_index
        endBar = bar_index + extend_right

        // Create the line directly with x,y coordinates
        result := line.new(
             x1=startBar,
             y1=price_level,
             x2=endBar,
             y2=price_level,
             color=color,
             style=lineStyleValue,
             width=line_width)

        // Only create labels if show_text is enabled
        if show_text
            // Determine label size based on text_size setting
            label_size = size.small
            if text_size == "Tiny"
                label_size := size.tiny
            else if text_size == "Small"
                label_size := size.small
            else if text_size == "Normal"
                label_size := size.normal
            else if text_size == "Large"
                label_size := size.large

            // Create label at the end bar position
            label.new(
                 x=endBar,
                 y=price_level,
                 text=label_text,
                 style=label.style_label_left,
                 color=color.rgb(0, 0, 0, 0),  // Transparent background
                 textcolor=color,
                 size=label_size)
    result

// Update Daily ADR levels on new day
if timeframe.change("D") or barstate.isfirst
    // Calculate new Daily ADR levels
    daily_full_up := daily_open + daily_range
    daily_full_down := daily_open - daily_range
    daily_one_third_up := daily_open + (daily_range / 3)
    daily_one_third_down := daily_open - (daily_range / 3)
    daily_two_third_up := daily_open + (daily_range * 2 / 3)
    daily_two_third_down := daily_open - (daily_range * 2 / 3)
    daily_half_up := daily_open + (daily_range / 2)
    daily_half_down := daily_open - (daily_range / 2)

    // Clear old daily lines if not showing historical
    if not show_historical
        clearOldLines(daily_full_up_lines)
        clearOldLines(daily_full_down_lines)
        clearOldLines(daily_one_third_up_lines)
        clearOldLines(daily_one_third_down_lines)
        clearOldLines(daily_two_third_up_lines)
        clearOldLines(daily_two_third_down_lines)
        clearOldLines(daily_half_up_lines)
        clearOldLines(daily_half_down_lines)

// Update Weekly AWR levels on new week
if timeframe.change("W") or barstate.isfirst
    // Calculate new Weekly AWR levels
    weekly_full_up := weekly_open + weekly_range
    weekly_full_down := weekly_open - weekly_range
    weekly_one_third_up := weekly_open + (weekly_range / 3)
    weekly_one_third_down := weekly_open - (weekly_range / 3)
    weekly_two_third_up := weekly_open + (weekly_range * 2 / 3)
    weekly_two_third_down := weekly_open - (weekly_range * 2 / 3)
    weekly_half_up := weekly_open + (weekly_range / 2)
    weekly_half_down := weekly_open - (weekly_range / 2)

    // Clear old weekly lines if not showing historical
    if not show_historical
        clearOldLines(weekly_full_up_lines)
        clearOldLines(weekly_full_down_lines)
        clearOldLines(weekly_one_third_up_lines)
        clearOldLines(weekly_one_third_down_lines)
        clearOldLines(weekly_two_third_up_lines)
        clearOldLines(weekly_two_third_down_lines)
        clearOldLines(weekly_half_up_lines)
        clearOldLines(weekly_half_down_lines)

// Update Monthly AMR levels on new month
if timeframe.change("M") or barstate.isfirst
    // Calculate new Monthly AMR levels
    monthly_full_up := monthly_open + monthly_range
    monthly_full_down := monthly_open - monthly_range
    monthly_one_third_up := monthly_open + (monthly_range / 3)
    monthly_one_third_down := monthly_open - (monthly_range / 3)
    monthly_two_third_up := monthly_open + (monthly_range * 2 / 3)
    monthly_two_third_down := monthly_open - (monthly_range * 2 / 3)
    monthly_half_up := monthly_open + (monthly_range / 2)
    monthly_half_down := monthly_open - (monthly_range / 2)

    // Clear old monthly lines if not showing historical
    if not show_historical
        clearOldLines(monthly_full_up_lines)
        clearOldLines(monthly_full_down_lines)
        clearOldLines(monthly_one_third_up_lines)
        clearOldLines(monthly_one_third_down_lines)
        clearOldLines(monthly_two_third_up_lines)
        clearOldLines(monthly_two_third_down_lines)
        clearOldLines(monthly_half_up_lines)
        clearOldLines(monthly_half_down_lines)

// Draw ADR levels on the last bar
if barstate.islast
    // Draw Daily ADR levels
    if show_daily
        // Draw Full ADR levels
        if show_full_adr
            line daily_full_up_line = drawADRLevel(daily_full_up, daily_full_color, "ADR+")
            line daily_full_down_line = drawADRLevel(daily_full_down, daily_full_color, "ADR-")
            if not na(daily_full_up_line)
                array.push(daily_full_up_lines, daily_full_up_line)
            if not na(daily_full_down_line)
                array.push(daily_full_down_lines, daily_full_down_line)

        // Draw 1/3 ADR levels
        if show_one_third_adr
            line daily_one_third_up_line = drawADRLevel(daily_one_third_up, daily_one_third_color, "1/3 ADR+")
            line daily_one_third_down_line = drawADRLevel(daily_one_third_down, daily_one_third_color, "1/3 ADR-")
            if not na(daily_one_third_up_line)
                array.push(daily_one_third_up_lines, daily_one_third_up_line)
            if not na(daily_one_third_down_line)
                array.push(daily_one_third_down_lines, daily_one_third_down_line)

        // Draw 2/3 ADR levels
        if show_two_third_adr
            line daily_two_third_up_line = drawADRLevel(daily_two_third_up, daily_two_third_color, "2/3 ADR+")
            line daily_two_third_down_line = drawADRLevel(daily_two_third_down, daily_two_third_color, "2/3 ADR-")
            if not na(daily_two_third_up_line)
                array.push(daily_two_third_up_lines, daily_two_third_up_line)
            if not na(daily_two_third_down_line)
                array.push(daily_two_third_down_lines, daily_two_third_down_line)

        // Draw 1/2 ADR levels
        if show_half_adr
            line daily_half_up_line = drawADRLevel(daily_half_up, daily_half_color, "1/2 ADR+")
            line daily_half_down_line = drawADRLevel(daily_half_down, daily_half_color, "1/2 ADR-")
            if not na(daily_half_up_line)
                array.push(daily_half_up_lines, daily_half_up_line)
            if not na(daily_half_down_line)
                array.push(daily_half_down_lines, daily_half_down_line)

    // Draw Weekly AWR levels
    if show_weekly
        // Draw Full AWR levels
        if show_full_adr
            line weekly_full_up_line = drawADRLevel(weekly_full_up, weekly_full_color, "AWR+")
            line weekly_full_down_line = drawADRLevel(weekly_full_down, weekly_full_color, "AWR-")
            if not na(weekly_full_up_line)
                array.push(weekly_full_up_lines, weekly_full_up_line)
            if not na(weekly_full_down_line)
                array.push(weekly_full_down_lines, weekly_full_down_line)

        // Draw 1/3 AWR levels
        if show_one_third_adr
            line weekly_one_third_up_line = drawADRLevel(weekly_one_third_up, weekly_one_third_color, "1/3 AWR+")
            line weekly_one_third_down_line = drawADRLevel(weekly_one_third_down, weekly_one_third_color, "1/3 AWR-")
            if not na(weekly_one_third_up_line)
                array.push(weekly_one_third_up_lines, weekly_one_third_up_line)
            if not na(weekly_one_third_down_line)
                array.push(weekly_one_third_down_lines, weekly_one_third_down_line)

        // Draw 2/3 AWR levels
        if show_two_third_adr
            line weekly_two_third_up_line = drawADRLevel(weekly_two_third_up, weekly_two_third_color, "2/3 AWR+")
            line weekly_two_third_down_line = drawADRLevel(weekly_two_third_down, weekly_two_third_color, "2/3 AWR-")
            if not na(weekly_two_third_up_line)
                array.push(weekly_two_third_up_lines, weekly_two_third_up_line)
            if not na(weekly_two_third_down_line)
                array.push(weekly_two_third_down_lines, weekly_two_third_down_line)

        // Draw 1/2 AWR levels
        if show_half_adr
            line weekly_half_up_line = drawADRLevel(weekly_half_up, weekly_half_color, "1/2 AWR+")
            line weekly_half_down_line = drawADRLevel(weekly_half_down, weekly_half_color, "1/2 AWR-")
            if not na(weekly_half_up_line)
                array.push(weekly_half_up_lines, weekly_half_up_line)
            if not na(weekly_half_down_line)
                array.push(weekly_half_down_lines, weekly_half_down_line)

    // Draw Monthly AMR levels
    if show_monthly
        // Draw Full AMR levels
        if show_full_adr
            line monthly_full_up_line = drawADRLevel(monthly_full_up, monthly_full_color, "AMR+")
            line monthly_full_down_line = drawADRLevel(monthly_full_down, monthly_full_color, "AMR-")
            if not na(monthly_full_up_line)
                array.push(monthly_full_up_lines, monthly_full_up_line)
            if not na(monthly_full_down_line)
                array.push(monthly_full_down_lines, monthly_full_down_line)

        // Draw 1/3 AMR levels
        if show_one_third_adr
            line monthly_one_third_up_line = drawADRLevel(monthly_one_third_up, monthly_one_third_color, "1/3 AMR+")
            line monthly_one_third_down_line = drawADRLevel(monthly_one_third_down, monthly_one_third_color, "1/3 AMR-")
            if not na(monthly_one_third_up_line)
                array.push(monthly_one_third_up_lines, monthly_one_third_up_line)
            if not na(monthly_one_third_down_line)
                array.push(monthly_one_third_down_lines, monthly_one_third_down_line)

        // Draw 2/3 AMR levels
        if show_two_third_adr
            line monthly_two_third_up_line = drawADRLevel(monthly_two_third_up, monthly_two_third_color, "2/3 AMR+")
            line monthly_two_third_down_line = drawADRLevel(monthly_two_third_down, monthly_two_third_color, "2/3 AMR-")
            if not na(monthly_two_third_up_line)
                array.push(monthly_two_third_up_lines, monthly_two_third_up_line)
            if not na(monthly_two_third_down_line)
                array.push(monthly_two_third_down_lines, monthly_two_third_down_line)

        // Draw 1/2 AMR levels
        if show_half_adr
            line monthly_half_up_line = drawADRLevel(monthly_half_up, monthly_half_color, "1/2 AMR+")
            line monthly_half_down_line = drawADRLevel(monthly_half_down, monthly_half_color, "1/2 AMR-")
            if not na(monthly_half_up_line)
                array.push(monthly_half_up_lines, monthly_half_up_line)
            if not na(monthly_half_down_line)
                array.push(monthly_half_down_lines, monthly_half_down_line)

// Plot Daily ADR levels for visibility in non-last bars
plot(show_daily and show_full_adr ? daily_full_up : na, "Daily Full ADR+", color=daily_full_color, style=plot.style_circles, linewidth=1)
plot(show_daily and show_full_adr ? daily_full_down : na, "Daily Full ADR-", color=daily_full_color, style=plot.style_circles, linewidth=1)
plot(show_daily and show_one_third_adr ? daily_one_third_up : na, "Daily 1/3 ADR+", color=daily_one_third_color, style=plot.style_circles, linewidth=1)
plot(show_daily and show_one_third_adr ? daily_one_third_down : na, "Daily 1/3 ADR-", color=daily_one_third_color, style=plot.style_circles, linewidth=1)
plot(show_daily and show_two_third_adr ? daily_two_third_up : na, "Daily 2/3 ADR+", color=daily_two_third_color, style=plot.style_circles, linewidth=1)
plot(show_daily and show_two_third_adr ? daily_two_third_down : na, "Daily 2/3 ADR-", color=daily_two_third_color, style=plot.style_circles, linewidth=1)
plot(show_daily and show_half_adr ? daily_half_up : na, "Daily 1/2 ADR+", color=daily_half_color, style=plot.style_circles, linewidth=1)
plot(show_daily and show_half_adr ? daily_half_down : na, "Daily 1/2 ADR-", color=daily_half_color, style=plot.style_circles, linewidth=1)

// Plot Weekly AWR levels for visibility in non-last bars
plot(show_weekly and show_full_adr ? weekly_full_up : na, "Weekly Full AWR+", color=weekly_full_color, style=plot.style_circles, linewidth=1)
plot(show_weekly and show_full_adr ? weekly_full_down : na, "Weekly Full AWR-", color=weekly_full_color, style=plot.style_circles, linewidth=1)
plot(show_weekly and show_one_third_adr ? weekly_one_third_up : na, "Weekly 1/3 AWR+", color=weekly_one_third_color, style=plot.style_circles, linewidth=1)
plot(show_weekly and show_one_third_adr ? weekly_one_third_down : na, "Weekly 1/3 AWR-", color=weekly_one_third_color, style=plot.style_circles, linewidth=1)
plot(show_weekly and show_two_third_adr ? weekly_two_third_up : na, "Weekly 2/3 AWR+", color=weekly_two_third_color, style=plot.style_circles, linewidth=1)
plot(show_weekly and show_two_third_adr ? weekly_two_third_down : na, "Weekly 2/3 AWR-", color=weekly_two_third_color, style=plot.style_circles, linewidth=1)
plot(show_weekly and show_half_adr ? weekly_half_up : na, "Weekly 1/2 AWR+", color=weekly_half_color, style=plot.style_circles, linewidth=1)
plot(show_weekly and show_half_adr ? weekly_half_down : na, "Weekly 1/2 AWR-", color=weekly_half_color, style=plot.style_circles, linewidth=1)

// Plot Monthly AMR levels for visibility in non-last bars
plot(show_monthly and show_full_adr ? monthly_full_up : na, "Monthly Full AMR+", color=monthly_full_color, style=plot.style_circles, linewidth=1)
plot(show_monthly and show_full_adr ? monthly_full_down : na, "Monthly Full AMR-", color=monthly_full_color, style=plot.style_circles, linewidth=1)
plot(show_monthly and show_one_third_adr ? monthly_one_third_up : na, "Monthly 1/3 AMR+", color=monthly_one_third_color, style=plot.style_circles, linewidth=1)
plot(show_monthly and show_one_third_adr ? monthly_one_third_down : na, "Monthly 1/3 AMR-", color=monthly_one_third_color, style=plot.style_circles, linewidth=1)
plot(show_monthly and show_two_third_adr ? monthly_two_third_up : na, "Monthly 2/3 AMR+", color=monthly_two_third_color, style=plot.style_circles, linewidth=1)
plot(show_monthly and show_two_third_adr ? monthly_two_third_down : na, "Monthly 2/3 AMR-", color=monthly_two_third_color, style=plot.style_circles, linewidth=1)
plot(show_monthly and show_half_adr ? monthly_half_up : na, "Monthly 1/2 AMR+", color=monthly_half_color, style=plot.style_circles, linewidth=1)
plot(show_monthly and show_half_adr ? monthly_half_down : na, "Monthly 1/2 AMR-", color=monthly_half_color, style=plot.style_circles, linewidth=1)

// Determine table position based on settings
var table_pos = position.top_right
if show_data_table == "Left"
    table_pos := position.top_left
else if show_data_table == "Right"
    table_pos := position.top_right
else if show_data_table == "Both"
    // For "Both" we'll use top_right as default and create another table later
    table_pos := position.top_right

// Adjust vertical position based on table_position setting
if table_position == "Center"
    if table_pos == position.top_left
        table_pos := position.middle_left
    else if table_pos == position.top_right
        table_pos := position.middle_right
else if table_position == "Bottom"
    if table_pos == position.top_left
        table_pos := position.bottom_left
    else if table_pos == position.top_right
        table_pos := position.bottom_right

// Create the tables
var table range_table = table.new(table_pos, 4, 4, color.new(color.black, 70))
var table range_table2 = na

if show_data_table == "Both"
    var table_pos2 = position.top_left
    if table_position == "Center"
        table_pos2 := position.middle_left
    else if table_position == "Bottom"
        table_pos2 := position.bottom_left

    range_table2 := table.new(table_pos2, 4, 4, color.new(color.black, 70))

// Add table to display range values
if show_data_table != "None" and barstate.islast
    // Table headers
    table.cell(range_table, 0, 0, "", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 1, 0, "Daily", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 2, 0, "Weekly", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 3, 0, "Monthly", text_color=color.white, bgcolor=color.new(color.black, 70))

    // Range values
    table.cell(range_table, 0, 1, "Range", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 1, 1, str.tostring(daily_range, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 2, 1, str.tostring(weekly_range, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 3, 1, str.tostring(monthly_range, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))

    // Current day's range
    table.cell(range_table, 0, 2, "Today", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 1, 2, str.tostring(high - low, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))

    // Percentage of ADR used
    table.cell(range_table, 0, 3, "% Used", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table, 1, 3, str.tostring(math.round((high - low) / daily_range * 100, 2)) + "%", text_color=color.white, bgcolor=color.new(color.black, 70))

    // Update second table if "Both" is selected
    if show_data_table == "Both" and not na(range_table2)
        // Copy the same data to the second table
        table.cell(range_table2, 0, 0, "", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 1, 0, "Daily", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 2, 0, "Weekly", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 3, 0, "Monthly", text_color=color.white, bgcolor=color.new(color.black, 70))

        table.cell(range_table2, 0, 1, "Range", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 1, 1, str.tostring(daily_range, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 2, 1, str.tostring(weekly_range, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 3, 1, str.tostring(monthly_range, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))

        table.cell(range_table2, 0, 2, "Today", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 1, 2, str.tostring(high - low, "#.####"), text_color=color.white, bgcolor=color.new(color.black, 70))

        table.cell(range_table2, 0, 3, "% Used", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table2, 1, 3, str.tostring(math.round((high - low) / daily_range * 100, 2)) + "%", text_color=color.white, bgcolor=color.new(color.black, 70))



// Alert conditions for price touching ADR levels
// Daily ADR alerts - only active if show_alerts is true
alertcondition(show_alerts and (ta.crossover(high, daily_full_up) or ta.crossunder(low, daily_full_up)), "Price touched Daily Full ADR+", "Price touched Daily Full ADR+ level")
alertcondition(show_alerts and (ta.crossover(high, daily_full_down) or ta.crossunder(low, daily_full_down)), "Price touched Daily Full ADR-", "Price touched Daily Full ADR- level")
alertcondition(show_alerts and (ta.crossover(high, daily_one_third_up) or ta.crossunder(low, daily_one_third_up)), "Price touched Daily 1/3 ADR+", "Price touched Daily 1/3 ADR+ level")
alertcondition(show_alerts and (ta.crossover(high, daily_one_third_down) or ta.crossunder(low, daily_one_third_down)), "Price touched Daily 1/3 ADR-", "Price touched Daily 1/3 ADR- level")

// Weekly AWR alerts - only active if show_alerts is true
alertcondition(show_alerts and (ta.crossover(high, weekly_full_up) or ta.crossunder(low, weekly_full_up)), "Price touched Weekly Full AWR+", "Price touched Weekly Full AWR+ level")
alertcondition(show_alerts and (ta.crossover(high, weekly_full_down) or ta.crossunder(low, weekly_full_down)), "Price touched Weekly Full AWR-", "Price touched Weekly Full AWR- level")
alertcondition(show_alerts and (ta.crossover(high, weekly_one_third_up) or ta.crossunder(low, weekly_one_third_up)), "Price touched Weekly 1/3 AWR+", "Price touched Weekly 1/3 AWR+ level")
alertcondition(show_alerts and (ta.crossover(high, weekly_one_third_down) or ta.crossunder(low, weekly_one_third_down)), "Price touched Weekly 1/3 AWR-", "Price touched Weekly 1/3 AWR- level")

// Monthly AMR alerts - only active if show_alerts is true
alertcondition(show_alerts and (ta.crossover(high, monthly_full_up) or ta.crossunder(low, monthly_full_up)), "Price touched Monthly Full AMR+", "Price touched Monthly Full AMR+ level")
alertcondition(show_alerts and (ta.crossover(high, monthly_full_down) or ta.crossunder(low, monthly_full_down)), "Price touched Monthly Full AMR-", "Price touched Monthly Full AMR- level")
alertcondition(show_alerts and (ta.crossover(high, monthly_one_third_up) or ta.crossunder(low, monthly_one_third_up)), "Price touched Monthly 1/3 AMR+", "Price touched Monthly 1/3 AMR+ level")
alertcondition(show_alerts and (ta.crossover(high, monthly_one_third_down) or ta.crossunder(low, monthly_one_third_down)), "Price touched Monthly 1/3 AMR-", "Price touched Monthly 1/3 AMR- level")
