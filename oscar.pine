//@version=4
// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GenZai 18-08-2021 v5 

study("OSCAR", resolution="")
len = input(title="Oscar Candles", defval=8)
slowLen = input(title="Slow Oscar Candles", defval=16)
smoothing = input(title="Oscar Smoothing", defval="RMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"])
slowOscarSmoothing = input(title="Slow Oscar Smoothing", defval="WMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"])
crossSignalSensitivity = input(title="Cross Signal Sensitivity", defval=0.5, step=0.1)
OscarOfIndicator = input(title="Choose input type", type=input.source, defval=close)
bottomSignalLine = input(title="Bottom Signal Line", defval=35, step=5)
topSignalLine = input(title="Top Signal Line", defval=65, step=5)


ma(smoothing, OscarParam, len) => 
    if smoothing == "RMA"
        rma(OscarParam, len)
    else
        if smoothing == "SMA"
            sma(OscarParam, len)
        else 
            if smoothing == "EMA"
                ema(OscarParam, len)
            else 
                if smoothing == "WMA"
                    wma(OscarParam, len)
                else 
                    if smoothing == "LSMA"
                        linreg(OscarParam, len, 0)
                    else 
                        if smoothing == "HMA"
                            hma(OscarParam, len)   
                        else 
                            OscarParam

A = highest(OscarOfIndicator, len)
B = lowest(OscarOfIndicator, len)
OscarRough = (OscarOfIndicator - B) / (A - B) * 100
Oscar1 = (OscarRough[1] / 3) * 2
OscarThird = OscarRough / 3
Oscar = Oscar1 + OscarThird
smoothedOscarRough = ma(smoothing, OscarRough, len)
smoothedOscar = ma(smoothing, Oscar, len)
plot(smoothedOscarRough, title="Oscar Rough", linewidth=2, color=color.green)
plot(smoothedOscar, title="Oscar", linewidth=2, color=color.red)
hline(bottomSignalLine, title="Bottom Signal", color=color.green, linestyle=hline.style_dashed)
hline(topSignalLine, title="Top Signal", color=color.red, linestyle=hline.style_dashed)
hline(20, title="Bottom Level", color=color.gray, linestyle=hline.style_dashed)
hline(80, title="Top Level", color=color.gray, linestyle=hline.style_dashed)

crossSensitivity = max(smoothedOscarRough, smoothedOscar) - min(smoothedOscarRough, smoothedOscar)
plotchar(crossSensitivity, "cross sensitivity Value", "", location=location.top, color = color.blue)

plotshape(crossover(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough < bottomSignalLine, color = color.green, style=shape.triangleup, location=location.bottom)
plotshape(crossover(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > smoothedOscar and smoothedOscar < bottomSignalLine, color = color.green, style=shape.arrowup, location=location.bottom)
plotshape(crossunder(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > topSignalLine, color = color.red, style=shape.triangledown, location=location.top)
plotshape(crossunder(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscar > smoothedOscarRough and smoothedOscar > topSignalLine, color = color.red, style=shape.arrowdown, location=location.top)

// Slow Oscar

slowOscarMa(slowOscarSmoothing, slowOscarParam, slowLen) => 
    if slowOscarSmoothing == "RMA"
        rma(slowOscarParam, slowLen)
    else
        if slowOscarSmoothing == "SMA"
            sma(slowOscarParam, slowLen)
        else 
            if slowOscarSmoothing == "EMA"
                ema(slowOscarParam, slowLen)
            else 
                if slowOscarSmoothing == "WMA"
                    wma(slowOscarParam, slowLen)
                else 
                    if slowOscarSmoothing == "LSMA"
                        linreg(slowOscarParam, slowLen, 0)
                    else 
                        if slowOscarSmoothing == "HMA"
                            hma(slowOscarParam, slowLen)   
                        else 
                            slowOscarParam

slowA = highest(OscarOfIndicator, slowLen)
slowB = lowest(OscarOfIndicator, slowLen)
slowOscarRough = (OscarOfIndicator - slowB) / (slowA - slowB) * 100
slowOscar1 = (slowOscarRough[1] / 3) * 2
slowOscarThird = slowOscarRough / 3
slowOscar = slowOscar1 + slowOscarThird
smoothedSlowOscar = slowOscarMa(slowOscarSmoothing, slowOscar, slowLen)
plot(smoothedSlowOscar, title="slowOscar", linewidth=2, color=color.purple, display=display.none)

