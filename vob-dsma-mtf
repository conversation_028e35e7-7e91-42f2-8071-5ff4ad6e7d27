// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// ©️ Dev Lucem

//@version=5
//@author=devlucem

// THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
// THE <PERSON><PERSON><PERSON><PERSON> SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
// I HOPE U LOVE IT

// Seek Menu

// indicator(title = 'Volumized Order Blocks | Ravi', overlay = true, max_boxes_count = vobMaxBoxesCount, max_labels_count = vobMaxBoxesCount, max_lines_count = vobMaxBoxesCount, max_bars_back = 5000)

const bool vobDEBUG = false
const int vobMaxBoxesCount = 500
const float vobOverlapThresholdPercentage = 0
const int vobMaxDistanceToLastBar = 1750 // Affects Running Time
const int vobMaxOrderBlocks = 30







//dsma


//@version=5
// indicator("Multi Deviation Scaled Moving Average [Ravi] - Extended", "Multi DSMA - [Ravi] - Ext", overlay=true)

// ---------------------------------------------------------------------------------------------------------------------
// USER INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

int dsma_period = input.int(30, title="Period")
int dsma_step = 100 - input.int(60, "Sensitivity", minval = 0, maxval = 100, tooltip = "The Lower input Lower sensitivity")
series float dsma_src = hlc3

// Timeframe Inputs for DSMA
bool dsma_show_tf1 = input.bool(true, title="Show 3-Minute DSMA")
dsma_tf1 = input.timeframe('3', "3-Minute Timeframe")

bool dsma_show_tf2 = input.bool(true, title="Show 5-Minute DSMA")
dsma_tf2 = input.timeframe('5', "5-Minute Timeframe")

bool dsma_show_tf3 = input.bool(true, title="Show 15-Minute DSMA")
dsma_tf3 = input.timeframe('15', "15-Minute Timeframe")

bool dsma_show_tf4 = input.bool(true, title="Show 1-Hour DSMA")
dsma_tf4 = input.timeframe('60', "1-Hour Timeframe")

bool dsma_show_tf5 = input.bool(true, title="Show 4-Hour DSMA")
dsma_tf5 = input.timeframe('240', "4-Hour Timeframe")

bool dsma_show_tf6 = input.bool(true, title="Show 1-Day DSMA")
dsma_tf6 = input.timeframe('D', "1-Day Timeframe")

// Configurable Colors for upper and lower for each timeframe
color dsma_upper_color1 = input.color(#41a1ce, "3-Minute Upper Color")
color dsma_down_color1 = input.color(#ce8541, "3-Minute Down Color")
color dsma_upper_color2 = input.color(#41a1ce, "5-Minute Upper Color")
color dsma_down_color2 = input.color(#ce8541, "5-Minute Down Color")
color dsma_upper_color3 = input.color(#41a1ce, "15-Minute Upper Color")
color dsma_down_color3 = input.color(#ce8541, "15-Minute Down Color")
color dsma_upper_color4 = input.color(#41a1ce, "1-Hour Upper Color")
color dsma_down_color4 = input.color(#ce8541, "1-Hour Down Color")
color dsma_upper_color5 = input.color(#41a1ce, "4-Hour Upper Color")
color dsma_down_color5 = input.color(#ce8541, "4-Hour Down Color")
color dsma_upper_color6 = input.color(#41a1ce, "1-Day Upper Color")
color dsma_down_color6 = input.color(#ce8541, "1-Day Down Color")

// Colors for each timeframe's DSMA
color dsma_dsma1_color = color.new(#1f77b4, 0) // 3-Minute DSMA Color
color dsma_dsma2_color = color.new(#ff7f0e, 0) // 5-Minute DSMA Color
color dsma_dsma3_color = color.new(#2ca02c, 0) // 15-Minute DSMA Color
color dsma_dsma4_color = color.new(#d62728, 0) // 1-Hour DSMA Color
color dsma_dsma5_color = color.new(#9467bd, 0) // 4-Hour DSMA Color
color dsma_dsma6_color = color.new(#8c564b, 0) // 1-Day DSMA Color

// ---------------------------------------------------------------------------------------------------------------------
// INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

// Function to calculate the Deviation Scaled Moving Average (DSMA)
dsma(src, int dsma_period)=>
    var float dsma_a1 = 0.0
    var float dsma_b1 = 0.0
    var float dsma_c1 = 0.0
    var float dsma_c2 = 0.0
    var float dsma_c3 = 0.0
    var float dsma_filt = 0.0
    var float dsma_dsma = 0.0
    var float dsma_s = 0.0

    if barstate.isfirst
        dsma_pi = 3.1415926535897932
        dsma_g = math.sqrt(2)
        dsma_s := 2 * dsma_pi / dsma_period
        dsma_a1 := math.exp(-dsma_g * dsma_pi / (0.5 * dsma_period))
        dsma_b1 := 2 * dsma_a1 * math.cos(dsma_g * dsma_s / (0.5 * dsma_period))
        dsma_c2 := dsma_b1
        dsma_c3 := -dsma_a1 * dsma_a1
        dsma_c1 := 1 - dsma_c2 - dsma_c3

    dsma_zeros = (close - close[2])
    dsma_filt := dsma_c1 * (dsma_zeros + dsma_zeros[1]) / 2 + dsma_c2 * nz(dsma_filt[1]) + dsma_c3 * nz(dsma_filt[2])

    dsma_rms = math.sqrt(ta.ema(math.pow(dsma_filt, 2), dsma_period))
    dsma_scaled_filt = dsma_rms != 0 ? dsma_filt / dsma_rms : 0
    dsma_alpha1 = math.abs(dsma_scaled_filt) * 5 / dsma_period
    dsma_dsma := dsma_alpha1 * close + (1 - dsma_alpha1) * nz(dsma_dsma[1])

    dsma_dsma

// Function to calculate trend percentage, color, and average DSMA
dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color, dsma_down_color)=>
    dsma_dsma_arr = array.new<float>()

    dsma_length = dsma_period
    dsma1 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma2 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma3 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma4 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma5 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma6 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma7 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma8 = dsma(dsma_src, dsma_length)

    array.push(dsma_dsma_arr, dsma1)
    array.push(dsma_dsma_arr, dsma2)
    array.push(dsma_dsma_arr, dsma3)
    array.push(dsma_dsma_arr, dsma4)
    array.push(dsma_dsma_arr, dsma5)
    array.push(dsma_dsma_arr, dsma6)
    array.push(dsma_dsma_arr, dsma7)
    array.push(dsma_dsma_arr, dsma8)

    dsma_val = 0.14285714285714285714285714285714

    dsma_score = 0.
    for dsma_i = 0 to array.size(dsma_dsma_arr) - 1
        dsma_dsma = array.get(dsma_dsma_arr, dsma_i)
        if dsma_dsma > array.get(dsma_dsma_arr, 7)
            dsma_score += dsma_val

    dsma_color =  dsma_score > 0.5 
                 ? color.from_gradient(dsma_score, 0.5, 1, na, dsma_upper_color) 
                 : color.from_gradient(dsma_score, 0, 0.5, dsma_down_color, na)

    [dsma_score, array.avg(dsma_dsma_arr), dsma_color]

// Apply the multi-timeframe logic using `request.security` for all timeframes
[dsma_score1, dsma_ma1, dsma_color1] = request.security(syminfo.tickerid, dsma_tf1, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color1, dsma_down_color1))
[dsma_score2, dsma_ma2, dsma_color2] = request.security(syminfo.tickerid, dsma_tf2, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color2, dsma_down_color2))
[dsma_score3, dsma_ma3, dsma_color3] = request.security(syminfo.tickerid, dsma_tf3, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color3, dsma_down_color3))
[dsma_score4, dsma_ma4, dsma_color4] = request.security(syminfo.tickerid, dsma_tf4, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color4, dsma_down_color4))
[dsma_score5, dsma_ma5, dsma_color5] = request.security(syminfo.tickerid, dsma_tf5, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color5, dsma_down_color5))
[dsma_score6, dsma_ma6, dsma_color6] = request.security(syminfo.tickerid, dsma_tf6, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color6, dsma_down_color6))

// Detect crossovers for signal generation
dsma_cross_up1 = ta.crossover(dsma_score1, 0.3)
dsma_cross_dn1 = ta.crossunder(dsma_score1, 0.7)

dsma_cross_up2 = ta.crossover(dsma_score2, 0.3)
dsma_cross_dn2 = ta.crossunder(dsma_score2, 0.7)

dsma_cross_up3 = ta.crossover(dsma_score3, 0.3)
dsma_cross_dn3 = ta.crossunder(dsma_score3, 0.7)

dsma_cross_up4 = ta.crossover(dsma_score4, 0.3)
dsma_cross_dn4 = ta.crossunder(dsma_score4, 0.7)

dsma_cross_up5 = ta.crossover(dsma_score5, 0.3)
dsma_cross_dn5 = ta.crossunder(dsma_score5, 0.7)

dsma_cross_up6 = ta.crossover(dsma_score6, 0.3)
dsma_cross_dn6 = ta.crossunder(dsma_score6, 0.7)

// ---------------------------------------------------------------------------------------------------------------------
// VISUALIZATION
// ---------------------------------------------------------------------------------------------------------------------

// 3-Minute DSMA
plot(dsma_show_tf1 ? dsma_ma1 : na, color = dsma_color1, linewidth = 2)
plotshape(dsma_show_tf1 and dsma_cross_up1 ? dsma_ma1 : na, title="3-Min Up", location=location.absolute, color=color.new(dsma_upper_color1, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf1 and dsma_cross_dn1 ? dsma_ma1 : na, title="3-Min Down", location=location.absolute, color=color.new(dsma_down_color1, 50), size=size.small, style=shape.diamond)

// 5-Minute DSMA
plot(dsma_show_tf2 ? dsma_ma2 : na, color = dsma_color2, linewidth = 2)
plotshape(dsma_show_tf2 and dsma_cross_up2 ? dsma_ma2 : na, title="5-Min Up", location=location.absolute, color=color.new(dsma_upper_color2, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf2 and dsma_cross_dn2 ? dsma_ma2 : na, title="5-Min Down", location=location.absolute, color=color.new(dsma_down_color2, 50), size=size.small, style=shape.diamond)

// 15-Minute DSMA
plot(dsma_show_tf3 ? dsma_ma3 : na, color = dsma_color3, linewidth = 2)
plotshape(dsma_show_tf3 and dsma_cross_up3 ? dsma_ma3 : na, title="15-Min Up", location=location.absolute, color=color.new(dsma_upper_color3, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf3 and dsma_cross_dn3 ? dsma_ma3 : na, title="15-Min Down", location=location.absolute, color=color.new(dsma_down_color3, 50), size=size.small, style=shape.diamond)

// 1-Hour DSMA
plot(dsma_show_tf4 ? dsma_ma4 : na, color = dsma_color4, linewidth = 2)
plotshape(dsma_show_tf4 and dsma_cross_up4 ? dsma_ma4 : na, title="1-Hour Up", location=location.absolute, color=color.new(dsma_upper_color4, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf4 and dsma_cross_dn4 ? dsma_ma4 : na, title="1-Hour Down", location=location.absolute, color=color.new(dsma_down_color4, 50), size=size.small, style=shape.diamond)

// 4-Hour DSMA
plot(dsma_show_tf5 ? dsma_ma5 : na, color = dsma_color5, linewidth = 2)
plotshape(dsma_show_tf5 and dsma_cross_up5 ? dsma_ma5 : na, title="4-Hour Up", location=location.absolute, color=color.new(dsma_upper_color5, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf5 and dsma_cross_dn5 ? dsma_ma5 : na, title="4-Hour Down", location=location.absolute, color=color.new(dsma_down_color5, 50), size=size.small, style=shape.diamond)

// 1-Day DSMA
plot(dsma_show_tf6 ? dsma_ma6 : na, color = dsma_color6, linewidth = 2)
plotshape(dsma_show_tf6 and dsma_cross_up6 ? dsma_ma6 : na, title="1-Day Up", location=location.absolute, color=color.new(dsma_upper_color6, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf6 and dsma_cross_dn6 ? dsma_ma6 : na, title="1-Day Down", location=location.absolute, color=color.new(dsma_down_color6, 50), size=size.small, style=shape.diamond)




//vob


//@version=5



// indicator(title = 'Volumized Order Blocks | Ravi', overlay = true, max_boxes_count = vobMaxBoxesCount, max_labels_count = vobMaxBoxesCount, max_lines_count = vobMaxBoxesCount, max_bars_back = 5000)

vobShowInvalidated = input.bool(true, "Show Historic Zones", group = "General Configuration", display = display.none)
vobOBsEnabled = true
vobOrderBlockVolumetricInfo = input.bool(true, "Volumetric Info", group = "General Configuration", inline="EV", display = display.none)
vobObEndMethod = input.string("Wick", "Zone Invalidation", options = ["Wick", "Close"], group = "General Configuration", display = display.none)
vobCombineOBs = vobDEBUG ? input.bool(true, "Combine Zones", group = "General Configuration", display = display.none) : true
vobMaxATRMult = vobDEBUG ? input.float(3.5,"Max Atr Multiplier", group = "General Configuration") : 3.5
vobSwingLength = input.int(10, 'Swing Length', minval = 3, tooltip="Swing length is used when finding order block formations. Smaller values will result in finding smaller order blocks.",group = "General Configuration", display = display.none)
vobZoneCount = input.string("Low", 'Zone Count', options = ["High", "Medium", "Low", "One"], tooltip = "Number of Order Block Zones to be rendered. Higher options will result in older Order Blocks shown.",  group = "General Configuration", display = display.none)
vobBullOrderBlockColor = input(#08998180, 'Bullish', inline = 'obColor', group = 'General Configuration', display = display.none)
vobBearOrderBlockColor = input(#f2364680, 'Bearish', inline = 'obColor', group = 'General Configuration', display = display.none)

vobBullishOrderBlocks = vobZoneCount == "One" ? 1 : vobZoneCount == "Low" ? 3 : vobZoneCount == "Medium" ? 5 : 10
vobBearishOrderBlocks = vobZoneCount == "One" ? 1 : vobZoneCount == "Low" ? 3 : vobZoneCount == "Medium" ? 5 : 10

// Timeframe Inputs
vobTimeframe1Enabled = input.bool(true, title="Enable Timeframe 1", group="Timeframes")
vobTimeframe1 = input.timeframe('', title="Timeframe 1", group="Timeframes", inline="TF1")
vobTimeframe2Enabled = input.bool(false, title="Enable Timeframe 2", group="Timeframes")
vobTimeframe2 = input.timeframe('', title="Timeframe 2", group="Timeframes", inline="TF2")
vobTimeframe3Enabled = input.bool(false, title="Enable Timeframe 3", group="Timeframes")
vobTimeframe3 = input.timeframe('', title="Timeframe 3", group="Timeframes", inline="TF3")
vobTimeframe4Enabled = input.bool(false, title="Enable Timeframe 4", group="Timeframes")
vobTimeframe4 = input.timeframe('', title="Timeframe 4", group="Timeframes", inline="TF4")
vobTimeframe5Enabled = input.bool(false, title="Enable Timeframe 5", group="Timeframes")
vobTimeframe5 = input.timeframe('', title="Timeframe 5", group="Timeframes", inline="TF5")

vobTextColor = input.color(#ffffff80, "Text Color", group = "Style")
vobExtendZonesBy = vobDEBUG ? input.int(15, "Extend Zones", group = "Style", minval = 1, maxval = 30, inline = "ExtendZones") : 15
vobExtendZonesDynamic = vobDEBUG ? input.bool(true, "Dynamic", group = "Style", inline = "ExtendZones") : true
vobCombinedText = vobDEBUG ? input.bool(false, "Combined Text", group = "Style", inline = "CombinedColor") : false
vobVolumeBarsPlace = vobDEBUG ? input.string("Left", "Show Volume Bars At", options = ["Left", "Right"], group = "Style", inline = "volumebars") : "Left"
vobMirrorVolumeBars = vobDEBUG ? input.bool(true, "Mirror Volume Bars", group = "Style", inline = "volumebars") : true

vobVolumeBarsLeftSide = (vobVolumeBarsPlace == "Left")
vobExtendZonesByTime = vobExtendZonesBy * timeframe.in_seconds(timeframe.period) * 1000

vobAtr = ta.atr(10)

// Type Definitions
type vobOrderBlockInfo
    float vobTop
    float vobBottom
    float vobObVolume
    string vobObType
    int vobStartTime
    float vobBbVolume
    float vobObLowVolume
    float vobObHighVolume
    bool vobBreaker
    int vobBreakTime
    string vobTimeframeStr
    bool vobDisabled = false
    string vobCombinedTimeframesStr = na
    bool vobCombined = false

type vobOrderBlock
    vobOrderBlockInfo vobInfo
    bool vobIsRendered = false

    box vobOrderBox = na
    box vobBreakerBox = na

    line vobOrderBoxLineTop = na
    line vobOrderBoxLineBottom = na
    line vobBreakerBoxLineTop = na
    line vobBreakerBoxLineBottom = na

    box vobOrderBoxText = na
    box vobOrderBoxPositive = na
    box vobOrderBoxNegative = na

    line vobOrderSeperator = na
    line vobOrderTextSeperator = na

vobCreateOrderBlock(vobOrderBlockInfo vobOrderBlockInfoF) =>
    vobOrderBlock vobNewOrderBlock = vobOrderBlock.new(vobOrderBlockInfoF)
    vobNewOrderBlock

vobSafeDeleteOrderBlock(vobOrderBlock vobOrderBlockF) =>
    vobOrderBlockF.vobIsRendered := false

    box.delete(vobOrderBlockF.vobOrderBox)
    box.delete(vobOrderBlockF.vobBreakerBox)
    box.delete(vobOrderBlockF.vobOrderBoxText)
    box.delete(vobOrderBlockF.vobOrderBoxPositive)
    box.delete(vobOrderBlockF.vobOrderBoxNegative)

    line.delete(vobOrderBlockF.vobOrderBoxLineTop)
    line.delete(vobOrderBlockF.vobOrderBoxLineBottom)
    line.delete(vobOrderBlockF.vobBreakerBoxLineTop)
    line.delete(vobOrderBlockF.vobBreakerBoxLineBottom)
    line.delete(vobOrderBlockF.vobOrderSeperator)
    line.delete(vobOrderBlockF.vobOrderTextSeperator)

type vobTimeframeInfo
    int vobIndex = na
    string vobTimeframeStr = na
    bool vobIsEnabled = false

    vobOrderBlockInfo[] vobBullishOrderBlocksList = na
    vobOrderBlockInfo[] vobBearishOrderBlocksList = na

vobNewTimeframeInfo(vobIndex, vobTimeframeStr, vobIsEnabled) =>
    vobNewTFInfo = vobTimeframeInfo.new()
    vobNewTFInfo.vobIndex := vobIndex
    vobNewTFInfo.vobIsEnabled := vobIsEnabled
    vobNewTFInfo.vobTimeframeStr := vobTimeframeStr

    vobNewTFInfo

type vobOBSwing
    int vobX = na    
    float vobY = na
    float vobSwingVolume = na
    bool vobCrossed = false

// ____ TYPES END ____

// Timeframe information initialization
var vobTimeframeInfo[] vobTimeframeInfos = array.from(vobNewTimeframeInfo(1, vobTimeframe1, vobTimeframe1Enabled),vobNewTimeframeInfo(2, vobTimeframe2, vobTimeframe2Enabled),vobNewTimeframeInfo(3, vobTimeframe3, vobTimeframe3Enabled),vobNewTimeframeInfo(4, vobTimeframe4, vobTimeframe4Enabled),vobNewTimeframeInfo(5, vobTimeframe5, vobTimeframe5Enabled))

var vobBullishOrderBlocksList = array.new<vobOrderBlockInfo>(0)
var vobBearishOrderBlocksList = array.new<vobOrderBlockInfo>(0)

var vobAllOrderBlocksList = array.new<vobOrderBlock>(0)

vobMoveLine(vob_line, vob_x, vob_y, vob_x2) =>
    line.set_xy1(vob_line, vob_x,  vob_y)
    line.set_xy2(vob_line, vob_x2, vob_y)

vobMoveBox(vob_box, vob_topLeftX, vob_topLeftY, vob_bottomRightX, vob_bottomRightY) =>
    box.set_lefttop(vob_box, vob_topLeftX, vob_topLeftY)
    box.set_rightbottom(vob_box, vob_bottomRightX, vob_bottomRightY)

vobIsTimeframeLower(vobTimeframe1F, vobTimeframe2F) =>
    timeframe.in_seconds(vobTimeframe1F) < timeframe.in_seconds(vobTimeframe2F)

vobGetMinTimeframe(vobTimeframe1F, vobTimeframe2F) =>
    if vobIsTimeframeLower(vobTimeframe1F, vobTimeframe2F)
        vobTimeframe1F
    else
        vobTimeframe2F

vobGetMaxTimeframe(vobTimeframe1F, vobTimeframe2F) =>
    if vobIsTimeframeLower(vobTimeframe1F, vobTimeframe2F)
        vobTimeframe2F
    else
        vobTimeframe1F

vobFormatTimeframeString(vobFormatTimeframe) =>
    vobTimeframeF = vobFormatTimeframe == "" ? timeframe.period : vobFormatTimeframe
    
    if str.contains(vobTimeframeF, "D") or str.contains(vobTimeframeF, "W") or str.contains(vobTimeframeF, "S") or str.contains(vobTimeframeF, "M")
        vobTimeframeF
    else
        vobSeconds = timeframe.in_seconds(vobTimeframeF)
        if vobSeconds >= 3600
            vobHourCount = int(vobSeconds / 3600)
            str.tostring(vobHourCount) + " Hour" + (vobHourCount > 1 ? "s" : "")
        else
            vobTimeframeF + " Min"

vobBetterCross(vobS1, vobS2) =>
    string vobRet = na
    if vobS1 >= vobS2 and vobS1[1] < vobS2
        vobRet := "Bull"
    if vobS1 < vobS2 and vobS1[1] >= vobS2
        vobRet := "Bear"
    vobRet

vobColorWithTransparency(vobColorF, vobTransparencyX) =>
    color.new(vobColorF, color.t(vobColorF) * vobTransparencyX)

vobCreateOBBox(vobBoxColor, vobTransparencyX = 1.0, vobXlocType = xloc.bar_time) =>
    box.new(na, na, na, na, text_size = size.normal, xloc = vobXlocType, extend = extend.none, bgcolor = vobColorWithTransparency(vobBoxColor, vobTransparencyX), text_color = vobTextColor, text_halign = text.align_center, border_color = #00000000)

vobRenderOrderBlock(vobOrderBlock vobOb) =>
    vobOrderBlockInfo vobInfo = vobOb.vobInfo
    vobOb.vobIsRendered := true
    vobOrderColor = vobOb.vobInfo.vobObType == "Bull" ? vobBullOrderBlockColor : vobBearOrderBlockColor

    if vobOBsEnabled and (not false or not (false and vobInfo.vobBreaker)) and not (not vobShowInvalidated and vobInfo.vobBreaker)
        vobOb.vobOrderBox := vobCreateOBBox(vobOrderColor, 1.5)
        if vobOb.vobInfo.vobCombined
            vobOb.vobOrderBox.set_bgcolor(vobColorWithTransparency(vobOrderColor, 1.1))
        vobOb.vobOrderBoxText := vobCreateOBBox(color.new(color.white, 100))
        if vobOrderBlockVolumetricInfo
            vobOb.vobOrderBoxPositive := vobCreateOBBox(vobBullOrderBlockColor)
            vobOb.vobOrderBoxNegative := vobCreateOBBox(vobBearOrderBlockColor)
            vobOb.vobOrderSeperator := line.new(na,na,na,na,xloc.bar_time,extend.none,vobTextColor,line.style_dashed,1)
            vobOb.vobOrderTextSeperator := line.new(na,na,na,na,xloc.bar_time,extend.none,vobTextColor,line.style_solid,1)

        vobZoneSize = vobExtendZonesDynamic ? na(vobInfo.vobBreakTime) ? vobExtendZonesByTime : (vobInfo.vobBreakTime - vobInfo.vobStartTime) : vobExtendZonesByTime
        if na(vobInfo.vobBreakTime)
            vobZoneSize := (time + 1) - vobInfo.vobStartTime

        vobStartX = vobVolumeBarsLeftSide ? vobInfo.vobStartTime : vobInfo.vobStartTime + vobZoneSize - vobZoneSize / 3
        vobMaxEndX = vobVolumeBarsLeftSide ? vobInfo.vobStartTime + vobZoneSize / 3 : vobInfo.vobStartTime + vobZoneSize

        vobMoveBox(vobOb.vobOrderBox, vobInfo.vobStartTime, vobInfo.vobTop, vobInfo.vobStartTime + vobZoneSize, vobInfo.vobBottom)
        vobMoveBox(vobOb.vobOrderBoxText, vobVolumeBarsLeftSide ? vobMaxEndX : vobInfo.vobStartTime, vobInfo.vobTop, vobVolumeBarsLeftSide ? vobInfo.vobStartTime + vobZoneSize : vobStartX, vobInfo.vobBottom)

        vobPercentage = int((math.min(vobInfo.vobObHighVolume, vobInfo.vobObLowVolume) / math.max(vobInfo.vobObHighVolume, vobInfo.vobObLowVolume)) * 100.0)
        vobOBText = (na(vobOb.vobInfo.vobCombinedTimeframesStr) ? vobFormatTimeframeString(vobOb.vobInfo.vobTimeframeStr) : vobOb.vobInfo.vobCombinedTimeframesStr) + " OB"
        box.set_text(vobOb.vobOrderBoxText, (vobOrderBlockVolumetricInfo ? str.tostring(vobOb.vobInfo.vobObVolume, format.volume) + " (" + str.tostring(vobPercentage) + "%)\n" : "") + (vobCombinedText and vobOb.vobInfo.vobCombined ? "[Combined]\n" : "") + vobOBText)

        if vobOrderBlockVolumetricInfo
            vobShowHighLowBoxText = false

            vobCurEndXHigh = int(math.ceil((vobInfo.vobObHighVolume / vobInfo.vobObVolume) * (vobMaxEndX - vobStartX) + vobStartX))
            vobCurEndXLow = int(math.ceil((vobInfo.vobObLowVolume / vobInfo.vobObVolume) * (vobMaxEndX - vobStartX) + vobStartX))

            vobMoveBox(vobOb.vobOrderBoxPositive, vobMirrorVolumeBars ? vobStartX : vobCurEndXLow, vobInfo.vobTop, vobMirrorVolumeBars ? vobCurEndXHigh : vobMaxEndX, (vobInfo.vobBottom + vobInfo.vobTop) / 2)
            box.set_text(vobOb.vobOrderBoxPositive, vobShowHighLowBoxText ? str.tostring(vobInfo.vobObHighVolume, format.volume) : "")

            vobMoveBox(vobOb.vobOrderBoxNegative, vobMirrorVolumeBars ? vobStartX : vobCurEndXHigh, vobInfo.vobBottom, vobMirrorVolumeBars ? vobCurEndXLow : vobMaxEndX, (vobInfo.vobBottom + vobInfo.vobTop) / 2)
            box.set_text(vobOb.vobOrderBoxNegative, vobShowHighLowBoxText ? str.tostring(vobInfo.vobObLowVolume, format.volume) : "")

            vobMoveLine(vobOb.vobOrderSeperator, vobVolumeBarsLeftSide ? vobStartX : vobMaxEndX, (vobInfo.vobBottom + vobInfo.vobTop) / 2, vobVolumeBarsLeftSide ? vobMaxEndX : vobStartX)

            line.set_xy1(vobOb.vobOrderTextSeperator, vobVolumeBarsLeftSide ? vobMaxEndX : vobStartX, vobInfo.vobTop)
            line.set_xy2(vobOb.vobOrderTextSeperator, vobVolumeBarsLeftSide ? vobMaxEndX : vobStartX, vobInfo.vobBottom)

vobFindOBSwings(vobLen) =>
    var vobSwingType = 0
    var vobOBSwing vobTop = vobOBSwing.new(na, na)
    var vobOBSwing vobBottom = vobOBSwing.new(na, na)
    
    vobUpper = ta.highest(vobLen)
    vobLower = ta.lowest(vobLen)

    vobSwingType := high[vobLen] > vobUpper ? 0 : low[vobLen] < vobLower ? 1 : vobSwingType

    if vobSwingType == 0 and vobSwingType[1] != 0
        vobTop := vobOBSwing.new(bar_index[vobLen], high[vobLen], volume[vobLen])
    
    if vobSwingType == 1 and vobSwingType[1] != 1
        vobBottom := vobOBSwing.new(bar_index[vobLen], low[vobLen], volume[vobLen])

    [vobTop, vobBottom]

vobFindOrderBlocks () =>
    if bar_index > last_bar_index - vobMaxDistanceToLastBar
        [vobTop, vobBtm] = vobFindOBSwings(vobSwingLength)
        vobUseBody = false
        vobMax = vobUseBody ? math.max(close, open) : high
        vobMin = vobUseBody ? math.min(close, open) : low

        // Bullish Order Block
        vobBullishBreaked = 0

        if vobBullishOrderBlocksList.size() > 0
            for i = vobBullishOrderBlocksList.size() - 1 to 0
                vobCurrentOB = vobBullishOrderBlocksList.get(i)
            
                if not vobCurrentOB.vobBreaker 
                    if (vobObEndMethod == "Wick" ? low : math.min(open, close)) < vobCurrentOB.vobBottom
                        vobCurrentOB.vobBreaker := true
                        vobCurrentOB.vobBreakTime := time
                        vobCurrentOB.vobBbVolume := volume
                else
                    if high > vobCurrentOB.vobTop
                        vobBullishOrderBlocksList.remove(i)
                    else if i < vobBullishOrderBlocks and vobTop.vobY < vobCurrentOB.vobTop and vobTop.vobY > vobCurrentOB.vobBottom 
                        vobBullishBreaked := 1

        if close > vobTop.vobY and not vobTop.vobCrossed
            vobTop.vobCrossed := true

            vobBoxBtm = vobMax[1]
            vobBoxTop = vobMin[1]
            vobBoxLoc = time[1]

            for i = 1 to (bar_index - vobTop.vobX) - 1
                vobBoxBtm := math.min(vobMin[i], vobBoxBtm)
                vobBoxTop := vobBoxBtm == vobMin[i] ? vobMax[i] : vobBoxTop
                vobBoxLoc := vobBoxBtm == vobMin[i] ? time[i] : vobBoxLoc

            vobNewOrderBlockInfo = vobOrderBlockInfo.new(vobBoxTop, vobBoxBtm, volume + volume[1] + volume[2], "Bull", vobBoxLoc)
            vobNewOrderBlockInfo.vobObLowVolume := volume[2]
            vobNewOrderBlockInfo.vobObHighVolume := volume + volume[1]
            
            vobObSize = math.abs(vobNewOrderBlockInfo.vobTop - vobNewOrderBlockInfo.vobBottom)
            if vobObSize <= vobAtr * vobMaxATRMult
                vobBullishOrderBlocksList.unshift(vobNewOrderBlockInfo)
                if vobBullishOrderBlocksList.size() > vobMaxOrderBlocks
                    vobBullishOrderBlocksList.pop()

        // Bearish Order Block
        
        vobBearishBreaked = 0

        if vobBearishOrderBlocksList.size() > 0
            for i = vobBearishOrderBlocksList.size() - 1 to 0
                vobCurrentOB = vobBearishOrderBlocksList.get(i)

                if not vobCurrentOB.vobBreaker 
                    if (vobObEndMethod == "Wick" ? high : math.max(open, close)) > vobCurrentOB.vobTop
                        vobCurrentOB.vobBreaker := true
                        vobCurrentOB.vobBreakTime := time
                        vobCurrentOB.vobBbVolume := volume
                else
                    if low < vobCurrentOB.vobBottom
                        vobBearishOrderBlocksList.remove(i)
                    else if i < vobBearishOrderBlocks and vobBtm.vobY > vobCurrentOB.vobBottom and vobBtm.vobY < vobCurrentOB.vobTop 
                        vobBearishBreaked := 1

        if close < vobBtm.vobY and not vobBtm.vobCrossed
            vobBtm.vobCrossed := true

            vobBoxBtm = vobMin[1]
            vobBoxTop = vobMax[1]
            vobBoxLoc = time[1]

            for i = 1 to (bar_index - vobBtm.vobX) - 1
                vobBoxTop := math.max(vobMax[i], vobBoxTop)
                vobBoxBtm := vobBoxTop == vobMax[i] ? vobMin[i] : vobBoxBtm
                vobBoxLoc := vobBoxTop == vobMax[i] ? time[i] : vobBoxLoc

            vobNewOrderBlockInfo = vobOrderBlockInfo.new(vobBoxTop, vobBoxBtm, volume + volume[1] + volume[2], "Bear", vobBoxLoc)
            vobNewOrderBlockInfo.vobObLowVolume := volume + volume[1]
            vobNewOrderBlockInfo.vobObHighVolume := volume[2]

            vobObSize = math.abs(vobNewOrderBlockInfo.vobTop - vobNewOrderBlockInfo.vobBottom)
            if vobObSize <= vobAtr * vobMaxATRMult
                vobBearishOrderBlocksList.unshift(vobNewOrderBlockInfo)
                if vobBearishOrderBlocksList.size() > vobMaxOrderBlocks
                    vobBearishOrderBlocksList.pop()
    true

vobAreaOfOB(vobOrderBlockInfo vobOBInfoF) =>
    float vobXA1 = vobOBInfoF.vobStartTime
    float vobXA2 = na(vobOBInfoF.vobBreakTime) ? time + 1 : vobOBInfoF.vobBreakTime
    float vobYA1 = vobOBInfoF.vobTop
    float vobYA2 = vobOBInfoF.vobBottom
    float vobEdge1 = math.sqrt((vobXA2 - vobXA1) * (vobXA2 - vobXA1) + (vobYA2 - vobYA2) * (vobYA2 - vobYA2))
    float vobEdge2 = math.sqrt((vobXA2 - vobXA2) * (vobXA2 - vobXA2) + (vobYA2 - vobYA1) * (vobYA2 - vobYA1))
    float vobTotalArea = vobEdge1 * vobEdge2
    vobTotalArea

vobDoOBsTouch(vobOrderBlockInfo vobOBInfo1, vobOrderBlockInfo vobOBInfo2) =>
    float vobXA1 = vobOBInfo1.vobStartTime
    float vobXA2 = na(vobOBInfo1.vobBreakTime) ? time + 1 : vobOBInfo1.vobBreakTime
    float vobYA1 = vobOBInfo1.vobTop
    float vobYA2 = vobOBInfo1.vobBottom

    float vobXB1 = vobOBInfo2.vobStartTime
    float vobXB2 = na(vobOBInfo2.vobBreakTime) ? time + 1 : vobOBInfo2.vobBreakTime
    float vobYB1 = vobOBInfo2.vobTop
    float vobYB2 = vobOBInfo2.vobBottom
    float vobIntersectionArea = math.max(0, math.min(vobXA2, vobXB2) - math.max(vobXA1, vobXB1)) * math.max(0, math.min(vobYA1, vobYB1) - math.max(vobYA2, vobYB2))
    float vobUnionArea = vobAreaOfOB(vobOBInfo1) + vobAreaOfOB(vobOBInfo2) - vobIntersectionArea
    
    float vobOverlapPercentage = (vobIntersectionArea / vobUnionArea) * 100.0

    if vobOverlapPercentage > vobOverlapThresholdPercentage
        true
    else
        false

vobIsOBValid(vobOrderBlockInfo vobOBInfo) =>
    vobValid = true
    if vobOBInfo.vobDisabled
        vobValid := false
    vobValid

vobCombineOBsFunc() =>
    if vobAllOrderBlocksList.size() > 0
        vobLastCombinations = 999
        while vobLastCombinations > 0
            vobLastCombinations := 0
            for i = 0 to vobAllOrderBlocksList.size() - 1
                vobCurOB1 = vobAllOrderBlocksList.get(i)
                for j = 0 to vobAllOrderBlocksList.size() - 1
                    vobCurOB2 = vobAllOrderBlocksList.get(j)
                    if i == j
                        continue
                    if not vobIsOBValid(vobCurOB1.vobInfo) or not vobIsOBValid(vobCurOB2.vobInfo)
                        continue
                    if vobCurOB1.vobInfo.vobObType != vobCurOB2.vobInfo.vobObType
                        continue
                    if vobDoOBsTouch(vobCurOB1.vobInfo, vobCurOB2.vobInfo)
                        vobCurOB1.vobInfo.vobDisabled := true
                        vobCurOB2.vobInfo.vobDisabled := true
                        vobOrderBlock vobNewOB = vobCreateOrderBlock(vobOrderBlockInfo.new(math.max(vobCurOB1.vobInfo.vobTop, vobCurOB2.vobInfo.vobTop), math.min(vobCurOB1.vobInfo.vobBottom, vobCurOB2.vobInfo.vobBottom), vobCurOB1.vobInfo.vobObVolume + vobCurOB2.vobInfo.vobObVolume, vobCurOB1.vobInfo.vobObType))
                        vobNewOB.vobInfo.vobStartTime := math.min(vobCurOB1.vobInfo.vobStartTime, vobCurOB2.vobInfo.vobStartTime)
                        vobNewOB.vobInfo.vobBreakTime := math.max(nz(vobCurOB1.vobInfo.vobBreakTime), nz(vobCurOB2.vobInfo.vobBreakTime))
                        vobNewOB.vobInfo.vobBreakTime := vobNewOB.vobInfo.vobBreakTime == 0 ? na : vobNewOB.vobInfo.vobBreakTime
                        vobNewOB.vobInfo.vobTimeframeStr := vobCurOB1.vobInfo.vobTimeframeStr

                        vobNewOB.vobInfo.vobObVolume := vobCurOB1.vobInfo.vobObVolume + vobCurOB2.vobInfo.vobObVolume
                        vobNewOB.vobInfo.vobObLowVolume := vobCurOB1.vobInfo.vobObLowVolume + vobCurOB2.vobInfo.vobObLowVolume
                        vobNewOB.vobInfo.vobObHighVolume := vobCurOB1.vobInfo.vobObHighVolume + vobCurOB2.vobInfo.vobObHighVolume
                        vobNewOB.vobInfo.vobBbVolume := nz(vobCurOB1.vobInfo.vobBbVolume, 0) + nz(vobCurOB2.vobInfo.vobBbVolume, 0)
                        vobNewOB.vobInfo.vobBreaker := vobCurOB1.vobInfo.vobBreaker or vobCurOB2.vobInfo.vobBreaker
                        
                        vobNewOB.vobInfo.vobCombined := true
                        if timeframe.in_seconds(vobCurOB1.vobInfo.vobTimeframeStr) != timeframe.in_seconds(vobCurOB2.vobInfo.vobTimeframeStr)
                            vobNewOB.vobInfo.vobCombinedTimeframesStr := (na(vobCurOB1.vobInfo.vobCombinedTimeframesStr) ? vobFormatTimeframeString(vobCurOB1.vobInfo.vobTimeframeStr) : vobCurOB1.vobInfo.vobCombinedTimeframesStr) + " & " + (na(vobCurOB2.vobInfo.vobCombinedTimeframesStr) ? vobFormatTimeframeString(vobCurOB2.vobInfo.vobTimeframeStr) : vobCurOB2.vobInfo.vobCombinedTimeframesStr)
                        vobAllOrderBlocksList.unshift(vobNewOB)
                        vobLastCombinations += 1

vobReqSeq(vobTimeframeStr) =>
    [vobBullishOrderBlocksListF, vobBearishOrderBlocksListF] = request.security(syminfo.tickerid, vobTimeframeStr, [vobBullishOrderBlocksList, vobBearishOrderBlocksList])
    [vobBullishOrderBlocksListF, vobBearishOrderBlocksListF]

vobGetTFData(vobTimeframeInfo vobTimeframeInfoF, vobTimeframeStr) =>
    if not vobIsTimeframeLower(vobTimeframeInfoF.vobTimeframeStr, timeframe.period) and vobTimeframeInfoF.vobIsEnabled
        [vobBullishOrderBlocksListF, vobBearishOrderBlocksListF] = vobReqSeq(vobTimeframeStr)
        [vobBullishOrderBlocksListF, vobBearishOrderBlocksListF]
    else
        [na, na]

vobHandleTimeframeInfo(vobTimeframeInfo vobTimeframeInfoF, vobBullishOrderBlocksListF, vobBearishOrderBlocksListF) =>
    if not vobIsTimeframeLower(vobTimeframeInfoF.vobTimeframeStr, timeframe.period) and vobTimeframeInfoF.vobIsEnabled
        vobTimeframeInfoF.vobBullishOrderBlocksList := vobBullishOrderBlocksListF
        vobTimeframeInfoF.vobBearishOrderBlocksList := vobBearishOrderBlocksListF

vobHandleOrderBlocksFinal() =>
    if vobDEBUG
        log.info("Bullish OB Count " + str.tostring(vobBullishOrderBlocksList.size()))
        log.info("Bearish OB Count " + str.tostring(vobBearishOrderBlocksList.size()))

    // Clear the previous order blocks
    if vobAllOrderBlocksList.size() > 0
        for i = 0 to vobAllOrderBlocksList.size() - 1
            vobSafeDeleteOrderBlock(vobAllOrderBlocksList.get(i))
    vobAllOrderBlocksList.clear()    

    // Process each enabled timeframe
    for i = 0 to vobTimeframeInfos.size() - 1
        vobCurTimeframe = vobTimeframeInfos.get(i)
        if not vobCurTimeframe.vobIsEnabled
            continue

        if vobCurTimeframe.vobBullishOrderBlocksList.size() > 0
            for j = 0 to math.min(vobCurTimeframe.vobBullishOrderBlocksList.size() - 1, vobBullishOrderBlocks - 1)
                vobOrderBlockInfoF = vobCurTimeframe.vobBullishOrderBlocksList.get(j)
                vobOrderBlockInfoF.vobTimeframeStr := vobCurTimeframe.vobTimeframeStr
                vobAllOrderBlocksList.unshift(vobCreateOrderBlock(vobOrderBlockInfo.copy(vobOrderBlockInfoF)))

        if vobCurTimeframe.vobBearishOrderBlocksList.size() > 0
            for j = 0 to math.min(vobCurTimeframe.vobBearishOrderBlocksList.size() - 1, vobBearishOrderBlocks - 1)
                vobOrderBlockInfoF = vobCurTimeframe.vobBearishOrderBlocksList.get(j)
                vobOrderBlockInfoF.vobTimeframeStr := vobCurTimeframe.vobTimeframeStr
                vobAllOrderBlocksList.unshift(vobCreateOrderBlock(vobOrderBlockInfo.copy(vobOrderBlockInfoF)))

    // Combine order blocks if necessary
    if vobCombineOBs
        vobCombineOBsFunc()    

    // Render the order blocks
    if vobAllOrderBlocksList.size() > 0
        for i = 0 to vobAllOrderBlocksList.size() - 1
            vobCurOB = vobAllOrderBlocksList.get(i)
            if vobIsOBValid(vobCurOB.vobInfo)
                vobRenderOrderBlock(vobCurOB)

// Main logic
vobFindOrderBlocks()

[vobBullishOrderBlocksListTimeframe1, vobBearishOrderBlocksListTimeframe1] = vobGetTFData(vobTimeframeInfos.get(0), vobTimeframe1)
[vobBullishOrderBlocksListTimeframe2, vobBearishOrderBlocksListTimeframe2] = vobGetTFData(vobTimeframeInfos.get(1), vobTimeframe2)
[vobBullishOrderBlocksListTimeframe3, vobBearishOrderBlocksListTimeframe3] = vobGetTFData(vobTimeframeInfos.get(2), vobTimeframe3)
[vobBullishOrderBlocksListTimeframe4, vobBearishOrderBlocksListTimeframe4] = vobGetTFData(vobTimeframeInfos.get(3), vobTimeframe4)
[vobBullishOrderBlocksListTimeframe5, vobBearishOrderBlocksListTimeframe5] = vobGetTFData(vobTimeframeInfos.get(4), vobTimeframe5)

if barstate.isconfirmed
    vobHandleTimeframeInfo(vobTimeframeInfos.get(0), vobBullishOrderBlocksListTimeframe1, vobBearishOrderBlocksListTimeframe1)
    vobHandleTimeframeInfo(vobTimeframeInfos.get(1), vobBullishOrderBlocksListTimeframe2, vobBearishOrderBlocksListTimeframe2)
    vobHandleTimeframeInfo(vobTimeframeInfos.get(2), vobBullishOrderBlocksListTimeframe3, vobBearishOrderBlocksListTimeframe3)
    vobHandleTimeframeInfo(vobTimeframeInfos.get(3), vobBullishOrderBlocksListTimeframe4, vobBearishOrderBlocksListTimeframe4)
    vobHandleTimeframeInfo(vobTimeframeInfos.get(4), vobBullishOrderBlocksListTimeframe5, vobBearishOrderBlocksListTimeframe5)

    vobHandleOrderBlocksFinal()

