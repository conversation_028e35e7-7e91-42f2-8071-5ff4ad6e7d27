//@version=5
strategy("AR-DIV-VS Trading Strategy", 
     overlay=true, 
     max_labels_count=500, 
     max_boxes_count=500,
     initial_capital=10000,
     default_qty_type=strategy.percent_of_equity,
     default_qty_value=100,
     commission_type=strategy.commission.percent,
     commission_value=0.1)

// =================== Input Parameters ===================
var GRP1 = "General Settings"
var GRP2 = "AR (Average Range) Settings"
var GRP3 = "DIV (Divergence) Settings"
var GRP4 = "VS (Volume Spike) Settings"
var GRP5 = "Risk Management"
var GRP6 = "Session Settings"

// General Settings
timezone = input.string("America/New_York", "Time Zone", group=GRP1)
useSessionFilter = input.bool(true, "Use Session Filter", group=GRP1)

// Session Settings
asianSession = input.session("2100-0200", "Asian Session", group=GRP6)
londonSession = input.session("0200-0500", "London Session", group=GRP6)
nySession = input.session("0830-1100", "NY Session", group=GRP6)
preferredSession = input.string("NY", "Preferred Trading Session", options=["Asian", "London", "NY", "Any"], group=GRP6)

// AR Settings
useAR = input.bool(true, "Use Average Range Levels", group=GRP2)
adrDays = input.int(5, "ADR Days to Average", minval=1, maxval=30, group=GRP2)
useADR = input.bool(true, "Use ADR Levels", group=GRP2)
useAWR = input.bool(true, "Use AWR Levels", group=GRP2)
useAMR = input.bool(true, "Use AMR Levels", group=GRP2)
useOneThirdLevels = input.bool(true, "Use 1/3 Levels", group=GRP2)
useTwoThirdLevels = input.bool(false, "Use 2/3 Levels", group=GRP2)
useHalfLevels = input.bool(false, "Use 1/2 Levels", group=GRP2)
arLevelTolerance = input.float(0.1, "AR Level Tolerance %", minval=0.01, maxval=1.0, step=0.01, group=GRP2)

// DIV Settings
useDIV = input.bool(true, "Use Divergence Signals", group=GRP3)
pivotPeriod = input.int(5, "Pivot Period", minval=1, maxval=50, group=GRP3)
divergenceType = input.string("Regular", "Divergence Type", options=["Regular", "Hidden", "Regular/Hidden"], group=GRP3)
useRSI = input.bool(true, "Use RSI", group=GRP3)
useMACD = input.bool(true, "Use MACD", group=GRP3)
useStoch = input.bool(true, "Use Stochastic", group=GRP3)
useCCI = input.bool(true, "Use CCI", group=GRP3)
useMomentum = input.bool(true, "Use Momentum", group=GRP3)
useOBV = input.bool(true, "Use OBV", group=GRP3)
maxBarsBackForDivergence = input.int(100, "Max Bars Back for Divergence", minval=20, maxval=500, group=GRP3)

// VS Settings
useVS = input.bool(true, "Use Volume Spike Signals", group=GRP4)
volumeMALength = input.int(20, "Volume MA Length", minval=1, group=GRP4)
volumeSpikeThreshold = input.float(2.0, "Volume Spike Threshold", minval=1.0, step=0.1, group=GRP4)
minVolumeCandlesBetweenDivergences = input.int(1, "Min Volume Candles Between Divergences", minval=1, maxval=10, group=GRP4)

// Risk Management
riskPerTrade = input.float(1.0, "Risk Per Trade %", minval=0.1, maxval=5.0, group=GRP5)
takeProfitAtr = input.float(2.0, "Take Profit ATR", minval=0.5, group=GRP5)
stopLossAtr = input.float(1.0, "Stop Loss ATR", minval=0.5, group=GRP5)
maxDailyLoss = input.float(-3.0, "Max Daily Loss %", minval=-10.0, maxval=0.0, group=GRP5)
trailingStop = input.bool(false, "Use Trailing Stop", group=GRP5)
trailingStopAtr = input.float(1.5, "Trailing Stop ATR", minval=0.5, group=GRP5)

// =================== Variable Declarations ===================
var float dailyProfit = 0.0
var int lastTradeTime = 0
var float lastTradePrice = 0.0

// Arrays to store divergence information
var int[] bullDivBars = array.new_int()
var int[] bearDivBars = array.new_int()
var float[] bullDivPrices = array.new_float()
var float[] bearDivPrices = array.new_float()

// Variables to track volume spikes between divergences
var int[] volumeSpikeBars = array.new_int()

// =================== Function Definitions ===================
// Function to calculate the Average Daily Range
calculate_adr(lookback_period) =>
    // Request daily high-low range data
    dh = request.security(syminfo.tickerid, "D", high, barmerge.gaps_off, barmerge.lookahead_off)
    dl = request.security(syminfo.tickerid, "D", low, barmerge.gaps_off, barmerge.lookahead_off)
    drange = dh - dl

    // Calculate the average of the daily ranges
    ta.sma(drange, lookback_period)

// Function to calculate the Average Weekly Range
calculate_awr(lookback_period) =>
    // Request weekly high-low range data
    wh = request.security(syminfo.tickerid, "W", high, barmerge.gaps_off, barmerge.lookahead_off)
    wl = request.security(syminfo.tickerid, "W", low, barmerge.gaps_off, barmerge.lookahead_off)
    wrange = wh - wl

    // Calculate the average of the weekly ranges
    ta.sma(wrange, lookback_period)

// Function to calculate the Average Monthly Range
calculate_amr(lookback_period) =>
    // Request monthly high-low range data
    mh = request.security(syminfo.tickerid, "M", high, barmerge.gaps_off, barmerge.lookahead_off)
    ml = request.security(syminfo.tickerid, "M", low, barmerge.gaps_off, barmerge.lookahead_off)
    mrange = mh - ml

    // Calculate the average of the monthly ranges
    ta.sma(mrange, lookback_period)

// Function to detect positive regular divergence
positive_regular_divergence(src, pivot_period) =>
    // Get pivot lows for price
    price_pivot_low = ta.pivotlow(low, pivot_period, pivot_period)
    
    // If we have a pivot low
    if not na(price_pivot_low)
        // Find the previous pivot low
        prev_price_pivot_low = float(na)
        prev_price_pivot_idx = int(na)
        
        for i = 1 to maxBarsBackForDivergence
            if not na(ta.pivotlow(low[i], pivot_period, pivot_period))
                prev_price_pivot_low := ta.pivotlow(low[i], pivot_period, pivot_period)
                prev_price_pivot_idx := i
                break
        
        // If we found a previous pivot low
        if not na(prev_price_pivot_low)
            // Get indicator values at pivot points
            curr_src_value = src[pivot_period]
            prev_src_value = src[prev_price_pivot_idx + pivot_period]
            
            // Check for bullish divergence: price makes lower low but indicator makes higher low
            if price_pivot_low < prev_price_pivot_low and curr_src_value > prev_src_value
                true
            else
                false
        else
            false
    else
        false

// Function to detect negative regular divergence
negative_regular_divergence(src, pivot_period) =>
    // Get pivot highs for price
    price_pivot_high = ta.pivothigh(high, pivot_period, pivot_period)
    
    // If we have a pivot high
    if not na(price_pivot_high)
        // Find the previous pivot high
        prev_price_pivot_high = float(na)
        prev_price_pivot_idx = int(na)
        
        for i = 1 to maxBarsBackForDivergence
            if not na(ta.pivothigh(high[i], pivot_period, pivot_period))
                prev_price_pivot_high := ta.pivothigh(high[i], pivot_period, pivot_period)
                prev_price_pivot_idx := i
                break
        
        // If we found a previous pivot high
        if not na(prev_price_pivot_high)
            // Get indicator values at pivot points
            curr_src_value = src[pivot_period]
            prev_src_value = src[prev_price_pivot_idx + pivot_period]
            
            // Check for bearish divergence: price makes higher high but indicator makes lower high
            if price_pivot_high > prev_price_pivot_high and curr_src_value < prev_src_value
                true
            else
                false
        else
            false
    else
        false

// Function to check if price is near an AR level
is_near_ar_level(price, tolerance) =>
    // Calculate tolerance in price terms
    tol = price * tolerance / 100
    
    // Check if price is near any ADR level
    near_adr = useADR and (
        math.abs(price - adr_full_up) <= tol or
        math.abs(price - adr_full_down) <= tol or
        (useOneThirdLevels and (math.abs(price - adr_one_third_up) <= tol or math.abs(price - adr_one_third_down) <= tol)) or
        (useTwoThirdLevels and (math.abs(price - adr_two_third_up) <= tol or math.abs(price - adr_two_third_down) <= tol)) or
        (useHalfLevels and (math.abs(price - adr_half_up) <= tol or math.abs(price - adr_half_down) <= tol))
    )
    
    // Check if price is near any AWR level
    near_awr = useAWR and (
        math.abs(price - awr_full_up) <= tol or
        math.abs(price - awr_full_down) <= tol or
        (useOneThirdLevels and (math.abs(price - awr_one_third_up) <= tol or math.abs(price - awr_one_third_down) <= tol)) or
        (useTwoThirdLevels and (math.abs(price - awr_two_third_up) <= tol or math.abs(price - awr_two_third_down) <= tol)) or
        (useHalfLevels and (math.abs(price - awr_half_up) <= tol or math.abs(price - awr_half_down) <= tol))
    )
    
    // Check if price is near any AMR level
    near_amr = useAMR and (
        math.abs(price - amr_full_up) <= tol or
        math.abs(price - amr_full_down) <= tol or
        (useOneThirdLevels and (math.abs(price - amr_one_third_up) <= tol or math.abs(price - amr_one_third_down) <= tol)) or
        (useTwoThirdLevels and (math.abs(price - amr_two_third_up) <= tol or math.abs(price - amr_two_third_down) <= tol)) or
        (useHalfLevels and (math.abs(price - amr_half_up) <= tol or math.abs(price - amr_half_down) <= tol))
    )
    
    near_adr or near_awr or near_amr

// =================== Calculations ===================
// Session Logic
inAsianSession = not na(time(timeframe.period, asianSession))
inLondonSession = not na(time(timeframe.period, londonSession))
inNYSession = not na(time(timeframe.period, nySession))

inTradingSession = switch preferredSession
    "Asian" => inAsianSession
    "London" => inLondonSession
    "NY" => inNYSession
    "Any" => inAsianSession or inLondonSession or inNYSession

// AR Calculations
current_adr = calculate_adr(adrDays)
current_awr = calculate_awr(adrDays)
current_amr = calculate_amr(adrDays)

// Get current day open price
day_open = request.security(syminfo.tickerid, "D", open, lookahead=barmerge.lookahead_off)

// Calculate ADR levels
adr_full_up = day_open + current_adr
adr_full_down = day_open - current_adr
adr_one_third_up = day_open + (current_adr / 3)
adr_one_third_down = day_open - (current_adr / 3)
adr_two_third_up = day_open + (current_adr * 2 / 3)
adr_two_third_down = day_open - (current_adr * 2 / 3)
adr_half_up = day_open + (current_adr / 2)
adr_half_down = day_open - (current_adr / 2)

// Get current week open price
week_open = request.security(syminfo.tickerid, "W", open, lookahead=barmerge.lookahead_off)

// Calculate AWR levels
awr_full_up = week_open + current_awr
awr_full_down = week_open - current_awr
awr_one_third_up = week_open + (current_awr / 3)
awr_one_third_down = week_open - (current_awr / 3)
awr_two_third_up = week_open + (current_awr * 2 / 3)
awr_two_third_down = week_open - (current_awr * 2 / 3)
awr_half_up = week_open + (current_awr / 2)
awr_half_down = week_open - (current_awr / 2)

// Get current month open price
month_open = request.security(syminfo.tickerid, "M", open, lookahead=barmerge.lookahead_off)

// Calculate AMR levels
amr_full_up = month_open + current_amr
amr_full_down = month_open - current_amr
amr_one_third_up = month_open + (current_amr / 3)
amr_one_third_down = month_open - (current_amr / 3)
amr_two_third_up = month_open + (current_amr * 2 / 3)
amr_two_third_down = month_open - (current_amr * 2 / 3)
amr_half_up = month_open + (current_amr / 2)
amr_half_down = month_open - (current_amr / 2)

// DIV Calculations
// Get indicators
rsi = ta.rsi(close, 14)
[macd, signal, macd_hist] = ta.macd(close, 12, 26, 9)
stoch = ta.sma(ta.stoch(close, high, low, 14), 3)
cci = ta.cci(close, 10)
mom = ta.mom(close, 10)
obv = ta.obv

// Check for divergences
rsi_bull_div = useRSI ? positive_regular_divergence(rsi, pivotPeriod) : false
macd_bull_div = useMACD ? positive_regular_divergence(macd, pivotPeriod) : false
stoch_bull_div = useStoch ? positive_regular_divergence(stoch, pivotPeriod) : false
cci_bull_div = useCCI ? positive_regular_divergence(cci, pivotPeriod) : false
mom_bull_div = useMomentum ? positive_regular_divergence(mom, pivotPeriod) : false
obv_bull_div = useOBV ? positive_regular_divergence(obv, pivotPeriod) : false

rsi_bear_div = useRSI ? negative_regular_divergence(rsi, pivotPeriod) : false
macd_bear_div = useMACD ? negative_regular_divergence(macd, pivotPeriod) : false
stoch_bear_div = useStoch ? negative_regular_divergence(stoch, pivotPeriod) : false
cci_bear_div = useCCI ? negative_regular_divergence(cci, pivotPeriod) : false
mom_bear_div = useMomentum ? negative_regular_divergence(mom, pivotPeriod) : false
obv_bear_div = useOBV ? negative_regular_divergence(obv, pivotPeriod) : false

// Detect any bullish or bearish divergence
any_bull_div = rsi_bull_div or macd_bull_div or stoch_bull_div or cci_bull_div or mom_bull_div or obv_bull_div
any_bear_div = rsi_bear_div or macd_bear_div or stoch_bear_div or cci_bear_div or mom_bear_div or obv_bear_div

// VS Calculations
vol_sma = ta.sma(volume, volumeMALength)
volume_spike = volume > vol_sma * volumeSpikeThreshold

// ATR for stop loss and take profit
atr_value = ta.atr(14)

// =================== Divergence and Volume Spike Tracking ===================
// Track bullish divergences
if any_bull_div and is_near_ar_level(low, arLevelTolerance)
    array.push(bullDivBars, bar_index)
    array.push(bullDivPrices, low)
    
    // Keep only the last 10 divergences
    if array.size(bullDivBars) > 10
        array.shift(bullDivBars)
        array.shift(bullDivPrices)

// Track bearish divergences
if any_bear_div and is_near_ar_level(high, arLevelTolerance)
    array.push(bearDivBars, bar_index)
    array.push(bearDivPrices, high)
    
    // Keep only the last 10 divergences
    if array.size(bearDivBars) > 10
        array.shift(bearDivBars)
        array.shift(bearDivPrices)

// Track volume spikes
if volume_spike
    array.push(volumeSpikeBars, bar_index)
    
    // Keep only the last 20 volume spikes
    if array.size(volumeSpikeBars) > 20
        array.shift(volumeSpikeBars)

// =================== Trading Logic ===================
// Update daily profit on new day
if ta.change(dayofmonth)
    dailyProfit := 0

// Check for two bullish divergences with volume spike between them
bull_signal = false
if array.size(bullDivBars) >= 2
    div1_bar = array.get(bullDivBars, array.size(bullDivBars) - 2)
    div2_bar = array.get(bullDivBars, array.size(bullDivBars) - 1)
    
    // Check if there's at least one volume spike between the two divergences
    volume_between = false
    for i = 0 to array.size(volumeSpikeBars) - 1
        vol_bar = array.get(volumeSpikeBars, i)
        if vol_bar > div1_bar and vol_bar < div2_bar
            volume_between := true
            break
    
    // Signal is valid if there's a volume spike between divergences and current candle is bullish
    bull_signal := volume_between and close > open and bar_index > div2_bar

// Check for two bearish divergences with volume spike between them
bear_signal = false
if array.size(bearDivBars) >= 2
    div1_bar = array.get(bearDivBars, array.size(bearDivBars) - 2)
    div2_bar = array.get(bearDivBars, array.size(bearDivBars) - 1)
    
    // Check if there's at least one volume spike between the two divergences
    volume_between = false
    for i = 0 to array.size(volumeSpikeBars) - 1
        vol_bar = array.get(volumeSpikeBars, i)
        if vol_bar > div1_bar and vol_bar < div2_bar
            volume_between := true
            break
    
    // Signal is valid if there's a volume spike between divergences and current candle is bearish
    bear_signal := volume_between and close < open and bar_index > div2_bar

// Trading Conditions
canTrade = useSessionFilter ? inTradingSession : true
canTrade := canTrade and dailyProfit > (strategy.initial_capital * maxDailyLoss / 100)
canTrade := canTrade and (time - lastTradeTime) > 1000 * 60 * 5  // 5-minute minimum between trades

// Entry Logic
if (canTrade and bull_signal)
    stopPrice = close - (atr_value * stopLossAtr)
    targetPrice = close + (atr_value * takeProfitAtr)
    strategy.entry("Long", strategy.long)
    strategy.exit("Long TP/SL", "Long", stop=stopPrice, limit=targetPrice)
    lastTradeTime := time
    lastTradePrice := close

if (canTrade and bear_signal)
    stopPrice = close + (atr_value * stopLossAtr)
    targetPrice = close - (atr_value * takeProfitAtr)
    strategy.entry("Short", strategy.short)
    strategy.exit("Short TP/SL", "Short", stop=stopPrice, limit=targetPrice)
    lastTradeTime := time
    lastTradePrice := close

// Trailing Stop Logic
if (trailingStop and strategy.position_size > 0)
    trailPrice = close - (atr_value * trailingStopAtr)
    if (trailPrice > strategy.position_avg_price)
        strategy.exit("Long Trail", "Long", stop=trailPrice)

if (trailingStop and strategy.position_size < 0)
    trailPrice = close + (atr_value * trailingStopAtr)
    if (trailPrice < strategy.position_avg_price)
        strategy.exit("Short Trail", "Short", stop=trailPrice)

// Close all positions if max daily loss is hit
if dailyProfit < (strategy.initial_capital * maxDailyLoss / 100)
    strategy.close_all("Max Daily Loss")

// Update daily profit
dailyProfit := dailyProfit + (strategy.netprofit - strategy.netprofit[1])

// =================== Plotting ===================
// Plot AR Levels
plot(useAR and useADR ? adr_full_up : na, "ADR Full Up", color=color.new(color.white, 50), linewidth=1, style=plot.style_circles)
plot(useAR and useADR ? adr_full_down : na, "ADR Full Down", color=color.new(color.white, 50), linewidth=1, style=plot.style_circles)
plot(useAR and useADR and useOneThirdLevels ? adr_one_third_up : na, "ADR 1/3 Up", color=color.new(color.white, 70), linewidth=1, style=plot.style_circles)
plot(useAR and useADR and useOneThirdLevels ? adr_one_third_down : na, "ADR 1/3 Down", color=color.new(color.white, 70), linewidth=1, style=plot.style_circles)

plot(useAR and useAWR ? awr_full_up : na, "AWR Full Up", color=color.new(color.blue, 50), linewidth=1, style=plot.style_circles)
plot(useAR and useAWR ? awr_full_down : na, "AWR Full Down", color=color.new(color.blue, 50), linewidth=1, style=plot.style_circles)
plot(useAR and useAWR and useOneThirdLevels ? awr_one_third_up : na, "AWR 1/3 Up", color=color.new(color.blue, 70), linewidth=1, style=plot.style_circles)
plot(useAR and useAWR and useOneThirdLevels ? awr_one_third_down : na, "AWR 1/3 Down", color=color.new(color.blue, 70), linewidth=1, style=plot.style_circles)

plot(useAR and useAMR ? amr_full_up : na, "AMR Full Up", color=color.new(color.yellow, 50), linewidth=1, style=plot.style_circles)
plot(useAR and useAMR ? amr_full_down : na, "AMR Full Down", color=color.new(color.yellow, 50), linewidth=1, style=plot.style_circles)
plot(useAR and useAMR and useOneThirdLevels ? amr_one_third_up : na, "AMR 1/3 Up", color=color.new(color.yellow, 70), linewidth=1, style=plot.style_circles)
plot(useAR and useAMR and useOneThirdLevels ? amr_one_third_down : na, "AMR 1/3 Down", color=color.new(color.yellow, 70), linewidth=1, style=plot.style_circles)

// Plot Divergence Signals
plotshape(any_bull_div and is_near_ar_level(low, arLevelTolerance), "Bullish Divergence", shape.triangleup, location.belowbar, color.green, size=size.small)
plotshape(any_bear_div and is_near_ar_level(high, arLevelTolerance), "Bearish Divergence", shape.triangledown, location.abovebar, color.red, size=size.small)

// Plot Volume Spike
plotshape(volume_spike, "Volume Spike", shape.circle, location.bottom, color.purple, size=size.small)

// Plot Entry Signals
plotshape(bull_signal, "Long Signal", shape.triangleup, location.belowbar, color.green, size=size.normal)
plotshape(bear_signal, "Short Signal", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Label the divergences with numbers
if any_bull_div and is_near_ar_level(low, arLevelTolerance)
    label.new(bar_index, low, str.tostring(array.size(bullDivBars)), color=color.green, style=label.style_circle, textcolor=color.white, size=size.small)

if any_bear_div and is_near_ar_level(high, arLevelTolerance)
    label.new(bar_index, high, str.tostring(array.size(bearDivBars)), color=color.red, style=label.style_circle, textcolor=color.white, size=size.small)
