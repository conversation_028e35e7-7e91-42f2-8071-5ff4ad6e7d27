// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// ©️ Dev Lucem

//@version=5
//@author=devlucem

// THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
// THE <PERSON><PERSON><PERSON><PERSON> SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
// I HOPE U LOVE IT

// Seek Menu

const bool vobDEBUG = false
const int vobMaxBoxesCount = 500
const float vobOverlapThresholdPercentage = 0
const int vobMaxDistanceToLastBar = 1750 // Affects Running Time
const int vobMaxOrderBlocks = 30


indicator(title = 'Dsma-ifvg', overlay = true, max_boxes_count = vobMaxBoxesCount, max_labels_count = vobMaxBoxesCount, max_lines_count = vobMaxBoxesCount, max_bars_back = 5000,dynamic_requests = true)


//dsma


//@version=5
// indicator("Multi Deviation Scaled Moving Average [Ravi] - Extended", "Multi DSMA - [Ravi] - Ext", overlay=true)

// ---------------------------------------------------------------------------------------------------------------------
// USER INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

int dsma_period = input.int(30, title="Period")
int dsma_step = 100 - input.int(60, "Sensitivity", minval = 0, maxval = 100, tooltip = "The Lower input Lower sensitivity")
series float dsma_src = hlc3

// Timeframe Inputs for DSMA
bool dsma_show_tf0 = input.bool(true, title="Show 1-Minute DSMA")
dsma_tf0 = input.timeframe('1', "1-Minute Timeframe")

bool dsma_show_tf1 = input.bool(true, title="Show 3-Minute DSMA")
dsma_tf1 = input.timeframe('3', "3-Minute Timeframe")

bool dsma_show_tf2 = input.bool(true, title="Show 5-Minute DSMA")
dsma_tf2 = input.timeframe('5', "5-Minute Timeframe")

bool dsma_show_tf7 = input.bool(true, title="Show 7-Minute DSMA")
dsma_tf7 = input.timeframe('7', "7-Minute Timeframe")

bool dsma_show_tf3 = input.bool(true, title="Show 15-Minute DSMA")
dsma_tf3 = input.timeframe('15', "15-Minute Timeframe")

bool dsma_show_tf4 = input.bool(true, title="Show 1-Hour DSMA")
dsma_tf4 = input.timeframe('60', "1-Hour Timeframe")

bool dsma_show_tf5 = input.bool(true, title="Show 4-Hour DSMA")
dsma_tf5 = input.timeframe('240', "4-Hour Timeframe")

bool dsma_show_tf6 = input.bool(true, title="Show 1-Day DSMA")
dsma_tf6 = input.timeframe('D', "1-Day Timeframe")

// Configurable Colors for upper and lower for each timeframe
color dsma_upper_color0 = input.color(#41a1ce, "1-Minute Upper Color")
color dsma_down_color0 = input.color(#ce8541, "1-Minute Down Color")
color dsma_upper_color1 = input.color(#41a1ce, "3-Minute Upper Color")
color dsma_down_color1 = input.color(#ce8541, "3-Minute Down Color")
color dsma_upper_color2 = input.color(#41a1ce, "5-Minute Upper Color")
color dsma_down_color2 = input.color(#ce8541, "5-Minute Down Color")
color dsma_upper_color7 = input.color(#41a1ce, "7-Minute Upper Color")
color dsma_down_color7 = input.color(#ce8541, "7-Minute Down Color")
color dsma_upper_color3 = input.color(#41a1ce, "15-Minute Upper Color")
color dsma_down_color3 = input.color(#ce8541, "15-Minute Down Color")
color dsma_upper_color4 = input.color(#41a1ce, "1-Hour Upper Color")
color dsma_down_color4 = input.color(#ce8541, "1-Hour Down Color")
color dsma_upper_color5 = input.color(#41a1ce, "4-Hour Upper Color")
color dsma_down_color5 = input.color(#ce8541, "4-Hour Down Color")
color dsma_upper_color6 = input.color(#41a1ce, "1-Day Upper Color")
color dsma_down_color6 = input.color(#ce8541, "1-Day Down Color")

// Colors for each timeframe's DSMA
color dsma_dsma1_color = color.new(#1f77b4, 0) // 3-Minute DSMA Color
color dsma_dsma2_color = color.new(#ff7f0e, 0) // 5-Minute DSMA Color
color dsma_dsma3_color = color.new(#2ca02c, 0) // 15-Minute DSMA Color
color dsma_dsma4_color = color.new(#d62728, 0) // 1-Hour DSMA Color
color dsma_dsma5_color = color.new(#9467bd, 0) // 4-Hour DSMA Color
color dsma_dsma6_color = color.new(#8c564b, 0) // 1-Day DSMA Color

// ---------------------------------------------------------------------------------------------------------------------
// INDICATIONS
// ---------------------------------------------------------------------------------------------------------------------

// Function to calculate the Deviation Scaled Moving Average (DSMA)
dsma(src, int dsma_period)=>
    var float dsma_a1 = 0.0
    var float dsma_b1 = 0.0
    var float dsma_c1 = 0.0
    var float dsma_c2 = 0.0
    var float dsma_c3 = 0.0
    var float dsma_filt = 0.0
    var float dsma_dsma = 0.0
    var float dsma_s = 0.0

    if barstate.isfirst
        dsma_pi = 3.1415926535897932
        dsma_g = math.sqrt(2)
        dsma_s := 2 * dsma_pi / dsma_period
        dsma_a1 := math.exp(-dsma_g * dsma_pi / (0.5 * dsma_period))
        dsma_b1 := 2 * dsma_a1 * math.cos(dsma_g * dsma_s / (0.5 * dsma_period))
        dsma_c2 := dsma_b1
        dsma_c3 := -dsma_a1 * dsma_a1
        dsma_c1 := 1 - dsma_c2 - dsma_c3

    dsma_zeros = (close - close[2])
    dsma_filt := dsma_c1 * (dsma_zeros + dsma_zeros[1]) / 2 + dsma_c2 * nz(dsma_filt[1]) + dsma_c3 * nz(dsma_filt[2])

    dsma_rms = math.sqrt(ta.ema(math.pow(dsma_filt, 2), dsma_period))
    dsma_scaled_filt = dsma_rms != 0 ? dsma_filt / dsma_rms : 0
    dsma_alpha1 = math.abs(dsma_scaled_filt) * 5 / dsma_period
    dsma_dsma := dsma_alpha1 * close + (1 - dsma_alpha1) * nz(dsma_dsma[1])

    dsma_dsma

// Function to calculate trend percentage, color, and average DSMA
dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color, dsma_down_color)=>
    dsma_dsma_arr = array.new<float>()

    dsma_length = dsma_period
    dsma1 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma2 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma3 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma4 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma5 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma6 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma7 = dsma(dsma_src, dsma_length)
    dsma_length += dsma_step 
    dsma8 = dsma(dsma_src, dsma_length)

    array.push(dsma_dsma_arr, dsma1)
    array.push(dsma_dsma_arr, dsma2)
    array.push(dsma_dsma_arr, dsma3)
    array.push(dsma_dsma_arr, dsma4)
    array.push(dsma_dsma_arr, dsma5)
    array.push(dsma_dsma_arr, dsma6)
    array.push(dsma_dsma_arr, dsma7)
    array.push(dsma_dsma_arr, dsma8)

    dsma_val = 0.14285714285714285714285714285714

    dsma_score = 0.
    for dsma_i = 0 to array.size(dsma_dsma_arr) - 1
        dsma_dsma = array.get(dsma_dsma_arr, dsma_i)
        if dsma_dsma > array.get(dsma_dsma_arr, 7)
            dsma_score += dsma_val

    dsma_color =  dsma_score > 0.5 
                 ? dsma_upper_color 
                 : dsma_down_color

    [dsma_score, array.avg(dsma_dsma_arr), dsma_color]

// Apply the multi-timeframe logic using `request.security` for all timeframes
[dsma_score0, dsma_ma0, dsma_color0] = request.security(syminfo.tickerid, dsma_tf0, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color0, dsma_down_color0))
[dsma_score1, dsma_ma1, dsma_color1] = request.security(syminfo.tickerid, dsma_tf1, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color1, dsma_down_color1))
[dsma_score2, dsma_ma2, dsma_color2] = request.security(syminfo.tickerid, dsma_tf2, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color2, dsma_down_color2))
[dsma_score3, dsma_ma3, dsma_color3] = request.security(syminfo.tickerid, dsma_tf3, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color3, dsma_down_color3))
[dsma_score4, dsma_ma4, dsma_color4] = request.security(syminfo.tickerid, dsma_tf4, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color4, dsma_down_color4))
[dsma_score5, dsma_ma5, dsma_color5] = request.security(syminfo.tickerid, dsma_tf5, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color5, dsma_down_color5))
[dsma_score6, dsma_ma6, dsma_color6] = request.security(syminfo.tickerid, dsma_tf6, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color6, dsma_down_color6))
[dsma_score7, dsma_ma7, dsma_color7] = request.security(syminfo.tickerid, dsma_tf7, dsma_percent_trend(dsma_src, dsma_period, dsma_step, dsma_upper_color7, dsma_down_color7))

// Detect crossovers for signal generation
dsma_cross_up0 = ta.crossover(dsma_score0, 0.3)
dsma_cross_dn0 = ta.crossunder(dsma_score0, 0.7)

dsma_cross_up1 = ta.crossover(dsma_score1, 0.3)
dsma_cross_dn1 = ta.crossunder(dsma_score1, 0.7)

dsma_cross_up2 = ta.crossover(dsma_score2, 0.3)
dsma_cross_dn2 = ta.crossunder(dsma_score2, 0.7)

dsma_cross_up3 = ta.crossover(dsma_score3, 0.3)
dsma_cross_dn3 = ta.crossunder(dsma_score3, 0.7)

dsma_cross_up4 = ta.crossover(dsma_score4, 0.3)
dsma_cross_dn4 = ta.crossunder(dsma_score4, 0.7)

dsma_cross_up5 = ta.crossover(dsma_score5, 0.3)
dsma_cross_dn5 = ta.crossunder(dsma_score5, 0.7)

dsma_cross_up6 = ta.crossover(dsma_score6, 0.3)
dsma_cross_dn6 = ta.crossunder(dsma_score6, 0.7)

dsma_cross_up7 = ta.crossover(dsma_score7, 0.3)
dsma_cross_dn7 = ta.crossunder(dsma_score7, 0.7)

// ---------------------------------------------------------------------------------------------------------------------
// VISUALIZATION
// ---------------------------------------------------------------------------------------------------------------------

// Detect crossovers for signal generation






// 1-Minute DSMA
plot(dsma_show_tf0 ? dsma_ma0 : na, color = dsma_color0, linewidth = 2)
plotshape(dsma_show_tf0 and dsma_cross_up0 ? dsma_ma0 : na, title="3-Min Up", location=location.absolute, color=color.new(dsma_upper_color0, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf0 and dsma_cross_dn0 ? dsma_ma0 : na, title="3-Min Down", location=location.absolute, color=color.new(dsma_down_color0, 50), size=size.small, style=shape.diamond)

// 3-Minute DSMA
plot(dsma_show_tf1 ? dsma_ma1 : na, color = dsma_color1, linewidth = 2)
plotshape(dsma_show_tf1 and dsma_cross_up1 ? dsma_ma1 : na, title="3-Min Up", location=location.absolute, color=color.new(dsma_upper_color1, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf1 and dsma_cross_dn1 ? dsma_ma1 : na, title="3-Min Down", location=location.absolute, color=color.new(dsma_down_color1, 50), size=size.small, style=shape.diamond)


// //3-Minute Plot buy and sell signals
// plotshape(dsma_show_tf1 and dsma_cross_up1, title="3m-buy signal", location=location.belowbar, color=#c3eac4, size=size.small, style=shape.labelup, text="3-ma-↑")
// plotshape(dsma_show_tf1 and dsma_cross_dn1, title="3m-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="3-ma-↓")

//3-Minute alerts
alertcondition(dsma_show_tf1 and dsma_cross_up1, "3m-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf1 and dsma_cross_dn1, "3m-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 5-Minute DSMA
plot(dsma_show_tf2 ? dsma_ma2 : na, color = dsma_color2, linewidth = 2)
plotshape(dsma_show_tf2 and dsma_cross_up2 ? dsma_ma2 : na, title="5-Min Up", location=location.absolute, color=color.new(dsma_upper_color2, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf2 and dsma_cross_dn2 ? dsma_ma2 : na, title="5-Min Down", location=location.absolute, color=color.new(dsma_down_color2, 50), size=size.small, style=shape.diamond)

// 7-Minute DSMA
plot(dsma_show_tf7 ? dsma_ma7 : na, color = dsma_color7, linewidth = 2)
plotshape(dsma_show_tf7 and dsma_cross_up7 ? dsma_ma7 : na, title="5-Min Up", location=location.absolute, color=color.new(dsma_upper_color7, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf7 and dsma_cross_dn7 ? dsma_ma7 : na, title="5-Min Down", location=location.absolute, color=color.new(dsma_down_color7, 50), size=size.small, style=shape.diamond)
// //5-Minute Plot buy and sell signals
// plotshape(dsma_show_tf2 and dsma_cross_up2, title="5m-buy signal", location=location.belowbar, color=#c5eac4, size=size.small, style=shape.labelup, text="5m-dsma-↑")
// plotshape(dsma_show_tf2 and dsma_cross_dn2, title="5m-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="5m-dsma-↓")

//5-Minute alerts
alertcondition(dsma_show_tf2 and dsma_cross_up2, "5m-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf2 and dsma_cross_dn2, "5m-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 15-Minute DSMA
plot(dsma_show_tf3 ? dsma_ma3 : na, color = dsma_color3, linewidth = 2)
plotshape(dsma_show_tf3 and dsma_cross_up3 ? dsma_ma3 : na, title="15-Min Up", location=location.absolute, color=color.new(dsma_upper_color3, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf3 and dsma_cross_dn3 ? dsma_ma3 : na, title="15-Min Down", location=location.absolute, color=color.new(dsma_down_color3, 50), size=size.small, style=shape.diamond)

//15-Minute Plot buy and sell signals
// plotshape(dsma_show_tf3 and dsma_cross_up3, title="15m-buy signal", location=location.belowbar, color=#c5eac4, size=size.small, style=shape.labelup, text="15m-dsma-↑")
// plotshape(dsma_show_tf3 and dsma_cross_dn3, title="15m-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="15m-dsma-↓")

//15-Minute alerts
alertcondition(dsma_show_tf3 and dsma_cross_up3, "15m-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf3 and dsma_cross_dn3, "15m-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 1-Hour DSMA
plot(dsma_show_tf4 ? dsma_ma4 : na, color = dsma_color4, linewidth = 2)
plotshape(dsma_show_tf4 and dsma_cross_up4 ? dsma_ma4 : na, title="1-Hour Up", location=location.absolute, color=color.new(dsma_upper_color4, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf4 and dsma_cross_dn4 ? dsma_ma4 : na, title="1-Hour Down", location=location.absolute, color=color.new(dsma_down_color4, 50), size=size.small, style=shape.diamond)


//1-Hout Plot buy and sell signals
// plotshape(dsma_show_tf4 and dsma_cross_up4, title="1h-buy signal", location=location.belowbar, color=#c5eac4, size=size.small, style=shape.labelup, text="1h-dsma-↑")
// plotshape(dsma_show_tf4 and dsma_cross_dn4, title="1h-sell signal", location=location.abovebar, color=color.rgb(249, 204, 204), size=size.small, style=shape.labeldown, text="1h-dsma-↓")

//1-Hour alerts
alertcondition(dsma_show_tf4 and dsma_cross_up4, "1h-DSMA Buy Alert", 'DSMA Buy on {{ticker}} detected at {{time}}')
alertcondition(dsma_show_tf4 and dsma_cross_dn4, "1h-DSMA Sell Alert", 'DSMA Sell on {{ticker}} detected at {{time}}')

// 4-Hour DSMA
plot(dsma_show_tf5 ? dsma_ma5 : na, color = dsma_color5, linewidth = 2)
plotshape(dsma_show_tf5 and dsma_cross_up5 ? dsma_ma5 : na, title="4-Hour Up", location=location.absolute, color=color.new(dsma_upper_color5, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf5 and dsma_cross_dn5 ? dsma_ma5 : na, title="4-Hour Down", location=location.absolute, color=color.new(dsma_down_color5, 50), size=size.small, style=shape.diamond)

// 1-Day DSMA
plot(dsma_show_tf6 ? dsma_ma6 : na, color = dsma_color6, linewidth = 2)
plotshape(dsma_show_tf6 and dsma_cross_up6 ? dsma_ma6 : na, title="1-Day Up", location=location.absolute, color=color.new(dsma_upper_color6, 50), size=size.small, style=shape.diamond)
plotshape(dsma_show_tf6 and dsma_cross_dn6 ? dsma_ma6 : na, title="1-Day Down", location=location.absolute, color=color.new(dsma_down_color6, 50), size=size.small, style=shape.diamond)





// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// ©LuxAlgo




// This work is licensed under a Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0) https://creativecommons.org/licenses/by-nc-sa/4.0/
// ©LuxAlgo

//@version=5
// indicator("Inversion Fair Value Gaps (IFVG) [LuxAlgo]", "LuxAlgo - Inversion Fair Value Gaps (IFVG)", overlay = true, max_boxes_count = 500, max_lines_count = 500, max_labels_count = 500)
//---------------------------------------------------------------------------------------------------------------------}
//Settings
//---------------------------------------------------------------------------------------------------------------------{
disp_num = input.int(5, maxval = 100, minval = 1, title = "Show Last", tooltip = "Specifies the amount of most recent inversion FVG to display in Bullish/Bearish pairs, starting at the current and looking back.")
signal_pref = input.string("Close", title = "Signal Preference", options = ["Close","Wick"], tooltip = "Choose to send signals based on Wicks or Close Price.")
wt = signal_pref == "Wick"
atr_multi = input.float(0.25, step = 0.25,minval = 0,  title = "ATR Multiplier", tooltip = "Filters FVGs based on ATR Width, Only displays Inversions that are Greater-Than the ATR*Multiplier.")

//Colors
green = input.color(color.new(#089981, 80), title = "Bull Color", group = "Colors")
red = input.color(color.new(#f23645, 80), title = "Bear Color", group = "Colors")
gray = input.color(#787b86, title = "Midline Color", group = "Colors")
invis = color.rgb(0,0,0,100)

//---------------------------------------------------------------------------------------------------------------------}
//UDT's
//---------------------------------------------------------------------------------------------------------------------{
type lab //Contains Necessary Label Data to Send to Label Function
    int x
    float y
    int dir

type fvg //Contains Necessary FVG Data to Send to Chart.
    int left = na
    float top = na
    int right = na
    float bot = na
    float mid = na
    int dir = na
    int state = na
    array<lab> labs = na
    int x_val = na

//---------------------------------------------------------------------------------------------------------------------}
//Functions
//---------------------------------------------------------------------------------------------------------------------{
//Basic Calcs
buffer = 100 //How many FVGs to keep in memory.
c_top = math.max(open,close)
c_bot = math.min(open,close)

label_maker(_x,_y,_dir) => //Used for making Labels
    switch
        _dir == 1 => label.new(_x,_y,"\n▲", style = label.style_text_outline, color = invis,  textcolor = color.new(green,0), size = size.small, xloc = xloc.bar_time)
        _dir == -1 => label.new(_x,_y, "▼\n", style = label.style_text_outline, color = invis,  textcolor = color.new(red,0), size = size.small, xloc = xloc.bar_time)

fvg_manage(_ary,_inv_ary) => //First step filtering of FVG data, Not all FVGs will be displayed, only inversions.
    if _ary.size() >= buffer
        _ary.shift()

    if _ary.size() > 0
        for i = _ary.size()-1 to 0
            value = _ary.get(i)
            _dir = value.dir

            if _dir == 1 and (c_bot < value.bot)
                value.x_val := time
                _inv_ary.push(_ary.remove(i))
            if _dir == -1 and (c_top > value.top)
                value.x_val := time
                _inv_ary.push(_ary.remove(i))
            

inv_manage(_ary) => //All inversions will be displayed.
    fire = false
    if _ary.size() >= buffer
        _ary.shift()
    if _ary.size() > 0
        for i = _ary.size()-1 to 0
            value = _ary.get(i)
            bx_top = value.top
            bx_bot = value.bot
            _dir = value.dir
            st = value.state

            if (st == 0 and _dir == 1)
                value.state := 1
                value.dir := -1
            if (_dir == -1 and st == 0)
                value.state := 1
                value.dir := 1
            if st >= 1
                value.right := time
            if (_dir == -1 and st == 1 and close < bx_bot and (wt?high:close[1]) >= bx_bot and (wt?high:close[1]) < bx_top)
                value.labs.push(lab.new(time,bx_top,-1))
                fire := true
            if (_dir == 1 and st == 1 and close > bx_top and (wt?low:close[1]) <= bx_top and (wt?low:close[1]) > bx_bot)
                value.labs.push(lab.new(time,bx_bot,1))
                fire := true
            if st >= 1 and ((_dir == -1 and c_top > bx_top) or (_dir == 1 and c_bot < bx_bot))
                _ary.remove(i)
            
    fire

send_it(_ary) => // Draws Everything on the Chart
    last_index = _ary.size()-1
    for [index,value] in _ary
        bx_top = value.top
        bx_bot = value.bot
        bx_left = value.left
        xval = value.x_val
        mid = value.mid
        col = value.dir == -1 ? green : red
        o_col = value.dir == -1 ? red : green

        if index > last_index - disp_num
            box.new(bx_left,bx_top,xval,bx_bot,bgcolor = col, border_color = invis, xloc = xloc.bar_time)
            box.new(xval,bx_top,time,bx_bot, bgcolor = o_col, border_color = invis, xloc = xloc.bar_time)

            line.new(bx_left,mid,time,mid, color = gray, style = line.style_dashed, xloc = xloc.bar_time)
            box.new(bar_index,bx_top,bar_index+50,bx_bot, bgcolor = o_col, border_color = invis)
            line.new(bar_index,mid,bar_index+50,mid, color = gray, style = line.style_dashed)

            for stuff in value.labs
                label_maker(stuff.x,stuff.y,stuff.dir)

//---------------------------------------------------------------------------------------------------------------------}
//Delete drawings
//---------------------------------------------------------------------------------------------------------------------{
for boxes in box.all
    box.delete(boxes)

for lines in line.all
    line.delete(lines)

for labels in label.all
    label.delete(labels)

//---------------------------------------------------------------------------------------------------------------------}
//Data Arrays
//---------------------------------------------------------------------------------------------------------------------{
var bull_fvg_ary = array.new<fvg>(na) // FVG Data, Not all will be Drawn
var bear_fvg_ary = array.new<fvg>(na)

var bull_inv_ary = array.new<fvg>(na) // Inversion Data, All will be Drawn
var bear_inv_ary = array.new<fvg>(na)

//---------------------------------------------------------------------------------------------------------------------}
//FVG Detection
//---------------------------------------------------------------------------------------------------------------------{
atr = nz(ta.atr(200)*atr_multi, ta.cum(high - low) / (bar_index+1))

fvg_up = (low > high[2]) and (close[1] > high[2])
fvg_down = (high < low[2]) and (close[1] < low[2])

if fvg_up and math.abs(low-high[2]) > atr
    array.push(bull_fvg_ary,fvg.new(time[1], low, time, high[2], math.avg(low,high[2]), 1, 0,array.new<lab>(na),na))

if fvg_down and math.abs(low[2]-high) > atr
    array.push(bear_fvg_ary,fvg.new(time[1], low[2], time, high, math.avg(high,low[2]),-1 ,0,array.new<lab>(na),na))

//---------------------------------------------------------------------------------------------------------------------}
//Running Functions
//---------------------------------------------------------------------------------------------------------------------{
// FVG_Data -> Inversion_Data -> Chart
fvg_manage(bull_fvg_ary,bull_inv_ary)
fvg_manage(bear_fvg_ary,bear_inv_ary)

bear_signal = inv_manage(bull_inv_ary)
bull_signal = inv_manage(bear_inv_ary)

if barstate.islast
    send_it(bull_inv_ary)
    send_it(bear_inv_ary)

//Alert Options
alertcondition(bull_signal, "Bullish Signal")
alertcondition(bear_signal, "Bearish Signal")
