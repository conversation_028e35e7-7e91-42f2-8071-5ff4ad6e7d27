//@version=5
indicator("HTF Boxes", "", true, max_boxes_count = 500, max_lines_count = 500, max_labels_count = 500, max_bars_back = 5000)

// ---------------------------------------------------------------------------------------------- //
// Inputs  -------------------------------------------------------------------------------------- //

// HTF Box Settings
group_candle = 'HTF Box Settings'
htfCndl1  = input.bool(true, '1st HTF Box', inline='TYP', group=group_candle)
htfUser1  = input.string('4 Hours', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP', group=group_candle)
htfCndl2  = input.bool(false, '2nd HTF Box', inline='TYP3', group=group_candle)
htfUser2  = input.string('1 Hour', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP3', group=group_candle)
bullC     = input.color(#26a69a, 'Box : Bull', inline='CNDL', group=group_candle)
bearC     = input.color(#ef5350, 'Bear', inline='CNDL', group=group_candle)
trans     = input.int(85, 'Transp', inline='CNDL', minval=65, maxval=95, group=group_candle)
lineWidth = input.int(1, 'Line Width', inline='CNDL', minval=1, maxval=4, group=group_candle)

// Optional 3rd HTF Box
htfCndl3  = input.bool(false, '3rd HTF Box', inline='TYP4', group=group_candle)
htfUser3  = input.string('1 Month', 'TF', options=['3 Mins', '5 Mins', '10 Mins', '15 Mins', '30 Mins', '45 Mins', '1 Hour', '2 Hours', '3 Hours', '4 Hours', '1 Day', '1 Week', '1 Month', '3 Months', '6 Months', '1 Year'], inline='TYP4', group=group_candle)

// Moving Averages ------------------------------------------------------------------------------ //
// Inputs for Moving Averages
group_ma = 'Moving Averages Settings'
showMA = input.bool(true, 'Show Moving Averages', group=group_ma)
source = input.source(close, 'Source', group=group_ma)

// Current Timeframe Moving Averages
i_masw = input.bool(false, 'Show Current TF MAs', inline='MA', group=group_ma)
i_ma20C = input.color(color.blue, 'SMA 20 Color', inline='MA', group=group_ma)
i_ma50C = input.color(color.orange, 'SMA 50 Color', inline='MA', group=group_ma)
i_ma100C = input.color(color.green, 'SMA 100 Color', inline='MA', group=group_ma)
i_ma200C = input.color(color.red, 'SMA 200 Color', inline='MA', group=group_ma)

// Higher Timeframe Moving Averages
i_maswh_15 = input.bool(false, '15 Min', inline='HTF1', group=group_ma)
i_maswh_1h = input.bool(false, '1 Hour', inline='HTF2', group=group_ma)
i_maswh_2h = input.bool(false, '2 Hour', inline='HTF3', group=group_ma)
i_maswh_4h = input.bool(false, '4 Hour', inline='HTF4', group=group_ma)
i_maswh_1d = input.bool(false, '1 Day', inline='HTF5', group=group_ma)
i_ma20Ch  = input.color(#311B92, '', inline='MAHTF', group=group_ma)
i_ma50Ch  = input.color(#2196F3, '', inline='MAHTF', group=group_ma)
i_ma100Ch = input.color(#00BCD4, '', inline='MAHTF', group=group_ma)
i_ma200Ch = input.color(#FF9800, '', inline='MAHTF', group=group_ma)

maAlarm = input.bool(true, 'Enable Moving Average Alerts', group=group_ma)

// Function to calculate HTF MAs
f_get_htf_ma(htf) =>
    [ta.sma(source, 20), ta.sma(source, 50), ta.sma(source, 100), ta.sma(source, 200)]

// Request HTF MAs
[hsma20_15, hsma50_15, hsma100_15, hsma200_15] = request.security(syminfo.tickerid, '15', f_get_htf_ma('15'))
[hsma20_1h, hsma50_1h, hsma100_1h, hsma200_1h] = request.security(syminfo.tickerid, '60', f_get_htf_ma('60'))
[hsma20_2h, hsma50_2h, hsma100_2h, hsma200_2h] = request.security(syminfo.tickerid, '120', f_get_htf_ma('120'))
[hsma20_4h, hsma50_4h, hsma100_4h, hsma200_4h] = request.security(syminfo.tickerid, '240', f_get_htf_ma('240'))
[hsma20_1d, hsma50_1d, hsma100_1d, hsma200_1d] = request.security(syminfo.tickerid, 'D', f_get_htf_ma('D'))

// Plot Current Timeframe MAs
sma20 = ta.sma(source, 20)
sma50 = ta.sma(source, 50)
sma100 = ta.sma(source, 100)
sma200 = ta.sma(source, 200)
plot(showMA and i_masw ? sma20 : na, title='SMA 20', color=i_ma20C, linewidth=1)
plot(showMA and i_masw ? sma50 : na, title='SMA 50', color=i_ma50C, linewidth=1)
plot(showMA and i_masw ? sma100 : na, title='SMA 100', color=i_ma100C, linewidth=1)
plot(showMA and i_masw ? sma200 : na, title='SMA 200', color=i_ma200C, linewidth=1)

// Multipliers for each timeframe in minutes
multiplier_15 = 15
multiplier_1h = 60
multiplier_2h = 120
multiplier_4h = 240
multiplier_1d = 1440

// Function to get HTF SMA with a fallback
f_sma(htf_sma, period, multiplier) =>
    cond1 = period * multiplier / timeframe.multiplier < 5000
    cond2 = period * multiplier / timeframe.multiplier > 5000
    cond1 ? ta.sma(source, period * multiplier / timeframe.multiplier) : (cond2 and not na(htf_sma) ? htf_sma : ta.sma(source, period))

// Plot Higher Timeframe MAs (15 Min)
plot(i_maswh_15 ? f_sma(hsma20_15, 20, multiplier_15) : na, title='HTF SMA 20 (15 Min)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_15 ? f_sma(hsma50_15, 50, multiplier_15) : na, title='HTF SMA 50 (15 Min)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_15 ? f_sma(hsma100_15, 100, multiplier_15) : na, title='HTF SMA 100 (15 Min)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_15 ? f_sma(hsma200_15, 200, multiplier_15) : na, title='HTF SMA 200 (15 Min)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (1 Hour)
plot(i_maswh_1h ? f_sma(hsma20_1h, 20, multiplier_1h) : na, title='HTF SMA 20 (1 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_1h ? f_sma(hsma50_1h, 50, multiplier_1h) : na, title='HTF SMA 50 (1 Hour)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_1h ? f_sma(hsma100_1h, 100, multiplier_1h) : na, title='HTF SMA 100 (1 Hour)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_1h ? f_sma(hsma200_1h, 200, multiplier_1h) : na, title='HTF SMA 200 (1 Hour)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (2 Hour)
plot(i_maswh_2h ? f_sma(hsma20_2h, 20, multiplier_2h) : na, title='HTF SMA 20 (2 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_2h ? f_sma(hsma50_2h, 50, multiplier_2h) : na, title='HTF SMA 50 (2 Hour)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_2h ? f_sma(hsma100_2h, 100, multiplier_2h) : na, title='HTF SMA 100 (2 Hour)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_2h ? f_sma(hsma200_2h, 200, multiplier_2h) : na, title='HTF SMA 200 (2 Hour)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (4 Hour)
plot(i_maswh_4h ? f_sma(hsma20_4h, 20, multiplier_4h) : na, title='HTF SMA 20 (4 Hour)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_4h ? f_sma(hsma50_4h, 50, multiplier_4h) : na, title='HTF SMA 50 (4 Hour)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_4h ? f_sma(hsma100_4h, 100, multiplier_4h) : na, title='HTF SMA 100 (4 Hour)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_4h ? f_sma(hsma200_4h, 200, multiplier_4h) : na, title='HTF SMA 200 (4 Hour)', color=i_ma200Ch, linewidth=2)

// Plot Higher Timeframe MAs (1 Day)
plot(i_maswh_1d ? f_sma(hsma20_1d, 20, multiplier_1d) : na, title='HTF SMA 20 (1 Day)', color=i_ma20Ch, linewidth=2)
plot(i_maswh_1d ? f_sma(hsma50_1d, 50, multiplier_1d) : na, title='HTF SMA 50 (1 Day)', color=i_ma50Ch, linewidth=2)
plot(i_maswh_1d ? f_sma(hsma100_1d, 100, multiplier_1d) : na, title='HTF SMA 100 (1 Day)', color=i_ma100Ch, linewidth=2)
plot(i_maswh_1d ? f_sma(hsma200_1d, 200, multiplier_1d) : na, title='HTF SMA 200 (1 Day)', color=i_ma200Ch, linewidth=2)

// Moving Averages ------------------------------------------------------------------------------ //
// ---------------------------------------------------------------------------------------------- //
// Functions  ----------------------------------------------------------------------------------- //

checkIf(_chartTF, _candlestickTF) =>
    var stat = false
    candlestickTF = _candlestickTF == 'D' ? 1440 : _candlestickTF == 'W' ? 10080 :  _candlestickTF == 'M' ? 302400 :  _candlestickTF == '3M' ? 3 * 302400 :  _candlestickTF == '6M' ? 6 * 302400 :  _candlestickTF == '12M' ? 12 * 302400 : str.tonumber(_candlestickTF)

    if timeframe.isintraday
        stat := candlestickTF >= str.tonumber(_chartTF)
    else
        chartTF = str.contains(_chartTF, 'D') ? _chartTF == 'D' ? 1440 : str.tonumber(str.replace(_chartTF, 'D', '', 0)) * 1440 : str.contains(_chartTF, 'W') ? _chartTF == 'W' ? 10080 : str.tonumber(str.replace(_chartTF, 'W', '', 0)) * 10080 : _chartTF == 'M' ? 302400 : str.tonumber(str.replace(_chartTF, 'M', '', 0)) * 302400
        stat := candlestickTF >= chartTF
    stat

f_htf_ohlc(_htf) =>
    var htf_o  = 0., var htf_h  = 0., var htf_l  = 0.,     htf_c  = close
    var htf_ox = 0., var htf_hx = 0., var htf_lx = 0., var htf_cx = 0.

    if ta.change(time(_htf))
        htf_ox := htf_o, htf_o := open
        htf_hx := htf_h, htf_h := high
        htf_lx := htf_l, htf_l := low
        htf_cx := htf_c[1]
        true
    else
        htf_h := math.max(high, htf_h)
        htf_l := math.min(low , htf_l)
        true

    [htf_ox, htf_hx, htf_lx, htf_cx, htf_o, htf_h, htf_l, htf_c]

f_getTF(_htf) =>
    htf = _htf == '3 Mins' ? '3' : _htf == '5 Mins' ? '5' : _htf == '10 Mins' ? '10' : _htf == '15 Mins' ? '15' : _htf == '30 Mins' ? '30' : _htf == '45 Mins' ? '45' : _htf == '1 Hour' ? '60' : _htf == '2 Hours' ? '120' : _htf == '3 Hours' ? '180' : _htf == '4 Hours' ? '240' : _htf == '1 Day' ? 'D' : _htf == '1 Week' ? 'W' : _htf == '1 Month' ? 'M' : _htf == '3 Months' ? '3M' : _htf == '6 Months' ? '6M' : _htf == '1 Year' ? '12M' : na
    htf

f_processCandles(_show, _htf, _bullC, _bearC, _trans, _width) =>
    if _show
        [O1, H1, L1, C1, O0, H0, L0, C0] = f_htf_ohlc(_htf)

        color0  = O0 < C0 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color01 = O0 < C0 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)
        color1  = O1 < C1 ? color.new(_bullC, _trans)   : color.new(_bearC, _trans)
        color11 = O1 < C1 ? color.new(_bullC, _trans/2) : color.new(_bearC, _trans/2)

        var box hl  = na
        var box oc  = na
        var int x11 = na
        var int x1  = na

        if _htf != timeframe.period
            if ta.change(time(_htf))
                x11 := x1
                x1  := bar_index

                if L1 != 0
                    box.new(x11, H1, x1 - 1, L1, color11, _width, line.style_dotted, extend.none, xloc.bar_index, color1)
                    box.new(x11, O1, x1 - 1, C1, color11, _width, line.style_solid , extend.none, xloc.bar_index, color1)

                box.delete(hl), hl := box.new(x1, H0, 2 * x1 - x11 - 1, L0, color01, _width, line.style_dotted, extend.none, xloc.bar_index, color0)
                box.delete(oc), oc := box.new(x1, O0, 2 * x1 - x11 - 1, C0, color01, _width, line.style_solid , extend.none, xloc.bar_index, color0)
                true
            else
                box.set_top(hl, H0)
                box.set_bottom(hl, L0)
                box.set_bgcolor(hl, color0)
                box.set_border_color(hl, color01)

                box.set_top(oc, math.max(O0, C0))
                box.set_bottom(oc, math.min(O0, C0))
                box.set_bgcolor(oc, color0)
                box.set_border_color(oc, color01)
                true

// ---------------------------------------------------------------------------------------------- //
// Main Logic ---------------------------------------------------------------------------------- //

htf1 = f_getTF(htfUser1)
htf2 = f_getTF(htfUser2)
htf3 = f_getTF(htfUser3)
supported1 = checkIf(timeframe.period, htf1)
supported2 = checkIf(timeframe.period, htf2)
supported3 = checkIf(timeframe.period, htf3)

// Process HTF Boxes
if chart.is_standard
    if supported1
        f_processCandles(htfCndl1, htf1, bullC, bearC, trans, lineWidth)
    if supported2
        f_processCandles(htfCndl2, htf2, bullC, bearC, trans, lineWidth)
    if supported3
        f_processCandles(htfCndl3, htf3, bullC, bearC, trans, lineWidth)
