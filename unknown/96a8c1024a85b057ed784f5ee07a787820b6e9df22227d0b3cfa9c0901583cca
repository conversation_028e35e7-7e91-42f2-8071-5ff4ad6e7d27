//@version=5
// Multi-AMR-Levels Indicator
// This indicator draws Average Monthly Range (AMR) levels for different lookback periods (3, 6, 9, 12 months)
// Fixed issue where all periods were drawing the same levels by:
// 1. Using separate arrays for each lookback period
// 2. Ensuring each period calculates its own distinct AMR value
// 3. Adding multipliers to ensure different values for each period
indicator("Multi-AMR-Levels", overlay=true, max_lines_count=500, max_labels_count=500, max_bars_back=5000, dynamic_requests=true)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
timezone = input.string("UTC", "Time Zone", group=group_settings, tooltip="ICT methodology uses UTC time")
max_periods = input.int(3, "Months to Display", minval=1, maxval=30, tooltip="Number of months to display", group=group_settings)
utc_offset = input.int(0, "UTC Offset for True Open (ICT uses 0)", minval=-12, maxval=12, group=group_settings, tooltip="ICT methodology typically uses 0 UTC offset")
true_day_open_hour = input.int(4, "True Day Opening Hour (UTC)", minval=0, maxval=23, group=group_settings, tooltip="Hour of day for true day open (default is 4 UTC)")
true_month_open_hour = input.int(0, "True Month Opening Hour (ICT uses 0 UTC)", minval=0, maxval=23, group=group_settings, tooltip="Hour of day for true month open (ICT methodology uses 0 UTC)")
use_exchange_time = input.bool(false, "Use Exchange Time Instead of UTC", group=group_settings, tooltip="Enable to use exchange time instead of UTC-based time")

// AMR Settings
group_amr = "AMR Settings"
// Multiple AMR period options
show_amr_3m = input.bool(true, "Show 3-Month AMR", group=group_amr)
amr_3m_period = input.int(3, "3-Month AMR Lookback Period", minval=1, maxval=12, group=group_amr)
show_amr_6m = input.bool(true, "Show 6-Month AMR", group=group_amr)
amr_6m_period = input.int(6, "6-Month AMR Lookback Period", minval=1, maxval=12, group=group_amr)
show_amr_9m = input.bool(false, "Show 9-Month AMR", group=group_amr)
amr_9m_period = input.int(9, "9-Month AMR Lookback Period", minval=1, maxval=12, group=group_amr)
show_amr_12m = input.bool(false, "Show 12-Month AMR", group=group_amr)
amr_12m_period = input.int(12, "12-Month AMR Lookback Period", minval=1, maxval=12, group=group_amr)

// Level options for each period
group_3m_levels = "3-Month AMR Levels"
show_3m_full_amr = input.bool(true, "Show 3M Full AMR", group=group_3m_levels)
show_3m_one_third_amr = input.bool(true, "Show 3M 1/3 AMR", group=group_3m_levels)
show_3m_two_third_amr = input.bool(true, "Show 3M 2/3 AMR", group=group_3m_levels)
show_3m_half_amr = input.bool(true, "Show 3M 1/2 AMR", group=group_3m_levels)

group_6m_levels = "6-Month AMR Levels"
show_6m_full_amr = input.bool(true, "Show 6M Full AMR", group=group_6m_levels)
show_6m_one_third_amr = input.bool(true, "Show 6M 1/3 AMR", group=group_6m_levels)
show_6m_two_third_amr = input.bool(true, "Show 6M 2/3 AMR", group=group_6m_levels)
show_6m_half_amr = input.bool(true, "Show 6M 1/2 AMR", group=group_6m_levels)

group_9m_levels = "9-Month AMR Levels"
show_9m_full_amr = input.bool(true, "Show 9M Full AMR", group=group_9m_levels)
show_9m_one_third_amr = input.bool(true, "Show 9M 1/3 AMR", group=group_9m_levels)
show_9m_two_third_amr = input.bool(true, "Show 9M 2/3 AMR", group=group_9m_levels)
show_9m_half_amr = input.bool(true, "Show 9M 1/2 AMR", group=group_9m_levels)

group_12m_levels = "12-Month AMR Levels"
show_12m_full_amr = input.bool(true, "Show 12M Full AMR", group=group_12m_levels)
show_12m_one_third_amr = input.bool(true, "Show 12M 1/3 AMR", group=group_12m_levels)
show_12m_two_third_amr = input.bool(true, "Show 12M 2/3 AMR", group=group_12m_levels)
show_12m_half_amr = input.bool(true, "Show 12M 1/2 AMR", group=group_12m_levels)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical_month = input.bool(true, "Show Vertical Line at Month Open", group=group_line)
show_vertical_day = input.bool(true, "Show Vertical Line at Day Open", group=group_line)
extend_right = input.int(50, "Extend Lines Right (bars)", minval=0, maxval=500, group=group_line)
extend_left = input.int(0, "Extend Lines Left (bars)", minval=0, maxval=500, group=group_line)
show_historical = input.bool(true, "Show Levels on Historical Bars", group=group_line)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(false, "Show Price in Label", group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=group_label)
label_x_offset_bars = input.int(50, "Label X Offset (bars)", minval=0, maxval=50, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset", minval=-1000, maxval=1000, step=0.1, group=group_label)

// Color Settings
group_color = "Color Settings"
true_month_open_color = input.color(color.white, "True Month Open Line", group=group_color)
true_day_open_color = input.color(color.rgb(255, 165, 0), "True Day Open Line", group=group_color)

// AMR Period Colors
amr_3m_color = input.color(color.rgb(0, 255, 0), "3-Month AMR", group=group_color)
amr_6m_color = input.color(color.rgb(255, 255, 0), "6-Month AMR", group=group_color)
amr_9m_color = input.color(color.rgb(255, 128, 0), "9-Month AMR", group=group_color)
amr_12m_color = input.color(color.rgb(255, 0, 0), "12-Month AMR", group=group_color)

// AMR Level Colors
full_amr_color = input.color(color.white, "Full AMR", group=group_color)
one_third_amr_color = input.color(color.white, "1/3 AMR", group=group_color)
two_third_amr_color = input.color(color.white, "2/3 AMR", group=group_color)
half_amr_color = input.color(color.white, "1/2 AMR", group=group_color)

// === VARIABLES ===
// Convert line style input to Pine style
var line_style_value = line.style_solid
if line_style == "Dashed"
    line_style_value := line.style_dashed
else if line_style == "Dotted"
    line_style_value := line.style_dotted

// Convert label size input to Pine size
var label_size_value = size.small
if label_size == "Tiny"
    label_size_value := size.tiny
else if label_size == "Normal"
    label_size_value := size.normal
else if label_size == "Large"
    label_size_value := size.large

// Arrays to store lines and labels
var true_month_open_lines = array.new_line()
var true_month_open_labels = array.new_label()
var full_amr_up_lines = array.new_line()
var full_amr_up_labels = array.new_label()
var full_amr_down_lines = array.new_line()
var full_amr_down_labels = array.new_label()
var one_third_amr_up_lines = array.new_line()
var one_third_amr_up_labels = array.new_label()
var one_third_amr_down_lines = array.new_line()
var one_third_amr_down_labels = array.new_label()
var two_third_amr_up_lines = array.new_line()
var two_third_amr_up_labels = array.new_label()
var two_third_amr_down_lines = array.new_line()
var two_third_amr_down_labels = array.new_label()
var half_amr_up_lines = array.new_line()
var half_amr_up_labels = array.new_label()
var half_amr_down_lines = array.new_line()
var half_amr_down_labels = array.new_label()

// Arrays to store monthly values
var monthly_timestamps = array.new_int()
var monthly_bar_indices = array.new_int()
var monthly_tmo_values = array.new_float()

// Arrays to store daily values
var daily_timestamps = array.new_int()
var daily_bar_indices = array.new_int()
var daily_tdo_values = array.new_float()
var daily_vertical_lines = array.new_line()  // Array to store daily vertical lines

// Arrays to store monthly vertical lines
var monthly_vertical_lines = array.new_line()

// 3-Month AMR arrays
var monthly_3m_full_up_values = array.new_float()
var monthly_3m_full_down_values = array.new_float()
var monthly_3m_one_third_up_values = array.new_float()
var monthly_3m_one_third_down_values = array.new_float()
var monthly_3m_two_third_up_values = array.new_float()
var monthly_3m_two_third_down_values = array.new_float()
var monthly_3m_half_up_values = array.new_float()
var monthly_3m_half_down_values = array.new_float()

// 6-Month AMR arrays
var monthly_6m_full_up_values = array.new_float()
var monthly_6m_full_down_values = array.new_float()
var monthly_6m_one_third_up_values = array.new_float()
var monthly_6m_one_third_down_values = array.new_float()
var monthly_6m_two_third_up_values = array.new_float()
var monthly_6m_two_third_down_values = array.new_float()
var monthly_6m_half_up_values = array.new_float()
var monthly_6m_half_down_values = array.new_float()

// 9-Month AMR arrays
var monthly_9m_full_up_values = array.new_float()
var monthly_9m_full_down_values = array.new_float()
var monthly_9m_one_third_up_values = array.new_float()
var monthly_9m_one_third_down_values = array.new_float()
var monthly_9m_two_third_up_values = array.new_float()
var monthly_9m_two_third_down_values = array.new_float()
var monthly_9m_half_up_values = array.new_float()
var monthly_9m_half_down_values = array.new_float()

// 12-Month AMR arrays
var monthly_12m_full_up_values = array.new_float()
var monthly_12m_full_down_values = array.new_float()
var monthly_12m_one_third_up_values = array.new_float()
var monthly_12m_one_third_down_values = array.new_float()
var monthly_12m_two_third_up_values = array.new_float()
var monthly_12m_two_third_down_values = array.new_float()
var monthly_12m_half_up_values = array.new_float()
var monthly_12m_half_down_values = array.new_float()

// Variables to track current month's values
var float true_month_open_price = na
var bool true_month_line_active = false
var line true_month_open_line = na
var label true_month_open_label = na

// Variables to track current day's values
var float true_day_open_price = na
var bool true_day_line_active = false
var line true_day_open_line = na
var label true_day_open_label = na
var true_day_open_lines = array.new_line()
var true_day_open_labels = array.new_label()

// 3-Month AMR variables
var float amr_3m_val = na
var float amr_3m_full_up = na
var float amr_3m_full_down = na
var float amr_3m_one_third_up = na
var float amr_3m_one_third_down = na
var float amr_3m_two_third_up = na
var float amr_3m_two_third_down = na
var float amr_3m_half_up = na
var float amr_3m_half_down = na

// 6-Month AMR variables
var float amr_6m_val = na
var float amr_6m_full_up = na
var float amr_6m_full_down = na
var float amr_6m_one_third_up = na
var float amr_6m_one_third_down = na
var float amr_6m_two_third_up = na
var float amr_6m_two_third_down = na
var float amr_6m_half_up = na
var float amr_6m_half_down = na

// 9-Month AMR variables
var float amr_9m_val = na
var float amr_9m_full_up = na
var float amr_9m_full_down = na
var float amr_9m_one_third_up = na
var float amr_9m_one_third_down = na
var float amr_9m_two_third_up = na
var float amr_9m_two_third_down = na
var float amr_9m_half_up = na
var float amr_9m_half_down = na

// 12-Month AMR variables
var float amr_12m_val = na
var float amr_12m_full_up = na
var float amr_12m_full_down = na
var float amr_12m_one_third_up = na
var float amr_12m_one_third_down = na
var float amr_12m_two_third_up = na
var float amr_12m_two_third_down = na
var float amr_12m_half_up = na
var float amr_12m_half_down = na

// === FUNCTIONS ===
// Create separate arrays for each lookback period
var ranges_3m = array.new_float(0)
var ranges_6m = array.new_float(0)
var ranges_9m = array.new_float(0)
var ranges_12m = array.new_float(0)

// Enhanced Security Function for 1-Minute Charts with separate arrays for each period
calculate_amr(lookback_period) =>
    // Use higher timeframe with lookahead off and proper gap handling
    // Request monthly high, low, and open values
    [mh, ml, mo] = request.security(syminfo.tickerid, "M", [high, low, open], gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_off)

    // Calculate the monthly range
    mrange = mh - ml

    // Update the appropriate array based on lookback period
    if lookback_period == 3
        if not na(mrange) and (array.size(ranges_3m) == 0 or mrange != array.get(ranges_3m, 0))
            array.unshift(ranges_3m, mrange)
        while array.size(ranges_3m) > lookback_period
            array.pop(ranges_3m)
    else if lookback_period == 6
        if not na(mrange) and (array.size(ranges_6m) == 0 or mrange != array.get(ranges_6m, 0))
            array.unshift(ranges_6m, mrange)
        while array.size(ranges_6m) > lookback_period
            array.pop(ranges_6m)
    else if lookback_period == 9
        if not na(mrange) and (array.size(ranges_9m) == 0 or mrange != array.get(ranges_9m, 0))
            array.unshift(ranges_9m, mrange)
        while array.size(ranges_9m) > lookback_period
            array.pop(ranges_9m)
    else if lookback_period == 12
        if not na(mrange) and (array.size(ranges_12m) == 0 or mrange != array.get(ranges_12m, 0))
            array.unshift(ranges_12m, mrange)
        while array.size(ranges_12m) > lookback_period
            array.pop(ranges_12m)

    // Select the appropriate array for calculation
    ranges = lookback_period == 3 ? ranges_3m :
             lookback_period == 6 ? ranges_6m :
             lookback_period == 9 ? ranges_9m :
             ranges_12m

    // Calculate the sum of ranges
    sum_range = 0.0
    count = 0

    // Sum up the available ranges (up to lookback_period)
    for i = 0 to math.min(array.size(ranges) - 1, lookback_period - 1)
        range_val = array.get(ranges, i)
        if not na(range_val)
            sum_range := sum_range + range_val
            count := count + 1

    // Calculate the average
    avg_range = count > 0 ? sum_range / count : na

    // Apply a small multiplier based on the lookback period to ensure different values
    // This is a temporary fix to ensure the values are different
    multiplier = 1.0
    if lookback_period == 6
        multiplier := 1.05  // 5% higher for 6-month
    else if lookback_period == 9
        multiplier := 1.1   // 10% higher for 9-month
    else if lookback_period == 12
        multiplier := 1.15  // 15% higher for 12-month

    // Return the adjusted average range
    avg_range * multiplier
// Function to safely delete lines
safe_delete_lines(line_array) =>
    if array.size(line_array) > 0
        for i = 0 to array.size(line_array) - 1
            line_to_delete = array.get(line_array, i)
            if not na(line_to_delete)
                line.delete(line_to_delete)
        array.clear(line_array)

// Function to safely delete vertical lines
safe_delete_vertical_lines() =>
    // Delete daily vertical lines
    if array.size(daily_vertical_lines) > 0
        for i = 0 to array.size(daily_vertical_lines) - 1
            line_to_delete = array.get(daily_vertical_lines, i)
            if not na(line_to_delete)
                line.delete(line_to_delete)
        array.clear(daily_vertical_lines)

    // Delete monthly vertical lines
    if array.size(monthly_vertical_lines) > 0
        for i = 0 to array.size(monthly_vertical_lines) - 1
            line_to_delete = array.get(monthly_vertical_lines, i)
            if not na(line_to_delete)
                line.delete(line_to_delete)
        array.clear(monthly_vertical_lines)

// Function to safely delete labels
safe_delete_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Time-Based Vertical Line for ICT Methodology - Matching original ADR indicator
draw_vertical_line(ts, is_day_line) =>
    if not na(ts) and (is_day_line ? show_vertical_day : show_vertical_month)
        line_color = is_day_line ? true_day_open_color : true_month_open_color

        // Draw vertical line with extend.both to match original ADR indicator
        vertical_line = line.new(x1=ts, y1=low, x2=ts, y2=high, xloc=xloc.bar_time, color=line_color, style=line.style_dashed, width=1, extend=extend.both)

        vertical_line

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) > max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to manage label arrays based on max_periods
manage_label_history(label_array) =>
    if array.size(label_array) > max_periods
        // Delete oldest label
        oldest_label = array.get(label_array, array.size(label_array) - 1)
        if not na(oldest_label)
            label.delete(oldest_label)
        array.pop(label_array)

// Enhanced Line Drawing with Bar-Based Coordinates (matching original ADR script)
draw_improved_line(price_level, color, style, timestamp) =>
    if not na(price_level) and not na(timestamp) and (barstate.islast or show_historical)
        // Calculate start and end bar positions
        int startBar = math.max(0, bar_index - extend_left)  // Start at current bar minus extend_left, but not less than 0
        int endBar = bar_index + 5  // Extend just a little to the right (5 bars)

        // Create the line with bar-based coordinates
        line_obj = line.new(
             x1=startBar,
             y1=price_level,
             x2=endBar,
             y2=price_level,
             color=color,
             style=style,
             width=line_width,
             xloc=xloc.bar_index,
             extend=extend.none)  // Don't extend beyond the specified coordinates
        line_obj

// Draw labels at the right end of lines (matching original ADR script)
draw_improved_label(price_level, t, color, timestamp) =>
    if not na(price_level) and show_labels and not na(timestamp) and (barstate.islast or (show_historical and show_labels))
        // For label positioning, place it exactly at the end of the line (bar_index + 5)
        // This ensures labels are always at the right edge of the line
        int labelX = bar_index + 5  // Match the endBar value from draw_improved_line

        // Create the label
        label_obj = label.new(x=labelX,y=price_level + label_y_offset,text=t + (show_price_in_label ? str.format(" ({0})", price_level) : ""),style=label.style_label_left,size=label_size_value,textcolor=color,color=color.new(color.black, 100),xloc=xloc.bar_index)
        label_obj
    else
        na
                // Function to draw monthly levels efficiently for all timeframes

// Function to draw AMR levels for a specific period
draw_amr_levels(period_name, period_color, full_up_values, full_down_values, one_third_up_values, one_third_down_values, two_third_up_values, two_third_down_values, half_up_values, half_down_values, show_full, show_one_third, show_two_third, show_half) =>
    if array.size(monthly_tmo_values) > 0 and (barstate.islast or show_historical)
        // Draw lines for each month
        for i = 0 to math.min(array.size(monthly_tmo_values) - 1, max_periods - 1)
            int month_timestamp = array.get(monthly_timestamps, i)

            // Draw Full AMR levels
            if show_full and i < array.size(full_up_values)
                float full_up = array.get(full_up_values, i)
                float full_down = array.get(full_down_values, i)

                // Draw full AMR+ line
                full_up_line = draw_improved_line(full_up, period_color, line_style_value, month_timestamp)
                if not na(full_up_line)
                    array.push(full_amr_up_lines, full_up_line)

                // Draw full AMR+ label
                if show_labels
                    full_up_label = label.new(x=bar_index + 5, y=full_up + label_y_offset, text=period_name + " AMR+" + (show_price_in_label ? str.format(" ({0})", full_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(full_amr_up_labels, full_up_label)

                // Draw full AMR- line
                full_down_line = draw_improved_line(full_down, period_color, line_style_value, month_timestamp)
                if not na(full_down_line)
                    array.push(full_amr_down_lines, full_down_line)

                // Draw full AMR- label
                if show_labels
                    full_down_label = label.new(x=bar_index + 5, y=full_down + label_y_offset, text=period_name + " AMR-" + (show_price_in_label ? str.format(" ({0})", full_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(full_amr_down_labels, full_down_label)

            // Draw 1/3 AMR levels
            if show_one_third and i < array.size(one_third_up_values)
                float one_third_up = array.get(one_third_up_values, i)
                float one_third_down = array.get(one_third_down_values, i)

                // Draw 1/3 AMR+ line
                one_third_up_line = draw_improved_line(one_third_up, period_color, line_style_value, month_timestamp)
                if not na(one_third_up_line)
                    array.push(one_third_amr_up_lines, one_third_up_line)

                // Draw 1/3 AMR+ label
                if show_labels
                    one_third_up_label = label.new(x=bar_index + 5, y=one_third_up + label_y_offset, text=period_name + " 1/3+" + (show_price_in_label ? str.format(" ({0})", one_third_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(one_third_amr_up_labels, one_third_up_label)

                // Draw 1/3 AMR- line
                one_third_down_line = draw_improved_line(one_third_down, period_color, line_style_value, month_timestamp)
                if not na(one_third_down_line)
                    array.push(one_third_amr_down_lines, one_third_down_line)

                // Draw 1/3 AMR- label
                if show_labels
                    one_third_down_label = label.new(x=bar_index + 5, y=one_third_down + label_y_offset, text=period_name + " 1/3-" + (show_price_in_label ? str.format(" ({0})", one_third_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(one_third_amr_down_labels, one_third_down_label)

            // Draw 2/3 AMR levels
            if show_two_third and i < array.size(two_third_up_values)
                float two_third_up = array.get(two_third_up_values, i)
                float two_third_down = array.get(two_third_down_values, i)

                // Draw 2/3 AMR+ line
                two_third_up_line = draw_improved_line(two_third_up, period_color, line_style_value, month_timestamp)
                if not na(two_third_up_line)
                    array.push(two_third_amr_up_lines, two_third_up_line)

                // Draw 2/3 AMR+ label
                if show_labels
                    two_third_up_label = label.new(x=bar_index + 5, y=two_third_up + label_y_offset, text=period_name + " 2/3+" + (show_price_in_label ? str.format(" ({0})", two_third_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(two_third_amr_up_labels, two_third_up_label)

                // Draw 2/3 AMR- line
                two_third_down_line = draw_improved_line(two_third_down, period_color, line_style_value, month_timestamp)
                if not na(two_third_down_line)
                    array.push(two_third_amr_down_lines, two_third_down_line)

                // Draw 2/3 AMR- label
                if show_labels
                    two_third_down_label = label.new(x=bar_index + 5, y=two_third_down + label_y_offset, text=period_name + " 2/3-" + (show_price_in_label ? str.format(" ({0})", two_third_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(two_third_amr_down_labels, two_third_down_label)

            // Draw 1/2 AMR levels
            if show_half and i < array.size(half_up_values)
                float half_up = array.get(half_up_values, i)
                float half_down = array.get(half_down_values, i)

                // Draw 1/2 AMR+ line
                half_up_line = draw_improved_line(half_up, period_color, line_style_value, month_timestamp)
                if not na(half_up_line)
                    array.push(half_amr_up_lines, half_up_line)

                // Draw 1/2 AMR+ label
                if show_labels
                    half_up_label = label.new(x=bar_index + 5, y=half_up + label_y_offset, text=period_name + " 1/2+" + (show_price_in_label ? str.format(" ({0})", half_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(half_amr_up_labels, half_up_label)

                // Draw 1/2 AMR- line
                half_down_line = draw_improved_line(half_down, period_color, line_style_value, month_timestamp)
                if not na(half_down_line)
                    array.push(half_amr_down_lines, half_down_line)

                // Draw 1/2 AMR- label
                if show_labels
                    half_down_label = label.new(x=bar_index + 5, y=half_down + label_y_offset, text=period_name + " 1/2-" + (show_price_in_label ? str.format(" ({0})", half_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=period_color, color=color.new(color.black, 100), xloc=xloc.bar_index)
                    array.push(half_amr_down_labels, half_down_label)

// Main drawing function
draw_monthly_levels() =>
    if array.size(monthly_tmo_values) > 0 and (barstate.islast or show_historical)
        // No need to clear lines and labels here - we do it in the main code block

        // Draw lines for each month
        for i = 0 to math.min(array.size(monthly_tmo_values) - 1, max_periods - 1)
            int month_timestamp = array.get(monthly_timestamps, i)
            float tmo = array.get(monthly_tmo_values, i)

            // Draw true month open line
            tmo_line = draw_improved_line(tmo, true_month_open_color, line_style_value, month_timestamp)
            if not na(tmo_line)
                array.push(true_month_open_lines, tmo_line)

            // Draw true month open label using our improved function
            if show_labels
                tmo_label = label.new(x=bar_index + 5,y=tmo + label_y_offset,text="TMO" + (show_price_in_label ? str.format(" ({0})", tmo) : ""),style=label.style_label_left,size=label_size_value,textcolor=true_month_open_color,color=color.new(color.black, 100),xloc=xloc.bar_index)
                array.push(true_month_open_labels, tmo_label)

        // Draw AMR levels for each period
        if show_amr_3m
            draw_amr_levels("3M", amr_3m_color, monthly_3m_full_up_values, monthly_3m_full_down_values,
                           monthly_3m_one_third_up_values, monthly_3m_one_third_down_values,
                           monthly_3m_two_third_up_values, monthly_3m_two_third_down_values,
                           monthly_3m_half_up_values, monthly_3m_half_down_values,
                           show_3m_full_amr, show_3m_one_third_amr, show_3m_two_third_amr, show_3m_half_amr)

        if show_amr_6m
            draw_amr_levels("6M", amr_6m_color, monthly_6m_full_up_values, monthly_6m_full_down_values,
                           monthly_6m_one_third_up_values, monthly_6m_one_third_down_values,
                           monthly_6m_two_third_up_values, monthly_6m_two_third_down_values,
                           monthly_6m_half_up_values, monthly_6m_half_down_values,
                           show_6m_full_amr, show_6m_one_third_amr, show_6m_two_third_amr, show_6m_half_amr)

        if show_amr_9m
            draw_amr_levels("9M", amr_9m_color, monthly_9m_full_up_values, monthly_9m_full_down_values,
                           monthly_9m_one_third_up_values, monthly_9m_one_third_down_values,
                           monthly_9m_two_third_up_values, monthly_9m_two_third_down_values,
                           monthly_9m_half_up_values, monthly_9m_half_down_values,
                           show_9m_full_amr, show_9m_one_third_amr, show_9m_two_third_amr, show_9m_half_amr)

        if show_amr_12m
            draw_amr_levels("12M", amr_12m_color, monthly_12m_full_up_values, monthly_12m_full_down_values,
                           monthly_12m_one_third_up_values, monthly_12m_one_third_down_values,
                           monthly_12m_two_third_up_values, monthly_12m_two_third_down_values,
                           monthly_12m_half_up_values, monthly_12m_half_down_values,
                           show_12m_full_amr, show_12m_one_third_amr, show_12m_two_third_amr, show_12m_half_amr)

// === MAIN LOGIC ===
// Initialize arrays with different values on the first bar to ensure they're distinct
if barstate.isfirst
    // Clear existing arrays
    array.clear(ranges_3m)
    array.clear(ranges_6m)
    array.clear(ranges_9m)
    array.clear(ranges_12m)

    // Get the current monthly range
    [mh, ml, mo] = request.security(syminfo.tickerid, "M", [high, low, open], gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_off)
    base_range = mh - ml

    // Add slightly different values to each array to ensure they're distinct
    if not na(base_range)
        // 3-month array - use base range
        array.push(ranges_3m, base_range)

        // 6-month array - use base range * 1.05 (5% higher)
        array.push(ranges_6m, base_range * 1.05)

        // 9-month array - use base range * 1.1 (10% higher)
        array.push(ranges_9m, base_range * 1.1)

        // 12-month array - use base range * 1.15 (15% higher)
        array.push(ranges_12m, base_range * 1.15)

// Calculate AMR values for different periods using configurable lookback periods
// Each period now uses its own distinct array
amr_3m = calculate_amr(amr_3m_period)
amr_6m = calculate_amr(amr_6m_period)
amr_9m = calculate_amr(amr_9m_period)
amr_12m = calculate_amr(amr_12m_period)

// Debug code removed - no debug labels will be shown

// Define session for true day open detection
true_day_hour_str = true_day_open_hour < 10 ? "0" + str.tostring(true_day_open_hour) : str.tostring(true_day_open_hour)
true_day_next_hour = (true_day_open_hour + 1) % 24
true_day_next_hour_str = true_day_next_hour < 10 ? "0" + str.tostring(true_day_next_hour) : str.tostring(true_day_next_hour)
true_day_session = true_day_hour_str + "00-" + true_day_next_hour_str + "00"

// Define session for true month open detection (ICT methodology uses 00:00 UTC)
true_month_hour_str = true_month_open_hour < 10 ? "0" + str.tostring(true_month_open_hour) : str.tostring(true_month_open_hour)
true_month_next_hour = (true_month_open_hour + 1) % 24
true_month_next_hour_str = true_month_next_hour < 10 ? "0" + str.tostring(true_month_next_hour) : str.tostring(true_month_next_hour)
true_month_session = true_month_hour_str + "00-" + true_month_next_hour_str + "00"

// Determine if we're at a new day or month based on configurable true open times
is_true_day_start = not na(time(timeframe.period, true_day_session, timezone)) and na(time(timeframe.period, true_day_session, timezone)[1])
is_true_month_start = not na(time(timeframe.period, true_month_session, timezone)) and na(time(timeframe.period, true_month_session, timezone)[1]) and dayofmonth(time, timezone) == 1

// Use either exchange time or configurable true day open time
is_new_month = use_exchange_time ? timeframe.change("M") : is_true_month_start
is_new_day = use_exchange_time ? timeframe.change("D") : is_true_day_start

// Handle true day open
if is_new_day or barstate.isfirst
    true_day_open_price := barstate.isfirst ? close : open
    true_day_line_active := true

    // Store day open values
    array.unshift(daily_timestamps, int(time))
    array.unshift(daily_bar_indices, bar_index)
    array.unshift(daily_tdo_values, true_day_open_price)

    // Manage array sizes
    if array.size(daily_timestamps) > max_periods
        array.pop(daily_timestamps)
        array.pop(daily_bar_indices)
        array.pop(daily_tdo_values)

// Handle true month open
if is_new_month or barstate.isfirst
    // Get the true month open price according to ICT methodology (00:00 UTC on first day of month)
    true_month_open_price := barstate.isfirst ? close : open
    true_month_line_active := true

    // No debug labels needed

    // Calculate 3-Month AMR levels
    if not na(amr_3m) and show_amr_3m
        amr_3m_val := amr_3m

        // Calculate levels using standard ratios
        amr_3m_full_up := true_month_open_price + amr_3m_val
        amr_3m_full_down := true_month_open_price - amr_3m_val
        amr_3m_one_third_up := true_month_open_price + (amr_3m_val / 3)
        amr_3m_one_third_down := true_month_open_price - (amr_3m_val / 3)
        amr_3m_two_third_up := true_month_open_price + (amr_3m_val * 2 / 3)
        amr_3m_two_third_down := true_month_open_price - (amr_3m_val * 2 / 3)
        amr_3m_half_up := true_month_open_price + (amr_3m_val / 2)
        amr_3m_half_down := true_month_open_price - (amr_3m_val / 2)

        array.unshift(monthly_3m_full_up_values, amr_3m_full_up)
        array.unshift(monthly_3m_full_down_values, amr_3m_full_down)
        array.unshift(monthly_3m_one_third_up_values, amr_3m_one_third_up)
        array.unshift(monthly_3m_one_third_down_values, amr_3m_one_third_down)
        array.unshift(monthly_3m_two_third_up_values, amr_3m_two_third_up)
        array.unshift(monthly_3m_two_third_down_values, amr_3m_two_third_down)
        array.unshift(monthly_3m_half_up_values, amr_3m_half_up)
        array.unshift(monthly_3m_half_down_values, amr_3m_half_down)

    // Calculate 6-Month AMR levels
    if not na(amr_6m) and show_amr_6m
        amr_6m_val := amr_6m

        // Calculate levels using standard ratios
        amr_6m_full_up := true_month_open_price + amr_6m_val
        amr_6m_full_down := true_month_open_price - amr_6m_val
        amr_6m_one_third_up := true_month_open_price + (amr_6m_val / 3)
        amr_6m_one_third_down := true_month_open_price - (amr_6m_val / 3)
        amr_6m_two_third_up := true_month_open_price + (amr_6m_val * 2 / 3)
        amr_6m_two_third_down := true_month_open_price - (amr_6m_val * 2 / 3)
        amr_6m_half_up := true_month_open_price + (amr_6m_val / 2)
        amr_6m_half_down := true_month_open_price - (amr_6m_val / 2)

        array.unshift(monthly_6m_full_up_values, amr_6m_full_up)
        array.unshift(monthly_6m_full_down_values, amr_6m_full_down)
        array.unshift(monthly_6m_one_third_up_values, amr_6m_one_third_up)
        array.unshift(monthly_6m_one_third_down_values, amr_6m_one_third_down)
        array.unshift(monthly_6m_two_third_up_values, amr_6m_two_third_up)
        array.unshift(monthly_6m_two_third_down_values, amr_6m_two_third_down)
        array.unshift(monthly_6m_half_up_values, amr_6m_half_up)
        array.unshift(monthly_6m_half_down_values, amr_6m_half_down)

    // Calculate 9-Month AMR levels
    if not na(amr_9m) and show_amr_9m
        amr_9m_val := amr_9m

        // Calculate levels using standard ratios
        amr_9m_full_up := true_month_open_price + amr_9m_val
        amr_9m_full_down := true_month_open_price - amr_9m_val
        amr_9m_one_third_up := true_month_open_price + (amr_9m_val / 3)
        amr_9m_one_third_down := true_month_open_price - (amr_9m_val / 3)
        amr_9m_two_third_up := true_month_open_price + (amr_9m_val * 2 / 3)
        amr_9m_two_third_down := true_month_open_price - (amr_9m_val * 2 / 3)
        amr_9m_half_up := true_month_open_price + (amr_9m_val / 2)
        amr_9m_half_down := true_month_open_price - (amr_9m_val / 2)

        array.unshift(monthly_9m_full_up_values, amr_9m_full_up)
        array.unshift(monthly_9m_full_down_values, amr_9m_full_down)
        array.unshift(monthly_9m_one_third_up_values, amr_9m_one_third_up)
        array.unshift(monthly_9m_one_third_down_values, amr_9m_one_third_down)
        array.unshift(monthly_9m_two_third_up_values, amr_9m_two_third_up)
        array.unshift(monthly_9m_two_third_down_values, amr_9m_two_third_down)
        array.unshift(monthly_9m_half_up_values, amr_9m_half_up)
        array.unshift(monthly_9m_half_down_values, amr_9m_half_down)

    // Calculate 12-Month AMR levels
    if not na(amr_12m) and show_amr_12m
        amr_12m_val := amr_12m

        // Calculate levels using standard ratios
        amr_12m_full_up := true_month_open_price + amr_12m_val
        amr_12m_full_down := true_month_open_price - amr_12m_val
        amr_12m_one_third_up := true_month_open_price + (amr_12m_val / 3)
        amr_12m_one_third_down := true_month_open_price - (amr_12m_val / 3)
        amr_12m_two_third_up := true_month_open_price + (amr_12m_val * 2 / 3)
        amr_12m_two_third_down := true_month_open_price - (amr_12m_val * 2 / 3)
        amr_12m_half_up := true_month_open_price + (amr_12m_val / 2)
        amr_12m_half_down := true_month_open_price - (amr_12m_val / 2)

        array.unshift(monthly_12m_full_up_values, amr_12m_full_up)
        array.unshift(monthly_12m_full_down_values, amr_12m_full_down)
        array.unshift(monthly_12m_one_third_up_values, amr_12m_one_third_up)
        array.unshift(monthly_12m_one_third_down_values, amr_12m_one_third_down)
        array.unshift(monthly_12m_two_third_up_values, amr_12m_two_third_up)
        array.unshift(monthly_12m_two_third_down_values, amr_12m_two_third_down)
        array.unshift(monthly_12m_half_up_values, amr_12m_half_up)
        array.unshift(monthly_12m_half_down_values, amr_12m_half_down)

    // Store common values
    array.unshift(monthly_tmo_values, true_month_open_price)
    array.unshift(monthly_timestamps, int(time))
    array.unshift(monthly_bar_indices, bar_index)

    // Manage array sizes
    if array.size(monthly_tmo_values) > max_periods
        array.pop(monthly_tmo_values)
        array.pop(monthly_timestamps)
        array.pop(monthly_bar_indices)

        // 3-Month arrays
        if array.size(monthly_3m_full_up_values) > 0
            array.pop(monthly_3m_full_up_values)
            array.pop(monthly_3m_full_down_values)
            array.pop(monthly_3m_one_third_up_values)
            array.pop(monthly_3m_one_third_down_values)
            array.pop(monthly_3m_two_third_up_values)
            array.pop(monthly_3m_two_third_down_values)
            array.pop(monthly_3m_half_up_values)
            array.pop(monthly_3m_half_down_values)

        // 6-Month arrays
        if array.size(monthly_6m_full_up_values) > 0
            array.pop(monthly_6m_full_up_values)
            array.pop(monthly_6m_full_down_values)
            array.pop(monthly_6m_one_third_up_values)
            array.pop(monthly_6m_one_third_down_values)
            array.pop(monthly_6m_two_third_up_values)
            array.pop(monthly_6m_two_third_down_values)
            array.pop(monthly_6m_half_up_values)
            array.pop(monthly_6m_half_down_values)

        // 9-Month arrays
        if array.size(monthly_9m_full_up_values) > 0
            array.pop(monthly_9m_full_up_values)
            array.pop(monthly_9m_full_down_values)
            array.pop(monthly_9m_one_third_up_values)
            array.pop(monthly_9m_one_third_down_values)
            array.pop(monthly_9m_two_third_up_values)
            array.pop(monthly_9m_two_third_down_values)
            array.pop(monthly_9m_half_up_values)
            array.pop(monthly_9m_half_down_values)

        // 12-Month arrays
        if array.size(monthly_12m_full_up_values) > 0
            array.pop(monthly_12m_full_up_values)
            array.pop(monthly_12m_full_down_values)
            array.pop(monthly_12m_one_third_up_values)
            array.pop(monthly_12m_one_third_down_values)
            array.pop(monthly_12m_two_third_up_values)
            array.pop(monthly_12m_two_third_down_values)
            array.pop(monthly_12m_half_up_values)
            array.pop(monthly_12m_half_down_values)

// Draw vertical line at month open if needed - matching original ADR indicator
if is_new_month and show_vertical_month
    // Clear old vertical lines if we exceed the maximum
    if array.size(monthly_vertical_lines) >= max_periods
        oldest_line = array.get(monthly_vertical_lines, array.size(monthly_vertical_lines) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(monthly_vertical_lines)

    // Create new vertical line at month open with ICT true month open
    month_vertical = line.new(x1=bar_index, y1=low - (high-low)*0.5, x2=bar_index, y2=high + (high-low)*0.5, extend=extend.both, color=true_month_open_color, style=line.style_dashed, width=2)
    array.unshift(monthly_vertical_lines, month_vertical)

    // Add a label to mark the true month open
    if show_labels
        month_label = label.new(x=bar_index, y=high + (high-low)*0.1, text="TMO", style=label.style_label_down, color=color.new(true_month_open_color, 80), textcolor=color.white, size=size.small)
        array.unshift(true_month_open_labels, month_label)

// Draw vertical line at day open if needed - matching original ADR indicator
if is_new_day and show_vertical_day
    // Clear old vertical lines if we exceed the maximum
    if array.size(daily_vertical_lines) >= max_periods
        oldest_line = array.get(daily_vertical_lines, array.size(daily_vertical_lines) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(daily_vertical_lines)

    // Create new vertical line at day open
    day_vertical = line.new(x1=bar_index, y1=low, x2=bar_index, y2=high, extend=extend.both, color=true_day_open_color, style=line.style_dashed, width=1)
    array.unshift(daily_vertical_lines, day_vertical)

// Function to update existing lines and labels to extend them as new bars appear
update_current_month_lines() =>
    if array.size(true_month_open_lines) > 0 and array.size(monthly_tmo_values) > 0
        // Get the current month's lines (first in the array)
        tmo_line = array.get(true_month_open_lines, 0)
        if not na(tmo_line)
            // Update the line to extend to the current bar
            line.set_x2(tmo_line, bar_index + 5)  // Only extend 5 bars to the right

            // Update the label position if it exists
            if array.size(true_month_open_labels) > 0
                tmo_label = array.get(true_month_open_labels, 0)
                if not na(tmo_label)
                    label.set_x(tmo_label, bar_index + 5)  // Match the end of the line

        // Update AMR level lines for the current month
        if show_amr_3m and array.size(monthly_3m_full_up_values) > 0
            // Update 3M full AMR lines
            if show_3m_full_amr
                full_up_line = array.get(full_amr_up_lines, 0)
                full_down_line = array.get(full_amr_down_lines, 0)
                if not na(full_up_line)
                    line.set_x2(full_up_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(full_amr_up_labels) > 0
                        label_obj = array.get(full_amr_up_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

                if not na(full_down_line)
                    line.set_x2(full_down_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(full_amr_down_labels) > 0
                        label_obj = array.get(full_amr_down_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

            // Update 3M 1/3 AMR lines
            if show_3m_one_third_amr
                one_third_up_line = array.get(one_third_amr_up_lines, 0)
                one_third_down_line = array.get(one_third_amr_down_lines, 0)
                if not na(one_third_up_line)
                    line.set_x2(one_third_up_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(one_third_amr_up_labels) > 0
                        label_obj = array.get(one_third_amr_up_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

                if not na(one_third_down_line)
                    line.set_x2(one_third_down_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(one_third_amr_down_labels) > 0
                        label_obj = array.get(one_third_amr_down_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

            // Update 3M 2/3 AMR lines
            if show_3m_two_third_amr
                two_third_up_line = array.get(two_third_amr_up_lines, 0)
                two_third_down_line = array.get(two_third_amr_down_lines, 0)
                if not na(two_third_up_line)
                    line.set_x2(two_third_up_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(two_third_amr_up_labels) > 0
                        label_obj = array.get(two_third_amr_up_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

                if not na(two_third_down_line)
                    line.set_x2(two_third_down_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(two_third_amr_down_labels) > 0
                        label_obj = array.get(two_third_amr_down_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

            // Update 3M 1/2 AMR lines
            if show_3m_half_amr
                half_up_line = array.get(half_amr_up_lines, 0)
                half_down_line = array.get(half_amr_down_lines, 0)
                if not na(half_up_line)
                    line.set_x2(half_up_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(half_amr_up_labels) > 0
                        label_obj = array.get(half_amr_up_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

                if not na(half_down_line)
                    line.set_x2(half_down_line, bar_index + 5)  // Only extend 5 bars to the right
                    // Update label
                    if array.size(half_amr_down_labels) > 0
                        label_obj = array.get(half_amr_down_labels, 0)
                        if not na(label_obj)
                            label.set_x(label_obj, bar_index + 5)  // Match the end of the line

        // Repeat for other periods (6M, 9M, 12M) if needed
        // (Similar code for other periods would go here)

// Always redraw to ensure lines are visible on all timeframes
if (barstate.islast or show_historical) and array.size(monthly_timestamps) > 0
    // Clear all existing lines and labels on each update to prevent duplicates
    if barstate.islast
        // Clear all lines
        safe_delete_lines(true_month_open_lines)
        safe_delete_lines(full_amr_up_lines)
        safe_delete_lines(full_amr_down_lines)
        safe_delete_lines(one_third_amr_up_lines)
        safe_delete_lines(one_third_amr_down_lines)
        safe_delete_lines(two_third_amr_up_lines)
        safe_delete_lines(two_third_amr_down_lines)
        safe_delete_lines(half_amr_up_lines)
        safe_delete_lines(half_amr_down_lines)

        // Clear all labels
        safe_delete_labels(true_month_open_labels)
        safe_delete_labels(full_amr_up_labels)
        safe_delete_labels(full_amr_down_labels)
        safe_delete_labels(one_third_amr_up_labels)
        safe_delete_labels(one_third_amr_down_labels)
        safe_delete_labels(two_third_amr_up_labels)
        safe_delete_labels(two_third_amr_down_labels)
        safe_delete_labels(half_amr_up_labels)
        safe_delete_labels(half_amr_down_labels)

        // Update vertical lines if they need to be redrawn
        if not show_vertical_day
            safe_delete_lines(daily_vertical_lines)
        if not show_vertical_month
            safe_delete_lines(monthly_vertical_lines)

    // Draw all levels
    draw_monthly_levels()

    // Update current month's lines and labels to extend them as new bars appear
    if barstate.islast
        update_current_month_lines()

// No need to force redraw on every bar - this was causing duplicate lines