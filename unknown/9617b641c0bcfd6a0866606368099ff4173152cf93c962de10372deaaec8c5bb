//@version=5
indicator(title="Multi-Anchor VWAP", shorttitle="MAVWAP", overlay=true, timeframe="", timeframe_gaps=true)
// VWAP Settings Group
hideonDWM = input(false, title="Hide VWAP on 1D or Above", group="VWAP Settings", display = display.data_window)
src = input(title = "Source", defval = hlc3, group="VWAP Settings", display = display.data_window)
offset = input.int(0, title="Offset", group="VWAP Settings", minval=0, display = display.data_window)

// Anchor Period Settings
show_session = input(true, title="Session", group="Anchor Periods", inline="session")
session_color = input.color(color.rgb(155, 244, 137), title="", group="Anchor Periods", inline="session")

show_week = input(false, title="Week", group="Anchor Periods", inline="week")
week_color = input.color(color.rgb(244, 155, 137), title="", group="Anchor Periods", inline="week")

show_month = input(false, title="Month", group="Anchor Periods", inline="month")
month_color = input.color(color.rgb(137, 155, 244), title="", group="Anchor Periods", inline="month")

show_quarter = input(false, title="Quarter", group="Anchor Periods", inline="quarter")
quarter_color = input.color(color.rgb(244, 137, 155), title="", group="Anchor Periods", inline="quarter")

show_year = input(false, title="Year", group="Anchor Periods", inline="year")
year_color = input.color(color.rgb(137, 244, 155), title="", group="Anchor Periods", inline="year")

cumVolume = ta.cum(volume)
if barstate.islast and cumVolume == 0
    runtime.error("No volume is provided by the data vendor.")

// Define new period conditions for each anchor period
isNewSession = timeframe.change("D")
isNewWeek = timeframe.change("W")
isNewMonth = timeframe.change("M")
isNewQuarter = timeframe.change("3M")
isNewYear = timeframe.change("12M")

// Handle data gaps
if na(src[1])
    isNewSession := true
    isNewWeek := true
    isNewMonth := true
    isNewQuarter := true
    isNewYear := true

// Initialize VWAP variables
float sessionVwap = na
float weekVwap = na
float monthVwap = na
float quarterVwap = na
float yearVwap = na

// Calculate VWAPs for each selected anchor period if not on daily or higher timeframe when hidden
if not (hideonDWM and timeframe.isdwm)
    // Session VWAP
    if show_session
        var float sumSrc = 0.0
        var float sumVol = 0.0

        if isNewSession
            sumSrc := src * volume
            sumVol := volume
        else
            sumSrc += src * volume
            sumVol += volume

        sessionVwap := sumVol != 0 ? sumSrc / sumVol : na

    // Week VWAP
    if show_week
        var float sumSrcWeek = 0.0
        var float sumVolWeek = 0.0

        if isNewWeek
            sumSrcWeek := src * volume
            sumVolWeek := volume
        else
            sumSrcWeek += src * volume
            sumVolWeek += volume

        weekVwap := sumVolWeek != 0 ? sumSrcWeek / sumVolWeek : na

    // Month VWAP
    if show_month
        var float sumSrcMonth = 0.0
        var float sumVolMonth = 0.0

        if isNewMonth
            sumSrcMonth := src * volume
            sumVolMonth := volume
        else
            sumSrcMonth += src * volume
            sumVolMonth += volume

        monthVwap := sumVolMonth != 0 ? sumSrcMonth / sumVolMonth : na

    // Quarter VWAP
    if show_quarter
        var float sumSrcQuarter = 0.0
        var float sumVolQuarter = 0.0

        if isNewQuarter
            sumSrcQuarter := src * volume
            sumVolQuarter := volume
        else
            sumSrcQuarter += src * volume
            sumVolQuarter += volume

        quarterVwap := sumVolQuarter != 0 ? sumSrcQuarter / sumVolQuarter : na

    // Year VWAP
    if show_year
        var float sumSrcYear = 0.0
        var float sumVolYear = 0.0

        if isNewYear
            sumSrcYear := src * volume
            sumVolYear := volume
        else
            sumSrcYear += src * volume
            sumVolYear += volume

        yearVwap := sumVolYear != 0 ? sumSrcYear / sumVolYear : na

// Plot each VWAP line with its own color
plot(show_session ? sessionVwap : na, title = "Session VWAP", color = session_color, offset = offset, linewidth = 2)
plot(show_week ? weekVwap : na, title = "Week VWAP", color = week_color, offset = offset, linewidth = 2)
plot(show_month ? monthVwap : na, title = "Month VWAP", color = month_color, offset = offset, linewidth = 2)
plot(show_quarter ? quarterVwap : na, title = "Quarter VWAP", color = quarter_color, offset = offset, linewidth = 2)
plot(show_year ? yearVwap : na, title = "Year VWAP", color = year_color, offset = offset, linewidth = 2)