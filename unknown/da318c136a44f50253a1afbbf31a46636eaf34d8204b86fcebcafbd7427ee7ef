// This Pine Script® code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © rvdvjn

//@version=5
indicator("VWAP-VWCB-SMI-HLO [Ravi]", "",true,format.price, max_labels_count=200, max_lines_count=50,dynamic_requests = true)

// Master toggles for each component to reduce plot count
show_vwap = input.bool(true, "Show VWAP", group="Component Toggles")
show_round_numbers = input.bool(false, "Show Round Numbers", group="Component Toggles")
show_ar_levels = input.bool(true, "Show AR Levels", group="Component Toggles")

//vwap

hideonDWM = input(false, title="Hide VWAP on 1D or Above", group="VWAP Settings", display = display.data_window)
var anchor = input.string(defval = "Session", title="Anchor Period",
 options=["Session", "Week", "Month", "Quarter", "Year", "Decade", "Century", "Earnings", "Dividends", "Splits"], group="VWAP Settings")
src = input(title = "Source", defval = hlc3, group="VWAP Settings", display = display.data_window)
offset = input.int(0, title="Offset", group="VWAP Settings", minval=0, display = display.data_window)

BANDS_GROUP = "Bands Settings"
CALC_MODE_TOOLTIP = "Determines the units used to calculate the distance of the bands. When 'Percentage' is selected, a multiplier of 1 means 1%."
calcModeInput = input.string("Standard Deviation", "Bands Calculation Mode", options = ["Standard Deviation", "Percentage"], group = BANDS_GROUP, tooltip = CALC_MODE_TOOLTIP, display = display.data_window)
showBand_1 = input(true, title = "", group = BANDS_GROUP, inline = "band_1", display = display.data_window)
bandMult_1 = input.float(1.0, title = "Bands Multiplier #1", group = BANDS_GROUP, inline = "band_1", step = 0.5, minval=0, display = display.data_window)
showBand_2 = input(false, title = "", group = BANDS_GROUP, inline = "band_2", display = display.data_window)
bandMult_2 = input.float(2.0, title = "Bands Multiplier #2", group = BANDS_GROUP, inline = "band_2", step = 0.5, minval=0, display = display.data_window)
showBand_3 = input(false, title = "", group = BANDS_GROUP, inline = "band_3", display = display.data_window)
bandMult_3 = input.float(3.0, title = "Bands Multiplier #3", group = BANDS_GROUP, inline = "band_3", step = 0.5, minval=0, display = display.data_window)

cumVolume = ta.cum(volume)
if barstate.islast and cumVolume == 0
    runtime.error("No volume is provided by the data vendor.")

new_earnings = request.earnings(syminfo.tickerid, earnings.actual, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_dividends = request.dividends(syminfo.tickerid, dividends.gross, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_split = request.splits(syminfo.tickerid, splits.denominator, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)

isNewPeriod = switch anchor
	"Earnings"  => not na(new_earnings)
	"Dividends" => not na(new_dividends)
	"Splits"    => not na(new_split)
	"Session"   => timeframe.change("D")
	"Week"      => timeframe.change("W")
	"Month"     => timeframe.change("M")
	"Quarter"   => timeframe.change("3M")
	"Year"      => timeframe.change("12M")
	"Decade"    => timeframe.change("12M") and year % 10 == 0
	"Century"   => timeframe.change("12M") and year % 100 == 0
	=> false

isEsdAnchor = anchor == "Earnings" or anchor == "Dividends" or anchor == "Splits"
if na(src[1]) and not isEsdAnchor
	isNewPeriod := true

float vwapValue = na
float upperBandValue1 = na
float lowerBandValue1 = na
float upperBandValue2 = na
float lowerBandValue2 = na
float upperBandValue3 = na
float lowerBandValue3 = na

if not (hideonDWM and timeframe.isdwm)
    [_vwap, _stdevUpper, _] = ta.vwap(src, isNewPeriod, 1)
	vwapValue := _vwap
    stdevAbs = _stdevUpper - _vwap
	bandBasis = calcModeInput == "Standard Deviation" ? stdevAbs : _vwap * 0.01
	upperBandValue1 := _vwap + bandBasis * bandMult_1
	lowerBandValue1 := _vwap - bandBasis * bandMult_1
	upperBandValue2 := _vwap + bandBasis * bandMult_2
	lowerBandValue2 := _vwap - bandBasis * bandMult_2
	upperBandValue3 := _vwap + bandBasis * bandMult_3
	lowerBandValue3 := _vwap - bandBasis * bandMult_3

plot(show_vwap ? vwapValue : na, title = "VWAP", color = color.rgb(155, 244, 137), offset = offset, linewidth = 3)


// Round Numbers Settings Group
// Use the master toggle instead of a separate input
showRoundNumbers = show_round_numbers
roundNumbersExpanded = input.bool(false, "⚙", group="Round Numbers", inline="rn_toggle")

// Only show these inputs if roundNumbersExpanded is true
numLevels = input.int(20, "Number of Levels (Above/Below)", minval=1, maxval=50,
         group="Round Numbers Settings", inline="rn_levels")
priceInterval = input.int(5, "Price Interval", minval=1,
         tooltip="Draw lines at every X price points",
         group="Round Numbers Settings", inline="rn_levels")

// Line Settings (only visible when roundNumbersExpanded is true)
extendLeft = input.int(500, "Extend Left", minval=0,
         group="Round Numbers Settings")
extendRight = input.int(500, "Extend Right", minval=0,
         group="Round Numbers Settings")
lineColor = input.color(#eadf10dc, "Line Color",
         group="Round Numbers Settings")
lineStyle = input.string("Solid", "Line Style",
         options=["Solid", "Dashed", "Dotted"],
         group="Round Numbers Settings")
lineWidth = input.int(2, "Line Width", minval=1, maxval=4,
         group="Round Numbers Settings")
showLabels = input.bool(true, "Show Labels",
         group="Round Numbers Settings")

// Convert line style input to Pine style
var lineStyleDict = line.style_solid
if lineStyle == "Dashed"
    lineStyleDict := line.style_dashed
else if lineStyle == "Dotted"
    lineStyleDict := line.style_dotted

// Function to determine if the number is a big round number
isBigRoundNumber(num) =>
    num % priceInterval == 0

// Store lines in array
var line[] linesArray = array.new_line()

// Clear previous lines and draw new ones on the last bar
if barstate.islast and showRoundNumbers
    // Get current price and round it to nearest interval
    currentPrice = close
    basePrice = math.round(currentPrice / priceInterval) * priceInterval

    // Calculate start and end prices based on current price
    startPrice = basePrice - (numLevels * priceInterval)
    endPrice = basePrice + (numLevels * priceInterval)

    // Clear previous lines
    if array.size(linesArray) > 0
        for i = array.size(linesArray) - 1 to 0
            line.delete(array.get(linesArray, i))
        array.clear(linesArray)

    // Plot new lines
    for i = startPrice to endPrice by priceInterval
        if isBigRoundNumber(i)
            newLine = line.new(
                 x1=bar_index - extendLeft,
                 y1=i,
                 x2=bar_index + extendRight,
                 y2=i,
                 color=lineColor,
                 style=lineStyleDict,
                 width=lineWidth)
            array.push(linesArray, newLine)

            // Add labels on both ends if enabled
            if showLabels
                // label.new(
                //      bar_index - extendLeft,
                //      i,
                //      str.tostring(i),
                //      style=label.style_label_right,
                //      color=color.new(lineColor, 100),
                //      textcolor=color.new(lineColor, 0))
                label.new(
                     bar_index + extendRight,
                     i,
                     str.tostring(i),
                     style=label.style_label_left,
                     color=color.new(lineColor, 100),
                     textcolor=color.new(lineColor, 0))








//@version=5
// indicator(title = 'Volume Weighted Colored Bars with Exhaustion & Volatility', overlay = true, max_lines_count = 500)

// -Definitions ════════════════════════════════════════════════════════════════════════════════ //
group_volume_weighted_colored_bars      = 'Volume Weighted Colored Bars'
tooltip_volume_weighted_colored_bars    = 'Colors bars based on the bar\'s volume relative to volume moving average\n' +
                                          'trading tip : a potential breakout trading opportunity may occur when price moves above a resistance level or moves below a support level on increasing volume'

group_volume_spike_sign_of_exhaustion   = 'Volume Spike - Sign of Exhaustion'
tooltip_volume_spike_sign_of_exhaustion = 'Moments where\n' +
                                          'huge volume detected : current volume is grater than the product of the theshold value and volume moving average\n' +
                                          'presents idea : huge volume may be a sign of exhaustion and may lead to sharp reversals'

group_high_volatility                   = 'High Volatility'
tooltip_high_volatility                 = 'Moments where\n' +
                                           'price range of the current bar is grater than the product of the theshold value and average true range value of defined period'

tooltip_volume_moving_average           = 'Volume simple moving average, serves as reference to Volume Weighted Colored Bars calculations'

// -Inputs ════════════════════════════════════════════════════════════════════════════════════ //
// General Settings
i_vSMA_length = input.int(89, 'Volume Moving Average Length', group='General Settings', tooltip=tooltip_volume_moving_average)
srLookbackRange = input.string('Visible Range', 'Lookback Range', options = ['Fixed Range', 'Visible Range'], group='General Settings')
i_lenLookback = input.int(360, 'Fixed Range : Lookback Interval (Bars)', minval=0, step=10, group='General Settings')

// Volume Weighted Colored Bars
i_vwcb = input.bool(true, 'Enable Volume Weighted Colored Bars', inline='VWC', group=group_volume_weighted_colored_bars, tooltip=tooltip_volume_weighted_colored_bars)
i_vwcbHighThresh = input.float(1.618, 'Thresholds : High ', minval=1., step=.1, inline='VWC', group=group_volume_weighted_colored_bars)
i_vwcbLowThresh = input.float(0.618, 'Low', minval=.1, step=.1, inline='VWC', group=group_volume_weighted_colored_bars)

// Volume Spike - Sign of Exhaustion
i_vSpikeLb = input.bool(true, '🚦 Show Volume Spike Indicator', group=group_volume_spike_sign_of_exhaustion, tooltip=tooltip_volume_spike_sign_of_exhaustion)
i_vSpikeThresh = input.float(4.669, 'Volume Spike Threshold', minval=.1, step=.1, group=group_volume_spike_sign_of_exhaustion)

// High Volatility
i_hATRLb = input.bool(true, '⚡ Show High Volatility Indicator', group=group_high_volatility, tooltip=tooltip_high_volatility)
i_atrLength = input.int(11, 'ATR Length', group=group_high_volatility)
i_atrMult = input.float(2.718, 'ATR Multiplier', minval=.1, step=.1, group=group_high_volatility)

// -Calculations ════════════════════════════════════════════════════════════════════════════════ //
nzVolume = nz(volume)
i_vSMA = ta.sma(nzVolume, i_vSMA_length)

bullCandle = close > open
bearCandle = close < open

risingPrice  = close > close[1]
fallingPrice = close < close[1]

lwstPrice = ta.lowest(low, 3)
hstPrice  = ta.highest(high, 3)

weightedATR = i_atrMult * ta.atr(i_atrLength)
range_1 = math.abs(high - low)

x2 = timenow + 7 * math.round(ta.change(time))

var sProcessing = false
if srLookbackRange == 'Visible Range'
    sProcessing := time >= chart.left_visible_bar_time
else
    sProcessing := time > timenow - i_lenLookback * (timeframe.isintraday ? timeframe.multiplier * 86400000 / 1440 : timeframe.multiplier * 86400000)

// Volume Spike - Sign of Exhaustion
exhaustVol = nzVolume > i_vSpikeThresh * i_vSMA
x1V = ta.valuewhen(exhaustVol, time, 0)

// High Volatility
highVolatility = range_1 > weightedATR
x1hV = ta.valuewhen(highVolatility, time, 0)

// -Plotting ════════════════════════════════════════════════════════════════════════════════════ //

// Volume Weighted Colored Bars
vwcbCol = nzVolume > i_vSMA * i_vwcbHighThresh ?bearCandle ? color.rgb(153, 6, 6) : #056205 :nzVolume < i_vSMA * i_vwcbLowThresh ?  bearCandle ? #FF9800 : #7FFFD4 :na

barcolor(i_vwcb and not na(nzVolume) ? vwcbCol : na, title='Volume Weighted Colored Bars')

// Volume Spike - Sign of Exhaustion

plotchar(i_vSpikeLb and not na(nzVolume) and sProcessing ? exhaustVol : na, 'Exhaustion Bar', '🚦', location.abovebar, size=size.tiny)

// High Volatility
plotchar(i_hATRLb and sProcessing ? highVolatility : na, 'High Volatile Bar', '⚡', location.belowbar, size=size.tiny)

// -Alerts ════════════════════════════════════════════════════════════════════════════════════ //
priceTxt = str.tostring(close, format.mintick)
tickerTxt = syminfo.ticker

if nzVolume > i_vSMA * i_vwcbHighThresh and i_vwcb
    alert(tickerTxt + ' High Volume, price ' + priceTxt)

if nzVolume > i_vSMA * i_vSpikeThresh and i_vSpikeLb
    alert(tickerTxt + ' Volume Spike : sign of exhaustion, huge volume increase detected, price ' + priceTxt)

if ta.crossover(range_1, weightedATR) and i_hATRLb
    alert(tickerTxt + ' High Volatility detected, price ' + priceTxt)

// -Credits ════════════════════════════════════════════════════════════════════════════════════ //
var table logo = table.new(position.bottom_right, 1, 1)
if barstate.islast
    table.cell(logo, 0, 0, '☼☾  ', text_size=size.normal, text_color=color.teal)

//@version=5
// indicator("Smart Money Interest Index [Ravi]"
//   , overlay=true // Ensure the script is drawn on the main chart
//   , max_boxes_count = 500
//   , max_labels_count = 500
//   , max_lines_count = 500)

x = input.int(25, "Index Period", minval = 1)
rr = input.int(14, "Volume Flow Period", minval = 1)
peakslen = input.int(500, "Normalization Period", minval = 1)
thr = input.float(0.9, "High Interest Threshold", minval = 0.01, maxval = 0.99)
green = input.color(#00ffbb, "Up Color")
red = input.color(#ff1100, "Down Color")

dumb = ta.pvi-ta.ema(ta.pvi,255)
smart = ta.nvi-ta.ema(ta.nvi,255)

drsi = ta.rsi(dumb, rr)
srsi = ta.rsi(smart, rr)

r = srsi/drsi //ratio shows if smart money is buying from dumb money selling and vice versa

sums = math.sum(r, x)
peak = ta.highest(sums, peakslen)

index = sums/peak

condition = index > thr
// barcolor(condition ? green : na)

// Main Panel Arrows
plotshape(series= condition ? 1 : na, title="High Smart Money Interest", color=color.rgb(233, 239, 233), style=shape.arrowup, size=size.normal, location=location.belowbar, force_overlay=true)

// Alert condition
alertcondition(condition=condition, title="High Smart Money Interest Alert", message="High Smart Money Interest detected!")
