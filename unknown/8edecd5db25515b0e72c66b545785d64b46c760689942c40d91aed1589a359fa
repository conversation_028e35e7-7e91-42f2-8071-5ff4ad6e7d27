//@version=5
// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © GenZai 18-08-2021 v5
// Modified to plot arrows on candles

indicator("OSCAR with Candle Arrows", overlay=true)

// Input parameters
len = input.int(title="Oscar Candles", defval=8)
slowLen = input.int(title="Slow Oscar Candles", defval=16)
smoothing = input.string(title="Oscar Smoothing", defval="RMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"])
slowOscarSmoothing = input.string(title="Slow Oscar Smoothing", defval="WMA", options=["RMA", "SMA", "EMA", "WMA", "LSMA", "HMA", "NONE"])
crossSignalSensitivity = input.float(title="Cross Signal Sensitivity", defval=0.5, step=0.1)
OscarOfIndicator = input.source(title="Choose input type", defval=close)
bottomSignalLine = input.float(title="Bottom Signal Line", defval=35, step=5)
topSignalLine = input.float(title="Top Signal Line", defval=65, step=5)
showValues = input.bool(title="Show Indicator Values", defval=false)

// Moving average function
ma(smoothingType, OscarParam, length) =>
    if smoothingType == "RMA"
        ta.rma(OscarParam, length)
    else if smoothingType == "SMA"
        ta.sma(OscarParam, length)
    else if smoothingType == "EMA"
        ta.ema(OscarParam, length)
    else if smoothingType == "WMA"
        ta.wma(OscarParam, length)
    else if smoothingType == "LSMA"
        ta.linreg(OscarParam, length, 0)
    else if smoothingType == "HMA"
        ta.hma(OscarParam, length)
    else
        OscarParam

// Oscar calculation
A = ta.highest(OscarOfIndicator, len)
B = ta.lowest(OscarOfIndicator, len)
OscarRough = (OscarOfIndicator - B) / (A - B) * 100
Oscar1 = (OscarRough[1] / 3) * 2
OscarThird = OscarRough / 3
Oscar = Oscar1 + OscarThird
smoothedOscarRough = ma(smoothing, OscarRough, len)
smoothedOscar = ma(smoothing, Oscar, len)

// Slow Oscar calculation
slowOscarMa(slowOscarSmoothing, slowOscarParam, slowLen) =>
    if slowOscarSmoothing == "RMA"
        ta.rma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "SMA"
        ta.sma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "EMA"
        ta.ema(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "WMA"
        ta.wma(slowOscarParam, slowLen)
    else if slowOscarSmoothing == "LSMA"
        ta.linreg(slowOscarParam, slowLen, 0)
    else if slowOscarSmoothing == "HMA"
        ta.hma(slowOscarParam, slowLen)
    else
        slowOscarParam

slowA = ta.highest(OscarOfIndicator, slowLen)
slowB = ta.lowest(OscarOfIndicator, slowLen)
slowOscarRough = (OscarOfIndicator - slowB) / (slowA - slowB) * 100
slowOscar1 = (slowOscarRough[1] / 3) * 2
slowOscarThird = slowOscarRough / 3
slowOscar = slowOscar1 + slowOscarThird
smoothedSlowOscar = slowOscarMa(slowOscarSmoothing, slowOscar, slowLen)

// Cross sensitivity calculation
crossSensitivity = math.max(smoothedOscarRough, smoothedOscar) - math.min(smoothedOscarRough, smoothedOscar)

// Signal conditions
buySignal1 = ta.crossover(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough < bottomSignalLine
buySignal2 = ta.crossover(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > smoothedOscar and smoothedOscar < bottomSignalLine

sellSignal1 = ta.crossunder(smoothedOscarRough, smoothedOscar) and crossSensitivity > crossSignalSensitivity and smoothedOscarRough > topSignalLine
sellSignal2 = ta.crossunder(smoothedOscarRough, smoothedOscar)[1] and crossSensitivity > crossSignalSensitivity and smoothedOscar > smoothedOscarRough and smoothedOscar > topSignalLine

// Plot arrows on candles
// Buy Signal 1 - Triangle Up
plotshape(
    series=buySignal1,
    title="Buy Signal 1",
    style=shape.triangleup,
    location=location.belowbar,
    color=color.green,
    size=size.small
    )

// Buy Signal 2 - Arrow Up
plotshape(
    series=buySignal2,
    title="Buy Signal 2",
    style=shape.arrowup,
    location=location.belowbar,
    color=color.green,
    size=size.small
    )

// Sell Signal 1 - Triangle Down
plotshape(
    series=sellSignal1,
    title="Sell Signal 1",
    style=shape.triangledown,
    location=location.abovebar,
    color=color.red,
    size=size.small
    )

// Sell Signal 2 - Arrow Down
plotshape(
    series=sellSignal2,
    title="Sell Signal 2",
    style=shape.arrowdown,
    location=location.abovebar,
    color=color.red,
    size=size.small
    )

// Display indicator values as labels
var label valueLabel = na
if showValues
    valueLabel := label.new(
        x=bar_index,
        y=high,
        text="Oscar: " + str.tostring(smoothedOscar, "#.##") +
             "\nOscar Rough: " + str.tostring(smoothedOscarRough, "#.##") +
             "\nSensitivity: " + str.tostring(crossSensitivity, "#.##"),
        style=label.style_label_down,
        color=color.rgb(0, 0, 0, 80),
        textcolor=color.white,
        yloc=yloc.price
    )
    label.delete(valueLabel[1])
