//@version=5
indicator("Advanced Time Technique (ATT)", overlay=true, max_boxes_count=500, max_labels_count=500)

// ===== Input Parameters =====
// ATT Settings
var g_att = "ATT Settings"
show_att_numbers = input.bool(true, "Show ATT Numbers", group=g_att)
show_att_markers = input.bool(true, "Show ATT Markers", group=g_att)
show_att_labels = input.bool(true, "Show ATT Labels", group=g_att)
wait_for_new_hour = input.bool(true, "Wait for New Hour", tooltip="Only start ATT at the beginning of a new hour", group=g_att)
market_hours_only = input.bool(true, "Market Hours Only", tooltip="Only show ATT during market hours", group=g_att)
market_open_time = input.session("0930-1600", "Market Hours", group=g_att)

// ATT Numbers Selection
var g_att_numbers = "ATT Numbers"
use_3 = input.bool(true, "Use 3", inline="att_3", group=g_att_numbers)
att_3_color = input.color(color.yellow, "", inline="att_3", group=g_att_numbers)

use_11 = input.bool(true, "Use 11", inline="att_11", group=g_att_numbers)
att_11_color = input.color(color.yellow, "", inline="att_11", group=g_att_numbers)

use_17 = input.bool(true, "Use 17", inline="att_17", group=g_att_numbers)
att_17_color = input.color(color.yellow, "", inline="att_17", group=g_att_numbers)

use_29 = input.bool(true, "Use 29", inline="att_29", group=g_att_numbers)
att_29_color = input.color(color.yellow, "", inline="att_29", group=g_att_numbers)

use_41 = input.bool(true, "Use 41", inline="att_41", group=g_att_numbers)
att_41_color = input.color(color.yellow, "", inline="att_41", group=g_att_numbers)

use_47 = input.bool(true, "Use 47", inline="att_47", group=g_att_numbers)
att_47_color = input.color(color.yellow, "", inline="att_47", group=g_att_numbers)

use_53 = input.bool(true, "Use 53", inline="att_53", group=g_att_numbers)
att_53_color = input.color(color.yellow, "", inline="att_53", group=g_att_numbers)

use_59 = input.bool(true, "Use 59", inline="att_59", group=g_att_numbers)
att_59_color = input.color(color.yellow, "", inline="att_59", group=g_att_numbers)

// Appearance Settings
var g_appearance = "Appearance Settings"
high_color = input.color(color.lime, "High Color", group=g_appearance)
low_color = input.color(color.red, "Low Color", group=g_appearance)
reentry_color = input.color(color.yellow, "Re-entry Color", group=g_appearance)
marker_size = input.string("Small", "Marker Size", options=["Tiny", "Small", "Normal", "Large"], group=g_appearance)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=g_appearance)
label_offset = input.float(0.0003, "Label Offset", minval=0.0001, maxval=0.01, step=0.0001, group=g_appearance)

// Swing Detection Settings
var g_swing = "Swing Detection Settings"
swing_length = input.int(3, "Swing Detection Length", minval=2, maxval=10, group=g_swing)
swing_strength = input.int(2, "Swing Strength", minval=1, maxval=5, tooltip="Higher values require stronger swings", group=g_swing)
allow_one_bar_offset = input.bool(true, "Allow 1-Bar Offset", tooltip="Allow ATT to form 1 bar before or after the exact ATT minute", group=g_swing)

// ===== ATT Numbers =====
// Create an array of ATT numbers based on user selection
var att_numbers = array.new_int()
var att_colors = array.new_color()

if barstate.isfirst
    // Clear arrays
    array.clear(att_numbers)
    array.clear(att_colors)
    
    // Add selected ATT numbers and their colors
    if use_3
        array.push(att_numbers, 3)
        array.push(att_colors, att_3_color)
    if use_11
        array.push(att_numbers, 11)
        array.push(att_colors, att_11_color)
    if use_17
        array.push(att_numbers, 17)
        array.push(att_colors, att_17_color)
    if use_29
        array.push(att_numbers, 29)
        array.push(att_colors, att_29_color)
    if use_41
        array.push(att_numbers, 41)
        array.push(att_colors, att_41_color)
    if use_47
        array.push(att_numbers, 47)
        array.push(att_colors, att_47_color)
    if use_53
        array.push(att_numbers, 53)
        array.push(att_colors, att_53_color)
    if use_59
        array.push(att_numbers, 59)
        array.push(att_colors, att_59_color)

// ===== Time Calculations =====
// Get current minute within the hour
current_minute = minute(time)

// Check if we're in market hours
in_market_hours = not na(time(timeframe.period, market_open_time))

// Check if we're at the start of a new hour
is_new_hour = hour(time) != hour(time[1]) or na(time[1])

// Track if we've seen a new hour since the market opened
var seen_new_hour = false
if is_new_hour and in_market_hours
    seen_new_hour := true

// Function to check if current bar is an ATT number
isAttNumber(int min) =>
    result = false
    for i = 0 to array.size(att_numbers) - 1
        if min == array.get(att_numbers, i)
            result := true
    result

// Function to get color for a specific ATT number
getAttColor(int min) =>
    color result = color.yellow
    for i = 0 to array.size(att_numbers) - 1
        if min == array.get(att_numbers, i)
            result := array.get(att_colors, i)
    result

// Check if current bar is an ATT minute
is_att_minute = isAttNumber(current_minute)

// Check if previous or next bar is an ATT minute (for offset)
is_prev_att_minute = isAttNumber(current_minute[1])
is_next_att_minute = isAttNumber(current_minute == 59 ? 0 : current_minute + 1)

// ===== Swing Detection =====
// More robust swing detection
bool swingHighCondition = false
bool swingLowCondition = false

// Check if current bar is a swing high
if high == ta.highest(high, swing_length)
    int highCount = 0
    for i = 1 to swing_length
        if high > high[i]
            highCount += 1
    swingHighCondition := highCount >= swing_strength

// Check if current bar is a swing low
if low == ta.lowest(low, swing_length)
    int lowCount = 0
    for i = 1 to swing_length
        if low < low[i]
            lowCount += 1
    swingLowCondition := lowCount >= swing_strength

// ===== ATT Point Classification =====
// Determine if this ATT point is a high, low, or re-entry
is_att_high = is_att_minute and swingHighCondition
is_att_low = is_att_minute and swingLowCondition
is_att_reentry = is_att_minute and not swingHighCondition and not swingLowCondition

// Handle 1-bar offset if enabled
if allow_one_bar_offset
    // Check if previous bar was an ATT number and this bar forms a swing
    if is_prev_att_minute and not is_att_minute
        is_att_high := is_att_high or swingHighCondition
        is_att_low := is_att_low or swingLowCondition
        is_att_reentry := is_att_reentry or (not swingHighCondition and not swingLowCondition)
    
    // Check if next bar is an ATT number and this bar forms a swing
    if is_next_att_minute and not is_att_minute
        is_att_high := is_att_high or swingHighCondition
        is_att_low := is_att_low or swingLowCondition
        is_att_reentry := is_att_reentry or (not swingHighCondition and not swingLowCondition)

// ===== Visualization =====
// Only show ATT if we're in market hours and have seen a new hour (if those options are enabled)
show_att = (not market_hours_only or in_market_hours) and 
           (not wait_for_new_hour or seen_new_hour) and
           show_att_numbers

// Convert size string to actual size value
var size_map = array.from(size.tiny, size.small, size.normal, size.large)
var size_names = array.from("Tiny", "Small", "Normal", "Large")

getSize(string size_str) =>
    int index = 1  // Default to small
    for i = 0 to array.size(size_names) - 1
        if size_str == array.get(size_names, i)
            index := i
    array.get(size_map, index)

marker_size_value = getSize(marker_size)
label_size_value = getSize(label_size)

// Plot ATT markers
plotshape(
    show_att and is_att_high,
    title="ATT High",
    style=shape.circle,
    location=location.abovebar,
    color=high_color,
    size=marker_size_value
)

plotshape(
    show_att and is_att_low,
    title="ATT Low",
    style=shape.circle,
    location=location.belowbar,
    color=low_color,
    size=marker_size_value
)

plotshape(
    show_att and is_att_reentry and not is_att_high and not is_att_low,
    title="ATT Re-entry",
    style=shape.circle,
    location=location.absolute,
    color=reentry_color,
    size=marker_size_value,
    y=close
)

// Add labels for ATT numbers
if show_att and show_att_labels and is_att_minute
    label_text = str.tostring(current_minute)
    label_color = getAttColor(current_minute)
    
    if is_att_high
        label.new(
            bar_index,
            high + (high * label_offset),
            label_text,
            color=color.new(label_color, 80),
            style=label.style_label_down,
            textcolor=high_color,
            size=label_size_value
        )
    else if is_att_low
        label.new(
            bar_index,
            low - (low * label_offset),
            label_text,
            color=color.new(label_color, 80),
            style=label.style_label_up,
            textcolor=low_color,
            size=label_size_value
        )
    else
        label.new(
            bar_index,
            close,
            label_text,
            color=color.new(label_color, 80),
            style=label.style_label_right,
            textcolor=reentry_color,
            size=label_size_value
        )

// ===== Alerts =====
// Alert conditions for ATT points
alertcondition(is_att_high, "ATT High", "ATT High formed at minute {{current_minute}}")
alertcondition(is_att_low, "ATT Low", "ATT Low formed at minute {{current_minute}}")
alertcondition(is_att_reentry, "ATT Re-entry", "ATT Re-entry formed at minute {{current_minute}}")
