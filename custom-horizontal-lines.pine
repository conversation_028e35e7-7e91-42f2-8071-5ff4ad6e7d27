//@version=5
indicator("Custom Horizontal Lines [TMO Anchored]", overlay=true, max_lines_count=500, max_labels_count=500)

// ===== INPUT SETTINGS =====
// General Settings
cl_group_settings = "General Settings"
cl_timezone = input.string("America/New_York", "Time Zone", options=['America/New_York','GMT-12','GMT-11','GMT-10','GMT-9','GMT-8','GMT-7','GMT-6','GMT-5','GMT-4','GMT-3','GMT-2','GMT-1','GMT+0','GMT+1','GMT+2','GMT+3','GMT+4','GMT+5','GMT+6','GMT+7','GMT+8','GMT+9','GMT+10','GMT+11','GMT+12','GMT+13','GMT+14'], group=cl_group_settings)
cl_anchor_type = input.string("TMO", "Anchor Type", options=["TMO", "TWO", "TDO"], tooltip="TMO = True Month Open, TWO = True Week Open, TDO = True Day Open", group=cl_group_settings)

// Line Settings
cl_group_line = "Line Settings"
cl_line_width = input.int(1, "Line Width", minval=1, maxval=5, group=cl_group_line)
cl_line_style = input.string("Solid", "Line Style", options=["Solid", "Dotted", "Dashed"], group=cl_group_line)

// Label Settings
cl_group_label = "Label Settings"
cl_show_labels = input.bool(true, "Show Labels", group=cl_group_label)
cl_show_price_in_label = input.bool(false, "Show Price in Labels", group=cl_group_label)
cl_label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=cl_group_label)
cl_label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=cl_group_label)
cl_label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large"], group=cl_group_label)

// ===== CUSTOM HORIZONTAL LINES INPUTS =====
// Custom Lines (anchored to selected time period)
cl_group_lines = "Custom Lines"
cl_line1_enabled = input.bool(false, "Line 1", inline="line1", group=cl_group_lines)
cl_line1_price = input.float(0.0, "Price", inline="line1", group=cl_group_lines)
cl_line1_color = input.color(color.white, "Color", inline="line1", group=cl_group_lines)
cl_line1_label = input.string("Line 1", "Label", inline="line1", group=cl_group_lines)

cl_line2_enabled = input.bool(false, "Line 2", inline="line2", group=cl_group_lines)
cl_line2_price = input.float(0.0, "Price", inline="line2", group=cl_group_lines)
cl_line2_color = input.color(color.white, "Color", inline="line2", group=cl_group_lines)
cl_line2_label = input.string("Line 2", "Label", inline="line2", group=cl_group_lines)

cl_line3_enabled = input.bool(false, "Line 3", inline="line3", group=cl_group_lines)
cl_line3_price = input.float(0.0, "Price", inline="line3", group=cl_group_lines)
cl_line3_color = input.color(color.white, "Color", inline="line3", group=cl_group_lines)
cl_line3_label = input.string("Line 3", "Label", inline="line3", group=cl_group_lines)

cl_line4_enabled = input.bool(false, "Line 4", inline="line4", group=cl_group_lines)
cl_line4_price = input.float(0.0, "Price", inline="line4", group=cl_group_lines)
cl_line4_color = input.color(color.white, "Color", inline="line4", group=cl_group_lines)
cl_line4_label = input.string("Line 4", "Label", inline="line4", group=cl_group_lines)

cl_line5_enabled = input.bool(false, "Line 5", inline="line5", group=cl_group_lines)
cl_line5_price = input.float(0.0, "Price", inline="line5", group=cl_group_lines)
cl_line5_color = input.color(color.white, "Color", inline="line5", group=cl_group_lines)
cl_line5_label = input.string("Line 5", "Label", inline="line5", group=cl_group_lines)

cl_line6_enabled = input.bool(false, "Line 6", inline="line6", group=cl_group_lines)
cl_line6_price = input.float(0.0, "Price", inline="line6", group=cl_group_lines)
cl_line6_color = input.color(color.white, "Color", inline="line6", group=cl_group_lines)
cl_line6_label = input.string("Line 6", "Label", inline="line6", group=cl_group_lines)

cl_line7_enabled = input.bool(false, "Line 7", inline="line7", group=cl_group_lines)
cl_line7_price = input.float(0.0, "Price", inline="line7", group=cl_group_lines)
cl_line7_color = input.color(color.white, "Color", inline="line7", group=cl_group_lines)
cl_line7_label = input.string("Line 7", "Label", inline="line7", group=cl_group_lines)

cl_line8_enabled = input.bool(false, "Line 8", inline="line8", group=cl_group_lines)
cl_line8_price = input.float(0.0, "Price", inline="line8", group=cl_group_lines)
cl_line8_color = input.color(color.white, "Color", inline="line8", group=cl_group_lines)
cl_line8_label = input.string("Line 8", "Label", inline="line8", group=cl_group_lines)

cl_line9_enabled = input.bool(false, "Line 9", inline="line9", group=cl_group_lines)
cl_line9_price = input.float(0.0, "Price", inline="line9", group=cl_group_lines)
cl_line9_color = input.color(color.white, "Color", inline="line9", group=cl_group_lines)
cl_line9_label = input.string("Line 9", "Label", inline="line9", group=cl_group_lines)

cl_line10_enabled = input.bool(false, "Line 10", inline="line10", group=cl_group_lines)
cl_line10_price = input.float(0.0, "Price", inline="line10", group=cl_group_lines)
cl_line10_color = input.color(color.white, "Color", inline="line10", group=cl_group_lines)
cl_line10_label = input.string("Line 10", "Label", inline="line10", group=cl_group_lines)

cl_line11_enabled = input.bool(false, "Line 11", inline="line11", group=cl_group_lines)
cl_line11_price = input.float(0.0, "Price", inline="line11", group=cl_group_lines)
cl_line11_color = input.color(color.white, "Color", inline="line11", group=cl_group_lines)
cl_line11_label = input.string("Line 11", "Label", inline="line11", group=cl_group_lines)

cl_line12_enabled = input.bool(false, "Line 12", inline="line12", group=cl_group_lines)
cl_line12_price = input.float(0.0, "Price", inline="line12", group=cl_group_lines)
cl_line12_color = input.color(color.white, "Color", inline="line12", group=cl_group_lines)
cl_line12_label = input.string("Line 12", "Label", inline="line12", group=cl_group_lines)

cl_line13_enabled = input.bool(false, "Line 13", inline="line13", group=cl_group_lines)
cl_line13_price = input.float(0.0, "Price", inline="line13", group=cl_group_lines)
cl_line13_color = input.color(color.white, "Color", inline="line13", group=cl_group_lines)
cl_line13_label = input.string("Line 13", "Label", inline="line13", group=cl_group_lines)

cl_line14_enabled = input.bool(false, "Line 14", inline="line14", group=cl_group_lines)
cl_line14_price = input.float(0.0, "Price", inline="line14", group=cl_group_lines)
cl_line14_color = input.color(color.white, "Color", inline="line14", group=cl_group_lines)
cl_line14_label = input.string("Line 14", "Label", inline="line14", group=cl_group_lines)

cl_line15_enabled = input.bool(false, "Line 15", inline="line15", group=cl_group_lines)
cl_line15_price = input.float(0.0, "Price", inline="line15", group=cl_group_lines)
cl_line15_color = input.color(color.white, "Color", inline="line15", group=cl_group_lines)
cl_line15_label = input.string("Line 15", "Label", inline="line15", group=cl_group_lines)

cl_line16_enabled = input.bool(false, "Line 16", inline="line16", group=cl_group_lines)
cl_line16_price = input.float(0.0, "Price", inline="line16", group=cl_group_lines)
cl_line16_color = input.color(color.white, "Color", inline="line16", group=cl_group_lines)
cl_line16_label = input.string("Line 16", "Label", inline="line16", group=cl_group_lines)

cl_line17_enabled = input.bool(false, "Line 17", inline="line17", group=cl_group_lines)
cl_line17_price = input.float(0.0, "Price", inline="line17", group=cl_group_lines)
cl_line17_color = input.color(color.white, "Color", inline="line17", group=cl_group_lines)
cl_line17_label = input.string("Line 17", "Label", inline="line17", group=cl_group_lines)

cl_line18_enabled = input.bool(false, "Line 18", inline="line18", group=cl_group_lines)
cl_line18_price = input.float(0.0, "Price", inline="line18", group=cl_group_lines)
cl_line18_color = input.color(color.white, "Color", inline="line18", group=cl_group_lines)
cl_line18_label = input.string("Line 18", "Label", inline="line18", group=cl_group_lines)

cl_line19_enabled = input.bool(false, "Line 19", inline="line19", group=cl_group_lines)
cl_line19_price = input.float(0.0, "Price", inline="line19", group=cl_group_lines)
cl_line19_color = input.color(color.white, "Color", inline="line19", group=cl_group_lines)
cl_line19_label = input.string("Line 19", "Label", inline="line19", group=cl_group_lines)

cl_line20_enabled = input.bool(false, "Line 20", inline="line20", group=cl_group_lines)
cl_line20_price = input.float(0.0, "Price", inline="line20", group=cl_group_lines)
cl_line20_color = input.color(color.white, "Color", inline="line20", group=cl_group_lines)
cl_line20_label = input.string("Line 20", "Label", inline="line20", group=cl_group_lines)


// ===== HELPER FUNCTIONS =====
cl_get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

cl_get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
    result

// ===== TIME LOGIC =====
// True period start logic (ICT methodology - 00:00 UTC)
cl_is_true_month_start = (dayofmonth == 1) and (hour == 0) and (minute == 0)
cl_is_true_week_start = (dayofweek == dayofweek.monday) and (hour == 0) and (minute == 0)
cl_is_true_day_start = (hour == 0) and (minute == 0)

// Function to check if current bar is the start of selected anchor period
cl_is_anchor_start() =>
    switch cl_anchor_type
        "TMO" => cl_is_true_month_start and not cl_is_true_month_start[1]
        "TWO" => cl_is_true_week_start and not cl_is_true_week_start[1]
        "TDO" => cl_is_true_day_start and not cl_is_true_day_start[1]

// ===== STATE VARIABLES =====
var cl_line_style_value = cl_get_line_style(cl_line_style)
var cl_label_size_value = cl_get_label_size(cl_label_size)

// ===== CUSTOM LINE VARIABLES =====
// Custom line variables
var line cl_line1_current = na
var line cl_line2_current = na
var line cl_line3_current = na
var line cl_line4_current = na
var line cl_line5_current = na
var line cl_line6_current = na
var line cl_line7_current = na
var line cl_line8_current = na
var line cl_line9_current = na
var line cl_line10_current = na
var line cl_line11_current = na
var line cl_line12_current = na
var line cl_line13_current = na
var line cl_line14_current = na
var line cl_line15_current = na
var line cl_line16_current = na
var line cl_line17_current = na
var line cl_line18_current = na
var line cl_line19_current = na
var line cl_line20_current = na

// Custom line labels
var label cl_line1_label_obj = na
var label cl_line2_label_obj = na
var label cl_line3_label_obj = na
var label cl_line4_label_obj = na
var label cl_line5_label_obj = na
var label cl_line6_label_obj = na
var label cl_line7_label_obj = na
var label cl_line8_label_obj = na
var label cl_line9_label_obj = na
var label cl_line10_label_obj = na
var label cl_line11_label_obj = na
var label cl_line12_label_obj = na
var label cl_line13_label_obj = na
var label cl_line14_label_obj = na
var label cl_line15_label_obj = na
var label cl_line16_label_obj = na
var label cl_line17_label_obj = na
var label cl_line18_label_obj = na
var label cl_line19_label_obj = na
var label cl_line20_label_obj = na

// Anchor tracking
var bool cl_anchor_line_active = false

// ===== MAIN LOGIC =====
// Check if we're at the start of selected anchor period (TMO/TWO/TDO)
if cl_is_anchor_start()
    cl_anchor_line_active := true

    // Create custom lines anchored to TMO
    if cl_line1_enabled and cl_line1_price != 0.0
        cl_line1_current := line.new(bar_index, cl_line1_price, bar_index, cl_line1_price, color=cl_line1_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line1_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line1_price) : "")
            cl_line1_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line1_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line1_color, color=color.new(color.black, 100))

    if cl_line2_enabled and cl_line2_price != 0.0
        cl_line2_current := line.new(bar_index, cl_line2_price, bar_index, cl_line2_price, color=cl_line2_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line2_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line2_price) : "")
            cl_line2_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line2_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line2_color, color=color.new(color.black, 100))

    if cl_line3_enabled and cl_line3_price != 0.0
        cl_line3_current := line.new(bar_index, cl_line3_price, bar_index, cl_line3_price, color=cl_line3_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line3_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line3_price) : "")
            cl_line3_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line3_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line3_color, color=color.new(color.black, 100))

    if cl_line4_enabled and cl_line4_price != 0.0
        cl_line4_current := line.new(bar_index, cl_line4_price, bar_index, cl_line4_price, color=cl_line4_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line4_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line4_price) : "")
            cl_line4_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line4_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line4_color, color=color.new(color.black, 100))

    if cl_line5_enabled and cl_line5_price != 0.0
        cl_line5_current := line.new(bar_index, cl_line5_price, bar_index, cl_line5_price, color=cl_line5_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line5_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line5_price) : "")
            cl_line5_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line5_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line5_color, color=color.new(color.black, 100))

    if cl_line6_enabled and cl_line6_price != 0.0
        cl_line6_current := line.new(bar_index, cl_line6_price, bar_index, cl_line6_price, color=cl_line6_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line6_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line6_price) : "")
            cl_line6_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line6_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line6_color, color=color.new(color.black, 100))

    if cl_line7_enabled and cl_line7_price != 0.0
        cl_line7_current := line.new(bar_index, cl_line7_price, bar_index, cl_line7_price, color=cl_line7_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line7_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line7_price) : "")
            cl_line7_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line7_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line7_color, color=color.new(color.black, 100))

    if cl_line8_enabled and cl_line8_price != 0.0
        cl_line8_current := line.new(bar_index, cl_line8_price, bar_index, cl_line8_price, color=cl_line8_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line8_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line8_price) : "")
            cl_line8_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line8_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line8_color, color=color.new(color.black, 100))

    if cl_line9_enabled and cl_line9_price != 0.0
        cl_line9_current := line.new(bar_index, cl_line9_price, bar_index, cl_line9_price, color=cl_line9_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line9_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line9_price) : "")
            cl_line9_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line9_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line9_color, color=color.new(color.black, 100))

    if cl_line10_enabled and cl_line10_price != 0.0
        cl_line10_current := line.new(bar_index, cl_line10_price, bar_index, cl_line10_price, color=cl_line10_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line10_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line10_price) : "")
            cl_line10_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line10_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line10_color, color=color.new(color.black, 100))

    if cl_line11_enabled and cl_line11_price != 0.0
        cl_line11_current := line.new(bar_index, cl_line11_price, bar_index, cl_line11_price, color=cl_line11_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line11_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line11_price) : "")
            cl_line11_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line11_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line11_color, color=color.new(color.black, 100))

    if cl_line12_enabled and cl_line12_price != 0.0
        cl_line12_current := line.new(bar_index, cl_line12_price, bar_index, cl_line12_price, color=cl_line12_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line12_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line12_price) : "")
            cl_line12_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line12_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line12_color, color=color.new(color.black, 100))

    if cl_line13_enabled and cl_line13_price != 0.0
        cl_line13_current := line.new(bar_index, cl_line13_price, bar_index, cl_line13_price, color=cl_line13_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line13_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line13_price) : "")
            cl_line13_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line13_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line13_color, color=color.new(color.black, 100))

    if cl_line14_enabled and cl_line14_price != 0.0
        cl_line14_current := line.new(bar_index, cl_line14_price, bar_index, cl_line14_price, color=cl_line14_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line14_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line14_price) : "")
            cl_line14_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line14_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line14_color, color=color.new(color.black, 100))

    if cl_line15_enabled and cl_line15_price != 0.0
        cl_line15_current := line.new(bar_index, cl_line15_price, bar_index, cl_line15_price, color=cl_line15_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line15_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line15_price) : "")
            cl_line15_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line15_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line15_color, color=color.new(color.black, 100))

    if cl_line16_enabled and cl_line16_price != 0.0
        cl_line16_current := line.new(bar_index, cl_line16_price, bar_index, cl_line16_price, color=cl_line16_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line16_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line16_price) : "")
            cl_line16_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line16_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line16_color, color=color.new(color.black, 100))

    if cl_line17_enabled and cl_line17_price != 0.0
        cl_line17_current := line.new(bar_index, cl_line17_price, bar_index, cl_line17_price, color=cl_line17_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line17_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line17_price) : "")
            cl_line17_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line17_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line17_color, color=color.new(color.black, 100))

    if cl_line18_enabled and cl_line18_price != 0.0
        cl_line18_current := line.new(bar_index, cl_line18_price, bar_index, cl_line18_price, color=cl_line18_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line18_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line18_price) : "")
            cl_line18_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line18_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line18_color, color=color.new(color.black, 100))

    if cl_line19_enabled and cl_line19_price != 0.0
        cl_line19_current := line.new(bar_index, cl_line19_price, bar_index, cl_line19_price, color=cl_line19_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line19_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line19_price) : "")
            cl_line19_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line19_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line19_color, color=color.new(color.black, 100))

    if cl_line20_enabled and cl_line20_price != 0.0
        cl_line20_current := line.new(bar_index, cl_line20_price, bar_index, cl_line20_price, color=cl_line20_color, width=cl_line_width, style=cl_line_style_value)
        if cl_show_labels
            cl_label_text = cl_line20_label + (cl_show_price_in_label ? str.format(" ({0})", cl_line20_price) : "")
            cl_line20_label_obj := label.new(bar_index + cl_label_x_offset_bars, cl_line20_price + cl_label_y_offset, cl_label_text, style=label.style_label_left, size=cl_label_size_value, textcolor=cl_line20_color, color=color.new(color.black, 100))

// ===== LINE EXTENSION LOGIC =====
// Continuously extend lines to current bar when anchor line is active (exactly like ADR levels)
if cl_anchor_line_active
    // Update custom lines
    if cl_line1_enabled and cl_line1_price != 0.0 and not na(cl_line1_current)
        line.set_x2(cl_line1_current, bar_index)
        line.set_y2(cl_line1_current, cl_line1_price)
        if cl_show_labels and not na(cl_line1_label_obj)
            label.set_x(cl_line1_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line1_label_obj, cl_line1_price + cl_label_y_offset)

    if cl_line2_enabled and cl_line2_price != 0.0 and not na(cl_line2_current)
        line.set_x2(cl_line2_current, bar_index)
        line.set_y2(cl_line2_current, cl_line2_price)
        if cl_show_labels and not na(cl_line2_label_obj)
            label.set_x(cl_line2_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line2_label_obj, cl_line2_price + cl_label_y_offset)

    if cl_line3_enabled and cl_line3_price != 0.0 and not na(cl_line3_current)
        line.set_x2(cl_line3_current, bar_index)
        line.set_y2(cl_line3_current, cl_line3_price)
        if cl_show_labels and not na(cl_line3_label_obj)
            label.set_x(cl_line3_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line3_label_obj, cl_line3_price + cl_label_y_offset)

    if cl_line4_enabled and cl_line4_price != 0.0 and not na(cl_line4_current)
        line.set_x2(cl_line4_current, bar_index)
        line.set_y2(cl_line4_current, cl_line4_price)
        if cl_show_labels and not na(cl_line4_label_obj)
            label.set_x(cl_line4_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line4_label_obj, cl_line4_price + cl_label_y_offset)

    if cl_line5_enabled and cl_line5_price != 0.0 and not na(cl_line5_current)
        line.set_x2(cl_line5_current, bar_index)
        line.set_y2(cl_line5_current, cl_line5_price)
        if cl_show_labels and not na(cl_line5_label_obj)
            label.set_x(cl_line5_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line5_label_obj, cl_line5_price + cl_label_y_offset)

    if cl_line6_enabled and cl_line6_price != 0.0 and not na(cl_line6_current)
        line.set_x2(cl_line6_current, bar_index)
        line.set_y2(cl_line6_current, cl_line6_price)
        if cl_show_labels and not na(cl_line6_label_obj)
            label.set_x(cl_line6_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line6_label_obj, cl_line6_price + cl_label_y_offset)

    if cl_line7_enabled and cl_line7_price != 0.0 and not na(cl_line7_current)
        line.set_x2(cl_line7_current, bar_index)
        line.set_y2(cl_line7_current, cl_line7_price)
        if cl_show_labels and not na(cl_line7_label_obj)
            label.set_x(cl_line7_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line7_label_obj, cl_line7_price + cl_label_y_offset)

    if cl_line8_enabled and cl_line8_price != 0.0 and not na(cl_line8_current)
        line.set_x2(cl_line8_current, bar_index)
        line.set_y2(cl_line8_current, cl_line8_price)
        if cl_show_labels and not na(cl_line8_label_obj)
            label.set_x(cl_line8_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line8_label_obj, cl_line8_price + cl_label_y_offset)

    if cl_line9_enabled and cl_line9_price != 0.0 and not na(cl_line9_current)
        line.set_x2(cl_line9_current, bar_index)
        line.set_y2(cl_line9_current, cl_line9_price)
        if cl_show_labels and not na(cl_line9_label_obj)
            label.set_x(cl_line9_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line9_label_obj, cl_line9_price + cl_label_y_offset)

    if cl_line10_enabled and cl_line10_price != 0.0 and not na(cl_line10_current)
        line.set_x2(cl_line10_current, bar_index)
        line.set_y2(cl_line10_current, cl_line10_price)
        if cl_show_labels and not na(cl_line10_label_obj)
            label.set_x(cl_line10_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line10_label_obj, cl_line10_price + cl_label_y_offset)

    if cl_line11_enabled and cl_line11_price != 0.0 and not na(cl_line11_current)
        line.set_x2(cl_line11_current, bar_index)
        line.set_y2(cl_line11_current, cl_line11_price)
        if cl_show_labels and not na(cl_line11_label_obj)
            label.set_x(cl_line11_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line11_label_obj, cl_line11_price + cl_label_y_offset)

    if cl_line12_enabled and cl_line12_price != 0.0 and not na(cl_line12_current)
        line.set_x2(cl_line12_current, bar_index)
        line.set_y2(cl_line12_current, cl_line12_price)
        if cl_show_labels and not na(cl_line12_label_obj)
            label.set_x(cl_line12_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line12_label_obj, cl_line12_price + cl_label_y_offset)

    if cl_line13_enabled and cl_line13_price != 0.0 and not na(cl_line13_current)
        line.set_x2(cl_line13_current, bar_index)
        line.set_y2(cl_line13_current, cl_line13_price)
        if cl_show_labels and not na(cl_line13_label_obj)
            label.set_x(cl_line13_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line13_label_obj, cl_line13_price + cl_label_y_offset)

    if cl_line14_enabled and cl_line14_price != 0.0 and not na(cl_line14_current)
        line.set_x2(cl_line14_current, bar_index)
        line.set_y2(cl_line14_current, cl_line14_price)
        if cl_show_labels and not na(cl_line14_label_obj)
            label.set_x(cl_line14_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line14_label_obj, cl_line14_price + cl_label_y_offset)

    if cl_line15_enabled and cl_line15_price != 0.0 and not na(cl_line15_current)
        line.set_x2(cl_line15_current, bar_index)
        line.set_y2(cl_line15_current, cl_line15_price)
        if cl_show_labels and not na(cl_line15_label_obj)
            label.set_x(cl_line15_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line15_label_obj, cl_line15_price + cl_label_y_offset)

    if cl_line16_enabled and cl_line16_price != 0.0 and not na(cl_line16_current)
        line.set_x2(cl_line16_current, bar_index)
        line.set_y2(cl_line16_current, cl_line16_price)
        if cl_show_labels and not na(cl_line16_label_obj)
            label.set_x(cl_line16_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line16_label_obj, cl_line16_price + cl_label_y_offset)

    if cl_line17_enabled and cl_line17_price != 0.0 and not na(cl_line17_current)
        line.set_x2(cl_line17_current, bar_index)
        line.set_y2(cl_line17_current, cl_line17_price)
        if cl_show_labels and not na(cl_line17_label_obj)
            label.set_x(cl_line17_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line17_label_obj, cl_line17_price + cl_label_y_offset)

    if cl_line18_enabled and cl_line18_price != 0.0 and not na(cl_line18_current)
        line.set_x2(cl_line18_current, bar_index)
        line.set_y2(cl_line18_current, cl_line18_price)
        if cl_show_labels and not na(cl_line18_label_obj)
            label.set_x(cl_line18_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line18_label_obj, cl_line18_price + cl_label_y_offset)

    if cl_line19_enabled and cl_line19_price != 0.0 and not na(cl_line19_current)
        line.set_x2(cl_line19_current, bar_index)
        line.set_y2(cl_line19_current, cl_line19_price)
        if cl_show_labels and not na(cl_line19_label_obj)
            label.set_x(cl_line19_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line19_label_obj, cl_line19_price + cl_label_y_offset)

    if cl_line20_enabled and cl_line20_price != 0.0 and not na(cl_line20_current)
        line.set_x2(cl_line20_current, bar_index)
        line.set_y2(cl_line20_current, cl_line20_price)
        if cl_show_labels and not na(cl_line20_label_obj)
            label.set_x(cl_line20_label_obj, bar_index + cl_label_x_offset_bars)
            label.set_y(cl_line20_label_obj, cl_line20_price + cl_label_y_offset)
