//@version=5
indicator("Silver Bullet FVG Indicator", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// === Inputs ===
showSB             = input.bool(true,    "Show 10–11 AM Session",      group="Session")
showHistoricalFVGs = input.bool(false,   "Show Historical FVGs",       group="Session")
extendLine         = input.bool(true,    "Extend Vertical Separators", group="Session")
extendHLine        = input.bool(false,   "Extend Historical Lines",    group="Session & FVG")
boxShift           = input.int(1,        "Shift FVG Box Left",         minval=0, group="Session")
historyDays        = input.int(10,       "Look-back (Days)",           minval=1, maxval=500, group="Session & FVG")
labelSizeOpt       = input.string("Tiny","Session Label Size",        options=["Auto","Tiny","Small","Normal","Large","Huge"], group="Session")
labelColor         = input.color(color.white, "Session Label Color",   group="Session")
labelXOffset       = input.int(20,       "Session Label X Offset",     minval=0, group="Session")
labelYOffset       = input.float(0.0,     "Session Label Y Offset",           group="Session")
flipSource         = input.string("Wick","Flip Source",               options=["Wick","Close"], group="FVG Style")
tradeLabelSize     = input.string("Normal","Trade Label Size",       options=["Tiny","Small","Normal","Large","Huge"], group="Labels")
debugLabels        = input.bool(false,   "Show Debug Labels",                 group="Debug")
debugNumbering     = input.bool(false,   "Show Numbering Debug",              group="Debug")

// Engulfing Settings
showEngulfingDebug = input.bool(false,   "Show Engulfing Debug",              group="Engulfing")

// === Styles ===
currentFVGCol = input.color(#e8ed5e89,   "Current FVG Color",   group="Colors")
histFVGCol    = input.color(color.rgb(94,134,237,80), "Historical FVG Color", group="Colors")
midlineColor  = input.color(#787b86,     "Midline Color",      group="Colors")
invisBorder   = color.new(color.black,100)

// === Helpers ===
labelSizeMap(x) =>
    switch x
        "Auto"   => size.auto
        "Tiny"   => size.tiny
        "Small"  => size.small
        "Normal" => size.normal
        "Large"  => size.large
        "Huge"   => size.huge

timeSess(tf,s) => time(tf, s, "America/New_York")

// === Session timing ===
SB_AM   = timeSess(timeframe.period, "1000-1100")
strAM   = SB_AM and not SB_AM[1]
inAM    = timeSess("1", "1000-1100")
after10 = inAM and not strAM
n       = bar_index
minT    = syminfo.mintick

// === Pull 1‑min wick/close ===
[lf_low, lf_high, lf_close] = request.security(syminfo.tickerid, "1", [low, high, close])

// === State vars ===
var line    vLine        = na
var line[]  sessHLines   = array.new_line()
var int[]   sessHTimes   = array.new_int()
var float[] sessHPrices  = array.new_float()
var label[] sessHLabels  = array.new_label()
var line    hLine        = na
var label   hLabel       = na
var float   hPrice       = na
var int     hStart       = na

var box[]   fvgBoxes     = array.new_box()
var line[]  fvgMids      = array.new_line()
var bool[]  fvgBull      = array.new_bool()
var int[]   fvgTimes     = array.new_int()
var box     currBox      = na
var line    currMid      = na
var bool    drawn        = false
var bool    flipped      = false
var bool    swingConfirmed = false
var bool    tapped       = false
var float   fvgTop       = na
var float   fvgBot       = na
var bool    isBull       = na

var int     tapBar        = na
var bool    waitEngulfing = false
var int     candlesSinceTap = 0
var float   lastUpLow     = na  // low of last up‑close candle
var float   lastDownHigh  = na  // high of last down‑close candle

// === FVG detect on 1m ===
fvgDetect() =>
    bull = low > high[2] and close[1] > high[2]
    bear = high < low[2]  and close[1] < low[2]
    top  = bull ? low     : bear ? low[2]     : na
    bot  = bull ? high[2] : bear ? high       : na
    [bull, bear, top, bot]
[bull, bear, top, bot] = request.security(syminfo.tickerid, "1", fvgDetect())

// ── New Session @10:00 ──
if strAM and showSB
    // vertical separator
    vLine := line.new(n, close, n, close + minT, color=color.white, extend=extendLine ? extend.both : extend.none)
    // finalize prior horizontal
    if not na(hLine)
        line.set_extend(hLine, extend.none)
        line.set_x2(hLine, n)
        line.set_y2(hLine, hPrice)
        array.push(sessHLines, hLine)
        array.push(sessHTimes, hStart)
        array.push(sessHPrices, hPrice)
        array.push(sessHLabels, hLabel)
    // start new horizontal
    hStart := n
    hPrice := open
    hLine  := line.new(hStart, hPrice, n, hPrice, color=color.white, style=line.style_dotted, extend=extend.none)
    // date label
    d = dayofmonth  < 10 ? "0"+str.tostring(dayofmonth) : str.tostring(dayofmonth)
    m = month       < 10 ? "0"+str.tostring(month)      : str.tostring(month)
    y = (year % 100) < 10 ? "0"+str.tostring(year%100)   : str.tostring(year%100)
    hLabel := label.new(x = n + labelXOffset,y = hPrice + labelYOffset,text      = d + "/" + m + "/" + y,xloc= xloc.bar_index,yloc= yloc.price,style= label.style_label_right,size= labelSizeMap(labelSizeOpt),textcolor = labelColor,color     = color.new(color.white,100))
    // reset everything
    drawn          := false
    flipped        := false
    swingConfirmed := false
    tapped         := false
    waitEngulfing    := false
    candlesSinceTap  := 0
    lastUpLow        := na
    lastDownHigh     := na

// ── Update session horizontal ──
if not na(hLine)
    line.set_extend(hLine, extend.none)
    line.set_x2(hLine, n)
    line.set_y2(hLine, hPrice)
    label.set_x(hLabel, n + labelXOffset)
    label.set_y(hLabel, hPrice + labelYOffset)

// ── Stash prior FVG ──
if strAM and not na(currBox)
    array.push(fvgBoxes, currBox)
    array.push(fvgMids,  currMid)
    array.push(fvgBull,  isBull)
    array.push(fvgTimes, hStart)

// ── Draw 1st FVG post‑10:00 ──
if after10 and not drawn and (bull or bear)
    isBull := bull
    fvgTop := top
    fvgBot := bot
    currBox := box.new(n - boxShift, fvgTop, n, fvgBot,
                       border_color = invisBorder,
                       bgcolor      = currentFVGCol)
    currMid := line.new(n - boxShift,(fvgTop+fvgBot)/2,n,(fvgTop+fvgBot)/2,color = midlineColor,style = line.style_dashed)
    drawn   := true
    flipped := false
    if debugLabels
        label.new(bar_index,
                  (fvgTop+fvgBot)/2,
                  isBull ? "Bull FVG" : "Bear FVG",
                  xloc.bar_index,
                  yloc.price,
                  style     = label.style_label_center,
                  textcolor = color.white,
                  color     = isBull ? color.blue : color.orange,
                  size      = labelSizeMap("Small"))

// ── Extend & flip FVG ──
if drawn and not na(currBox)
    box.set_right(currBox, n)
    line.set_x2(currMid, n)
    flipV = (flipSource == "Wick") ? lf_low  : lf_close
    flipH = (flipSource == "Wick") ? lf_high : lf_close
    if (isBull and flipV < fvgBot) or (not isBull and flipH > fvgTop)
        box.set_bgcolor(currBox, currentFVGCol)
        box.set_border_color(currBox, invisBorder)
        flipped := true

// ── Detect the FVG “tap” ──
if drawn and not tapped and after10
    // first confirm swing‑away
    if not swingConfirmed and ((isBull and close > fvgTop) or (not isBull and close < fvgBot))
        swingConfirmed := true
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Swing Confirmed",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.black,
                      color     = color.yellow,
                      size      = labelSizeMap("Small"))
    // then the actual tap
    else if swingConfirmed and ((isBull and low <= fvgTop) or (not isBull and high >= fvgBot))
        tapBar           := bar_index
        waitEngulfing    := true
        candlesSinceTap  := 0
        // **DON'T clear old numbering - preserve historical labels**
        if debugLabels
            label.new(bar_index,
                      (fvgTop+fvgBot)/2,
                      "Tapped FVG",
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.blue,
                      color     = color.new(color.white,90),
                      size      = labelSizeMap("Small"))
        if debugNumbering
            label.new(bar_index,
                      high + 20 * minT,
                      "TAP: Starting numbering from bar " + str.tostring(bar_index),
                      xloc.bar_index,
                      yloc.price,
                      style     = label.style_label_center,
                      textcolor = color.white,
                      color     = color.purple,
                      size      = labelSizeMap("Small"))

// No candle numbering function needed anymore

// ── Function to detect engulfing patterns ──
// No longer needed - using direct logic from silver-bullet-signals-final.pine

// ── WAIT UP TO X BARS FOR TRUE ENGULFING ──
if waitEngulfing
    barsSince = bar_index - tapBar

    // update last opposite‑close extremes
    if close > open
        lastUpLow := low
    else if close < open
        lastDownHigh := high

    if barsSince <= 10
        // BUY: bullish engulf = green close above last down-candle's high
        if isBull and close > open and not na(lastDownHigh) and close >= lastDownHigh
            label.new(bar_index, low - 10*minT,
                      "Buy",
                      style=label.style_label_up,
                      textcolor=color.white,
                      color=color.green,
                      size=labelSizeMap(tradeLabelSize))
            tapped := true
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "Bullish Engulfing: Green close above last red high",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.green,
                         size      = labelSizeMap("Small"))

        // SELL: bearish engulf = red close below last up-candle's low
        else if not isBull and close < open and not na(lastUpLow) and close <= lastUpLow
            label.new(bar_index, high + 10*minT,
                      "Sell",
                      style=label.style_label_down,
                      textcolor=color.white,
                      color=color.red,
                      size=labelSizeMap(tradeLabelSize))
            tapped := true
            waitEngulfing := false
            lastUpLow := na
            lastDownHigh := na
            if showEngulfingDebug
                label.new(bar_index, (fvgTop+fvgBot)/2, "Bearish Engulfing: Red close below last green low",
                         xloc.bar_index, yloc.price,
                         style     = label.style_label_center,
                         textcolor = color.white,
                         color     = color.red,
                         size      = labelSizeMap("Small"))

    else
        // no engulf within X bars → give up
        waitEngulfing := false
        lastUpLow := na
        lastDownHigh := na
        if showEngulfingDebug
            label.new(bar_index, (fvgTop+fvgBot)/2, "No Engulfing Found - Stopped after 10 candles",
                     xloc.bar_index, yloc.price,
                     style     = label.style_label_center,
                     textcolor = color.white,
                     color     = color.gray,
                     size      = labelSizeMap("Small"))

// ── Render last X days of historical FVGs ──
if array.size(fvgBoxes) > 0 and showHistoricalFVGs
    cutoffTime = time - historyDays * 86400000
    for i = 0 to array.size(fvgBoxes) - 1
        if array.get(fvgTimes, i) >= cutoffTime
            b = array.get(fvgBoxes, i)
            m = array.get(fvgMids,  i)
            box.set_right(b, n)
            line.set_x2(m, n)

// ── Extend & label last X days of session horizontals ──
if array.size(sessHLines) > 0 and extendHLine
    cutoffSess = time - historyDays * 86400000
    for i = 0 to array.size(sessHLines) - 1
        if array.get(sessHTimes, i) >= cutoffSess
            ln    = array.get(sessHLines,  i)
            price = array.get(sessHPrices, i)
            lb    = array.get(sessHLabels, i)
            line.set_extend(ln, extend.none)
            line.set_x2(ln, n)
            line.set_y2(ln, price)
            label.set_x(lb, n + labelXOffset)
            label.set_y(lb, price + labelYOffset)

// ── Timeframe warning ──
var table tab = table.new(position.top_right, 1, 1)
if barstate.islast and timeframe.in_seconds(timeframe.period) > 900
    table.cell(tab, 0, 0, "Use timeframe ≤ 15 min", text_color=color.red)
