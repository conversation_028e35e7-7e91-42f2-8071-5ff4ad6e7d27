// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © tradeforopp

//@version=5
indicator("Volume Profile [TFO]", "Volume Profile [TFO]", true, max_bars_back=5000, max_lines_count = 500, max_polylines_count = 100, max_labels_count = 500, max_boxes_count = 500)

var vpt_g_VP = "Volume Profile"
vpt_rows = input.int(200, "Rows", tooltip = "Number of price levels/rows", group = vpt_g_VP)
vpt_tf = input.timeframe("D", "Profile Timeframe", tooltip = "Timeframe the profile represents", group = vpt_g_VP)
vpt_ltf = input.timeframe("1", "Resolution Timeframe", tooltip = "Lower timeframe price data", group = vpt_g_VP)
vpt_extend = input.int(100, "Profile Extend %", 0, 100, tooltip = "Extension of profile to next session", group = vpt_g_VP)
vpt_vp_color = input.color(color.new(color.blue, 70), "Profile Color", tooltip = "Volume profile color", group = vpt_g_VP)

var vpt_g_VPOC = "Volume Point of Control"
vpt_show_vpoc = input.bool(true, "Show VPOC", inline = "VPOC", tooltip = "Show Volume Point of Control", group = vpt_g_VPOC)
vpt_vpoc_color = input.color(color.yellow, "", inline = "VPOC", group = vpt_g_VPOC)
vpt_ext_vpoc = input.bool(true, "Extend Last N VPOCs", inline = "EXT", tooltip = "Extend last N VPOC lines", group = vpt_g_VPOC)
vpt_ext_n_vpoc = input.int(5, "", 0, 100, inline = "EXT", group = vpt_g_VPOC)
vpt_vpoc_label_above = input.timeframe("D", "Show Labels Above", tooltip = "Show labels if TF >= this", group = vpt_g_VPOC)
vpt_vpoc_label_size = input.string('Normal', "Label Size", options = ['Auto', 'Tiny', 'Small', 'Normal', 'Large', 'Huge'], group = vpt_g_VPOC)
vpt_vpoc_width = input.int(2, "Line Width", group = vpt_g_VPOC)

var vpt_g_HVN = "High Volume Nodes"
vpt_show_hvn = input.bool(true, "Show Previous HVNs", inline = "HVN", group = vpt_g_HVN)
vpt_hvn_color_bull = input.color(color.new(color.teal, 70), "", inline = "HVN", group = vpt_g_HVN)
vpt_hvn_color_bear = input.color(color.new(color.red, 70), "", inline = "HVN", group = vpt_g_HVN)
vpt_hvn_strength = input.int(10, "HVN Strength", group = vpt_g_HVN)
vpt_hvn_type = input.string("Areas", "HVN Type", options = ['Levels','Areas'], group = vpt_g_HVN)
vpt_hvn_width = input.int(1, "Line Width", group = vpt_g_HVN)

vpt_get_label_size(x) =>
    switch x
        'Auto'   => size.auto
        'Tiny'   => size.tiny
        'Small'  => size.small
        'Normal' => size.normal
        'Large'  => size.large
        'Huge'   => size.huge

var vpt_vpoc = array.new_line()
var vpt_dates = array.new_label()

var vpt_values = array.new_float()
var vpt_x_vol = array.new_int()
var vpt_y_vol = array.new_float()

var vpt_hvn_lines = array.new_line()
var vpt_hvn_boxes = array.new_box()

var vpt_hvn_Ly = array.new_float()
var vpt_hvn_By = array.new_float()

var vpt_PLA = array.new<chart.point>()
var polyline vpt_PL = na
var line vpt_temp_vpoc = na

var int vpt_lb_idx = na
var int vpt_lb_time = na

vpt_ltf := timeframe.in_seconds(vpt_ltf) <= timeframe.in_seconds() ? vpt_ltf : ""
[vpt_ltf_H, vpt_ltf_L, vpt_ltf_V] = request.security_lower_tf(syminfo.tickerid, vpt_ltf, [high, low, volume])

if not na(vpt_lb_idx)
    vpt_lb = bar_index - vpt_lb_idx > 0 ? (bar_index - vpt_lb_idx) : 1
    vpt_y_max = ta.highest(high[1], vpt_lb)
    vpt_y_min = ta.lowest(low[1], vpt_lb)

    if timeframe.change(vpt_tf) or barstate.islast
        vpt_x_vol.clear()
        vpt_y_vol.clear()
        vpt_values.clear()

        for i = 0 to vpt_rows
            y = vpt_y_min + i * (vpt_y_max - vpt_y_min) / vpt_rows
            vpt_x_vol.push(vpt_lb_time)
            vpt_y_vol.push(y)
            vpt_values.push(0)

        for i = bar_index - vpt_lb_idx to 1
            vol = vpt_ltf_V[i]
            if vol.size() > 0
                for j = 0 to vpt_values.size() - 1
                    temp = vpt_y_vol.get(j)
                    for k = 0 to vol.size() - 1
                        H = vpt_ltf_H[i]
                        L = vpt_ltf_L[i]
                        V = vpt_ltf_V[i]
                        if H.get(k) >= temp and L.get(k) <= temp
                            add = math.floor(V.get(k) / ((H.get(k) - L.get(k)) / (vpt_y_max - vpt_y_min) / vpt_rows))
                            vpt_values.set(j, vpt_values.get(j) + add)

        max_y = vpt_y_vol.get(vpt_values.indexof(vpt_values.max()))
        sf = vpt_values.max() / (time[1] - vpt_lb_time) / (vpt_extend / 100)

        for j = 0 to vpt_values.size() - 1
            set = (vpt_lb_time + math.floor(vpt_values.get(j) / sf))
            vpt_x_vol.set(j, set)

        vpt_PLA.clear()
        vpt_PLA.push(chart.point.from_time(vpt_lb_time, vpt_y_min))
        for i = 0 to vpt_x_vol.size() - 1
            vpt_PLA.push(chart.point.from_time(vpt_x_vol.get(i), vpt_y_vol.get(i)))
        vpt_PLA.push(chart.point.from_time(vpt_lb_time, vpt_y_max))

        vpt_PL.delete()
        if timeframe.change(vpt_tf)
            polyline.new(vpt_PLA, curved = false, closed = true, line_color = vpt_vp_color, fill_color = vpt_vp_color, xloc = xloc.bar_time)
            vpt_temp_vpoc.delete()
            vpt_vpoc.unshift(line.new(vpt_lb_time, max_y, time, max_y, xloc = xloc.bar_time, color = vpt_show_vpoc ? vpt_vpoc_color : na, extend = vpt_ext_vpoc ? extend.right : extend.none, width = vpt_vpoc_width))
            if vpt_ext_vpoc and timeframe.in_seconds(vpt_tf) >= timeframe.in_seconds(vpt_vpoc_label_above)
                vpt_dates.unshift(label.new(bar_index, max_y, str.format("{0,date,short}", time("", session = "0000-0000", timezone = "America/New_York")), textcolor = vpt_show_vpoc ? vpt_vpoc_color : na, color = na, size = vpt_get_label_size(vpt_vpoc_label_size)))
        else
            vpt_PL := polyline.new(vpt_PLA, curved = false, closed = true, line_color = vpt_vp_color, fill_color = vpt_vp_color, xloc = xloc.bar_time)
            if na(vpt_temp_vpoc)
                vpt_temp_vpoc := line.new(vpt_lb_time, max_y, time, max_y, xloc = xloc.bar_time, color = vpt_show_vpoc ? vpt_vpoc_color : na, extend = vpt_ext_vpoc ? extend.right : extend.none, width = vpt_vpoc_width)
            vpt_temp_vpoc.set_y1(max_y)
            vpt_temp_vpoc.set_y2(max_y)
            vpt_temp_vpoc.set_x2(time)

vpt_get_hvn() =>
    if vpt_values.size() > vpt_hvn_strength
        for i = 0 to vpt_values.size() - 1
            start = vpt_values.get(i)
            valid = true
            for j = -vpt_hvn_strength to vpt_hvn_strength
                k = i + j
                if k < 0 or k > vpt_values.size() - 1
                    continue
                else
                    if j != 0 and vpt_values.get(k) > start
                        valid := false
                        break
            if valid
                idx = vpt_values.indexof(start)
                if idx != -1
                    y1 = vpt_y_vol.get(idx)
                    y2 = vpt_y_vol.get(idx)
                    val = vpt_y_vol.get(idx)
                    if i < vpt_values.size() - 1
                        for m = i to vpt_values.size() - 2
                            if vpt_values.get(m + 1) > vpt_values.get(m)
                                y1 := vpt_y_vol.get(m)
                                break
                    if i > 0
                        for m = i to 1
                            if vpt_values.get(m - 1) > vpt_values.get(m)
                                y2 := vpt_y_vol.get(m)
                                break
                    new_color = close[1] > math.avg(y1, y2) ? vpt_hvn_color_bull : vpt_hvn_color_bear
                    if vpt_hvn_type == "Levels"
                        if vpt_hvn_Ly.indexof(val) == -1
                            vpt_hvn_Ly.unshift(val)
                            vpt_hvn_lines.unshift(line.new(time, val, time + timeframe.in_seconds(vpt_tf)*1000, val, xloc = xloc.bar_time, color = color.new(new_color, 0), style = start == vpt_values.max() ? line.style_solid : line.style_dotted, width = vpt_hvn_width))
                    else
                        if vpt_hvn_By.indexof(y1) == -1
                            vpt_hvn_By.unshift(y1)
                            vpt_hvn_boxes.unshift(box.new(time, y1, time + timeframe.in_seconds(vpt_tf)*1000, y2, xloc = xloc.bar_time, bgcolor = new_color, border_color = na))

if timeframe.change(vpt_tf)
    vpt_lb_idx := bar_index
    vpt_lb_time := time

    if vpt_show_hvn
        vpt_hvn_lines.clear()
        vpt_hvn_boxes.clear()
        vpt_hvn_Ly.clear()
        vpt_hvn_By.clear()
        vpt_get_hvn()

    if vpt_ext_vpoc and vpt_vpoc.size() > vpt_ext_n_vpoc
        line.set_extend(vpt_vpoc.pop(), extend.none)
        if timeframe.in_seconds(vpt_tf) >= timeframe.in_seconds(vpt_vpoc_label_above)
            label.delete(vpt_dates.pop())

if vpt_dates.size() > 0
    for i = 0 to vpt_dates.size() - 1
        vpt_dates.get(i).set_x(bar_index + 20)
