//@version=5
indicator("ADR Levels Advanced", overlay=true, max_lines_count=500, max_labels_count=500)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
show_only_intraday = input.bool(false, "Show Only on Intraday Timeframes", group=group_settings)
timezone = input.string("America/New_York", "Time Zone", group=group_settings)
max_periods = input.int(3, "Periods to Display", minval=1, maxval=30, tooltip="Number of days to display", group=group_settings)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, group=group_time)

// Daily ADR Settings
group_adr = "ADR Settings"
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group=group_adr)
show_full_adr = input.bool(true, "Show Full ADR", group=group_adr)
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group=group_adr)
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group=group_adr)
show_half_adr = input.bool(false, "Show 1/2 ADR", group=group_adr)

// ADR Multiplier Settings
group_adr_mult = "ADR Multipliers"
show_adr_mult_1_5 = input.bool(false, "Show 1.5x ADR", group=group_adr_mult)
show_adr_mult_2_0 = input.bool(false, "Show 2.0x ADR", group=group_adr_mult)

// Fibonacci Extension Settings
group_fib = "Fibonacci Extensions"
show_fib_1_272 = input.bool(false, "Show 127.2% Extension", group=group_fib)
show_fib_1_618 = input.bool(false, "Show 161.8% Extension", group=group_fib)

// Previous Period Levels
group_prev = "Previous Period Levels"
show_prev_high = input.bool(false, "Show Previous Day High", group=group_prev)
show_prev_low = input.bool(false, "Show Previous Day Low", group=group_prev)
show_prev_open = input.bool(false, "Show Previous Day Open", group=group_prev)
show_prev_close = input.bool(false, "Show Previous Day Close", group=group_prev)

// Midpoint Levels
group_mid = "Midpoint Levels"
show_mid_tdo_adr = input.bool(false, "Show Midpoint between TDO and ADR", group=group_mid)

// Real-time ADR Tracking
group_rt = "Real-time ADR Tracking"
show_adr_tracking = input.bool(false, "Show ADR % Filled", group=group_rt)
adr_tracking_position = input.string("Top Right", "Position", options=["Top Right", "Top Left", "Bottom Right", "Bottom Left"], group=group_rt)

// Historical ADR Analysis
group_hist = "Historical ADR Analysis"
show_hist_analysis = input.bool(false, "Show Historical ADR Analysis", group=group_hist)
hist_lookback = input.int(20, "Lookback Period (Days)", minval=5, maxval=100, group=group_hist)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Day Open", group=group_line)

// Color Settings
group_color = "Color Settings"
true_day_open_color = input.color(color.white, "True Day Open Line", group=group_color)
full_adr_color = input.color(color.white, "Full ADR", group=group_color)
one_third_adr_color = input.color(color.white, "1/3 ADR", group=group_color)
two_third_adr_color = input.color(color.white, "2/3 ADR", group=group_color)
half_adr_color = input.color(color.white, "1/2 ADR", group=group_color)
adr_mult_1_5_color = input.color(color.yellow, "1.5x ADR", group=group_color)
adr_mult_2_0_color = input.color(color.orange, "2.0x ADR", group=group_color)
fib_1_272_color = input.color(color.purple, "127.2% Fib", group=group_color)
fib_1_618_color = input.color(color.fuchsia, "161.8% Fib", group=group_color)
prev_high_color = input.color(color.green, "Previous High", group=group_color)
prev_low_color = input.color(color.red, "Previous Low", group=group_color)
prev_open_color = input.color(color.blue, "Previous Open", group=group_color)
prev_close_color = input.color(color.aqua, "Previous Close", group=group_color)
mid_level_color = input.color(color.gray, "Midpoint Levels", group=group_color)
adr_tracking_color = input.color(color.white, "ADR Tracking", group=group_color)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === TIME LOGIC ===
// Daily time logic
start_of_day = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
end_of_day = timestamp(timezone, year, month, dayofmonth, 23, 59)
is_new_day = ta.change(time("D")) or barstate.isfirst
in_current_day = time >= start_of_day and time <= end_of_day

// Function to calculate the Average Daily Range
calculate_adr(lookback_period) =>
    // Request daily high-low range data
    dh = request.security(syminfo.tickerid, "D", high, barmerge.gaps_off, barmerge.lookahead_off)
    dl = request.security(syminfo.tickerid, "D", low, barmerge.gaps_off, barmerge.lookahead_off)
    drange = dh - dl

    // Calculate the average of the daily ranges
    ta.sma(drange, lookback_period)

// Function to get previous day's high, low, open, and close
get_prev_day_data() =>
    prev_high = request.security(syminfo.tickerid, "D", high[1], barmerge.gaps_off, barmerge.lookahead_off)
    prev_low = request.security(syminfo.tickerid, "D", low[1], barmerge.gaps_off, barmerge.lookahead_off)
    prev_open = request.security(syminfo.tickerid, "D", open[1], barmerge.gaps_off, barmerge.lookahead_off)
    prev_close = request.security(syminfo.tickerid, "D", close[1], barmerge.gaps_off, barmerge.lookahead_off)
    [prev_high, prev_low, prev_open, prev_close]

// Function to calculate real-time ADR percentage filled
calculate_adr_percent_filled(tdo_price, current_high, current_low, adr_value) =>
    if na(tdo_price) or na(adr_value) or adr_value == 0
        0.0
    else
        // Calculate the current day's range
        current_range = math.max(current_high - tdo_price, tdo_price - current_low)
        // Calculate as a percentage of ADR
        math.round(current_range / adr_value * 100, 1)

// === STATE VARIABLES ===
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// True Day Open variables
var line true_day_open_line = na
var label true_day_open_label = na
var line true_day_vertical = na
var float true_day_open_price = na
var bool true_day_line_active = false

// ADR level lines
var line full_adr_up_line = na
var line full_adr_down_line = na
var line one_third_adr_up_line = na
var line one_third_adr_down_line = na
var line two_third_adr_up_line = na
var line two_third_adr_down_line = na
var line half_adr_up_line = na
var line half_adr_down_line = na

// ADR Multiplier lines
var line adr_mult_1_5_up_line = na
var line adr_mult_1_5_down_line = na
var line adr_mult_2_0_up_line = na
var line adr_mult_2_0_down_line = na

// Fibonacci Extension lines
var line fib_1_272_up_line = na
var line fib_1_272_down_line = na
var line fib_1_618_up_line = na
var line fib_1_618_down_line = na

// Previous Period lines
var line prev_high_line = na
var line prev_low_line = na
var line prev_open_line = na
var line prev_close_line = na

// Midpoint lines
var line mid_tdo_adr_up_line = na
var line mid_tdo_adr_down_line = na

// ADR level labels
var label full_adr_up_label = na
var label full_adr_down_label = na
var label one_third_adr_up_label = na
var label one_third_adr_down_label = na
var label two_third_adr_up_label = na
var label two_third_adr_down_label = na
var label half_adr_up_label = na
var label half_adr_down_label = na

// ADR Multiplier labels
var label adr_mult_1_5_up_label = na
var label adr_mult_1_5_down_label = na
var label adr_mult_2_0_up_label = na
var label adr_mult_2_0_down_label = na

// Fibonacci Extension labels
var label fib_1_272_up_label = na
var label fib_1_272_down_label = na
var label fib_1_618_up_label = na
var label fib_1_618_down_label = na

// Previous Period labels
var label prev_high_label = na
var label prev_low_label = na
var label prev_open_label = na
var label prev_close_label = na

// Midpoint labels
var label mid_tdo_adr_up_label = na
var label mid_tdo_adr_down_label = na

// Real-time ADR tracking
var label adr_tracking_label = na
var float current_day_range = na
var float adr_percent_filled = na

// ADR level values
var float daily_range_val = na
var float full_adr_up = na
var float full_adr_down = na
var float one_third_adr_up = na
var float one_third_adr_down = na
var float two_third_adr_up = na
var float two_third_adr_down = na
var float half_adr_up = na
var float half_adr_down = na

// ADR Multiplier values
var float adr_mult_1_5_up = na
var float adr_mult_1_5_down = na
var float adr_mult_2_0_up = na
var float adr_mult_2_0_down = na

// Fibonacci Extension values
var float fib_1_272_up = na
var float fib_1_272_down = na
var float fib_1_618_up = na
var float fib_1_618_down = na

// Previous Period values
var float prev_high_val = na
var float prev_low_val = na
var float prev_open_val = na
var float prev_close_val = na

// Midpoint values
var float mid_tdo_adr_up = na
var float mid_tdo_adr_down = na

// Historical ADR Analysis
var float[] historical_adr_fill = array.new_float()

// === HISTORY ARRAYS ===
// Arrays to store historical range values
var float[] daily_range_history = array.new_float()

// Arrays to store historical daily lines by type
var line[] tdo_lines = array.new_line()
var line[] full_adr_up_lines = array.new_line()
var line[] full_adr_down_lines = array.new_line()
var line[] one_third_adr_up_lines = array.new_line()
var line[] one_third_adr_down_lines = array.new_line()
var line[] two_third_adr_up_lines = array.new_line()
var line[] two_third_adr_down_lines = array.new_line()
var line[] half_adr_up_lines = array.new_line()
var line[] half_adr_down_lines = array.new_line()
var line[] daily_vertical_lines = array.new_line()

// ADR Multiplier line arrays
var line[] adr_mult_1_5_up_lines = array.new_line()
var line[] adr_mult_1_5_down_lines = array.new_line()
var line[] adr_mult_2_0_up_lines = array.new_line()
var line[] adr_mult_2_0_down_lines = array.new_line()

// Fibonacci Extension line arrays
var line[] fib_1_272_up_lines = array.new_line()
var line[] fib_1_272_down_lines = array.new_line()
var line[] fib_1_618_up_lines = array.new_line()
var line[] fib_1_618_down_lines = array.new_line()

// Previous Period line arrays
var line[] prev_high_lines = array.new_line()
var line[] prev_low_lines = array.new_line()
var line[] prev_open_lines = array.new_line()
var line[] prev_close_lines = array.new_line()

// Midpoint line arrays
var line[] mid_tdo_adr_up_lines = array.new_line()
var line[] mid_tdo_adr_down_lines = array.new_line()

// Arrays to store historical daily labels by type
var label[] tdo_labels = array.new_label()
var label[] full_adr_up_labels = array.new_label()
var label[] full_adr_down_labels = array.new_label()
var label[] one_third_adr_up_labels = array.new_label()
var label[] one_third_adr_down_labels = array.new_label()
var label[] two_third_adr_up_labels = array.new_label()
var label[] two_third_adr_down_labels = array.new_label()
var label[] half_adr_up_labels = array.new_label()
var label[] half_adr_down_labels = array.new_label()

// ADR Multiplier label arrays
var label[] adr_mult_1_5_up_labels = array.new_label()
var label[] adr_mult_1_5_down_labels = array.new_label()
var label[] adr_mult_2_0_up_labels = array.new_label()
var label[] adr_mult_2_0_down_labels = array.new_label()

// Fibonacci Extension label arrays
var label[] fib_1_272_up_labels = array.new_label()
var label[] fib_1_272_down_labels = array.new_label()
var label[] fib_1_618_up_labels = array.new_label()
var label[] fib_1_618_down_labels = array.new_label()

// Previous Period label arrays
var label[] prev_high_labels = array.new_label()
var label[] prev_low_labels = array.new_label()
var label[] prev_open_labels = array.new_label()
var label[] prev_close_labels = array.new_label()

// Midpoint label arrays
var label[] mid_tdo_adr_up_labels = array.new_label()
var label[] mid_tdo_adr_down_labels = array.new_label()

// Real-time ADR tracking arrays
var label[] adr_tracking_labels = array.new_label()

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Function to delete all labels for a new day
delete_all_day_labels() =>
    delete_all_labels(tdo_labels)
    delete_all_labels(full_adr_up_labels)
    delete_all_labels(full_adr_down_labels)
    delete_all_labels(one_third_adr_up_labels)
    delete_all_labels(one_third_adr_down_labels)
    delete_all_labels(two_third_adr_up_labels)
    delete_all_labels(two_third_adr_down_labels)
    delete_all_labels(half_adr_up_labels)
    delete_all_labels(half_adr_down_labels)
    delete_all_labels(adr_mult_1_5_up_labels)
    delete_all_labels(adr_mult_1_5_down_labels)
    delete_all_labels(adr_mult_2_0_up_labels)
    delete_all_labels(adr_mult_2_0_down_labels)
    delete_all_labels(fib_1_272_up_labels)
    delete_all_labels(fib_1_272_down_labels)
    delete_all_labels(fib_1_618_up_labels)
    delete_all_labels(fib_1_618_down_labels)
    delete_all_labels(prev_high_labels)
    delete_all_labels(prev_low_labels)
    delete_all_labels(prev_open_labels)
    delete_all_labels(prev_close_labels)
    delete_all_labels(mid_tdo_adr_up_labels)
    delete_all_labels(mid_tdo_adr_down_labels)
    delete_all_labels(adr_tracking_labels)

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// === MAIN LOGIC ===
// Calculate range values
float current_adr = calculate_adr(adr_days)

// Check if we're at the start of a new day
if is_new_day
    // Store the current ADR value in history and for today's use
    array.unshift(daily_range_history, current_adr)
    if array.size(daily_range_history) > adr_days
        array.pop(daily_range_history)

    // Update the daily range value for today
    daily_range_val := current_adr

    // Update true day open price
    true_day_open_price := barstate.isfirst ? close : open
    true_day_line_active := true

    // Get previous day's data
    [prev_high_val, prev_low_val, prev_open_val, prev_close_val] = get_prev_day_data()

    // Calculate ADR levels
    full_adr_up := true_day_open_price + daily_range_val
    full_adr_down := true_day_open_price - daily_range_val
    one_third_adr_up := true_day_open_price + (daily_range_val / 3)
    one_third_adr_down := true_day_open_price - (daily_range_val / 3)
    two_third_adr_up := true_day_open_price + (daily_range_val * 2 / 3)
    two_third_adr_down := true_day_open_price - (daily_range_val * 2 / 3)
    half_adr_up := true_day_open_price + (daily_range_val / 2)
    half_adr_down := true_day_open_price - (daily_range_val / 2)

    // Calculate ADR Multiplier levels
    adr_mult_1_5_up := true_day_open_price + (daily_range_val * 1.5)
    adr_mult_1_5_down := true_day_open_price - (daily_range_val * 1.5)
    adr_mult_2_0_up := true_day_open_price + (daily_range_val * 2.0)
    adr_mult_2_0_down := true_day_open_price - (daily_range_val * 2.0)

    // Calculate Fibonacci Extension levels
    fib_1_272_up := true_day_open_price + (daily_range_val * 1.272)
    fib_1_272_down := true_day_open_price - (daily_range_val * 1.272)
    fib_1_618_up := true_day_open_price + (daily_range_val * 1.618)
    fib_1_618_down := true_day_open_price - (daily_range_val * 1.618)

    // Calculate Midpoint levels
    mid_tdo_adr_up := (true_day_open_price + full_adr_up) / 2
    mid_tdo_adr_down := (true_day_open_price + full_adr_down) / 2

    // Initialize real-time ADR tracking
    current_day_range := 0.0
    adr_percent_filled := 0.0

    // Create true day open line
    true_day_open_line := line.new(bar_index, true_day_open_price, bar_index, true_day_open_price, color=true_day_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tdo_lines, true_day_open_line)
    manage_line_history(tdo_lines)

    // Delete old labels when a new TDO appears
    if show_labels
        delete_all_day_labels()

        // Create true day open label
        true_day_open_label := label.new(bar_index + label_x_offset_bars, true_day_open_price + label_y_offset, "TDO" + (show_price_in_label ? str.format(" ({0})", true_day_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_day_open_color, color=color.new(color.black, 100))
        array.push(tdo_labels, true_day_open_label)

    // Create vertical line at day open
    if show_vertical
        true_day_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_day_open_color, style=line.style_dashed)
        array.unshift(daily_vertical_lines, true_day_vertical)
        manage_line_history(daily_vertical_lines)

    // Create ADR level lines
    if show_full_adr
        // Full ADR up line
        full_adr_up_line := line.new(bar_index, full_adr_up, bar_index, full_adr_up, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_up_lines, full_adr_up_line)
        manage_line_history(full_adr_up_lines)

        // Full ADR up label
        if show_labels
            full_adr_up_label := label.new(bar_index + label_x_offset_bars, full_adr_up + label_y_offset, "ADR+" + (show_price_in_label ? str.format(" ({0})", full_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_up_labels, full_adr_up_label)

        // Full ADR down line
        full_adr_down_line := line.new(bar_index, full_adr_down, bar_index, full_adr_down, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_down_lines, full_adr_down_line)
        manage_line_history(full_adr_down_lines)

        // Full ADR down label
        if show_labels
            full_adr_down_label := label.new(bar_index + label_x_offset_bars, full_adr_down + label_y_offset, "ADR-" + (show_price_in_label ? str.format(" ({0})", full_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_down_labels, full_adr_down_label)

    // Create 1/3 ADR level lines
    if show_one_third_adr
        // 1/3 ADR up line
        one_third_adr_up_line := line.new(bar_index, one_third_adr_up, bar_index, one_third_adr_up, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_up_lines, one_third_adr_up_line)
        manage_line_history(one_third_adr_up_lines)

        // 1/3 ADR up label
        if show_labels
            one_third_adr_up_label := label.new(bar_index + label_x_offset_bars, one_third_adr_up + label_y_offset, "1/3 ADR+" + (show_price_in_label ? str.format(" ({0})", one_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_up_labels, one_third_adr_up_label)

        // 1/3 ADR down line
        one_third_adr_down_line := line.new(bar_index, one_third_adr_down, bar_index, one_third_adr_down, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_down_lines, one_third_adr_down_line)
        manage_line_history(one_third_adr_down_lines)

        // 1/3 ADR down label
        if show_labels
            one_third_adr_down_label := label.new(bar_index + label_x_offset_bars, one_third_adr_down + label_y_offset, "1/3 ADR-" + (show_price_in_label ? str.format(" ({0})", one_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_down_labels, one_third_adr_down_label)

    // Create 2/3 ADR level lines
    if show_two_third_adr
        // 2/3 ADR up line
        two_third_adr_up_line := line.new(bar_index, two_third_adr_up, bar_index, two_third_adr_up, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_up_lines, two_third_adr_up_line)
        manage_line_history(two_third_adr_up_lines)

        // 2/3 ADR up label
        if show_labels
            two_third_adr_up_label := label.new(bar_index + label_x_offset_bars, two_third_adr_up + label_y_offset, "2/3 ADR+" + (show_price_in_label ? str.format(" ({0})", two_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_up_labels, two_third_adr_up_label)

        // 2/3 ADR down line
        two_third_adr_down_line := line.new(bar_index, two_third_adr_down, bar_index, two_third_adr_down, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_down_lines, two_third_adr_down_line)
        manage_line_history(two_third_adr_down_lines)

        // 2/3 ADR down label
        if show_labels
            two_third_adr_down_label := label.new(bar_index + label_x_offset_bars, two_third_adr_down + label_y_offset, "2/3 ADR-" + (show_price_in_label ? str.format(" ({0})", two_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_down_labels, two_third_adr_down_label)

    // Create 1/2 ADR level lines
    if show_half_adr
        // 1/2 ADR up line
        half_adr_up_line := line.new(bar_index, half_adr_up, bar_index, half_adr_up, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_up_lines, half_adr_up_line)
        manage_line_history(half_adr_up_lines)

        // 1/2 ADR up label
        if show_labels
            half_adr_up_label := label.new(bar_index + label_x_offset_bars, half_adr_up + label_y_offset, "1/2 ADR+" + (show_price_in_label ? str.format(" ({0})", half_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_up_labels, half_adr_up_label)

        // 1/2 ADR down line
        half_adr_down_line := line.new(bar_index, half_adr_down, bar_index, half_adr_down, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_down_lines, half_adr_down_line)
        manage_line_history(half_adr_down_lines)

        // 1/2 ADR down label
        if show_labels
            half_adr_down_label := label.new(bar_index + label_x_offset_bars, half_adr_down + label_y_offset, "1/2 ADR-" + (show_price_in_label ? str.format(" ({0})", half_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_down_labels, half_adr_down_label)

    // Create 1.5x ADR level lines
    if show_adr_mult_1_5
        // 1.5x ADR up line
        adr_mult_1_5_up_line := line.new(bar_index, adr_mult_1_5_up, bar_index, adr_mult_1_5_up, color=adr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_1_5_up_lines, adr_mult_1_5_up_line)
        manage_line_history(adr_mult_1_5_up_lines)

        // 1.5x ADR up label
        if show_labels
            adr_mult_1_5_up_label := label.new(bar_index + label_x_offset_bars, adr_mult_1_5_up + label_y_offset, "1.5x ADR+" + (show_price_in_label ? str.format(" ({0})", adr_mult_1_5_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(adr_mult_1_5_up_labels, adr_mult_1_5_up_label)

        // 1.5x ADR down line
        adr_mult_1_5_down_line := line.new(bar_index, adr_mult_1_5_down, bar_index, adr_mult_1_5_down, color=adr_mult_1_5_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_1_5_down_lines, adr_mult_1_5_down_line)
        manage_line_history(adr_mult_1_5_down_lines)

        // 1.5x ADR down label
        if show_labels
            adr_mult_1_5_down_label := label.new(bar_index + label_x_offset_bars, adr_mult_1_5_down + label_y_offset, "1.5x ADR-" + (show_price_in_label ? str.format(" ({0})", adr_mult_1_5_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_1_5_color, color=color.new(color.black, 100))
            array.push(adr_mult_1_5_down_labels, adr_mult_1_5_down_label)

    // Create 2.0x ADR level lines
    if show_adr_mult_2_0
        // 2.0x ADR up line
        adr_mult_2_0_up_line := line.new(bar_index, adr_mult_2_0_up, bar_index, adr_mult_2_0_up, color=adr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_2_0_up_lines, adr_mult_2_0_up_line)
        manage_line_history(adr_mult_2_0_up_lines)

        // 2.0x ADR up label
        if show_labels
            adr_mult_2_0_up_label := label.new(bar_index + label_x_offset_bars, adr_mult_2_0_up + label_y_offset, "2.0x ADR+" + (show_price_in_label ? str.format(" ({0})", adr_mult_2_0_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(adr_mult_2_0_up_labels, adr_mult_2_0_up_label)

        // 2.0x ADR down line
        adr_mult_2_0_down_line := line.new(bar_index, adr_mult_2_0_down, bar_index, adr_mult_2_0_down, color=adr_mult_2_0_color, width=line_width, style=line_style_value)
        array.unshift(adr_mult_2_0_down_lines, adr_mult_2_0_down_line)
        manage_line_history(adr_mult_2_0_down_lines)

        // 2.0x ADR down label
        if show_labels
            adr_mult_2_0_down_label := label.new(bar_index + label_x_offset_bars, adr_mult_2_0_down + label_y_offset, "2.0x ADR-" + (show_price_in_label ? str.format(" ({0})", adr_mult_2_0_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=adr_mult_2_0_color, color=color.new(color.black, 100))
            array.push(adr_mult_2_0_down_labels, adr_mult_2_0_down_label)

    // Create 127.2% Fibonacci Extension level lines
    if show_fib_1_272
        // 127.2% Fib up line
        fib_1_272_up_line := line.new(bar_index, fib_1_272_up, bar_index, fib_1_272_up, color=fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(fib_1_272_up_lines, fib_1_272_up_line)
        manage_line_history(fib_1_272_up_lines)

        // 127.2% Fib up label
        if show_labels
            fib_1_272_up_label := label.new(bar_index + label_x_offset_bars, fib_1_272_up + label_y_offset, "127.2% Fib+" + (show_price_in_label ? str.format(" ({0})", fib_1_272_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=fib_1_272_color, color=color.new(color.black, 100))
            array.push(fib_1_272_up_labels, fib_1_272_up_label)

        // 127.2% Fib down line
        fib_1_272_down_line := line.new(bar_index, fib_1_272_down, bar_index, fib_1_272_down, color=fib_1_272_color, width=line_width, style=line_style_value)
        array.unshift(fib_1_272_down_lines, fib_1_272_down_line)
        manage_line_history(fib_1_272_down_lines)

        // 127.2% Fib down label
        if show_labels
            fib_1_272_down_label := label.new(bar_index + label_x_offset_bars, fib_1_272_down + label_y_offset, "127.2% Fib-" + (show_price_in_label ? str.format(" ({0})", fib_1_272_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=fib_1_272_color, color=color.new(color.black, 100))
            array.push(fib_1_272_down_labels, fib_1_272_down_label)

    // Create 161.8% Fibonacci Extension level lines
    if show_fib_1_618
        // 161.8% Fib up line
        fib_1_618_up_line := line.new(bar_index, fib_1_618_up, bar_index, fib_1_618_up, color=fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(fib_1_618_up_lines, fib_1_618_up_line)
        manage_line_history(fib_1_618_up_lines)

        // 161.8% Fib up label
        if show_labels
            fib_1_618_up_label := label.new(bar_index + label_x_offset_bars, fib_1_618_up + label_y_offset, "161.8% Fib+" + (show_price_in_label ? str.format(" ({0})", fib_1_618_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=fib_1_618_color, color=color.new(color.black, 100))
            array.push(fib_1_618_up_labels, fib_1_618_up_label)

        // 161.8% Fib down line
        fib_1_618_down_line := line.new(bar_index, fib_1_618_down, bar_index, fib_1_618_down, color=fib_1_618_color, width=line_width, style=line_style_value)
        array.unshift(fib_1_618_down_lines, fib_1_618_down_line)
        manage_line_history(fib_1_618_down_lines)

        // 161.8% Fib down label
        if show_labels
            fib_1_618_down_label := label.new(bar_index + label_x_offset_bars, fib_1_618_down + label_y_offset, "161.8% Fib-" + (show_price_in_label ? str.format(" ({0})", fib_1_618_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=fib_1_618_color, color=color.new(color.black, 100))
            array.push(fib_1_618_down_labels, fib_1_618_down_label)

    // Create Previous Period level lines
    if show_prev_high
        // Previous High line
        prev_high_line := line.new(bar_index, prev_high_val, bar_index, prev_high_val, color=prev_high_color, width=line_width, style=line_style_value)
        array.unshift(prev_high_lines, prev_high_line)
        manage_line_history(prev_high_lines)

        // Previous High label
        if show_labels
            prev_high_label := label.new(bar_index + label_x_offset_bars, prev_high_val + label_y_offset, "PDH" + (show_price_in_label ? str.format(" ({0})", prev_high_val) : ""), style=label.style_label_left, size=label_size_value, textcolor=prev_high_color, color=color.new(color.black, 100))
            array.push(prev_high_labels, prev_high_label)

    if show_prev_low
        // Previous Low line
        prev_low_line := line.new(bar_index, prev_low_val, bar_index, prev_low_val, color=prev_low_color, width=line_width, style=line_style_value)
        array.unshift(prev_low_lines, prev_low_line)
        manage_line_history(prev_low_lines)

        // Previous Low label
        if show_labels
            prev_low_label := label.new(bar_index + label_x_offset_bars, prev_low_val + label_y_offset, "PDL" + (show_price_in_label ? str.format(" ({0})", prev_low_val) : ""), style=label.style_label_left, size=label_size_value, textcolor=prev_low_color, color=color.new(color.black, 100))
            array.push(prev_low_labels, prev_low_label)

    if show_prev_open
        // Previous Open line
        prev_open_line := line.new(bar_index, prev_open_val, bar_index, prev_open_val, color=prev_open_color, width=line_width, style=line_style_value)
        array.unshift(prev_open_lines, prev_open_line)
        manage_line_history(prev_open_lines)

        // Previous Open label
        if show_labels
            prev_open_label := label.new(bar_index + label_x_offset_bars, prev_open_val + label_y_offset, "PDO" + (show_price_in_label ? str.format(" ({0})", prev_open_val) : ""), style=label.style_label_left, size=label_size_value, textcolor=prev_open_color, color=color.new(color.black, 100))
            array.push(prev_open_labels, prev_open_label)

    if show_prev_close
        // Previous Close line
        prev_close_line := line.new(bar_index, prev_close_val, bar_index, prev_close_val, color=prev_close_color, width=line_width, style=line_style_value)
        array.unshift(prev_close_lines, prev_close_line)
        manage_line_history(prev_close_lines)

        // Previous Close label
        if show_labels
            prev_close_label := label.new(bar_index + label_x_offset_bars, prev_close_val + label_y_offset, "PDC" + (show_price_in_label ? str.format(" ({0})", prev_close_val) : ""), style=label.style_label_left, size=label_size_value, textcolor=prev_close_color, color=color.new(color.black, 100))
            array.push(prev_close_labels, prev_close_label)

    // Create Midpoint level lines
    if show_mid_tdo_adr
        // Midpoint TDO-ADR+ line
        mid_tdo_adr_up_line := line.new(bar_index, mid_tdo_adr_up, bar_index, mid_tdo_adr_up, color=mid_level_color, width=line_width, style=line_style_value)
        array.unshift(mid_tdo_adr_up_lines, mid_tdo_adr_up_line)
        manage_line_history(mid_tdo_adr_up_lines)

        // Midpoint TDO-ADR+ label
        if show_labels
            mid_tdo_adr_up_label := label.new(bar_index + label_x_offset_bars, mid_tdo_adr_up + label_y_offset, "Mid+" + (show_price_in_label ? str.format(" ({0})", mid_tdo_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=mid_level_color, color=color.new(color.black, 100))
            array.push(mid_tdo_adr_up_labels, mid_tdo_adr_up_label)

        // Midpoint TDO-ADR- line
        mid_tdo_adr_down_line := line.new(bar_index, mid_tdo_adr_down, bar_index, mid_tdo_adr_down, color=mid_level_color, width=line_width, style=line_style_value)
        array.unshift(mid_tdo_adr_down_lines, mid_tdo_adr_down_line)
        manage_line_history(mid_tdo_adr_down_lines)

        // Midpoint TDO-ADR- label
        if show_labels
            mid_tdo_adr_down_label := label.new(bar_index + label_x_offset_bars, mid_tdo_adr_down + label_y_offset, "Mid-" + (show_price_in_label ? str.format(" ({0})", mid_tdo_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=mid_level_color, color=color.new(color.black, 100))
            array.push(mid_tdo_adr_down_labels, mid_tdo_adr_down_label)

    // Create Real-time ADR Tracking
    if show_adr_tracking
        // Calculate current day's range and ADR percentage filled
        current_day_range := math.max(high - true_day_open_price, true_day_open_price - low)
        adr_percent_filled := calculate_adr_percent_filled(true_day_open_price, high, low, daily_range_val)

        // Determine label position
        var int x_pos = 0
        var float y_pos = 0.0

        if adr_tracking_position == "Top Right"
            x_pos := bar_index - 5
            y_pos := high + (high - low) * 0.1
        else if adr_tracking_position == "Top Left"
            x_pos := bar_index - 50
            y_pos := high + (high - low) * 0.1
        else if adr_tracking_position == "Bottom Right"
            x_pos := bar_index - 5
            y_pos := low - (high - low) * 0.1
        else // Bottom Left
            x_pos := bar_index - 50
            y_pos := low - (high - low) * 0.1

        // Create ADR tracking label
        adr_tracking_label := label.new(x_pos,y_pos,"ADR: " + str.tostring(adr_percent_filled) + "% filled",style=label.style_label_center,size=label_size_value,textcolor=adr_tracking_color,color=color.new(color.black, 80))
        array.push(adr_tracking_labels, adr_tracking_label)

        // Store historical ADR fill data for analysis
        if show_hist_analysis and is_new_day and array.size(historical_adr_fill) < hist_lookback
            array.push(historical_adr_fill, adr_percent_filled)

// Update lines during the current day
if in_current_day and true_day_line_active
    // Update true day open line
    line.set_x2(true_day_open_line, bar_index)
    line.set_y2(true_day_open_line, true_day_open_price)

    // Update true day open label
    if show_labels
        label.set_x(true_day_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_day_open_label, true_day_open_price + label_y_offset)

    // Update ADR level lines
    if show_full_adr
        line.set_x2(full_adr_up_line, bar_index)
        line.set_y2(full_adr_up_line, full_adr_up)
        line.set_x2(full_adr_down_line, bar_index)
        line.set_y2(full_adr_down_line, full_adr_down)

        if show_labels
            label.set_x(full_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_up_label, full_adr_up + label_y_offset)
            label.set_x(full_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_down_label, full_adr_down + label_y_offset)

    if show_one_third_adr
        line.set_x2(one_third_adr_up_line, bar_index)
        line.set_y2(one_third_adr_up_line, one_third_adr_up)
        line.set_x2(one_third_adr_down_line, bar_index)
        line.set_y2(one_third_adr_down_line, one_third_adr_down)

        if show_labels
            label.set_x(one_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_up_label, one_third_adr_up + label_y_offset)
            label.set_x(one_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_down_label, one_third_adr_down + label_y_offset)

    if show_two_third_adr
        line.set_x2(two_third_adr_up_line, bar_index)
        line.set_y2(two_third_adr_up_line, two_third_adr_up)
        line.set_x2(two_third_adr_down_line, bar_index)
        line.set_y2(two_third_adr_down_line, two_third_adr_down)

        if show_labels
            label.set_x(two_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_up_label, two_third_adr_up + label_y_offset)
            label.set_x(two_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_down_label, two_third_adr_down + label_y_offset)

    if show_half_adr
        line.set_x2(half_adr_up_line, bar_index)
        line.set_y2(half_adr_up_line, half_adr_up)
        line.set_x2(half_adr_down_line, bar_index)
        line.set_y2(half_adr_down_line, half_adr_down)

        if show_labels
            label.set_x(half_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_up_label, half_adr_up + label_y_offset)
            label.set_x(half_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_down_label, half_adr_down + label_y_offset)

    // Update ADR Multiplier lines
    if show_adr_mult_1_5
        line.set_x2(adr_mult_1_5_up_line, bar_index)
        line.set_y2(adr_mult_1_5_up_line, adr_mult_1_5_up)
        line.set_x2(adr_mult_1_5_down_line, bar_index)
        line.set_y2(adr_mult_1_5_down_line, adr_mult_1_5_down)

        if show_labels
            label.set_x(adr_mult_1_5_up_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_1_5_up_label, adr_mult_1_5_up + label_y_offset)
            label.set_x(adr_mult_1_5_down_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_1_5_down_label, adr_mult_1_5_down + label_y_offset)

    if show_adr_mult_2_0
        line.set_x2(adr_mult_2_0_up_line, bar_index)
        line.set_y2(adr_mult_2_0_up_line, adr_mult_2_0_up)
        line.set_x2(adr_mult_2_0_down_line, bar_index)
        line.set_y2(adr_mult_2_0_down_line, adr_mult_2_0_down)

        if show_labels
            label.set_x(adr_mult_2_0_up_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_2_0_up_label, adr_mult_2_0_up + label_y_offset)
            label.set_x(adr_mult_2_0_down_label, bar_index + label_x_offset_bars)
            label.set_y(adr_mult_2_0_down_label, adr_mult_2_0_down + label_y_offset)

    // Update Fibonacci Extension lines
    if show_fib_1_272
        line.set_x2(fib_1_272_up_line, bar_index)
        line.set_y2(fib_1_272_up_line, fib_1_272_up)
        line.set_x2(fib_1_272_down_line, bar_index)
        line.set_y2(fib_1_272_down_line, fib_1_272_down)

        if show_labels
            label.set_x(fib_1_272_up_label, bar_index + label_x_offset_bars)
            label.set_y(fib_1_272_up_label, fib_1_272_up + label_y_offset)
            label.set_x(fib_1_272_down_label, bar_index + label_x_offset_bars)
            label.set_y(fib_1_272_down_label, fib_1_272_down + label_y_offset)

    if show_fib_1_618
        line.set_x2(fib_1_618_up_line, bar_index)
        line.set_y2(fib_1_618_up_line, fib_1_618_up)
        line.set_x2(fib_1_618_down_line, bar_index)
        line.set_y2(fib_1_618_down_line, fib_1_618_down)

        if show_labels
            label.set_x(fib_1_618_up_label, bar_index + label_x_offset_bars)
            label.set_y(fib_1_618_up_label, fib_1_618_up + label_y_offset)
            label.set_x(fib_1_618_down_label, bar_index + label_x_offset_bars)
            label.set_y(fib_1_618_down_label, fib_1_618_down + label_y_offset)

    // Update Previous Period lines
    if show_prev_high
        line.set_x2(prev_high_line, bar_index)
        line.set_y2(prev_high_line, prev_high_val)

        if show_labels
            label.set_x(prev_high_label, bar_index + label_x_offset_bars)
            label.set_y(prev_high_label, prev_high_val + label_y_offset)

    if show_prev_low
        line.set_x2(prev_low_line, bar_index)
        line.set_y2(prev_low_line, prev_low_val)

        if show_labels
            label.set_x(prev_low_label, bar_index + label_x_offset_bars)
            label.set_y(prev_low_label, prev_low_val + label_y_offset)

    if show_prev_open
        line.set_x2(prev_open_line, bar_index)
        line.set_y2(prev_open_line, prev_open_val)

        if show_labels
            label.set_x(prev_open_label, bar_index + label_x_offset_bars)
            label.set_y(prev_open_label, prev_open_val + label_y_offset)

    if show_prev_close
        line.set_x2(prev_close_line, bar_index)
        line.set_y2(prev_close_line, prev_close_val)

        if show_labels
            label.set_x(prev_close_label, bar_index + label_x_offset_bars)
            label.set_y(prev_close_label, prev_close_val + label_y_offset)

    // Update Midpoint lines
    if show_mid_tdo_adr
        line.set_x2(mid_tdo_adr_up_line, bar_index)
        line.set_y2(mid_tdo_adr_up_line, mid_tdo_adr_up)
        line.set_x2(mid_tdo_adr_down_line, bar_index)
        line.set_y2(mid_tdo_adr_down_line, mid_tdo_adr_down)

        if show_labels
            label.set_x(mid_tdo_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(mid_tdo_adr_up_label, mid_tdo_adr_up + label_y_offset)
            label.set_x(mid_tdo_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(mid_tdo_adr_down_label, mid_tdo_adr_down + label_y_offset)

    // Update Real-time ADR Tracking
    if show_adr_tracking
        // Calculate current day's range and ADR percentage filled
        current_day_range := math.max(high - true_day_open_price, true_day_open_price - low)
        adr_percent_filled := calculate_adr_percent_filled(true_day_open_price, high, low, daily_range_val)

        // Update ADR tracking label
        if not na(adr_tracking_label)
            label.set_text(adr_tracking_label, "ADR: " + str.tostring(adr_percent_filled) + "% filled")

            // Determine label position
            var int x_pos = 0
            var float y_pos = 0.0

            if adr_tracking_position == "Top Right"
                x_pos := bar_index - 5
                y_pos := high + (high - low) * 0.1
            else if adr_tracking_position == "Top Left"
                x_pos := bar_index - 50
                y_pos := high + (high - low) * 0.1
            else if adr_tracking_position == "Bottom Right"
                x_pos := bar_index - 5
                y_pos := low - (high - low) * 0.1
            else // Bottom Left
                x_pos := bar_index - 50
                y_pos := low - (high - low) * 0.1

            label.set_x(adr_tracking_label, x_pos)
            label.set_y(adr_tracking_label, y_pos)

// Add alert conditions
// Standard ADR levels
alertcondition(close >= full_adr_up, "Price reached Full ADR+", "Price has reached the Full ADR+ level")
alertcondition(close <= full_adr_down, "Price reached Full ADR-", "Price has reached the Full ADR- level")
alertcondition(close >= one_third_adr_up, "Price reached 1/3 ADR+", "Price has reached the 1/3 ADR+ level")
alertcondition(close <= one_third_adr_down, "Price reached 1/3 ADR-", "Price has reached the 1/3 ADR- level")
alertcondition(close >= two_third_adr_up, "Price reached 2/3 ADR+", "Price has reached the 2/3 ADR+ level")
alertcondition(close <= two_third_adr_down, "Price reached 2/3 ADR-", "Price has reached the 2/3 ADR- level")
alertcondition(close >= half_adr_up, "Price reached 1/2 ADR+", "Price has reached the 1/2 ADR+ level")
alertcondition(close <= half_adr_down, "Price reached 1/2 ADR-", "Price has reached the 1/2 ADR- level")

// ADR Multiplier levels
alertcondition(close >= adr_mult_1_5_up, "Price reached 1.5x ADR+", "Price has reached the 1.5x ADR+ level")
alertcondition(close <= adr_mult_1_5_down, "Price reached 1.5x ADR-", "Price has reached the 1.5x ADR- level")
alertcondition(close >= adr_mult_2_0_up, "Price reached 2.0x ADR+", "Price has reached the 2.0x ADR+ level")
alertcondition(close <= adr_mult_2_0_down, "Price reached 2.0x ADR-", "Price has reached the 2.0x ADR- level")

// Fibonacci Extension levels
alertcondition(close >= fib_1_272_up, "Price reached 127.2% Fib+", "Price has reached the 127.2% Fibonacci Extension+ level")
alertcondition(close <= fib_1_272_down, "Price reached 127.2% Fib-", "Price has reached the 127.2% Fibonacci Extension- level")
alertcondition(close >= fib_1_618_up, "Price reached 161.8% Fib+", "Price has reached the 161.8% Fibonacci Extension+ level")
alertcondition(close <= fib_1_618_down, "Price reached 161.8% Fib-", "Price has reached the 161.8% Fibonacci Extension- level")

// Previous Period levels
alertcondition(close >= prev_high_val, "Price reached PDH", "Price has reached the Previous Day High level")
alertcondition(close <= prev_low_val, "Price reached PDL", "Price has reached the Previous Day Low level")
alertcondition(close >= prev_open_val, "Price reached PDO", "Price has reached the Previous Day Open level")
alertcondition(close >= prev_close_val, "Price reached PDC", "Price has reached the Previous Day Close level")

// Midpoint levels
alertcondition(close >= mid_tdo_adr_up, "Price reached Mid+", "Price has reached the Midpoint+ level")
alertcondition(close <= mid_tdo_adr_down, "Price reached Mid-", "Price has reached the Midpoint- level")

// ADR Tracking
alertcondition(adr_percent_filled >= 100, "ADR 100% Filled", "Today's ADR has been 100% filled")
alertcondition(adr_percent_filled >= 150, "ADR 150% Filled", "Today's ADR has been 150% filled")
alertcondition(adr_percent_filled >= 200, "ADR 200% Filled", "Today's ADR has been 200% filled")

// Historical ADR Analysis Display
if show_hist_analysis and array.size(historical_adr_fill) > 0
    var table hist_table = table.new(position.top_right, 2, array.size(historical_adr_fill) + 2, bgcolor=color.new(color.black, 80))

    // Clear the table
    table.clear(hist_table, 0, 0)

    // Add header
    table.cell(hist_table, 0, 0, "Historical ADR Analysis", text_color=color.white, bgcolor=color.new(color.blue, 70))
    table.cell(hist_table, 1, 0, "Last " + str.tostring(array.size(historical_adr_fill)) + " days", text_color=color.white, bgcolor=color.new(color.blue, 70))

    // Calculate statistics
    float avg_fill = 0.0
    float max_fill = 0.0
    float min_fill = 1000.0

    for i = 0 to array.size(historical_adr_fill) - 1
        fill_val = array.get(historical_adr_fill, i)
        avg_fill := avg_fill + fill_val
        max_fill := math.max(max_fill, fill_val)
        min_fill := math.min(min_fill, fill_val)

        // Add day data
        table.cell(hist_table, 0, i + 1, "Day " + str.tostring(i + 1), text_color=color.white)
        table.cell(hist_table, 1, i + 1, str.tostring(fill_val) + "%", text_color=color.white)

    // Calculate average
    if array.size(historical_adr_fill) > 0
        avg_fill := avg_fill / array.size(historical_adr_fill)

    // Add statistics row
    stats_row = array.size(historical_adr_fill) + 1
    table.cell(hist_table, 0, stats_row, "Stats (Avg/Min/Max)", text_color=color.white, bgcolor=color.new(color.blue, 70))
    table.cell(hist_table, 1, stats_row, str.tostring(math.round(avg_fill, 1)) + "% / " +
                                         str.tostring(math.round(min_fill, 1)) + "% / " +
                                         str.tostring(math.round(max_fill, 1)) + "%",
                                         text_color=color.white, bgcolor=color.new(color.blue, 70))
