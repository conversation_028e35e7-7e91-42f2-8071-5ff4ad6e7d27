//@version=5
indicator("ADR Levels", overlay=true, max_lines_count=500, max_labels_count=500)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
show_only_intraday = input.bool(false, "Show Only on Intraday Timeframes", group=group_settings)
timezone = input.string("America/New_York", "Time Zone", group=group_settings)
max_periods = input.int(3, "Periods to Display", minval=1, maxval=30, tooltip="Number of days to display", group=group_settings)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, group=group_time)

// Daily ADR Settings
group_adr = "ADR Settings"
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group=group_adr)
show_full_adr = input.bool(true, "Show Full ADR", group=group_adr)
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group=group_adr)
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group=group_adr)
show_half_adr = input.bool(false, "Show 1/2 ADR", group=group_adr)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Day Open", group=group_line)

// Color Settings
group_color = "Color Settings"
true_day_open_color = input.color(color.white, "True Day Open Line", group=group_color)
full_adr_color = input.color(color.white, "Full ADR", group=group_color)
one_third_adr_color = input.color(color.white, "1/3 ADR", group=group_color)
two_third_adr_color = input.color(color.white, "2/3 ADR", group=group_color)
half_adr_color = input.color(color.white, "1/2 ADR", group=group_color)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === TIME LOGIC ===
// Daily time logic
start_of_day = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
end_of_day = timestamp(timezone, year, month, dayofmonth, 23, 59)
is_new_day = ta.change(time("D")) or barstate.isfirst
in_current_day = time >= start_of_day and time <= end_of_day

// Function to calculate the Average Daily Range
calculate_adr(lookback_period) =>
    // Request daily high-low range data
    dh = request.security(syminfo.tickerid, "D", high, barmerge.gaps_off, barmerge.lookahead_off)
    dl = request.security(syminfo.tickerid, "D", low, barmerge.gaps_off, barmerge.lookahead_off)
    drange = dh - dl

    // Calculate the average of the daily ranges
    ta.sma(drange, lookback_period)

// === STATE VARIABLES ===
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// True Day Open variables
var line true_day_open_line = na
var label true_day_open_label = na
var line true_day_vertical = na
var float true_day_open_price = na
var bool true_day_line_active = false

// ADR level lines
var line full_adr_up_line = na
var line full_adr_down_line = na
var line one_third_adr_up_line = na
var line one_third_adr_down_line = na
var line two_third_adr_up_line = na
var line two_third_adr_down_line = na
var line half_adr_up_line = na
var line half_adr_down_line = na

// ADR level labels
var label full_adr_up_label = na
var label full_adr_down_label = na
var label one_third_adr_up_label = na
var label one_third_adr_down_label = na
var label two_third_adr_up_label = na
var label two_third_adr_down_label = na
var label half_adr_up_label = na
var label half_adr_down_label = na

// ADR level values
var float daily_range_val = na
var float full_adr_up = na
var float full_adr_down = na
var float one_third_adr_up = na
var float one_third_adr_down = na
var float two_third_adr_up = na
var float two_third_adr_down = na
var float half_adr_up = na
var float half_adr_down = na

// === HISTORY ARRAYS ===
// Arrays to store historical range values
var float[] daily_range_history = array.new_float()

// Arrays to store historical daily lines by type
var line[] tdo_lines = array.new_line()
var line[] full_adr_up_lines = array.new_line()
var line[] full_adr_down_lines = array.new_line()
var line[] one_third_adr_up_lines = array.new_line()
var line[] one_third_adr_down_lines = array.new_line()
var line[] two_third_adr_up_lines = array.new_line()
var line[] two_third_adr_down_lines = array.new_line()
var line[] half_adr_up_lines = array.new_line()
var line[] half_adr_down_lines = array.new_line()
var line[] daily_vertical_lines = array.new_line()

// Arrays to store historical daily labels by type
var label[] tdo_labels = array.new_label()
var label[] full_adr_up_labels = array.new_label()
var label[] full_adr_down_labels = array.new_label()
var label[] one_third_adr_up_labels = array.new_label()
var label[] one_third_adr_down_labels = array.new_label()
var label[] two_third_adr_up_labels = array.new_label()
var label[] two_third_adr_down_labels = array.new_label()
var label[] half_adr_up_labels = array.new_label()
var label[] half_adr_down_labels = array.new_label()

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// === MAIN LOGIC ===
// Calculate range values
float current_adr = calculate_adr(adr_days)

// Check if we're at the start of a new day
if is_new_day
    // Store the current ADR value in history and for today's use
    array.unshift(daily_range_history, current_adr)
    if array.size(daily_range_history) > adr_days
        array.pop(daily_range_history)

    // Update the daily range value for today
    daily_range_val := current_adr

    // Update true day open price
    true_day_open_price := barstate.isfirst ? close : open
    true_day_line_active := true

    // Calculate ADR levels
    full_adr_up := true_day_open_price + daily_range_val
    full_adr_down := true_day_open_price - daily_range_val
    one_third_adr_up := true_day_open_price + (daily_range_val / 3)
    one_third_adr_down := true_day_open_price - (daily_range_val / 3)
    two_third_adr_up := true_day_open_price + (daily_range_val * 2 / 3)
    two_third_adr_down := true_day_open_price - (daily_range_val * 2 / 3)
    half_adr_up := true_day_open_price + (daily_range_val / 2)
    half_adr_down := true_day_open_price - (daily_range_val / 2)

    // Create true day open line
    true_day_open_line := line.new(bar_index, true_day_open_price, bar_index, true_day_open_price, color=true_day_open_color, width=line_width, style=get_line_style(line_style))
    array.unshift(tdo_lines, true_day_open_line)
    manage_line_history(tdo_lines)

    // Delete old labels when a new TDO appears
    if show_labels
        delete_all_labels(tdo_labels)
        delete_all_labels(full_adr_up_labels)
        delete_all_labels(full_adr_down_labels)
        delete_all_labels(one_third_adr_up_labels)
        delete_all_labels(one_third_adr_down_labels)
        delete_all_labels(two_third_adr_up_labels)
        delete_all_labels(two_third_adr_down_labels)
        delete_all_labels(half_adr_up_labels)
        delete_all_labels(half_adr_down_labels)

        // Create true day open label
        true_day_open_label := label.new(bar_index + label_x_offset_bars, true_day_open_price + label_y_offset, "TDO" + (show_price_in_label ? str.format(" ({0})", true_day_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_day_open_color, color=color.new(color.black, 100))
        array.push(tdo_labels, true_day_open_label)

    // Create vertical line at day open
    if show_vertical
        true_day_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_day_open_color, style=line.style_dashed)
        array.unshift(daily_vertical_lines, true_day_vertical)
        manage_line_history(daily_vertical_lines)

    // Create ADR level lines
    if show_full_adr
        // Full ADR up line
        full_adr_up_line := line.new(bar_index, full_adr_up, bar_index, full_adr_up, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_up_lines, full_adr_up_line)
        manage_line_history(full_adr_up_lines)

        // Full ADR up label
        if show_labels
            full_adr_up_label := label.new(bar_index + label_x_offset_bars, full_adr_up + label_y_offset, "ADR+" + (show_price_in_label ? str.format(" ({0})", full_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_up_labels, full_adr_up_label)

        // Full ADR down line
        full_adr_down_line := line.new(bar_index, full_adr_down, bar_index, full_adr_down, color=full_adr_color, width=line_width, style=line_style_value)
        array.unshift(full_adr_down_lines, full_adr_down_line)
        manage_line_history(full_adr_down_lines)

        // Full ADR down label
        if show_labels
            full_adr_down_label := label.new(bar_index + label_x_offset_bars, full_adr_down + label_y_offset, "ADR-" + (show_price_in_label ? str.format(" ({0})", full_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
            array.push(full_adr_down_labels, full_adr_down_label)

    // Create 1/3 ADR level lines
    if show_one_third_adr
        // 1/3 ADR up line
        one_third_adr_up_line := line.new(bar_index, one_third_adr_up, bar_index, one_third_adr_up, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_up_lines, one_third_adr_up_line)
        manage_line_history(one_third_adr_up_lines)

        // 1/3 ADR up label
        if show_labels
            one_third_adr_up_label := label.new(bar_index + label_x_offset_bars, one_third_adr_up + label_y_offset, "1/3 ADR+" + (show_price_in_label ? str.format(" ({0})", one_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_up_labels, one_third_adr_up_label)

        // 1/3 ADR down line
        one_third_adr_down_line := line.new(bar_index, one_third_adr_down, bar_index, one_third_adr_down, color=one_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_adr_down_lines, one_third_adr_down_line)
        manage_line_history(one_third_adr_down_lines)

        // 1/3 ADR down label
        if show_labels
            one_third_adr_down_label := label.new(bar_index + label_x_offset_bars, one_third_adr_down + label_y_offset, "1/3 ADR-" + (show_price_in_label ? str.format(" ({0})", one_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
            array.push(one_third_adr_down_labels, one_third_adr_down_label)

    // Create 2/3 ADR level lines
    if show_two_third_adr
        // 2/3 ADR up line
        two_third_adr_up_line := line.new(bar_index, two_third_adr_up, bar_index, two_third_adr_up, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_up_lines, two_third_adr_up_line)
        manage_line_history(two_third_adr_up_lines)

        // 2/3 ADR up label
        if show_labels
            two_third_adr_up_label := label.new(bar_index + label_x_offset_bars, two_third_adr_up + label_y_offset, "2/3 ADR+" + (show_price_in_label ? str.format(" ({0})", two_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_up_labels, two_third_adr_up_label)

        // 2/3 ADR down line
        two_third_adr_down_line := line.new(bar_index, two_third_adr_down, bar_index, two_third_adr_down, color=two_third_adr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_adr_down_lines, two_third_adr_down_line)
        manage_line_history(two_third_adr_down_lines)

        // 2/3 ADR down label
        if show_labels
            two_third_adr_down_label := label.new(bar_index + label_x_offset_bars, two_third_adr_down + label_y_offset, "2/3 ADR-" + (show_price_in_label ? str.format(" ({0})", two_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
            array.push(two_third_adr_down_labels, two_third_adr_down_label)

    // Create 1/2 ADR level lines
    if show_half_adr
        // 1/2 ADR up line
        half_adr_up_line := line.new(bar_index, half_adr_up, bar_index, half_adr_up, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_up_lines, half_adr_up_line)
        manage_line_history(half_adr_up_lines)

        // 1/2 ADR up label
        if show_labels
            half_adr_up_label := label.new(bar_index + label_x_offset_bars, half_adr_up + label_y_offset, "1/2 ADR+" + (show_price_in_label ? str.format(" ({0})", half_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_up_labels, half_adr_up_label)

        // 1/2 ADR down line
        half_adr_down_line := line.new(bar_index, half_adr_down, bar_index, half_adr_down, color=half_adr_color, width=line_width, style=line_style_value)
        array.unshift(half_adr_down_lines, half_adr_down_line)
        manage_line_history(half_adr_down_lines)

        // 1/2 ADR down label
        if show_labels
            half_adr_down_label := label.new(bar_index + label_x_offset_bars, half_adr_down + label_y_offset, "1/2 ADR-" + (show_price_in_label ? str.format(" ({0})", half_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
            array.push(half_adr_down_labels, half_adr_down_label)

// Update lines during the current day
if in_current_day and true_day_line_active
    // Update true day open line
    line.set_x2(true_day_open_line, bar_index)
    line.set_y2(true_day_open_line, true_day_open_price)

    // Update true day open label
    if show_labels
        label.set_x(true_day_open_label, bar_index + label_x_offset_bars)
        label.set_y(true_day_open_label, true_day_open_price + label_y_offset)

    // Update ADR level lines
    if show_full_adr
        line.set_x2(full_adr_up_line, bar_index)
        line.set_y2(full_adr_up_line, full_adr_up)
        line.set_x2(full_adr_down_line, bar_index)
        line.set_y2(full_adr_down_line, full_adr_down)

        if show_labels
            label.set_x(full_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_up_label, full_adr_up + label_y_offset)
            label.set_x(full_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(full_adr_down_label, full_adr_down + label_y_offset)

    if show_one_third_adr
        line.set_x2(one_third_adr_up_line, bar_index)
        line.set_y2(one_third_adr_up_line, one_third_adr_up)
        line.set_x2(one_third_adr_down_line, bar_index)
        line.set_y2(one_third_adr_down_line, one_third_adr_down)

        if show_labels
            label.set_x(one_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_up_label, one_third_adr_up + label_y_offset)
            label.set_x(one_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(one_third_adr_down_label, one_third_adr_down + label_y_offset)

    if show_two_third_adr
        line.set_x2(two_third_adr_up_line, bar_index)
        line.set_y2(two_third_adr_up_line, two_third_adr_up)
        line.set_x2(two_third_adr_down_line, bar_index)
        line.set_y2(two_third_adr_down_line, two_third_adr_down)

        if show_labels
            label.set_x(two_third_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_up_label, two_third_adr_up + label_y_offset)
            label.set_x(two_third_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(two_third_adr_down_label, two_third_adr_down + label_y_offset)

    if show_half_adr
        line.set_x2(half_adr_up_line, bar_index)
        line.set_y2(half_adr_up_line, half_adr_up)
        line.set_x2(half_adr_down_line, bar_index)
        line.set_y2(half_adr_down_line, half_adr_down)

        if show_labels
            label.set_x(half_adr_up_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_up_label, half_adr_up + label_y_offset)
            label.set_x(half_adr_down_label, bar_index + label_x_offset_bars)
            label.set_y(half_adr_down_label, half_adr_down + label_y_offset)

// Add alert conditions
alertcondition(close >= full_adr_up, "Price reached Full ADR+", "Price has reached the Full ADR+ level")
alertcondition(close <= full_adr_down, "Price reached Full ADR-", "Price has reached the Full ADR- level")
alertcondition(close >= one_third_adr_up, "Price reached 1/3 ADR+", "Price has reached the 1/3 ADR+ level")
alertcondition(close <= one_third_adr_down, "Price reached 1/3 ADR-", "Price has reached the 1/3 ADR- level")
alertcondition(close >= two_third_adr_up, "Price reached 2/3 ADR+", "Price has reached the 2/3 ADR+ level")
alertcondition(close <= two_third_adr_down, "Price reached 2/3 ADR-", "Price has reached the 2/3 ADR- level")
alertcondition(close >= half_adr_up, "Price reached 1/2 ADR+", "Price has reached the 1/2 ADR+ level")
alertcondition(close <= half_adr_down, "Price reached 1/2 ADR-", "Price has reached the 1/2 ADR- level")
