//@version=5

indicator("Multi-Timeframe Fibonacci Levels with Alerts", overlay=true, max_lines_count=500, max_labels_count=500, dynamic_requests=true)

// Arrays to store labels and lines for each timeframe
var dailyLabelsFib = array.new_label()
var weeklyLabelsFib = array.new_label()
var monthlyLabelsFib = array.new_label()
var quarterlyLabelsFib = array.new_label()
var yearlyLabelsFib = array.new_label()

var dailyLinesFib = array.new_line()
var weeklyLinesFib = array.new_line()
var monthlyLinesFib = array.new_line()
var quarterlyLinesFib = array.new_line()
var yearlyLinesFib = array.new_line()

// Arrays to store open price labels and lines
var dailyOpenLabelsFib = array.new_label()
var weeklyOpenLabelsFib = array.new_label()
var monthlyOpenLabelsFib = array.new_label()

var dailyOpenLinesFib = array.new_line()
var weeklyOpenLinesFib = array.new_line()
var monthlyOpenLinesFib = array.new_line()

// Array to store all price levels for overlap detection
var allPriceLevelsFib = array.new_float()
var allLabelTextsFib = array.new_string()

// Function to clear old labels with safety limit
clearLabelsFib(labelsArrayFib) =>
    if array.size(labelsArrayFib) > 0
        // Limit the number of iterations to prevent potential issues
        maxSize = math.min(array.size(labelsArrayFib), 100)
        for iFib = 0 to maxSize - 1
            label.delete(array.get(labelsArrayFib, iFib))
        array.clear(labelsArrayFib)

// Function to clear old lines with safety limit
clearLinesFib(linesArrayFib) =>
    if array.size(linesArrayFib) > 0
        // Limit the number of iterations to prevent potential issues
        maxSize = math.min(array.size(linesArrayFib), 100)
        for iFib = 0 to maxSize - 1
            line.delete(array.get(linesArrayFib, iFib))
        array.clear(linesArrayFib)

// Function to check if a price level is close to an existing level
isOverlappingFib(priceFib, thresholdFib) =>
    result = false
    existingLabelFib = ""
    overlapIndexFib = -1

    if array.size(allPriceLevelsFib) > 0
        // Limit the number of iterations to prevent potential issues
        maxSize = math.min(array.size(allPriceLevelsFib), 100)
        for iFib = 0 to maxSize - 1
            existingPriceFib = array.get(allPriceLevelsFib, iFib)
            // Check if prices are within threshold (0.05% of current price)
            if math.abs(priceFib - existingPriceFib) <= thresholdFib
                result := true
                existingLabelFib := array.get(allLabelTextsFib, iFib)
                overlapIndexFib := iFib
                break

    [result, existingLabelFib, overlapIndexFib]

// Function to add a price level to the tracking arrays with size limit
addPriceLevelFib(priceFib, labelFib) =>
    // Limit array size to prevent overflow
    if array.size(allPriceLevelsFib) >= 100
        array.shift(allPriceLevelsFib)
        array.shift(allLabelTextsFib)

    array.push(allPriceLevelsFib, priceFib)
    array.push(allLabelTextsFib, labelFib)

// Function to clear price level tracking arrays
clearPriceLevelsFib() =>
    array.clear(allPriceLevelsFib)
    array.clear(allLabelTextsFib)
// Master enable/disable for all Fibonacci drawings
enableFibDrawingsFib = input.bool(true, "Enable Fibonacci Drawings", group="Main Settings")

// Enable/disable open price levels
showOpenLevelsFib = input.bool(true, "Enable Open Price Levels", group="Main Settings")
showDailyOpenFib = input.bool(true, "Show Daily Open", group="Open Levels")
showWeeklyOpenFib = input.bool(true, "Show Weekly Open", group="Open Levels")
showMonthlyOpenFib = input.bool(true, "Show Monthly Open", group="Open Levels")

// Inputs for enabling/disabling timeframes and lookback periods
showDailyFib = input.bool(true, "Show Daily Levels", group="Timeframe Settings")
dailyLookbackFib = input.int(500, "Daily Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showWeeklyFib = input.bool(true, "Show Weekly Levels", group="Timeframe Settings")
weeklyLookbackFib = input.int(500, "Weekly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showMonthlyFib = input.bool(true, "Show Monthly Levels", group="Timeframe Settings")
monthlyLookbackFib = input.int(500, "Monthly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showQuarterlyFib = input.bool(true, "Show Quarterly Levels", group="Timeframe Settings")
quarterlyLookbackFib = input.int(500, "Quarterly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showYearlyFib = input.bool(true, "Show Yearly Levels", group="Timeframe Settings")
yearlyLookbackFib = input.int(500, "Yearly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")


// Input for right and left extension
dailyExtendRightFib = input.int(50, "Daily Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
dailyExtendLeftFib = input.int(500, "Daily Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

weeklyExtendRightFib = input.int(50, "Weekly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
weeklyExtendLeftFib = input.int(500, "Weekly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

monthlyExtendRightFib = input.int(50, "Monthly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
monthlyExtendLeftFib = input.int(500, "Monthly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

quarterlyExtendRightFib = input.int(50, "Quarterly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
quarterlyExtendLeftFib = input.int(500, "Quarterly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

yearlyExtendRightFib = input.int(50, "Yearly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
yearlyExtendLeftFib = input.int(500, "Yearly Left Extension (candles)", minval=0, maxval=500, group="Extension Settings")

// Define Fibonacci levels and their labels from the screenshot
fibLevelsFib = array.from(0, 0.236, 0.382, 0.5, 0.618, 0.764, 1.0)
levelLabelsFib = array.from("High", "23.6", "38.2", "50.0", "61.8", "76.4", "Low")

// Toggle inputs for each Fibonacci level
show0Fib = input.bool(true, "Show High (0.00) Level", group="Fibonacci Levels")
show236Fib = input.bool(true, "Show 23.6 Level", group="Fibonacci Levels")
show382Fib = input.bool(true, "Show 38.2 Level", group="Fibonacci Levels")
show50Fib = input.bool(true, "Show 50.0 Level", group="Fibonacci Levels")
show618Fib = input.bool(true, "Show 61.8 Level", group="Fibonacci Levels")
show764Fib = input.bool(true, "Show 76.4 Level", group="Fibonacci Levels")
show100Fib = input.bool(true, "Show Low (100) Level", group="Fibonacci Levels")

// Color inputs for each Fibonacci level
color0Fib = input.color(#ffffff, "High (0.00) Level Color", group="Color Settings")
color236Fib = input.color(#ffffff, "23.6 Level Color", group="Color Settings")
color382Fib = input.color(#ffffff, "38.2 Level Color", group="Color Settings")
color50Fib = input.color(#ffffff, "50.0 Level Color", group="Color Settings")
color618Fib = input.color(#ffffff, "61.8 Level Color", group="Color Settings")
color764Fib = input.color(#ffffff, "76.4 Level Color", group="Color Settings")
color100Fib = input.color(#ffffff, "Low (100) Level Color", group="Color Settings")

fibColorsFib = array.from(color0Fib, color236Fib, color382Fib, color50Fib, color618Fib, color764Fib, color100Fib)
fibShowLevelsFib = array.from(show0Fib, show236Fib, show382Fib, show50Fib, show618Fib, show764Fib, show100Fib)

// Color inputs for open price levels
dailyOpenColorFib = input.color(#00FFFF, "Daily Open Color", group="Open Level Colors")
weeklyOpenColorFib = input.color(#FF00FF, "Weekly Open Color", group="Open Level Colors")
monthlyOpenColorFib = input.color(#FFFF00, "Monthly Open Color", group="Open Level Colors")

// Line style configuration
lineWidthFib = input.int(2, "Line Width", minval=1, maxval=5, group="Style Settings")
lineStyleFib = input.string("Dashed", "Line Style", options=["Solid", "Dotted", "Dashed"], group="Style Settings")

// Function to convert string style to Pine line style
getLineStyleFib(styleFib) =>
    if styleFib == "Solid"
        line.style_solid
    else if styleFib == "Dotted"
        line.style_dotted
    else
        line.style_dashed

// Function to calculate pivot highs and lows for a given timeframe
getPivotsFib(resFib) =>
    highPriceFib = request.security(syminfo.tickerid, resFib, high[1], lookahead=barmerge.lookahead_on)
    lowPriceFib = request.security(syminfo.tickerid, resFib, low[1], lookahead=barmerge.lookahead_on)
    [highPriceFib, lowPriceFib]

// Function to get open price for a given timeframe
getOpenPriceFib(resFib) =>
    openPriceFib = request.security(syminfo.tickerid, resFib, open, lookahead=barmerge.lookahead_on)
    openPriceFib

// Get pivots for each timeframe
[dHighFib, dLowFib] = getPivotsFib("D")
[wHighFib, wLowFib] = getPivotsFib("W")
[mHighFib, mLowFib] = getPivotsFib("M")
[qHighFib, qLowFib] = getPivotsFib("3M")
[yHighFib, yLowFib] = getPivotsFib("12M")

// Get open prices for each timeframe
dOpenFib = getOpenPriceFib("D")
wOpenFib = getOpenPriceFib("W")
mOpenFib = getOpenPriceFib("M")

// Function to plot Fibonacci levels with labels
plotFibLevelsFib(highValFib, lowValFib, showLevelsFib, suffixFib, lookbackFib, rightExtendFib, leftExtendFib, labelsArrayFib, linesArrayFib) =>
    if enableFibDrawingsFib and showLevelsFib and not na(highValFib) and not na(lowValFib)
        // Clear old labels and lines when on the last bar to prevent clutter
        if barstate.islast
            clearLabelsFib(labelsArrayFib)
            clearLinesFib(linesArrayFib)

        priceRangeFib = lowValFib - highValFib
        // Limit the number of Fibonacci levels to process to prevent array overflow
        maxLevels = math.min(array.size(fibLevelsFib), 7)  // We have 7 Fibonacci levels

        for iFib = 0 to maxLevels - 1
            // Check if this specific Fibonacci level is enabled
            if iFib < array.size(fibShowLevelsFib) and array.get(fibShowLevelsFib, iFib)
                levelFib = array.get(fibLevelsFib, iFib)
                priceLevelFib = highValFib + priceRangeFib * levelFib
                labelTextFib = array.get(levelLabelsFib, iFib) + suffixFib
                lineColorFib = array.get(fibColorsFib, iFib)

                // Draw line for lookback period with left extension
                startBarFib = math.max(0, bar_index - lookbackFib - leftExtendFib)
                endBarFib = bar_index + rightExtendFib

                // Create the line and store it in the array
                // Limit array size to prevent overflow
                if array.size(linesArrayFib) >= 100
                    if array.size(linesArrayFib) > 0
                        line.delete(array.shift(linesArrayFib))

                fibLineFib = line.new(x1=startBarFib,y1=priceLevelFib,x2=endBarFib,y2=priceLevelFib,color=lineColorFib,style=getLineStyleFib(lineStyleFib),width=lineWidthFib,xloc=xloc.bar_index,extend=extend.none)
                array.push(linesArrayFib, fibLineFib)

                // Check for overlapping levels
                thresholdFib = priceLevelFib * 0.0005  // 0.05% threshold for overlap detection
                [isOverlapFib, existingLabelFib, overlapIndexFib] = isOverlappingFib(priceLevelFib, thresholdFib)

                // Add label at the right side of the chart only on the last bar
                if barstate.islast
                    finalLabelTextFib = labelTextFib

                    // If overlapping with another level, combine the labels
                    if isOverlapFib
                        finalLabelTextFib := existingLabelFib + " + " + labelTextFib
                        // Update the existing label in the tracking array
                        if overlapIndexFib >= 0
                            array.set(allLabelTextsFib, overlapIndexFib, finalLabelTextFib)
                    else
                        // Add this level to the tracking arrays
                        addPriceLevelFib(priceLevelFib, labelTextFib)

                    // Limit label array size to prevent overflow
                    if array.size(labelsArrayFib) >= 100
                        if array.size(labelsArrayFib) > 0
                            label.delete(array.shift(labelsArrayFib))

                    newLabelFib = label.new(x=endBarFib,y=priceLevelFib,text=finalLabelTextFib,style=label.style_label_left,color=color.rgb(0, 0, 0, 100),textcolor=lineColorFib,size=size.normal)
                    // Store the label in the array for future management
                    array.push(labelsArrayFib, newLabelFib)

// Function to plot open price levels with labels
plotOpenLevelFib(openPriceFib, showLevelFib, labelTextFib, lookbackFib, rightExtendFib, leftExtendFib, labelsArrayFib, linesArrayFib, colorFib) =>
    if showOpenLevelsFib and showLevelFib and not na(openPriceFib)
        // Clear old labels and lines when on the last bar to prevent clutter
        if barstate.islast
            clearLabelsFib(labelsArrayFib)
            clearLinesFib(linesArrayFib)

        // Draw line for lookback period with left extension
        startBarFib = math.max(0, bar_index - lookbackFib - leftExtendFib)
        endBarFib = bar_index + rightExtendFib

        // Create the line and store it in the array
        // Limit array size to prevent overflow
        if array.size(linesArrayFib) >= 100
            if array.size(linesArrayFib) > 0
                line.delete(array.shift(linesArrayFib))

        openLineFib = line.new(x1=startBarFib, y1=openPriceFib, x2=endBarFib, y2=openPriceFib,
                             color=colorFib, style=getLineStyleFib(lineStyleFib), width=lineWidthFib,
                             xloc=xloc.bar_index, extend=extend.none)
        array.push(linesArrayFib, openLineFib)

        // Check for overlapping levels
        thresholdFib = openPriceFib * 0.0005  // 0.05% threshold for overlap detection
        [isOverlapFib, existingLabelFib, overlapIndexFib] = isOverlappingFib(openPriceFib, thresholdFib)

        // Add label at the right side of the chart only on the last bar
        if barstate.islast
            finalLabelTextFib = labelTextFib

            // If overlapping with another level, combine the labels
            if isOverlapFib
                finalLabelTextFib := existingLabelFib + " + " + labelTextFib
                // Update the existing label in the tracking array
                if overlapIndexFib >= 0
                    array.set(allLabelTextsFib, overlapIndexFib, finalLabelTextFib)
            else
                // Add this level to the tracking arrays
                addPriceLevelFib(openPriceFib, labelTextFib)

            // Limit label array size to prevent overflow
            if array.size(labelsArrayFib) >= 100
                if array.size(labelsArrayFib) > 0
                    label.delete(array.shift(labelsArrayFib))

            newLabelFib = label.new(x=endBarFib, y=openPriceFib, text=finalLabelTextFib,
                                  style=label.style_label_left, color=color.rgb(0, 0, 0, 100),
                                  textcolor=colorFib, size=size.normal)
            // Store the label in the array for future management
            array.push(labelsArrayFib, newLabelFib)

// Clear price level tracking arrays at the start of each bar
if barstate.islast
    clearPriceLevelsFib()

// Plot all Fibonacci levels with 500 candles lookback
plotFibLevelsFib(dHighFib, dLowFib, showDailyFib, " D", dailyLookbackFib, dailyExtendRightFib, dailyExtendLeftFib, dailyLabelsFib, dailyLinesFib)
plotFibLevelsFib(wHighFib, wLowFib, showWeeklyFib, " W", weeklyLookbackFib, weeklyExtendRightFib, weeklyExtendLeftFib, weeklyLabelsFib, weeklyLinesFib)
plotFibLevelsFib(mHighFib, mLowFib, showMonthlyFib, " M", monthlyLookbackFib, monthlyExtendRightFib, monthlyExtendLeftFib, monthlyLabelsFib, monthlyLinesFib)
plotFibLevelsFib(qHighFib, qLowFib, showQuarterlyFib, " Q", quarterlyLookbackFib, quarterlyExtendRightFib, quarterlyExtendLeftFib, quarterlyLabelsFib, quarterlyLinesFib)
plotFibLevelsFib(yHighFib, yLowFib, showYearlyFib, " Y", yearlyLookbackFib, yearlyExtendRightFib, yearlyExtendLeftFib, yearlyLabelsFib, yearlyLinesFib)

// Plot open price levels
plotOpenLevelFib(dOpenFib, showDailyOpenFib, "DO", dailyLookbackFib, dailyExtendRightFib, dailyExtendLeftFib, dailyOpenLabelsFib, dailyOpenLinesFib, dailyOpenColorFib)
plotOpenLevelFib(wOpenFib, showWeeklyOpenFib, "WO", weeklyLookbackFib, weeklyExtendRightFib, weeklyExtendLeftFib, weeklyOpenLabelsFib, weeklyOpenLinesFib, weeklyOpenColorFib)
plotOpenLevelFib(mOpenFib, showMonthlyOpenFib, "MO", monthlyLookbackFib, monthlyExtendRightFib, monthlyExtendLeftFib, monthlyOpenLabelsFib, monthlyOpenLinesFib, monthlyOpenColorFib)