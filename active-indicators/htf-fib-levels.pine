//@version=5

// indicator("Multi-Timeframe Fibonacci Levels with Alerts", overlay=true, max_lines_count=500, max_labels_count=500, dynamic_requests=true)

// Arrays to store labels and lines for each timeframe
var dailyLabelsFib = array.new_label()
var weeklyLabelsFib = array.new_label()
var monthlyLabelsFib = array.new_label()
var quarterlyLabelsFib = array.new_label()
var yearlyLabelsFib = array.new_label()

var dailyLinesFib = array.new_line()
var weeklyLinesFib = array.new_line()
var monthlyLinesFib = array.new_line()
var quarterlyLinesFib = array.new_line()
var yearlyLinesFib = array.new_line()

// Function to clear old labels
clearLabelsFib(labelsArrayFib) =>
    if array.size(labelsArrayFib) > 0
        for iFib = 0 to array.size(labelsArrayFib) - 1
            label.delete(array.get(labelsArrayFib, iFib))
        array.clear(labelsArrayFib)

// Function to clear old lines
clearLinesFib(linesArrayFib) =>
    if array.size(linesArrayFib) > 0
        for iFib = 0 to array.size(linesArrayFib) - 1
            line.delete(array.get(linesArrayFib, iFib))
        array.clear(linesArrayFib)
// Master enable/disable for all Fibonacci drawings
enableFibDrawingsFib = input.bool(true, "Enable Fibonacci Drawings", group="Main Settings")

// Inputs for enabling/disabling timeframes and lookback periods
showDailyFib = input.bool(true, "Show Daily Levels", group="Timeframe Settings")
dailyLookbackFib = input.int(500, "Daily Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showWeeklyFib = input.bool(true, "Show Weekly Levels", group="Timeframe Settings")
weeklyLookbackFib = input.int(500, "Weekly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showMonthlyFib = input.bool(true, "Show Monthly Levels", group="Timeframe Settings")
monthlyLookbackFib = input.int(500, "Monthly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showQuarterlyFib = input.bool(true, "Show Quarterly Levels", group="Timeframe Settings")
quarterlyLookbackFib = input.int(500, "Quarterly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")

showYearlyFib = input.bool(true, "Show Yearly Levels", group="Timeframe Settings")
yearlyLookbackFib = input.int(500, "Yearly Lookback (candles)", minval=1, maxval=500, group="Timeframe Settings")


// New input for right extension (add to your existing inputs)
dailyExtendRightFib = input.int(0, "Daily Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
weeklyExtendRightFib = input.int(0, "Weekly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
monthlyExtendRightFib = input.int(0, "Monthly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
quarterlyExtendRightFib = input.int(0, "Quarterly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")
yearlyExtendRightFib = input.int(0, "Yearly Right Extension (candles)", minval=0, maxval=500, group="Extension Settings")

// Define Fibonacci levels and their labels from the screenshot
fibLevelsFib = array.from(0, 0.236, 0.382, 0.5, 0.618, 0.764, 1.0)
levelLabelsFib = array.from("0.00", "23.6", "38.2", "50.0", "61.8", "76.4", "100")

// Color inputs for each Fibonacci level
color0Fib = input.color(#ffffff, "0.00 Level Color", group="Color Settings")
color236Fib = input.color(#ffffff, "23.6 Level Color", group="Color Settings")
color382Fib = input.color(#ffffff, "38.2 Level Color", group="Color Settings")
color50Fib = input.color(#ffffff, "50.0 Level Color", group="Color Settings")
color618Fib = input.color(#ffffff, "61.8 Level Color", group="Color Settings")
color764Fib = input.color(#ffffff, "76.4 Level Color", group="Color Settings")
color100Fib = input.color(#ffffff, "100 Level Color", group="Color Settings")

fibColorsFib = array.from(color0Fib, color236Fib, color382Fib, color50Fib, color618Fib, color764Fib, color100Fib)

// Line style configuration
lineWidthFib = input.int(2, "Line Width", minval=1, maxval=5, group="Style Settings")
lineStyleFib = input.string("Dashed", "Line Style", options=["Solid", "Dotted", "Dashed"], group="Style Settings")

// Function to convert string style to Pine line style
getLineStyleFib(styleFib) =>
    if styleFib == "Solid"
        line.style_solid
    else if styleFib == "Dotted"
        line.style_dotted
    else
        line.style_dashed

// Function to calculate pivot highs and lows for a given timeframe
getPivotsFib(resFib) =>
    highPriceFib = request.security(syminfo.tickerid, resFib, high[1], lookahead=barmerge.lookahead_on)
    lowPriceFib = request.security(syminfo.tickerid, resFib, low[1], lookahead=barmerge.lookahead_on)
    [highPriceFib, lowPriceFib]

// Get pivots for each timeframe
[dHighFib, dLowFib] = getPivotsFib("D")
[wHighFib, wLowFib] = getPivotsFib("W")
[mHighFib, mLowFib] = getPivotsFib("M")
[qHighFib, qLowFib] = getPivotsFib("3M")
[yHighFib, yLowFib] = getPivotsFib("12M")

// Function to plot Fibonacci levels with labels
plotFibLevelsFib(highValFib, lowValFib, showLevelsFib, suffixFib, lookbackFib, rightExtendFib, labelsArrayFib, linesArrayFib) =>
    if enableFibDrawingsFib and showLevelsFib and not na(highValFib) and not na(lowValFib)
        // Clear old labels and lines when on the last bar to prevent clutter
        if barstate.islast
            clearLabelsFib(labelsArrayFib)
            clearLinesFib(linesArrayFib)

        priceRangeFib = lowValFib - highValFib
        for iFib = 0 to array.size(fibLevelsFib) - 1
            levelFib = array.get(fibLevelsFib, iFib)
            priceLevelFib = highValFib + priceRangeFib * levelFib
            labelTextFib = array.get(levelLabelsFib, iFib) + suffixFib
            lineColorFib = array.get(fibColorsFib, iFib)

            // Draw line for lookback period
            startBarFib = math.max(0, bar_index - lookbackFib)
            endBarFib = bar_index + rightExtendFib

            // Create the line and store it in the array
            fibLineFib = line.new(x1=startBarFib,y1=priceLevelFib,x2=endBarFib,y2=priceLevelFib,color=lineColorFib,style=getLineStyleFib(lineStyleFib),width=lineWidthFib,xloc=xloc.bar_index,extend=extend.none)
            array.push(linesArrayFib, fibLineFib)

            // Add label at the right side of the chart only on the last bar
            if barstate.islast
                newLabelFib = label.new(x=endBarFib,y=priceLevelFib,text=labelTextFib,style=label.style_label_left,color=color.rgb(0, 0, 0, 100),textcolor=lineColorFib,size=size.normal)
                // Store the label in the array for future management
                array.push(labelsArrayFib, newLabelFib)

// Plot all Fibonacci levels with 500 candles lookback
plotFibLevelsFib(dHighFib, dLowFib, showDailyFib, " D", dailyLookbackFib, dailyExtendRightFib, dailyLabelsFib, dailyLinesFib)
plotFibLevelsFib(wHighFib, wLowFib, showWeeklyFib, " W", weeklyLookbackFib, weeklyExtendRightFib, weeklyLabelsFib, weeklyLinesFib)
plotFibLevelsFib(mHighFib, mLowFib, showMonthlyFib, " M", monthlyLookbackFib, monthlyExtendRightFib, monthlyLabelsFib, monthlyLinesFib)
plotFibLevelsFib(qHighFib, qLowFib, showQuarterlyFib, " Q", quarterlyLookbackFib, quarterlyExtendRightFib, quarterlyLabelsFib, quarterlyLinesFib)
plotFibLevelsFib(yHighFib, yLowFib, showYearlyFib, " Y", yearlyLookbackFib, yearlyExtendRightFib, yearlyLabelsFib, yearlyLinesFib)