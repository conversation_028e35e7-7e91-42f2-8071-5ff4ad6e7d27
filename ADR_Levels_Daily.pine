//@version=5
indicator("ICT ADR/AWR/AMR Levels", overlay=true, max_lines_count=500, max_labels_count=500)

// === INPUTS ===
// General Settings
group_settings = "General Settings"
show_only_intraday = input.bool(false, "Show Only on Intraday Timeframes", group=group_settings)
timezone = input.string("America/New_York", "Time Zone", group=group_settings)
max_periods = input.int(3, "Periods to Display", minval=1, maxval=30, tooltip="Number of days/weeks/months to display", group=group_settings)
show_data_table = input.string("Right", "Show Data Table", options=["None", "Left", "Right", "Both"], group=group_settings)
table_position = input.string("Normal", "Table Position", options=["Normal", "Center", "Bottom"], group=group_settings)

// Time Settings
group_time = "Time Settings"
day_start_hour = input.int(0, "Day Start Hour (0-23)", minval=0, maxval=23, group=group_time)
week_start_day = input.string("Monday", "Week Start Day", options=["Sunday", "Monday"], group=group_time)
month_start_day = input.int(1, "Month Start Day", minval=1, maxval=31, tooltip="Day of month to start monthly calculations", group=group_time)

// Daily ADR Settings
group_adr = "Daily ADR Settings"
show_daily_levels = input.bool(true, "Show Daily Levels", group=group_adr)
adr_days = input.int(5, "Days to Average", minval=1, maxval=30, group=group_adr)
show_full_adr = input.bool(true, "Show Full ADR", group=group_adr)
show_one_third_adr = input.bool(true, "Show 1/3 ADR", group=group_adr)
show_two_third_adr = input.bool(false, "Show 2/3 ADR", group=group_adr)
show_half_adr = input.bool(false, "Show 1/2 ADR", group=group_adr)

// Weekly AWR Settings
group_awr = "Weekly AWR Settings"
show_weekly_levels = input.bool(true, "Show Weekly Levels", group=group_awr)
awr_weeks = input.int(4, "Weeks to Average", minval=1, maxval=52, group=group_awr)
show_full_awr = input.bool(true, "Show Full AWR", group=group_awr)
show_one_third_awr = input.bool(true, "Show 1/3 AWR", group=group_awr)
show_two_third_awr = input.bool(false, "Show 2/3 AWR", group=group_awr)
show_half_awr = input.bool(false, "Show 1/2 AWR", group=group_awr)

// Monthly AMR Settings
group_amr = "Monthly AMR Settings"
show_monthly_levels = input.bool(true, "Show Monthly Levels", group=group_amr)
amr_months = input.int(3, "Months to Average", minval=1, maxval=24, group=group_amr)
show_full_amr = input.bool(true, "Show Full AMR", group=group_amr)
show_one_third_amr = input.bool(true, "Show 1/3 AMR", group=group_amr)
show_two_third_amr = input.bool(false, "Show 2/3 AMR", group=group_amr)
show_half_amr = input.bool(false, "Show 1/2 AMR", group=group_amr)

// Line Settings
group_line = "Line Settings"
line_width = input.int(1, "Line Width", minval=1, maxval=5, group=group_line)
line_style = input.string("Dotted", "Line Style", options=["Solid", "Dotted", "Dashed"], group=group_line)
show_vertical = input.bool(true, "Show Vertical Line at Day Open", group=group_line)

// Color Settings
group_color_daily = "Daily Color Settings"
true_day_open_color = input.color(color.orange, "True Day Open Line", group=group_color_daily)
full_adr_color = input.color(color.white, "Full ADR", group=group_color_daily)
one_third_adr_color = input.color(color.aqua, "1/3 ADR", group=group_color_daily)
two_third_adr_color = input.color(color.yellow, "2/3 ADR", group=group_color_daily)
half_adr_color = input.color(color.purple, "1/2 ADR", group=group_color_daily)

// Weekly Color Settings
group_color_weekly = "Weekly Color Settings"
true_week_open_color = input.color(color.lime, "True Week Open Line", group=group_color_weekly)
full_awr_color = input.color(color.white, "Full AWR", group=group_color_weekly)
one_third_awr_color = input.color(color.aqua, "1/3 AWR", group=group_color_weekly)
two_third_awr_color = input.color(color.yellow, "2/3 AWR", group=group_color_weekly)
half_awr_color = input.color(color.purple, "1/2 AWR", group=group_color_weekly)

// Monthly Color Settings
group_color_monthly = "Monthly Color Settings"
true_month_open_color = input.color(color.red, "True Month Open Line", group=group_color_monthly)
full_amr_color = input.color(color.white, "Full AMR", group=group_color_monthly)
one_third_amr_color = input.color(color.aqua, "1/3 AMR", group=group_color_monthly)
two_third_amr_color = input.color(color.yellow, "2/3 AMR", group=group_color_monthly)
half_amr_color = input.color(color.purple, "1/2 AMR", group=group_color_monthly)

// Label Settings
group_label = "Label Settings"
show_labels = input.bool(true, "Show Labels", group=group_label)
show_price_in_label = input.bool(true, "Show Price in Labels", group=group_label)
label_x_offset_bars = input.int(1, "Label X Offset (Bars Right)", minval=0, group=group_label)
label_y_offset = input.float(0.0, "Label Y Offset (Price)", step=0.1, group=group_label)
label_size = input.string("Small", "Label Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=group_label)

// === HELPER FUNCTIONS ===
get_line_style(styleStr) =>
    styleStr == "Solid" ? line.style_solid : styleStr == "Dashed" ? line.style_dashed : line.style_dotted

get_label_size(sizeStr) =>
    result = switch sizeStr
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
    result

// === TIME LOGIC ===
// Daily time logic
start_of_day = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
end_of_day = timestamp(timezone, year, month, dayofmonth, 23, 59)
is_new_day = (time == start_of_day) or (barstate.isfirst and show_daily_levels)
in_current_day = time >= start_of_day and time <= end_of_day

// Weekly time logic
is_sunday = dayofweek == 1
is_monday = dayofweek == 2
week_start_condition = week_start_day == "Sunday" ? is_sunday : is_monday
week_timestamp = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
start_of_week = week_timestamp and week_start_condition
is_new_week = (time == week_timestamp and week_start_condition) or (barstate.isfirst and show_weekly_levels)
in_current_week = true  // We'll always be in the current week, just need to track when a new week starts

// Monthly time logic
is_first_day_of_month = dayofmonth == month_start_day
month_timestamp = timestamp(timezone, year, month, dayofmonth, day_start_hour, 0)
start_of_month = month_timestamp and is_first_day_of_month
is_new_month = (time == month_timestamp and is_first_day_of_month) or (barstate.isfirst and show_monthly_levels)
in_current_month = true  // We'll always be in the current month, just need to track when a new month starts

// Function to calculate the Average Daily Range
calculate_adr(lookback_period) =>
    // Request daily high-low range data
    dh = request.security(syminfo.tickerid, "D", high, barmerge.gaps_off, barmerge.lookahead_off)
    dl = request.security(syminfo.tickerid, "D", low, barmerge.gaps_off, barmerge.lookahead_off)
    drange = dh - dl

    // Calculate the average of the daily ranges
    ta.sma(drange, lookback_period)

// Function to calculate the Average Weekly Range
calculate_awr(lookback_period) =>
    // Request weekly high-low range data
    wh = request.security(syminfo.tickerid, "W", high, barmerge.gaps_off, barmerge.lookahead_off)
    wl = request.security(syminfo.tickerid, "W", low, barmerge.gaps_off, barmerge.lookahead_off)
    wrange = wh - wl

    // Calculate the average of the weekly ranges
    ta.sma(wrange, lookback_period)

// Function to calculate the Average Monthly Range
calculate_amr(lookback_period) =>
    // Request monthly high-low range data
    mh = request.security(syminfo.tickerid, "M", high, barmerge.gaps_off, barmerge.lookahead_off)
    ml = request.security(syminfo.tickerid, "M", low, barmerge.gaps_off, barmerge.lookahead_off)
    mrange = mh - ml

    // Calculate the average of the monthly ranges
    ta.sma(mrange, lookback_period)

// === STATE VARIABLES ===
var line_style_value = get_line_style(line_style)
var label_size_value = get_label_size(label_size)

// === DAILY VARIABLES ===
// True Day Open variables
var line true_day_open_line = na
var label true_day_open_label = na
var line true_day_vertical = na
var float true_day_open_price = na
var bool true_day_line_active = false

// ADR level lines
var line full_adr_up_line = na
var line full_adr_down_line = na
var line one_third_adr_up_line = na
var line one_third_adr_down_line = na
var line two_third_adr_up_line = na
var line two_third_adr_down_line = na
var line half_adr_up_line = na
var line half_adr_down_line = na

// ADR level labels
var label full_adr_up_label = na
var label full_adr_down_label = na
var label one_third_adr_up_label = na
var label one_third_adr_down_label = na
var label two_third_adr_up_label = na
var label two_third_adr_down_label = na
var label half_adr_up_label = na
var label half_adr_down_label = na

// ADR level values
var float daily_range_val = na
var float full_adr_up = na
var float full_adr_down = na
var float one_third_adr_up = na
var float one_third_adr_down = na
var float two_third_adr_up = na
var float two_third_adr_down = na
var float half_adr_up = na
var float half_adr_down = na

// === WEEKLY VARIABLES ===
// True Week Open variables
var line true_week_open_line = na
var label true_week_open_label = na
var line true_week_vertical = na
var float true_week_open_price = na
var bool true_week_line_active = false

// AWR level lines
var line full_awr_up_line = na
var line full_awr_down_line = na
var line one_third_awr_up_line = na
var line one_third_awr_down_line = na
var line two_third_awr_up_line = na
var line two_third_awr_down_line = na
var line half_awr_up_line = na
var line half_awr_down_line = na

// AWR level labels
var label full_awr_up_label = na
var label full_awr_down_label = na
var label one_third_awr_up_label = na
var label one_third_awr_down_label = na
var label two_third_awr_up_label = na
var label two_third_awr_down_label = na
var label half_awr_up_label = na
var label half_awr_down_label = na

// AWR level values
var float weekly_range_val = na
var float full_awr_up = na
var float full_awr_down = na
var float one_third_awr_up = na
var float one_third_awr_down = na
var float two_third_awr_up = na
var float two_third_awr_down = na
var float half_awr_up = na
var float half_awr_down = na

// === MONTHLY VARIABLES ===
// True Month Open variables
var line true_month_open_line = na
var label true_month_open_label = na
var line true_month_vertical = na
var float true_month_open_price = na
var bool true_month_line_active = false

// AMR level lines
var line full_amr_up_line = na
var line full_amr_down_line = na
var line one_third_amr_up_line = na
var line one_third_amr_down_line = na
var line two_third_amr_up_line = na
var line two_third_amr_down_line = na
var line half_amr_up_line = na
var line half_amr_down_line = na

// AMR level labels
var label full_amr_up_label = na
var label full_amr_down_label = na
var label one_third_amr_up_label = na
var label one_third_amr_down_label = na
var label two_third_amr_up_label = na
var label two_third_amr_down_label = na
var label half_amr_up_label = na
var label half_amr_down_label = na

// AMR level values
var float monthly_range_val = na
var float full_amr_up = na
var float full_amr_down = na
var float one_third_amr_up = na
var float one_third_amr_down = na
var float two_third_amr_up = na
var float two_third_amr_down = na
var float half_amr_up = na
var float half_amr_down = na

// === HISTORY ARRAYS ===
// Arrays to store historical range values
var float[] daily_range_history = array.new_float()
var float[] weekly_range_history = array.new_float()
var float[] monthly_range_history = array.new_float()

// Arrays to store level values and timestamps
var float[] monthly_tmo_values = array.new_float()
var float[] monthly_full_up_values = array.new_float()
var float[] monthly_full_down_values = array.new_float()
var float[] monthly_one_third_up_values = array.new_float()
var float[] monthly_one_third_down_values = array.new_float()
var float[] monthly_two_third_up_values = array.new_float()
var float[] monthly_two_third_down_values = array.new_float()
var float[] monthly_half_up_values = array.new_float()
var float[] monthly_half_down_values = array.new_float()
var int[] monthly_timestamps = array.new_int()
var int[] monthly_bar_indices = array.new_int()

// === DAILY ARRAYS ===
// Arrays to store historical daily lines by type
var line[] tdo_lines = array.new_line()
var line[] full_adr_up_lines = array.new_line()
var line[] full_adr_down_lines = array.new_line()
var line[] one_third_adr_up_lines = array.new_line()
var line[] one_third_adr_down_lines = array.new_line()
var line[] two_third_adr_up_lines = array.new_line()
var line[] two_third_adr_down_lines = array.new_line()
var line[] half_adr_up_lines = array.new_line()
var line[] half_adr_down_lines = array.new_line()
var line[] daily_vertical_lines = array.new_line()

// Arrays to store historical daily labels by type
var label[] tdo_labels = array.new_label()
var label[] full_adr_up_labels = array.new_label()
var label[] full_adr_down_labels = array.new_label()
var label[] one_third_adr_up_labels = array.new_label()
var label[] one_third_adr_down_labels = array.new_label()
var label[] two_third_adr_up_labels = array.new_label()
var label[] two_third_adr_down_labels = array.new_label()
var label[] half_adr_up_labels = array.new_label()
var label[] half_adr_down_labels = array.new_label()

// Arrays to store level values and timestamps for daily levels
var float[] daily_tdo_values = array.new_float()
var float[] daily_full_up_values = array.new_float()
var float[] daily_full_down_values = array.new_float()
var float[] daily_one_third_up_values = array.new_float()
var float[] daily_one_third_down_values = array.new_float()
var float[] daily_two_third_up_values = array.new_float()
var float[] daily_two_third_down_values = array.new_float()
var float[] daily_half_up_values = array.new_float()
var float[] daily_half_down_values = array.new_float()
var int[] daily_timestamps = array.new_int()
var int[] daily_bar_indices = array.new_int()

// === WEEKLY ARRAYS ===
// Arrays to store historical weekly lines by type
var line[] two_lines = array.new_line()
var line[] full_awr_up_lines = array.new_line()
var line[] full_awr_down_lines = array.new_line()
var line[] one_third_awr_up_lines = array.new_line()
var line[] one_third_awr_down_lines = array.new_line()
var line[] two_third_awr_up_lines = array.new_line()
var line[] two_third_awr_down_lines = array.new_line()
var line[] half_awr_up_lines = array.new_line()
var line[] half_awr_down_lines = array.new_line()
var line[] weekly_vertical_lines = array.new_line()

// Arrays to store historical weekly labels by type
var label[] two_labels = array.new_label()
var label[] full_awr_up_labels = array.new_label()
var label[] full_awr_down_labels = array.new_label()
var label[] one_third_awr_up_labels = array.new_label()
var label[] one_third_awr_down_labels = array.new_label()
var label[] two_third_awr_up_labels = array.new_label()
var label[] two_third_awr_down_labels = array.new_label()
var label[] half_awr_up_labels = array.new_label()
var label[] half_awr_down_labels = array.new_label()

// Arrays to store level values and timestamps for weekly levels
var float[] weekly_two_values = array.new_float()
var float[] weekly_full_up_values = array.new_float()
var float[] weekly_full_down_values = array.new_float()
var float[] weekly_one_third_up_values = array.new_float()
var float[] weekly_one_third_down_values = array.new_float()
var float[] weekly_two_third_up_values = array.new_float()
var float[] weekly_two_third_down_values = array.new_float()
var float[] weekly_half_up_values = array.new_float()
var float[] weekly_half_down_values = array.new_float()
var int[] weekly_timestamps = array.new_int()
var int[] weekly_bar_indices = array.new_int()

// === MONTHLY ARRAYS ===
// Arrays to store historical monthly lines by type
var line[] tmo_lines = array.new_line()
var line[] full_amr_up_lines = array.new_line()
var line[] full_amr_down_lines = array.new_line()
var line[] one_third_amr_up_lines = array.new_line()
var line[] one_third_amr_down_lines = array.new_line()
var line[] two_third_amr_up_lines = array.new_line()
var line[] two_third_amr_down_lines = array.new_line()
var line[] half_amr_up_lines = array.new_line()
var line[] half_amr_down_lines = array.new_line()
var line[] monthly_vertical_lines = array.new_line()

// Arrays to store historical monthly labels by type
var label[] tmo_labels = array.new_label()
var label[] full_amr_up_labels = array.new_label()
var label[] full_amr_down_labels = array.new_label()
var label[] one_third_amr_up_labels = array.new_label()
var label[] one_third_amr_down_labels = array.new_label()
var label[] two_third_amr_up_labels = array.new_label()
var label[] two_third_amr_down_labels = array.new_label()
var label[] half_amr_up_labels = array.new_label()
var label[] half_amr_down_labels = array.new_label()

// Function to delete all labels in an array
delete_all_labels(label_array) =>
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1
            label_to_delete = array.get(label_array, i)
            if not na(label_to_delete)
                label.delete(label_to_delete)
        array.clear(label_array)

// Function to manage line arrays based on max_periods
manage_line_history(line_array) =>
    if array.size(line_array) >= max_periods
        // Delete oldest line
        oldest_line = array.get(line_array, array.size(line_array) - 1)
        if not na(oldest_line)
            line.delete(oldest_line)
        array.pop(line_array)

// Function to draw a line efficiently for any timeframe
draw_efficient_line(price_level, color_value, extend_left_bars, extend_right_bars, line_style) =>
    // Calculate start and end bar positions with limits to avoid TradingView rendering issues
    int startBar = math.max(0, bar_index - extend_left_bars)
    int endBar = bar_index + extend_right_bars

    // Create the line with limited extension to avoid TradingView rendering issues
    line.new(x1=startBar, y1=price_level, x2=endBar, y2=price_level,
             color=color_value, width=line_width, style=line_style, xloc=xloc.bar_index)

// Function to draw a line with proper extension for all timeframes
draw_improved_line(price_level, color_value, line_style) =>
    // For better visibility on all timeframes, use more conservative extension values
    int extend_left = 2000  // Extend further back for better visibility
    int extend_right = 500  // Extend forward but within TradingView limits

    // Calculate start and end bar positions
    int startBar = math.max(0, bar_index - extend_left)
    int endBar = bar_index + extend_right

    // Create the line with improved extension
    line.new(x1=startBar, y1=price_level, x2=endBar, y2=price_level,
             color=color_value, width=line_width, style=line_style, xloc=xloc.bar_index)



// Function to safely delete lines in an array
safe_delete_lines(line_array) =>
    if array.size(line_array) > 0
        for i = 0 to array.size(line_array) - 1
            line_to_delete = array.get(line_array, i)
            if not na(line_to_delete)
                line.delete(line_to_delete)
        array.clear(line_array)

// Function to check if a bar is within the last 3 days
is_within_last_3_days() =>
    // Get current time and time 3 days ago in milliseconds
    current_time = time
    three_days_ago = current_time - 3 * 24 * 60 * 60 * 1000

    // Check if the current bar's time is after three_days_ago
    time >= three_days_ago

// Function to draw daily levels efficiently for all timeframes
draw_daily_levels() =>
    if array.size(daily_tdo_values) > 0 and show_daily_levels
        // Clear existing daily lines
        safe_delete_lines(tdo_lines)
        safe_delete_lines(full_adr_up_lines)
        safe_delete_lines(full_adr_down_lines)
        safe_delete_lines(one_third_adr_up_lines)
        safe_delete_lines(one_third_adr_down_lines)
        safe_delete_lines(two_third_adr_up_lines)
        safe_delete_lines(two_third_adr_down_lines)
        safe_delete_lines(half_adr_up_lines)
        safe_delete_lines(half_adr_down_lines)

        // Draw lines regardless of timeframe to ensure visibility
        for i = 0 to math.min(max_periods - 1, array.size(daily_tdo_values) - 1)
            float tdo_price = array.get(daily_tdo_values, i)

            // Draw TDO line using improved line drawing
            tdo_line = draw_improved_line(tdo_price, true_day_open_color, get_line_style(line_style))
            array.push(tdo_lines, tdo_line)

            if show_full_adr
                float full_up = array.get(daily_full_up_values, i)
                float full_down = array.get(daily_full_down_values, i)

                full_up_line = draw_improved_line(full_up, full_adr_color, line_style_value)
                array.push(full_adr_up_lines, full_up_line)

                full_down_line = draw_improved_line(full_down, full_adr_color, line_style_value)
                array.push(full_adr_down_lines, full_down_line)

            if show_one_third_adr
                float one_third_up = array.get(daily_one_third_up_values, i)
                float one_third_down = array.get(daily_one_third_down_values, i)

                one_third_up_line = draw_improved_line(one_third_up, one_third_adr_color, line_style_value)
                array.push(one_third_adr_up_lines, one_third_up_line)

                one_third_down_line = draw_improved_line(one_third_down, one_third_adr_color, line_style_value)
                array.push(one_third_adr_down_lines, one_third_down_line)

            if show_two_third_adr
                float two_third_up = array.get(daily_two_third_up_values, i)
                float two_third_down = array.get(daily_two_third_down_values, i)

                two_third_up_line = draw_improved_line(two_third_up, two_third_adr_color, line_style_value)
                array.push(two_third_adr_up_lines, two_third_up_line)

                two_third_down_line = draw_improved_line(two_third_down, two_third_adr_color, line_style_value)
                array.push(two_third_adr_down_lines, two_third_down_line)

            if show_half_adr
                float half_up = array.get(daily_half_up_values, i)
                float half_down = array.get(daily_half_down_values, i)

                half_up_line = draw_improved_line(half_up, half_adr_color, line_style_value)
                array.push(half_adr_up_lines, half_up_line)

                half_down_line = draw_improved_line(half_down, half_adr_color, line_style_value)
                array.push(half_adr_down_lines, half_down_line)

// Function to draw weekly levels efficiently for all timeframes
draw_weekly_levels() =>
    if array.size(weekly_two_values) > 0 and show_weekly_levels
        // Clear existing weekly lines
        safe_delete_lines(two_lines)
        safe_delete_lines(full_awr_up_lines)
        safe_delete_lines(full_awr_down_lines)
        safe_delete_lines(one_third_awr_up_lines)
        safe_delete_lines(one_third_awr_down_lines)
        safe_delete_lines(two_third_awr_up_lines)
        safe_delete_lines(two_third_awr_down_lines)
        safe_delete_lines(half_awr_up_lines)
        safe_delete_lines(half_awr_down_lines)

        // Draw lines regardless of timeframe to ensure visibility
        for i = 0 to math.min(max_periods - 1, array.size(weekly_two_values) - 1)
            float two_price = array.get(weekly_two_values, i)

            // Draw TWO line using improved line drawing
            two_line = draw_improved_line(two_price, true_week_open_color, get_line_style(line_style))
            array.push(two_lines, two_line)

            if show_full_awr
                float full_up = array.get(weekly_full_up_values, i)
                float full_down = array.get(weekly_full_down_values, i)

                full_up_line = draw_improved_line(full_up, full_awr_color, line_style_value)
                array.push(full_awr_up_lines, full_up_line)

                full_down_line = draw_improved_line(full_down, full_awr_color, line_style_value)
                array.push(full_awr_down_lines, full_down_line)

            if show_one_third_awr
                float one_third_up = array.get(weekly_one_third_up_values, i)
                float one_third_down = array.get(weekly_one_third_down_values, i)

                one_third_up_line = draw_improved_line(one_third_up, one_third_awr_color, line_style_value)
                array.push(one_third_awr_up_lines, one_third_up_line)

                one_third_down_line = draw_improved_line(one_third_down, one_third_awr_color, line_style_value)
                array.push(one_third_awr_down_lines, one_third_down_line)

            if show_two_third_awr
                float two_third_up = array.get(weekly_two_third_up_values, i)
                float two_third_down = array.get(weekly_two_third_down_values, i)

                two_third_up_line = draw_improved_line(two_third_up, two_third_awr_color, line_style_value)
                array.push(two_third_awr_up_lines, two_third_up_line)

                two_third_down_line = draw_improved_line(two_third_down, two_third_awr_color, line_style_value)
                array.push(two_third_awr_down_lines, two_third_down_line)

            if show_half_awr
                float half_up = array.get(weekly_half_up_values, i)
                float half_down = array.get(weekly_half_down_values, i)

                half_up_line = draw_improved_line(half_up, half_awr_color, line_style_value)
                array.push(half_awr_up_lines, half_up_line)

                half_down_line = draw_improved_line(half_down, half_awr_color, line_style_value)
                array.push(half_awr_down_lines, half_down_line)

// Function to draw monthly levels efficiently for all timeframes
draw_monthly_levels() =>
    if array.size(monthly_tmo_values) > 0 and show_monthly_levels
        // Clear existing monthly lines
        safe_delete_lines(tmo_lines)
        safe_delete_lines(full_amr_up_lines)
        safe_delete_lines(full_amr_down_lines)
        safe_delete_lines(one_third_amr_up_lines)
        safe_delete_lines(one_third_amr_down_lines)
        safe_delete_lines(two_third_amr_up_lines)
        safe_delete_lines(two_third_amr_down_lines)
        safe_delete_lines(half_amr_up_lines)
        safe_delete_lines(half_amr_down_lines)

        // Draw lines regardless of timeframe to ensure visibility
        for i = 0 to math.min(max_periods - 1, array.size(monthly_tmo_values) - 1)
            float tmo_price = array.get(monthly_tmo_values, i)

            // Draw TMO line using improved line drawing
            tmo_line = draw_improved_line(tmo_price, true_month_open_color, get_line_style(line_style))
            array.push(tmo_lines, tmo_line)

            if show_full_amr
                float full_up = array.get(monthly_full_up_values, i)
                float full_down = array.get(monthly_full_down_values, i)

                full_up_line = draw_improved_line(full_up, full_amr_color, line_style_value)
                array.push(full_amr_up_lines, full_up_line)

                full_down_line = draw_improved_line(full_down, full_amr_color, line_style_value)
                array.push(full_amr_down_lines, full_down_line)

            if show_one_third_amr
                float one_third_up = array.get(monthly_one_third_up_values, i)
                float one_third_down = array.get(monthly_one_third_down_values, i)

                one_third_up_line = draw_improved_line(one_third_up, one_third_amr_color, line_style_value)
                array.push(one_third_amr_up_lines, one_third_up_line)

                one_third_down_line = draw_improved_line(one_third_down, one_third_amr_color, line_style_value)
                array.push(one_third_amr_down_lines, one_third_down_line)

            if show_two_third_amr
                float two_third_up = array.get(monthly_two_third_up_values, i)
                float two_third_down = array.get(monthly_two_third_down_values, i)

                two_third_up_line = draw_improved_line(two_third_up, two_third_amr_color, line_style_value)
                array.push(two_third_amr_up_lines, two_third_up_line)

                two_third_down_line = draw_improved_line(two_third_down, two_third_amr_color, line_style_value)
                array.push(two_third_amr_down_lines, two_third_down_line)

            if show_half_amr
                float half_up = array.get(monthly_half_up_values, i)
                float half_down = array.get(monthly_half_down_values, i)

                half_up_line = draw_improved_line(half_up, half_amr_color, line_style_value)
                array.push(half_amr_up_lines, half_up_line)

                half_down_line = draw_improved_line(half_down, half_amr_color, line_style_value)
                array.push(half_amr_down_lines, half_down_line)

// === MAIN LOGIC ===
// Calculate range values
float current_adr = calculate_adr(adr_days)
float current_awr = calculate_awr(awr_weeks)
float current_amr = calculate_amr(amr_months)

// Special initialization for monthly levels on the first bar
if barstate.isfirst and show_monthly_levels and not true_month_line_active
    monthly_range_val := current_amr
    true_month_open_price := close
    true_month_line_active := true

    // Calculate AMR levels
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Store the level values and timestamp in arrays
    array.unshift(monthly_tmo_values, true_month_open_price)
    array.unshift(monthly_full_up_values, full_amr_up)
    array.unshift(monthly_full_down_values, full_amr_down)
    array.unshift(monthly_one_third_up_values, one_third_amr_up)
    array.unshift(monthly_one_third_down_values, one_third_amr_down)
    array.unshift(monthly_two_third_up_values, two_third_amr_up)
    array.unshift(monthly_two_third_down_values, two_third_amr_down)
    array.unshift(monthly_half_up_values, half_amr_up)
    array.unshift(monthly_half_down_values, half_amr_down)
    array.unshift(monthly_timestamps, int(time))
    array.unshift(monthly_bar_indices, bar_index)

    // Use our improved drawing method for all timeframes
    draw_monthly_levels()

    // Create true month open label
    if show_labels
        true_month_open_label := label.new(bar_index + label_x_offset_bars, true_month_open_price + label_y_offset, "TMO" + (show_price_in_label ? str.format(" ({0})", true_month_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_month_open_color, color=color.new(color.black, 100))
        array.push(tmo_labels, true_month_open_label)

    // Create AMR level lines
    if show_full_amr
        // Full AMR up line
        full_amr_up_line := line.new(bar_index, full_amr_up, bar_index, full_amr_up, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_up_lines, full_amr_up_line)

        // Full AMR up label
        if show_labels
            full_amr_up_label := label.new(bar_index + label_x_offset_bars, full_amr_up + label_y_offset, "AMR+" + (show_price_in_label ? str.format(" ({0})", full_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_up_labels, full_amr_up_label)

        // Full AMR down line
        full_amr_down_line := line.new(bar_index, full_amr_down, bar_index, full_amr_down, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_down_lines, full_amr_down_line)

        // Full AMR down label
        if show_labels
            full_amr_down_label := label.new(bar_index + label_x_offset_bars, full_amr_down + label_y_offset, "AMR-" + (show_price_in_label ? str.format(" ({0})", full_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_down_labels, full_amr_down_label)

    if show_one_third_amr
        // 1/3 AMR up line
        one_third_amr_up_line := line.new(bar_index, one_third_amr_up, bar_index, one_third_amr_up, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_up_lines, one_third_amr_up_line)

        // 1/3 AMR up label
        if show_labels
            one_third_amr_up_label := label.new(bar_index + label_x_offset_bars, one_third_amr_up + label_y_offset, "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", one_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_up_labels, one_third_amr_up_label)

        // 1/3 AMR down line
        one_third_amr_down_line := line.new(bar_index, one_third_amr_down, bar_index, one_third_amr_down, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_down_lines, one_third_amr_down_line)

        // 1/3 AMR down label
        if show_labels
            one_third_amr_down_label := label.new(bar_index + label_x_offset_bars, one_third_amr_down + label_y_offset, "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", one_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_down_labels, one_third_amr_down_label)

    if show_two_third_amr
        // 2/3 AMR up line
        two_third_amr_up_line := line.new(bar_index, two_third_amr_up, bar_index, two_third_amr_up, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_up_lines, two_third_amr_up_line)

        // 2/3 AMR up label
        if show_labels
            two_third_amr_up_label := label.new(bar_index + label_x_offset_bars, two_third_amr_up + label_y_offset, "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", two_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_up_labels, two_third_amr_up_label)

        // 2/3 AMR down line
        two_third_amr_down_line := line.new(bar_index, two_third_amr_down, bar_index, two_third_amr_down, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_down_lines, two_third_amr_down_line)

        // 2/3 AMR down label
        if show_labels
            two_third_amr_down_label := label.new(bar_index + label_x_offset_bars, two_third_amr_down + label_y_offset, "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", two_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_down_labels, two_third_amr_down_label)

    if show_half_amr
        // 1/2 AMR up line
        half_amr_up_line := line.new(bar_index, half_amr_up, bar_index, half_amr_up, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_up_lines, half_amr_up_line)

        // 1/2 AMR up label
        if show_labels
            half_amr_up_label := label.new(bar_index + label_x_offset_bars, half_amr_up + label_y_offset, "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", half_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_up_labels, half_amr_up_label)

        // 1/2 AMR down line
        half_amr_down_line := line.new(bar_index, half_amr_down, bar_index, half_amr_down, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_down_lines, half_amr_down_line)

        // 1/2 AMR down label
        if show_labels
            half_amr_down_label := label.new(bar_index + label_x_offset_bars, half_amr_down + label_y_offset, "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", half_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_down_labels, half_amr_down_label)

// Check if we're at the start of a new day
if is_new_day and show_daily_levels
    // Store the current ADR value in history and for today's use
    array.unshift(daily_range_history, current_adr)
    if array.size(daily_range_history) > adr_days
        array.pop(daily_range_history)

    // Update the daily range value for today
    daily_range_val := current_adr

    // Update true day open price
    true_day_open_price := barstate.isfirst ? close : open
    true_day_line_active := true

    // Calculate ADR levels
    full_adr_up := true_day_open_price + daily_range_val
    full_adr_down := true_day_open_price - daily_range_val
    one_third_adr_up := true_day_open_price + (daily_range_val / 3)
    one_third_adr_down := true_day_open_price - (daily_range_val / 3)
    two_third_adr_up := true_day_open_price + (daily_range_val * 2 / 3)
    two_third_adr_down := true_day_open_price - (daily_range_val * 2 / 3)
    half_adr_up := true_day_open_price + (daily_range_val / 2)
    half_adr_down := true_day_open_price - (daily_range_val / 2)

    // Store the level values and timestamp in arrays
    array.unshift(daily_tdo_values, true_day_open_price)
    array.unshift(daily_full_up_values, full_adr_up)
    array.unshift(daily_full_down_values, full_adr_down)
    array.unshift(daily_one_third_up_values, one_third_adr_up)
    array.unshift(daily_one_third_down_values, one_third_adr_down)
    array.unshift(daily_two_third_up_values, two_third_adr_up)
    array.unshift(daily_two_third_down_values, two_third_adr_down)
    array.unshift(daily_half_up_values, half_adr_up)
    array.unshift(daily_half_down_values, half_adr_down)
    array.unshift(daily_timestamps, int(time))
    array.unshift(daily_bar_indices, bar_index)

    // Keep only the most recent days (max_periods)
    if array.size(daily_tdo_values) > max_periods
        array.pop(daily_tdo_values)
        array.pop(daily_full_up_values)
        array.pop(daily_full_down_values)
        array.pop(daily_one_third_up_values)
        array.pop(daily_one_third_down_values)
        array.pop(daily_two_third_up_values)
        array.pop(daily_two_third_down_values)
        array.pop(daily_half_up_values)
        array.pop(daily_half_down_values)
        array.pop(daily_timestamps)
        array.pop(daily_bar_indices)

    // Use our improved drawing method for all timeframes
    draw_daily_levels()

    // Delete old labels when a new TDO appears
    if show_labels
        delete_all_labels(tdo_labels)
        delete_all_labels(full_adr_up_labels)
        delete_all_labels(full_adr_down_labels)
        delete_all_labels(one_third_adr_up_labels)
        delete_all_labels(one_third_adr_down_labels)
        delete_all_labels(two_third_adr_up_labels)
        delete_all_labels(two_third_adr_down_labels)
        delete_all_labels(half_adr_up_labels)
        delete_all_labels(half_adr_down_labels)

        // Create true day open label
        true_day_open_label := label.new(bar_index + label_x_offset_bars, true_day_open_price + label_y_offset, "TDO" + (show_price_in_label ? str.format(" ({0})", true_day_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_day_open_color, color=color.new(color.black, 100))
        array.push(tdo_labels, true_day_open_label)

    // Create vertical line at day open
    if show_vertical
        true_day_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_day_open_color, style=line.style_dashed)
        array.unshift(daily_vertical_lines, true_day_vertical)
        manage_line_history(daily_vertical_lines)

        // Create ADR level lines
        if show_full_adr
            // Full ADR up line
            full_adr_up_line := line.new(bar_index, full_adr_up, bar_index, full_adr_up, color=full_adr_color, width=line_width, style=line_style_value)
            array.unshift(full_adr_up_lines, full_adr_up_line)
            manage_line_history(full_adr_up_lines)

            // Full ADR up label
            if show_labels
                full_adr_up_label := label.new(bar_index + label_x_offset_bars, full_adr_up + label_y_offset, "ADR+" + (show_price_in_label ? str.format(" ({0})", full_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
                array.push(full_adr_up_labels, full_adr_up_label)

            // Full ADR down line
            full_adr_down_line := line.new(bar_index, full_adr_down, bar_index, full_adr_down, color=full_adr_color, width=line_width, style=line_style_value)
            array.unshift(full_adr_down_lines, full_adr_down_line)
            manage_line_history(full_adr_down_lines)

            // Full ADR down label
            if show_labels
                full_adr_down_label := label.new(bar_index + label_x_offset_bars, full_adr_down + label_y_offset, "ADR-" + (show_price_in_label ? str.format(" ({0})", full_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_adr_color, color=color.new(color.black, 100))
                array.push(full_adr_down_labels, full_adr_down_label)

        // Create 1/3 ADR level lines
        if show_one_third_adr
            // 1/3 ADR up line
            one_third_adr_up_line := line.new(bar_index, one_third_adr_up, bar_index, one_third_adr_up, color=one_third_adr_color, width=line_width, style=line_style_value)
            array.unshift(one_third_adr_up_lines, one_third_adr_up_line)
            manage_line_history(one_third_adr_up_lines)

            // 1/3 ADR up label
            if show_labels
                one_third_adr_up_label := label.new(bar_index + label_x_offset_bars, one_third_adr_up + label_y_offset, "1/3 ADR+" + (show_price_in_label ? str.format(" ({0})", one_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
                array.push(one_third_adr_up_labels, one_third_adr_up_label)

            // 1/3 ADR down line
            one_third_adr_down_line := line.new(bar_index, one_third_adr_down, bar_index, one_third_adr_down, color=one_third_adr_color, width=line_width, style=line_style_value)
            array.unshift(one_third_adr_down_lines, one_third_adr_down_line)
            manage_line_history(one_third_adr_down_lines)

            // 1/3 ADR down label
            if show_labels
                one_third_adr_down_label := label.new(bar_index + label_x_offset_bars, one_third_adr_down + label_y_offset, "1/3 ADR-" + (show_price_in_label ? str.format(" ({0})", one_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_adr_color, color=color.new(color.black, 100))
                array.push(one_third_adr_down_labels, one_third_adr_down_label)

        // Create 2/3 ADR level lines
        if show_two_third_adr
            // 2/3 ADR up line
            two_third_adr_up_line := line.new(bar_index, two_third_adr_up, bar_index, two_third_adr_up, color=two_third_adr_color, width=line_width, style=line_style_value)
            array.unshift(two_third_adr_up_lines, two_third_adr_up_line)
            manage_line_history(two_third_adr_up_lines)

            // 2/3 ADR up label
            if show_labels
                two_third_adr_up_label := label.new(bar_index + label_x_offset_bars, two_third_adr_up + label_y_offset, "2/3 ADR+" + (show_price_in_label ? str.format(" ({0})", two_third_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
                array.push(two_third_adr_up_labels, two_third_adr_up_label)

            // 2/3 ADR down line
            two_third_adr_down_line := line.new(bar_index, two_third_adr_down, bar_index, two_third_adr_down, color=two_third_adr_color, width=line_width, style=line_style_value)
            array.unshift(two_third_adr_down_lines, two_third_adr_down_line)
            manage_line_history(two_third_adr_down_lines)

            // 2/3 ADR down label
            if show_labels
                two_third_adr_down_label := label.new(bar_index + label_x_offset_bars, two_third_adr_down + label_y_offset, "2/3 ADR-" + (show_price_in_label ? str.format(" ({0})", two_third_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_adr_color, color=color.new(color.black, 100))
                array.push(two_third_adr_down_labels, two_third_adr_down_label)

        // Create 1/2 ADR level lines
        if show_half_adr
            // 1/2 ADR up line
            half_adr_up_line := line.new(bar_index, half_adr_up, bar_index, half_adr_up, color=half_adr_color, width=line_width, style=line_style_value)
            array.unshift(half_adr_up_lines, half_adr_up_line)
            manage_line_history(half_adr_up_lines)

            // 1/2 ADR up label
            if show_labels
                half_adr_up_label := label.new(bar_index + label_x_offset_bars, half_adr_up + label_y_offset, "1/2 ADR+" + (show_price_in_label ? str.format(" ({0})", half_adr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
                array.push(half_adr_up_labels, half_adr_up_label)

            // 1/2 ADR down line
            half_adr_down_line := line.new(bar_index, half_adr_down, bar_index, half_adr_down, color=half_adr_color, width=line_width, style=line_style_value)
            array.unshift(half_adr_down_lines, half_adr_down_line)
            manage_line_history(half_adr_down_lines)

            // 1/2 ADR down label
            if show_labels
                half_adr_down_label := label.new(bar_index + label_x_offset_bars, half_adr_down + label_y_offset, "1/2 ADR-" + (show_price_in_label ? str.format(" ({0})", half_adr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_adr_color, color=color.new(color.black, 100))
                array.push(half_adr_down_labels, half_adr_down_label)

// Check if we're at the start of a new week
if is_new_week and show_weekly_levels
    // Store the current AWR value in history and for this week's use
    array.unshift(weekly_range_history, current_awr)
    if array.size(weekly_range_history) > awr_weeks
        array.pop(weekly_range_history)

    // Update the weekly range value for this week
    weekly_range_val := current_awr

    // Update true week open price
    true_week_open_price := barstate.isfirst ? close : open
    true_week_line_active := true

    // Calculate AWR levels
    full_awr_up := true_week_open_price + weekly_range_val
    full_awr_down := true_week_open_price - weekly_range_val
    one_third_awr_up := true_week_open_price + (weekly_range_val / 3)
    one_third_awr_down := true_week_open_price - (weekly_range_val / 3)
    two_third_awr_up := true_week_open_price + (weekly_range_val * 2 / 3)
    two_third_awr_down := true_week_open_price - (weekly_range_val * 2 / 3)
    half_awr_up := true_week_open_price + (weekly_range_val / 2)
    half_awr_down := true_week_open_price - (weekly_range_val / 2)

    // Store the level values and timestamp in arrays
    array.unshift(weekly_two_values, true_week_open_price)
    array.unshift(weekly_full_up_values, full_awr_up)
    array.unshift(weekly_full_down_values, full_awr_down)
    array.unshift(weekly_one_third_up_values, one_third_awr_up)
    array.unshift(weekly_one_third_down_values, one_third_awr_down)
    array.unshift(weekly_two_third_up_values, two_third_awr_up)
    array.unshift(weekly_two_third_down_values, two_third_awr_down)
    array.unshift(weekly_half_up_values, half_awr_up)
    array.unshift(weekly_half_down_values, half_awr_down)
    array.unshift(weekly_timestamps, int(time))
    array.unshift(weekly_bar_indices, bar_index)

    // Keep only the most recent weeks (max_periods)
    if array.size(weekly_two_values) > max_periods
        array.pop(weekly_two_values)
        array.pop(weekly_full_up_values)
        array.pop(weekly_full_down_values)
        array.pop(weekly_one_third_up_values)
        array.pop(weekly_one_third_down_values)
        array.pop(weekly_two_third_up_values)
        array.pop(weekly_two_third_down_values)
        array.pop(weekly_half_up_values)
        array.pop(weekly_half_down_values)
        array.pop(weekly_timestamps)
        array.pop(weekly_bar_indices)

    // Use our improved drawing method for all timeframes
    draw_weekly_levels()

    // Delete old labels when a new TWO appears
    if show_labels
        delete_all_labels(two_labels)
        delete_all_labels(full_awr_up_labels)
        delete_all_labels(full_awr_down_labels)
        delete_all_labels(one_third_awr_up_labels)
        delete_all_labels(one_third_awr_down_labels)
        delete_all_labels(two_third_awr_up_labels)
        delete_all_labels(two_third_awr_down_labels)
        delete_all_labels(half_awr_up_labels)
        delete_all_labels(half_awr_down_labels)

        // Create true week open label
        true_week_open_label := label.new(bar_index + label_x_offset_bars, true_week_open_price + label_y_offset, "TWO" + (show_price_in_label ? str.format(" ({0})", true_week_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_week_open_color, color=color.new(color.black, 100))
        array.push(two_labels, true_week_open_label)

    // Create vertical line at week open
    if show_vertical
        true_week_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_week_open_color, style=line.style_dashed)
        array.unshift(weekly_vertical_lines, true_week_vertical)
        manage_line_history(weekly_vertical_lines)

        // Create AWR level lines
        if show_full_awr
            // Full AWR up line
            full_awr_up_line := line.new(bar_index, full_awr_up, bar_index, full_awr_up, color=full_awr_color, width=line_width, style=line_style_value)
            array.unshift(full_awr_up_lines, full_awr_up_line)
            manage_line_history(full_awr_up_lines)

            // Full AWR up label
            if show_labels
                full_awr_up_label := label.new(bar_index + label_x_offset_bars, full_awr_up + label_y_offset, "AWR+" + (show_price_in_label ? str.format(" ({0})", full_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_awr_color, color=color.new(color.black, 100))
                array.push(full_awr_up_labels, full_awr_up_label)

            // Full AWR down line
            full_awr_down_line := line.new(bar_index, full_awr_down, bar_index, full_awr_down, color=full_awr_color, width=line_width, style=line_style_value)
            array.unshift(full_awr_down_lines, full_awr_down_line)
            manage_line_history(full_awr_down_lines)

            // Full AWR down label
            if show_labels
                full_awr_down_label := label.new(bar_index + label_x_offset_bars, full_awr_down + label_y_offset, "AWR-" + (show_price_in_label ? str.format(" ({0})", full_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_awr_color, color=color.new(color.black, 100))
                array.push(full_awr_down_labels, full_awr_down_label)

        // Create 1/3 AWR level lines
        if show_one_third_awr
            // 1/3 AWR up line
            one_third_awr_up_line := line.new(bar_index, one_third_awr_up, bar_index, one_third_awr_up, color=one_third_awr_color, width=line_width, style=line_style_value)
            array.unshift(one_third_awr_up_lines, one_third_awr_up_line)
            manage_line_history(one_third_awr_up_lines)

            // 1/3 AWR up label
            if show_labels
                one_third_awr_up_label := label.new(bar_index + label_x_offset_bars, one_third_awr_up + label_y_offset, "1/3 AWR+" + (show_price_in_label ? str.format(" ({0})", one_third_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_awr_color, color=color.new(color.black, 100))
                array.push(one_third_awr_up_labels, one_third_awr_up_label)

            // 1/3 AWR down line
            one_third_awr_down_line := line.new(bar_index, one_third_awr_down, bar_index, one_third_awr_down, color=one_third_awr_color, width=line_width, style=line_style_value)
            array.unshift(one_third_awr_down_lines, one_third_awr_down_line)
            manage_line_history(one_third_awr_down_lines)

            // 1/3 AWR down label
            if show_labels
                one_third_awr_down_label := label.new(bar_index + label_x_offset_bars, one_third_awr_down + label_y_offset, "1/3 AWR-" + (show_price_in_label ? str.format(" ({0})", one_third_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_awr_color, color=color.new(color.black, 100))
                array.push(one_third_awr_down_labels, one_third_awr_down_label)

        // Create 2/3 AWR level lines
        if show_two_third_awr
            // 2/3 AWR up line
            two_third_awr_up_line := line.new(bar_index, two_third_awr_up, bar_index, two_third_awr_up, color=two_third_awr_color, width=line_width, style=line_style_value)
            array.unshift(two_third_awr_up_lines, two_third_awr_up_line)
            manage_line_history(two_third_awr_up_lines)

            // 2/3 AWR up label
            if show_labels
                two_third_awr_up_label := label.new(bar_index + label_x_offset_bars, two_third_awr_up + label_y_offset, "2/3 AWR+" + (show_price_in_label ? str.format(" ({0})", two_third_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_awr_color, color=color.new(color.black, 100))
                array.push(two_third_awr_up_labels, two_third_awr_up_label)

            // 2/3 AWR down line
            two_third_awr_down_line := line.new(bar_index, two_third_awr_down, bar_index, two_third_awr_down, color=two_third_awr_color, width=line_width, style=line_style_value)
            array.unshift(two_third_awr_down_lines, two_third_awr_down_line)
            manage_line_history(two_third_awr_down_lines)

            // 2/3 AWR down label
            if show_labels
                two_third_awr_down_label := label.new(bar_index + label_x_offset_bars, two_third_awr_down + label_y_offset, "2/3 AWR-" + (show_price_in_label ? str.format(" ({0})", two_third_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_awr_color, color=color.new(color.black, 100))
                array.push(two_third_awr_down_labels, two_third_awr_down_label)

        // Create 1/2 AWR level lines
        if show_half_awr
            // 1/2 AWR up line
            half_awr_up_line := line.new(bar_index, half_awr_up, bar_index, half_awr_up, color=half_awr_color, width=line_width, style=line_style_value)
            array.unshift(half_awr_up_lines, half_awr_up_line)
            manage_line_history(half_awr_up_lines)

            // 1/2 AWR up label
            if show_labels
                half_awr_up_label := label.new(bar_index + label_x_offset_bars, half_awr_up + label_y_offset, "1/2 AWR+" + (show_price_in_label ? str.format(" ({0})", half_awr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_awr_color, color=color.new(color.black, 100))
                array.push(half_awr_up_labels, half_awr_up_label)

            // 1/2 AWR down line
            half_awr_down_line := line.new(bar_index, half_awr_down, bar_index, half_awr_down, color=half_awr_color, width=line_width, style=line_style_value)
            array.unshift(half_awr_down_lines, half_awr_down_line)
            manage_line_history(half_awr_down_lines)

            // 1/2 AWR down label
            if show_labels
                half_awr_down_label := label.new(bar_index + label_x_offset_bars, half_awr_down + label_y_offset, "1/2 AWR-" + (show_price_in_label ? str.format(" ({0})", half_awr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_awr_color, color=color.new(color.black, 100))
                array.push(half_awr_down_labels, half_awr_down_label)

// Check if we're at the start of a new month
if is_new_month and show_monthly_levels
    // Store the current AMR value in history and for this month's use
    array.unshift(monthly_range_history, current_amr)
    if array.size(monthly_range_history) > amr_months
        array.pop(monthly_range_history)

    // Update the monthly range value for this month
    monthly_range_val := current_amr

    // Update true month open price
    true_month_open_price := barstate.isfirst ? close : open
    true_month_line_active := true

    // Calculate AMR levels
    full_amr_up := true_month_open_price + monthly_range_val
    full_amr_down := true_month_open_price - monthly_range_val
    one_third_amr_up := true_month_open_price + (monthly_range_val / 3)
    one_third_amr_down := true_month_open_price - (monthly_range_val / 3)
    two_third_amr_up := true_month_open_price + (monthly_range_val * 2 / 3)
    two_third_amr_down := true_month_open_price - (monthly_range_val * 2 / 3)
    half_amr_up := true_month_open_price + (monthly_range_val / 2)
    half_amr_down := true_month_open_price - (monthly_range_val / 2)

    // Store the level values and timestamp in arrays
    array.unshift(monthly_tmo_values, true_month_open_price)
    array.unshift(monthly_full_up_values, full_amr_up)
    array.unshift(monthly_full_down_values, full_amr_down)
    array.unshift(monthly_one_third_up_values, one_third_amr_up)
    array.unshift(monthly_one_third_down_values, one_third_amr_down)
    array.unshift(monthly_two_third_up_values, two_third_amr_up)
    array.unshift(monthly_two_third_down_values, two_third_amr_down)
    array.unshift(monthly_half_up_values, half_amr_up)
    array.unshift(monthly_half_down_values, half_amr_down)
    array.unshift(monthly_timestamps, int(time))
    array.unshift(monthly_bar_indices, bar_index)

    // Keep only the most recent months (max_periods)
    if array.size(monthly_tmo_values) > max_periods
        array.pop(monthly_tmo_values)
        array.pop(monthly_full_up_values)
        array.pop(monthly_full_down_values)
        array.pop(monthly_one_third_up_values)
        array.pop(monthly_one_third_down_values)
        array.pop(monthly_two_third_up_values)
        array.pop(monthly_two_third_down_values)
        array.pop(monthly_half_up_values)
        array.pop(monthly_half_down_values)
        array.pop(monthly_timestamps)
        array.pop(monthly_bar_indices)

    // Use our improved drawing method for all timeframes
    draw_monthly_levels()

    // Delete old labels when a new TMO appears
    if show_labels
        delete_all_labels(tmo_labels)
        delete_all_labels(full_amr_up_labels)
        delete_all_labels(full_amr_down_labels)
        delete_all_labels(one_third_amr_up_labels)
        delete_all_labels(one_third_amr_down_labels)
        delete_all_labels(two_third_amr_up_labels)
        delete_all_labels(two_third_amr_down_labels)
        delete_all_labels(half_amr_up_labels)
        delete_all_labels(half_amr_down_labels)

        // Create true month open label
        true_month_open_label := label.new(bar_index + label_x_offset_bars, true_month_open_price + label_y_offset, "TMO" + (show_price_in_label ? str.format(" ({0})", true_month_open_price) : ""), style=label.style_label_left, size=label_size_value, textcolor=true_month_open_color, color=color.new(color.black, 100))
        array.push(tmo_labels, true_month_open_label)

    // Create vertical line at month open
    if show_vertical
        true_month_vertical := line.new(bar_index, high, bar_index, low, extend=extend.both, color=true_month_open_color, style=line.style_dashed)
        array.unshift(monthly_vertical_lines, true_month_vertical)
        manage_line_history(monthly_vertical_lines)

    // Create AMR level lines
    if show_full_amr
        // Full AMR up line
        full_amr_up_line := line.new(bar_index, full_amr_up, bar_index, full_amr_up, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_up_lines, full_amr_up_line)
        manage_line_history(full_amr_up_lines)

        // Full AMR up label
        if show_labels
            full_amr_up_label := label.new(bar_index + label_x_offset_bars, full_amr_up + label_y_offset, "AMR+" + (show_price_in_label ? str.format(" ({0})", full_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_up_labels, full_amr_up_label)

        // Full AMR down line
        full_amr_down_line := line.new(bar_index, full_amr_down, bar_index, full_amr_down, color=full_amr_color, width=line_width, style=line_style_value)
        array.unshift(full_amr_down_lines, full_amr_down_line)
        manage_line_history(full_amr_down_lines)

        // Full AMR down label
        if show_labels
            full_amr_down_label := label.new(bar_index + label_x_offset_bars, full_amr_down + label_y_offset, "AMR-" + (show_price_in_label ? str.format(" ({0})", full_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=full_amr_color, color=color.new(color.black, 100))
            array.push(full_amr_down_labels, full_amr_down_label)

    // Create 1/3 AMR level lines
    if show_one_third_amr
        // 1/3 AMR up line
        one_third_amr_up_line := line.new(bar_index, one_third_amr_up, bar_index, one_third_amr_up, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_up_lines, one_third_amr_up_line)
        manage_line_history(one_third_amr_up_lines)

        // 1/3 AMR up label
        if show_labels
            one_third_amr_up_label := label.new(bar_index + label_x_offset_bars, one_third_amr_up + label_y_offset, "1/3 AMR+" + (show_price_in_label ? str.format(" ({0})", one_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_up_labels, one_third_amr_up_label)

        // 1/3 AMR down line
        one_third_amr_down_line := line.new(bar_index, one_third_amr_down, bar_index, one_third_amr_down, color=one_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(one_third_amr_down_lines, one_third_amr_down_line)
        manage_line_history(one_third_amr_down_lines)

        // 1/3 AMR down label
        if show_labels
            one_third_amr_down_label := label.new(bar_index + label_x_offset_bars, one_third_amr_down + label_y_offset, "1/3 AMR-" + (show_price_in_label ? str.format(" ({0})", one_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=one_third_amr_color, color=color.new(color.black, 100))
            array.push(one_third_amr_down_labels, one_third_amr_down_label)

    // Create 2/3 AMR level lines
    if show_two_third_amr
        // 2/3 AMR up line
        two_third_amr_up_line := line.new(bar_index, two_third_amr_up, bar_index, two_third_amr_up, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_up_lines, two_third_amr_up_line)
        manage_line_history(two_third_amr_up_lines)

        // 2/3 AMR up label
        if show_labels
            two_third_amr_up_label := label.new(bar_index + label_x_offset_bars, two_third_amr_up + label_y_offset, "2/3 AMR+" + (show_price_in_label ? str.format(" ({0})", two_third_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_up_labels, two_third_amr_up_label)

        // 2/3 AMR down line
        two_third_amr_down_line := line.new(bar_index, two_third_amr_down, bar_index, two_third_amr_down, color=two_third_amr_color, width=line_width, style=line_style_value)
        array.unshift(two_third_amr_down_lines, two_third_amr_down_line)
        manage_line_history(two_third_amr_down_lines)

        // 2/3 AMR down label
        if show_labels
            two_third_amr_down_label := label.new(bar_index + label_x_offset_bars, two_third_amr_down + label_y_offset, "2/3 AMR-" + (show_price_in_label ? str.format(" ({0})", two_third_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=two_third_amr_color, color=color.new(color.black, 100))
            array.push(two_third_amr_down_labels, two_third_amr_down_label)

    // Create 1/2 AMR level lines
    if show_half_amr
        // 1/2 AMR up line
        half_amr_up_line := line.new(bar_index, half_amr_up, bar_index, half_amr_up, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_up_lines, half_amr_up_line)
        manage_line_history(half_amr_up_lines)

        // 1/2 AMR up label
        if show_labels
            half_amr_up_label := label.new(bar_index + label_x_offset_bars, half_amr_up + label_y_offset, "1/2 AMR+" + (show_price_in_label ? str.format(" ({0})", half_amr_up) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_up_labels, half_amr_up_label)

        // 1/2 AMR down line
        half_amr_down_line := line.new(bar_index, half_amr_down, bar_index, half_amr_down, color=half_amr_color, width=line_width, style=line_style_value)
        array.unshift(half_amr_down_lines, half_amr_down_line)
        manage_line_history(half_amr_down_lines)

        // 1/2 AMR down label
        if show_labels
            half_amr_down_label := label.new(bar_index + label_x_offset_bars, half_amr_down + label_y_offset, "1/2 AMR-" + (show_price_in_label ? str.format(" ({0})", half_amr_down) : ""), style=label.style_label_left, size=label_size_value, textcolor=half_amr_color, color=color.new(color.black, 100))
            array.push(half_amr_down_labels, half_amr_down_label)

// Update lines during the current day
if in_current_day and true_day_line_active
    // Use our improved drawing method for all timeframes
    if barstate.islast or (bar_index % 100 == 0)  // Only redraw periodically to improve performance
        draw_daily_levels()
    else
        // For other timeframes, use the standard approach
        // Update true day open line
        line.set_x2(true_day_open_line, bar_index)
        line.set_y2(true_day_open_line, true_day_open_price)

        // Update true day open label
        if show_labels
            label.set_x(true_day_open_label, bar_index + label_x_offset_bars)
            label.set_y(true_day_open_label, true_day_open_price + label_y_offset)

        // Update ADR level lines
        if show_full_adr
            line.set_x2(full_adr_up_line, bar_index)
            line.set_y2(full_adr_up_line, full_adr_up)
            line.set_x2(full_adr_down_line, bar_index)
            line.set_y2(full_adr_down_line, full_adr_down)

            if show_labels
                label.set_x(full_adr_up_label, bar_index + label_x_offset_bars)
                label.set_y(full_adr_up_label, full_adr_up + label_y_offset)
                label.set_x(full_adr_down_label, bar_index + label_x_offset_bars)
                label.set_y(full_adr_down_label, full_adr_down + label_y_offset)

        if show_one_third_adr
            line.set_x2(one_third_adr_up_line, bar_index)
            line.set_y2(one_third_adr_up_line, one_third_adr_up)
            line.set_x2(one_third_adr_down_line, bar_index)
            line.set_y2(one_third_adr_down_line, one_third_adr_down)

            if show_labels
                label.set_x(one_third_adr_up_label, bar_index + label_x_offset_bars)
                label.set_y(one_third_adr_up_label, one_third_adr_up + label_y_offset)
                label.set_x(one_third_adr_down_label, bar_index + label_x_offset_bars)
                label.set_y(one_third_adr_down_label, one_third_adr_down + label_y_offset)

        if show_two_third_adr
            line.set_x2(two_third_adr_up_line, bar_index)
            line.set_y2(two_third_adr_up_line, two_third_adr_up)
            line.set_x2(two_third_adr_down_line, bar_index)
            line.set_y2(two_third_adr_down_line, two_third_adr_down)

            if show_labels
                label.set_x(two_third_adr_up_label, bar_index + label_x_offset_bars)
                label.set_y(two_third_adr_up_label, two_third_adr_up + label_y_offset)
                label.set_x(two_third_adr_down_label, bar_index + label_x_offset_bars)
                label.set_y(two_third_adr_down_label, two_third_adr_down + label_y_offset)

        if show_half_adr
            line.set_x2(half_adr_up_line, bar_index)
            line.set_y2(half_adr_up_line, half_adr_up)
            line.set_x2(half_adr_down_line, bar_index)
            line.set_y2(half_adr_down_line, half_adr_down)

            if show_labels
                label.set_x(half_adr_up_label, bar_index + label_x_offset_bars)
                label.set_y(half_adr_up_label, half_adr_up + label_y_offset)
                label.set_x(half_adr_down_label, bar_index + label_x_offset_bars)
                label.set_y(half_adr_down_label, half_adr_down + label_y_offset)

// Update lines during the current week
if in_current_week and true_week_line_active and show_weekly_levels
    // Use our improved drawing method for all timeframes
    if barstate.islast or (bar_index % 100 == 0)  // Only redraw periodically to improve performance
        draw_weekly_levels()
    else
        // For other timeframes, use the standard approach
        // Update true week open line
        line.set_x2(true_week_open_line, bar_index)
        line.set_y2(true_week_open_line, true_week_open_price)

        // Update true week open label
        if show_labels
            label.set_x(true_week_open_label, bar_index + label_x_offset_bars)
            label.set_y(true_week_open_label, true_week_open_price + label_y_offset)

        // Update AWR level lines
        if show_full_awr
            line.set_x2(full_awr_up_line, bar_index)
            line.set_y2(full_awr_up_line, full_awr_up)
            line.set_x2(full_awr_down_line, bar_index)
            line.set_y2(full_awr_down_line, full_awr_down)

            if show_labels
                label.set_x(full_awr_up_label, bar_index + label_x_offset_bars)
                label.set_y(full_awr_up_label, full_awr_up + label_y_offset)
                label.set_x(full_awr_down_label, bar_index + label_x_offset_bars)
                label.set_y(full_awr_down_label, full_awr_down + label_y_offset)

        if show_one_third_awr
            line.set_x2(one_third_awr_up_line, bar_index)
            line.set_y2(one_third_awr_up_line, one_third_awr_up)
            line.set_x2(one_third_awr_down_line, bar_index)
            line.set_y2(one_third_awr_down_line, one_third_awr_down)

            if show_labels
                label.set_x(one_third_awr_up_label, bar_index + label_x_offset_bars)
                label.set_y(one_third_awr_up_label, one_third_awr_up + label_y_offset)
                label.set_x(one_third_awr_down_label, bar_index + label_x_offset_bars)
                label.set_y(one_third_awr_down_label, one_third_awr_down + label_y_offset)

        if show_two_third_awr
            line.set_x2(two_third_awr_up_line, bar_index)
            line.set_y2(two_third_awr_up_line, two_third_awr_up)
            line.set_x2(two_third_awr_down_line, bar_index)
            line.set_y2(two_third_awr_down_line, two_third_awr_down)

            if show_labels
                label.set_x(two_third_awr_up_label, bar_index + label_x_offset_bars)
                label.set_y(two_third_awr_up_label, two_third_awr_up + label_y_offset)
                label.set_x(two_third_awr_down_label, bar_index + label_x_offset_bars)
                label.set_y(two_third_awr_down_label, two_third_awr_down + label_y_offset)

        if show_half_awr
            line.set_x2(half_awr_up_line, bar_index)
            line.set_y2(half_awr_up_line, half_awr_up)
            line.set_x2(half_awr_down_line, bar_index)
            line.set_y2(half_awr_down_line, half_awr_down)

            if show_labels
                label.set_x(half_awr_up_label, bar_index + label_x_offset_bars)
                label.set_y(half_awr_up_label, half_awr_up + label_y_offset)
                label.set_x(half_awr_down_label, bar_index + label_x_offset_bars)
                label.set_y(half_awr_down_label, half_awr_down + label_y_offset)

// Update lines during the current month
if in_current_month and true_month_line_active and show_monthly_levels
    // Use our improved drawing method for all timeframes
    if barstate.islast or (bar_index % 100 == 0)  // Only redraw periodically to improve performance
        draw_monthly_levels()
    else
        // For other timeframes, use the standard approach
        // Update true month open line
        line.set_x2(true_month_open_line, bar_index)
        line.set_y2(true_month_open_line, true_month_open_price)

        // Update true month open label
        if show_labels
            label.set_x(true_month_open_label, bar_index + label_x_offset_bars)
            label.set_y(true_month_open_label, true_month_open_price + label_y_offset)

        // Update AMR level lines
        if show_full_amr
            line.set_x2(full_amr_up_line, bar_index)
            line.set_y2(full_amr_up_line, full_amr_up)
            line.set_x2(full_amr_down_line, bar_index)
            line.set_y2(full_amr_down_line, full_amr_down)

            if show_labels
                label.set_x(full_amr_up_label, bar_index + label_x_offset_bars)
                label.set_y(full_amr_up_label, full_amr_up + label_y_offset)
                label.set_x(full_amr_down_label, bar_index + label_x_offset_bars)
                label.set_y(full_amr_down_label, full_amr_down + label_y_offset)

        if show_one_third_amr
            line.set_x2(one_third_amr_up_line, bar_index)
            line.set_y2(one_third_amr_up_line, one_third_amr_up)
            line.set_x2(one_third_amr_down_line, bar_index)
            line.set_y2(one_third_amr_down_line, one_third_amr_down)

            if show_labels
                label.set_x(one_third_amr_up_label, bar_index + label_x_offset_bars)
                label.set_y(one_third_amr_up_label, one_third_amr_up + label_y_offset)
                label.set_x(one_third_amr_down_label, bar_index + label_x_offset_bars)
                label.set_y(one_third_amr_down_label, one_third_amr_down + label_y_offset)

        if show_two_third_amr
            line.set_x2(two_third_amr_up_line, bar_index)
            line.set_y2(two_third_amr_up_line, two_third_amr_up)
            line.set_x2(two_third_amr_down_line, bar_index)
            line.set_y2(two_third_amr_down_line, two_third_amr_down)

            if show_labels
                label.set_x(two_third_amr_up_label, bar_index + label_x_offset_bars)
                label.set_y(two_third_amr_up_label, two_third_amr_up + label_y_offset)
                label.set_x(two_third_amr_down_label, bar_index + label_x_offset_bars)
                label.set_y(two_third_amr_down_label, two_third_amr_down + label_y_offset)

        if show_half_amr
            line.set_x2(half_amr_up_line, bar_index)
            line.set_y2(half_amr_up_line, half_amr_up)
            line.set_x2(half_amr_down_line, bar_index)
            line.set_y2(half_amr_down_line, half_amr_down)

            if show_labels
                label.set_x(half_amr_up_label, bar_index + label_x_offset_bars)
                label.set_y(half_amr_up_label, half_amr_up + label_y_offset)
                label.set_x(half_amr_down_label, bar_index + label_x_offset_bars)
                label.set_y(half_amr_down_label, half_amr_down + label_y_offset)

// === TIMEFRAME RESTRICTION ===
// Only apply timeframe restriction if show_only_intraday is true and we're on a daily or higher timeframe
if show_only_intraday and timeframe.isdaily
    // Daily levels
    line.delete(true_day_open_line)
    label.delete(true_day_open_label)
    line.delete(true_day_vertical)

    if show_full_adr
        line.delete(full_adr_up_line)
        line.delete(full_adr_down_line)
        label.delete(full_adr_up_label)
        label.delete(full_adr_down_label)

    if show_one_third_adr
        line.delete(one_third_adr_up_line)
        line.delete(one_third_adr_down_line)
        label.delete(one_third_adr_up_label)
        label.delete(one_third_adr_down_label)

    if show_two_third_adr
        line.delete(two_third_adr_up_line)
        line.delete(two_third_adr_down_line)
        label.delete(two_third_adr_up_label)
        label.delete(two_third_adr_down_label)

    if show_half_adr
        line.delete(half_adr_up_line)
        line.delete(half_adr_down_line)
        label.delete(half_adr_up_label)
        label.delete(half_adr_down_label)

    // Delete all daily labels in arrays
    delete_all_labels(tdo_labels)
    delete_all_labels(full_adr_up_labels)
    delete_all_labels(full_adr_down_labels)
    delete_all_labels(one_third_adr_up_labels)
    delete_all_labels(one_third_adr_down_labels)
    delete_all_labels(two_third_adr_up_labels)
    delete_all_labels(two_third_adr_down_labels)
    delete_all_labels(half_adr_up_labels)
    delete_all_labels(half_adr_down_labels)

    true_day_line_active := false

    // Weekly levels
    line.delete(true_week_open_line)
    label.delete(true_week_open_label)
    line.delete(true_week_vertical)

    if show_full_awr
        line.delete(full_awr_up_line)
        line.delete(full_awr_down_line)
        label.delete(full_awr_up_label)
        label.delete(full_awr_down_label)

    if show_one_third_awr
        line.delete(one_third_awr_up_line)
        line.delete(one_third_awr_down_line)
        label.delete(one_third_awr_up_label)
        label.delete(one_third_awr_down_label)

    if show_two_third_awr
        line.delete(two_third_awr_up_line)
        line.delete(two_third_awr_down_line)
        label.delete(two_third_awr_up_label)
        label.delete(two_third_awr_down_label)

    if show_half_awr
        line.delete(half_awr_up_line)
        line.delete(half_awr_down_line)
        label.delete(half_awr_up_label)
        label.delete(half_awr_down_label)

    // Delete all weekly labels in arrays
    delete_all_labels(two_labels)
    delete_all_labels(full_awr_up_labels)
    delete_all_labels(full_awr_down_labels)
    delete_all_labels(one_third_awr_up_labels)
    delete_all_labels(one_third_awr_down_labels)
    delete_all_labels(two_third_awr_up_labels)
    delete_all_labels(two_third_awr_down_labels)
    delete_all_labels(half_awr_up_labels)
    delete_all_labels(half_awr_down_labels)

    true_week_line_active := false

    // Monthly levels
    line.delete(true_month_open_line)
    label.delete(true_month_open_label)
    line.delete(true_month_vertical)

    if show_full_amr
        line.delete(full_amr_up_line)
        line.delete(full_amr_down_line)
        label.delete(full_amr_up_label)
        label.delete(full_amr_down_label)

    if show_one_third_amr
        line.delete(one_third_amr_up_line)
        line.delete(one_third_amr_down_line)
        label.delete(one_third_amr_up_label)
        label.delete(one_third_amr_down_label)

    if show_two_third_amr
        line.delete(two_third_amr_up_line)
        line.delete(two_third_amr_down_line)
        label.delete(two_third_amr_up_label)
        label.delete(two_third_amr_down_label)

    if show_half_amr
        line.delete(half_amr_up_line)
        line.delete(half_amr_down_line)
        label.delete(half_amr_up_label)
        label.delete(half_amr_down_label)

    // Delete all monthly labels in arrays
    delete_all_labels(tmo_labels)
    delete_all_labels(full_amr_up_labels)
    delete_all_labels(full_amr_down_labels)
    delete_all_labels(one_third_amr_up_labels)
    delete_all_labels(one_third_amr_down_labels)
    delete_all_labels(two_third_amr_up_labels)
    delete_all_labels(two_third_amr_down_labels)
    delete_all_labels(half_amr_up_labels)
    delete_all_labels(half_amr_down_labels)

    true_month_line_active := false

// === DATA TABLE ===
// Create tables for displaying range values
if show_data_table != "None" and barstate.islast
    // Determine table position
    var table_pos_right = position.bottom_right
    var table_pos_left = position.bottom_left

    if table_position == "Normal"
        table_pos_right := position.bottom_right
        table_pos_left := position.bottom_left
    else if table_position == "Center"
        table_pos_right := position.middle_right
        table_pos_left := position.middle_left
    else if table_position == "Bottom"
        table_pos_right := position.bottom_right
        table_pos_left := position.bottom_left

    // Create tables
    var table range_table_right = table.new(table_pos_right, 5, 6, color.new(color.black, 70))
    var table range_table_left = na

    if show_data_table == "Both" or show_data_table == "Left"
        range_table_left := table.new(table_pos_left, 5, 6, color.new(color.black, 70))

    // Clear tables
    table.clear(range_table_right)
    if show_data_table == "Both" or show_data_table == "Left"
        table.clear(range_table_left)

    // Set up table headers
    table.cell(range_table_right, 0, 0, "AR Levels", text_color=color.white, bgcolor=color.new(color.gray, 20))
    table.cell(range_table_right, 1, 0, "Daily", text_color=color.white, bgcolor=color.new(color.gray, 20))
    table.cell(range_table_right, 2, 0, "Weekly", text_color=color.white, bgcolor=color.new(color.gray, 20))
    table.cell(range_table_right, 3, 0, "Monthly", text_color=color.white, bgcolor=color.new(color.gray, 20))

    if show_data_table == "Both" or show_data_table == "Left"
        table.cell(range_table_left, 0, 0, "AR Levels", text_color=color.white, bgcolor=color.new(color.gray, 20))
        table.cell(range_table_left, 1, 0, "Daily", text_color=color.white, bgcolor=color.new(color.gray, 20))
        table.cell(range_table_left, 2, 0, "Weekly", text_color=color.white, bgcolor=color.new(color.gray, 20))
        table.cell(range_table_left, 3, 0, "Monthly", text_color=color.white, bgcolor=color.new(color.gray, 20))

    // Row labels
    table.cell(range_table_right, 0, 1, "Full+", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table_right, 0, 2, "1/3+", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table_right, 0, 3, "TDO/TWO/TMO", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table_right, 0, 4, "1/3-", text_color=color.white, bgcolor=color.new(color.black, 70))
    table.cell(range_table_right, 0, 5, "Full-", text_color=color.white, bgcolor=color.new(color.black, 70))

    if show_data_table == "Both" or show_data_table == "Left"
        table.cell(range_table_left, 0, 1, "Full+", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table_left, 0, 2, "1/3+", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table_left, 0, 3, "TDO/TWO/TMO", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table_left, 0, 4, "1/3-", text_color=color.white, bgcolor=color.new(color.black, 70))
        table.cell(range_table_left, 0, 5, "Full-", text_color=color.white, bgcolor=color.new(color.black, 70))

    // Fill in values for Daily levels
    if array.size(daily_tdo_values) > 0 and show_daily_levels
        // Get the most recent values
        float tdo = array.get(daily_tdo_values, 0)
        float full_up = array.get(daily_full_up_values, 0)
        float one_third_up = array.get(daily_one_third_up_values, 0)
        float one_third_down = array.get(daily_one_third_down_values, 0)
        float full_down = array.get(daily_full_down_values, 0)

        // Add to table
        table.cell(range_table_right, 1, 1, str.tostring(full_up, "#.##"), text_color=full_adr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 1, 2, str.tostring(one_third_up, "#.##"), text_color=one_third_adr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 1, 3, str.tostring(tdo, "#.##"), text_color=true_day_open_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 1, 4, str.tostring(one_third_down, "#.##"), text_color=one_third_adr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 1, 5, str.tostring(full_down, "#.##"), text_color=full_adr_color, bgcolor=color.new(color.black, 70))

        if show_data_table == "Both" or show_data_table == "Left"
            table.cell(range_table_left, 1, 1, str.tostring(full_up, "#.##"), text_color=full_adr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 1, 2, str.tostring(one_third_up, "#.##"), text_color=one_third_adr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 1, 3, str.tostring(tdo, "#.##"), text_color=true_day_open_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 1, 4, str.tostring(one_third_down, "#.##"), text_color=one_third_adr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 1, 5, str.tostring(full_down, "#.##"), text_color=full_adr_color, bgcolor=color.new(color.black, 70))

    // Fill in values for Weekly levels
    if array.size(weekly_two_values) > 0 and show_weekly_levels
        // Get the most recent values
        float two = array.get(weekly_two_values, 0)
        float full_up = array.get(weekly_full_up_values, 0)
        float one_third_up = array.get(weekly_one_third_up_values, 0)
        float one_third_down = array.get(weekly_one_third_down_values, 0)
        float full_down = array.get(weekly_full_down_values, 0)

        // Add to table
        table.cell(range_table_right, 2, 1, str.tostring(full_up, "#.##"), text_color=full_awr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 2, 2, str.tostring(one_third_up, "#.##"), text_color=one_third_awr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 2, 3, str.tostring(two, "#.##"), text_color=true_week_open_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 2, 4, str.tostring(one_third_down, "#.##"), text_color=one_third_awr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 2, 5, str.tostring(full_down, "#.##"), text_color=full_awr_color, bgcolor=color.new(color.black, 70))

        if show_data_table == "Both" or show_data_table == "Left"
            table.cell(range_table_left, 2, 1, str.tostring(full_up, "#.##"), text_color=full_awr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 2, 2, str.tostring(one_third_up, "#.##"), text_color=one_third_awr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 2, 3, str.tostring(two, "#.##"), text_color=true_week_open_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 2, 4, str.tostring(one_third_down, "#.##"), text_color=one_third_awr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 2, 5, str.tostring(full_down, "#.##"), text_color=full_awr_color, bgcolor=color.new(color.black, 70))

    // Fill in values for Monthly levels
    if array.size(monthly_tmo_values) > 0 and show_monthly_levels
        // Get the most recent values
        float tmo = array.get(monthly_tmo_values, 0)
        float full_up = array.get(monthly_full_up_values, 0)
        float one_third_up = array.get(monthly_one_third_up_values, 0)
        float one_third_down = array.get(monthly_one_third_down_values, 0)
        float full_down = array.get(monthly_full_down_values, 0)

        // Add to table
        table.cell(range_table_right, 3, 1, str.tostring(full_up, "#.##"), text_color=full_amr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 3, 2, str.tostring(one_third_up, "#.##"), text_color=one_third_amr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 3, 3, str.tostring(tmo, "#.##"), text_color=true_month_open_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 3, 4, str.tostring(one_third_down, "#.##"), text_color=one_third_amr_color, bgcolor=color.new(color.black, 70))
        table.cell(range_table_right, 3, 5, str.tostring(full_down, "#.##"), text_color=full_amr_color, bgcolor=color.new(color.black, 70))

        if show_data_table == "Both" or show_data_table == "Left"
            table.cell(range_table_left, 3, 1, str.tostring(full_up, "#.##"), text_color=full_amr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 3, 2, str.tostring(one_third_up, "#.##"), text_color=one_third_amr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 3, 3, str.tostring(tmo, "#.##"), text_color=true_month_open_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 3, 4, str.tostring(one_third_down, "#.##"), text_color=one_third_amr_color, bgcolor=color.new(color.black, 70))
            table.cell(range_table_left, 3, 5, str.tostring(full_down, "#.##"), text_color=full_amr_color, bgcolor=color.new(color.black, 70))

// === ALERT CONDITIONS ===
// Alert conditions for price touching ADR levels
alertcondition(ta.crossover(high, full_adr_up) or ta.crossunder(low, full_adr_up), "Price touched Full ADR+", "Price touched Full ADR+ level")
alertcondition(ta.crossover(high, full_adr_down) or ta.crossunder(low, full_adr_down), "Price touched Full ADR-", "Price touched Full ADR- level")
alertcondition(ta.crossover(high, one_third_adr_up) or ta.crossunder(low, one_third_adr_up), "Price touched 1/3 ADR+", "Price touched 1/3 ADR+ level")
alertcondition(ta.crossover(high, one_third_adr_down) or ta.crossunder(low, one_third_adr_down), "Price touched 1/3 ADR-", "Price touched 1/3 ADR- level")
alertcondition(ta.crossover(high, two_third_adr_up) or ta.crossunder(low, two_third_adr_up), "Price touched 2/3 ADR+", "Price touched 2/3 ADR+ level")
alertcondition(ta.crossover(high, two_third_adr_down) or ta.crossunder(low, two_third_adr_down), "Price touched 2/3 ADR-", "Price touched 2/3 ADR- level")
alertcondition(ta.crossover(high, half_adr_up) or ta.crossunder(low, half_adr_up), "Price touched 1/2 ADR+", "Price touched 1/2 ADR+ level")
alertcondition(ta.crossover(high, half_adr_down) or ta.crossunder(low, half_adr_down), "Price touched 1/2 ADR-", "Price touched 1/2 ADR- level")

// Alert conditions for price touching AWR levels
alertcondition(ta.crossover(high, full_awr_up) or ta.crossunder(low, full_awr_up), "Price touched Full AWR+", "Price touched Full AWR+ level")
alertcondition(ta.crossover(high, full_awr_down) or ta.crossunder(low, full_awr_down), "Price touched Full AWR-", "Price touched Full AWR- level")
alertcondition(ta.crossover(high, one_third_awr_up) or ta.crossunder(low, one_third_awr_up), "Price touched 1/3 AWR+", "Price touched 1/3 AWR+ level")
alertcondition(ta.crossover(high, one_third_awr_down) or ta.crossunder(low, one_third_awr_down), "Price touched 1/3 AWR-", "Price touched 1/3 AWR- level")
alertcondition(ta.crossover(high, two_third_awr_up) or ta.crossunder(low, two_third_awr_up), "Price touched 2/3 AWR+", "Price touched 2/3 AWR+ level")
alertcondition(ta.crossover(high, two_third_awr_down) or ta.crossunder(low, two_third_awr_down), "Price touched 2/3 AWR-", "Price touched 2/3 AWR- level")
alertcondition(ta.crossover(high, half_awr_up) or ta.crossunder(low, half_awr_up), "Price touched 1/2 AWR+", "Price touched 1/2 AWR+ level")
alertcondition(ta.crossover(high, half_awr_down) or ta.crossunder(low, half_awr_down), "Price touched 1/2 AWR-", "Price touched 1/2 AWR- level")

// Alert conditions for price touching AMR levels
alertcondition(ta.crossover(high, full_amr_up) or ta.crossunder(low, full_amr_up), "Price touched Full AMR+", "Price touched Full AMR+ level")
alertcondition(ta.crossover(high, full_amr_down) or ta.crossunder(low, full_amr_down), "Price touched Full AMR-", "Price touched Full AMR- level")
alertcondition(ta.crossover(high, one_third_amr_up) or ta.crossunder(low, one_third_amr_up), "Price touched 1/3 AMR+", "Price touched 1/3 AMR+ level")
alertcondition(ta.crossover(high, one_third_amr_down) or ta.crossunder(low, one_third_amr_down), "Price touched 1/3 AMR-", "Price touched 1/3 AMR- level")
alertcondition(ta.crossover(high, two_third_amr_up) or ta.crossunder(low, two_third_amr_up), "Price touched 2/3 AMR+", "Price touched 2/3 AMR+ level")
alertcondition(ta.crossover(high, two_third_amr_down) or ta.crossunder(low, two_third_amr_down), "Price touched 2/3 AMR-", "Price touched 2/3 AMR- level")
alertcondition(ta.crossover(high, half_amr_up) or ta.crossunder(low, half_amr_up), "Price touched 1/2 AMR+", "Price touched 1/2 AMR+ level")
alertcondition(ta.crossover(high, half_amr_down) or ta.crossunder(low, half_amr_down), "Price touched 1/2 AMR-", "Price touched 1/2 AMR- level")